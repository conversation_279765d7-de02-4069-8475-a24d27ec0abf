const CompressionPlugin = require('compression-webpack-plugin')

module.exports = {
  publicPath: process.env.VUE_APP_PUBILC_URL,
  outputDir: 'proof-derate-mobile',
  devServer: {
    // host: 'localhost',
    // port: '8081',
    proxy: {
      '/proof-derate-api': {
        // target: 'http://*************:56554/proof-derate-api',
        target: 'https://*************:6041/proof-derate-api',
        changeOrigin: true,
        pathRewrite: {
          '^/proof-derate-api': '/'
        }
      }
    }
  },
  // 如果你不需要生产环境的 source map，可以将其设置为 false 以加速生产环境构建。
  productionSourceMap: false,

  configureWebpack: config => {
    if (process.env.NODE_ENV === 'production') {
      //生产环境是否屏蔽console
      config.optimization.minimizer[0].options.terserOptions.compress.drop_console = true
      //启用gizp压缩
      config.plugins.push(
        new CompressionPlugin({
          filename: '[path].gz[query]', // 提示compression-webpack-plugin@3.0.0的话asset改为filename
          algorithm: 'gzip',
          test: new RegExp('\\.(' + ['js', 'css'].join('|') + ')$'),
          threshold: 10240,
          minRatio: 0.8
        })
      )
    }
  }
}
