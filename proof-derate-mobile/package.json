{"name": "assist-tool", "version": "0.0.1", "private": true, "scripts": {"serve": "vue-cli-service serve --mode dev", "build:prod": "vue-cli-service build", "build:stage": "vue-cli-service build --mode staging"}, "dependencies": {"axios": "^0.21.1", "core-js": "^3.6.5", "element-ui": "^2.15.1", "sass": "^1.26.3", "sass-loader": "^8.0.2", "vant": "^2.12.23", "vue": "^2.6.11", "vue-router": "^3.2.0", "vuex": "^3.4.0"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-router": "~4.5.0", "@vue/cli-plugin-vuex": "~4.5.0", "@vue/cli-service": "~4.5.0", "babel-plugin-component": "^1.1.1", "compression-webpack-plugin": "^5.0.1", "less": "^4.1.1", "less-loader": "^7.3.0", "sass-loader": "^8.0.2", "vue-template-compiler": "^2.6.11"}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}