import { getAction, postAction, getActionAndHeaders,postActionAndHeaders } from '@/utils/util'

// 综受--获取协查详情
export const getAssistDetailForGeneralAccept = data => getAction(process.env.VUE_APP_BASE_API + '/api/v1/assist/detail', data)

// 综受--获取协查历史列表
export const getAssistHistoryForGeneralAccept = data => getAction(process.env.VUE_APP_BASE_API + '/api/v1/assist/history', data)

// 综受--获取协查历史详情
export const getAssistHistoryDetailForGeneralAccept = data => getAction(process.env.VUE_APP_BASE_API + '/api/v1/assist/detail', data)

// 以下是粤证易与消息入口接口

// 获取协查详情
export const getAssistDetail = data => getAction(process.env.VUE_APP_BASE_API + '/assist/detail', data)

// 获取协查历史列表
export const getAssistHistory = data => getAction(process.env.VUE_APP_BASE_API + '/assist/history', data)

// 获取协查历史详情
export const getAssistHistoryDetail = data => getAction(process.env.VUE_APP_BASE_API + '/assist/detail', data)

// 提交协查审核
export const postAssistAudit = data => postAction(process.env.VUE_APP_BASE_API + '/assist/audit', data)

// 获取粤证易用户id
export const getAssistEasyAccount = data => getAction(process.env.VUE_APP_BASE_API + '/api/v1/gov_easy_user_id', data)

// 校验粤证易跳转到协查工具
export const authThirdPartyApi = process.env.VUE_APP_BASE_API + '/api/v1/auth_third_party'

// 校验第三方接入系统跳转到协查工具
export const authGovEasyUserApi = process.env.VUE_APP_BASE_API + '/api/v1/auth_gov_easy_user'

// ********版本新增

// 微信登录获取用户信息
export const getUserInfo = data => getAction(process.env.VUE_APP_BASE_API + '/api/v1/get_gov_easy_user_by_code', data)

// 粤政易代办列表
export const getTodoList = data => getAction(process.env.VUE_APP_BASE_API + '/api/v1/user_wait_assist', data)

// 粤政易协查档案列表
export const getArchivesList = data => getAction(process.env.VUE_APP_BASE_API + '/assist/history_page', data)

// 查询数据共享数据对外接口
export const getDataSharedData = (data, config) => getActionAndHeaders(process.env.VUE_APP_BASE_API + '/api/v1/exempt_certificates/api/get_data_shared_data', data, config)

// 去除token账号协查账号
export const getAccountByGovEasyUser = data => getAction(process.env.VUE_APP_BASE_API + '/gov_easy/api/common/v1/get_account_by_gov_easy_user', data)


export const downloadFile = (data, config) => postActionAndHeaders(process.env.VUE_APP_BASE_API +'/gov_easy/api/v1/download_file',data,config)


export const downloadAttachment = (data) => getAction(process.env.VUE_APP_BASE_API + '/gov_easy/api/v1/common/license_item/download_attachment', data)


// 根据粤证易个人授权页回调code，获取无证明城市绑定的用户信息
export const personPageCodeToUserInfo = (data) => getAction(process.env.VUE_APP_BASE_API + '/gov_easy/api/common/v1/person_page_code_to_user_info', data)

// 根据粤证易回调的code，获取当前用户的userId和token
export const getGovEasyUserId = (data) => getAction(process.env.VUE_APP_BASE_API + '/api/v1/gov_easy_user_id', data)

// 根据粤证易当前userId，获取当前账号
export const getAccountByUserId = (data) => getAction(process.env.VUE_APP_BASE_API + '/gov_easy/api/common/v1/get_account_by_user_id', data)

// 获取粤政易-个人授权跳转url
export const getPersonAuthUrl = (data) => getAction(process.env.VUE_APP_BASE_API + '/gov_easy/api/common/v1/get_person_auth_url', data)
