import Vue from 'vue'
import VueRouter from 'vue-router'

Vue.use(VueRouter)
const pageTitlt = ''
const routes = [
  // 综受-协查处理跳转页
  {
    path: '/generalAccept/index',
    name: 'generalAcceptIndex',
    component: () => import('../views/generalAccept/index.vue'),
    meta: {
      title: pageTitlt + '协查处理-首页',
      keepAlive: true // 需要缓存
    }
  },
  {
    path: '/generalAccept/outSys',
    name: 'generalAcceptOutSys',
    component: () => import('../views/generalAccept/outSys.vue'),
    meta: {
      title: pageTitlt + '外部链接',
      keepAlive: true // 需要缓存
    }
  },
  
  // 综受-协查处理首页
  {
    path: '/generalAccept/assist',
    name: 'generalAcceptAssist',
    component: () => import('../views/generalAccept/assist.vue'),
    meta: {
      title: pageTitlt + '协查处理',
      keepAlive: true // 需要缓存
    }
  },
  // 协查处理详细页
  {
    path: '/generalAccept/detail',
    name: 'generalAcceptDetail',
    component: () => import('../views/generalAccept/detail.vue'),
    meta: {
      title: pageTitlt + '协查处理',
      keepAlive: true // 需要缓存
    }
  },
  // 协查处理结果页
  {
    path: '/generalAccept/result',
    name: 'generalAcceptResult',
    component: () => import('../views/generalAccept/result.vue'),
    meta: {
      title: pageTitlt + '协查处理',
      keepAlive: true // 需要缓存
    }
  },
  // 粤证易-协查处理跳转页
  {
    path: '/certEasy/index/:assistSerialNumber/:loginType',
    name: 'certEasyIndex',
    component: () => import('../views/certEasy/index.vue'),
    meta: {
      title: pageTitlt + '粤证易-首页',
      keepAlive: true // 需要缓存
    }
  },
  // 粤证易-协查处理首页
  {
    path: '/certEasy/assist',
    name: 'certEasyAssist',
    component: () => import('../views/certEasy/assist.vue'),
    meta: {
      title: pageTitlt + '协查处理',
      keepAlive: false // 需要缓存
    }
  },
  // 粤证易-协查处理详细页
  {
    path: '/certEasy/detail',
    name: 'certEasyDetail',
    component: () => import('../views/certEasy/detail.vue'),
    meta: {
      title: pageTitlt + '协查处理',
      keepAlive: false // 需要缓存
    }
  },
  // 粤证易-协查处理结果页
  {
    path: '/certEasy/result',
    name: 'certEasyResult',
    component: () => import('../views/certEasy/result.vue'),
    meta: {
      title: pageTitlt + '协查处理',
      keepAlive: false // 需要缓存
    }
  },

  // 清远粤证易-中转页
  {
    path: '/index/:loginType',
    name: 'index',
    component: () => import('../views/home/<USER>'),
    meta: {
      title: pageTitlt + '工作台',
      keepAlive: true // 需要缓存
    }
  },
    // 清远粤证易-重新授权-中转页
    {
      path: '/authorizeIndex/:loginType',
      name: 'authorizeIndex',
      component: () => import('../views/home/<USER>'),
      meta: {
        title: pageTitlt + '工作台',
        keepAlive: true // 需要缓存
      }
    },
  // 清远粤证易-首页
  {
    path: '/home',
    name: 'home',
    component: () => import('../views/home/<USER>'),
    meta: {
      title: pageTitlt + '工作台',
      keepAlive: true // 需要缓存
    }
  },
  // 清远粤证易-待处理协查列表
  {
    path: '/home/<USER>',
    name: 'homeTodoList',
    component: () => import('../views/home/<USER>'),
    meta: {
      title: pageTitlt + '待处理协查列表',
      keepAlive: true // 需要缓存
    }
  },
  // 清远粤证易-协查档案列表
  {
    path: '/home/<USER>',
    name: 'homeArchivesList',
    component: () => import('../views/home/<USER>'),
    meta: {
      title: pageTitlt + '协查档案列表',
      keepAlive: true // 需要缓存
    }
  },
  {
    path: '/outSystemLink',
    name: 'outSystemLink',
    component: () => import('../views/outSystemLink/index.vue'),
    meta: {
      title: pageTitlt + '外部链接',
      keepAlive: true // 需要缓存
    }
  },
  {
    path: '/license-info',
    name: 'license-info',
    component: () => import('../views/license-info/license-info.vue'),
    meta: {
      title: pageTitlt + '外部链接',
      keepAlive: true // 需要缓存
    }
  }
]

const router = new VueRouter({
  mode: 'history',
  base: '/assist-tools/',
  routes
})

router.beforeEach((to, from, next) => {
  /* 路由发生变化修改页面title */
  if (to.meta.title) {
    document.title = to.meta.title
  }
  next()
})

export default router
