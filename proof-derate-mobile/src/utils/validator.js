import {id,tyshxydm,tempTyshxydm,zzjgdm,mobile} from "@/utils/validType";

/**
 * 判断是否是身份证号码
 * @param {*} value 
 * @param {*} callback 
 */
export const isIdNumber = (rule, value, callback) => {
    if (!value) {
        callback(new Error('请输入身份证件号码'));
    }
    let rex_id = id;
    let rex_tyshxydm = tyshxydm;
    let rex_tempTyshxydm = tempTyshxydm;
    let rex_zzjgdm = zzjgdm;
    if (rex_id.test(value) || rex_tyshxydm.test(value) || rex_tempTyshxydm.test(value) || rex_zzjgdm.test(value)) {
        callback()
    }
    callback(new Error('请输入正确的身份证件号码'))
}

/**
 * 判断手机号码是否正确
 * @param {*} rule 
 * @param {*} value 
 * @param {*} callback 
 */
export const isMobile = (rule, value, callback) => {
    if (!value) {
        callback(new Error('请输入手机号码'));
    }
    let pattern = mobile
    if (pattern.test(value)) {
        callback()
    }
    callback(new Error('请输入正确的手机号码'))
}

/**
 * 判断是否有附件
 * @param {*} rule 
 * @param {*} value 
 * @param {*} callback 
 */
export const hasAttachment = (rule, value, callback) => {
    if (value.length > 0) {
        callback();
    } else {
        callback(new Error('请上传附件'));
    }
}
