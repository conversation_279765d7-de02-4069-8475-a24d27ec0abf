import axios from '@/utils/http'
import qs from 'qs'
import { Dialog, Toast } from 'vant'
/**
 * post请求
 *
 * @param {*} url url地址
 * @param {*} data 请求参数
 * @returns 请求结果
 */
export function postAction(url, data) {
  console.log(`requestUrl=${url}===requestParam=${data}`)
  // 这里请求一个http
  return new Promise((reslove, reject) => {
    axios({
      url: url,
      method: 'post',
      data: data ? data : {}
    })
      .then(res => {
        if (res.data.meta.code === '200') {
          reslove(res)
        } else if (res.data.meta.code === '2010814000') {
          // 无权限操作
          Toast({
            message: err.response.data.meta.message,
            overlay: true,
            forbidClick: true,
            duration: '0'
          })
        } else {
          reject(res)
          // alert(res.data.message)
        }
      })
      .catch(err => {
        reject(err)
        console.log('==请求失败==', err)
      })
  })
}

/**
 * get请求
 *
 * @param {*} url url地址
 * @param {*} params 请求参数
 * @returns 请求结果
 */
export function getAction(url, params) {
  console.log(`requestUrl=${url}===requestParam=${params}`)
  return new Promise((reslove, reject) => {
    axios({
      url: url,
      method: 'get',
      params: params ? params : {},
      paramsSerializer: params => {
        return qs.stringify(params, { arrayFormat: 'repeat' })
      }
    })
      .then(res => {
        console.log(res)
        if (res.data.meta.code === '200' || res.data.meta.code === '2C060106000') {
          reslove(res)
        } else {
          reject(res)
        }
      })
      .catch(err => {
        if (err.response.data.meta.code === '2010814000') {
          // 无权限操作

          Toast({
            message: err.response.data.meta.message,
            overlay: true,
            forbidClick: true,
            duration: '0'
          })
        } else {
          reject(err)
        }

        console.log('==请求失败==', err)
      })
  })
}

/**
 * get请求 带上请求头
 *
 * @param {*} url url地址
 * @param {*} params 请求参数
 * @param {*} config 请求头
 * @returns 请求结果
 */
export function getActionAndHeaders(url, params, config) {
  // console.log(`requestUrl=${url}===requestParam=${params}`,config)
  return new Promise((reslove, reject) => {
    axios({
      url: url,
      method: 'get',
      params: params ? params : {},
      headers: config.headers

      // paramsSerializer: params => {
      //   return qs.stringify(params, { arrayFormat: 'repeat' })
      // }
    })
      .then(res => {
        if (res.data.meta.code === '200') {
          reslove(res)
        } else {
          reject(res)
        }
      })
      .catch(err => {
        if (err.response.data.meta.code === '2010814000') {
          // 无权限操作

          Toast({
            message: err.response.data.meta.message,
            overlay: true,
            forbidClick: true,
            duration: '0'
          })
        } else {
          reject(err)
        }

        console.log('==请求失败==', err)
      })
  })
}

/**
 * post请求 带上请求头
 *
 * @param {*} url url地址
 * @param {*} data 请求参数
 * @param {*} config 请求头
 * @returns 请求结果
 */
export function postActionAndHeaders(url, data, config) {
  console.log(`config`,config.headers)
  return new Promise((reslove, reject) => {
    axios({
      url: url,
      method: 'post',
      params: data ? data : {},
      headers: config.headers

      // paramsSerializer: params => {
      //   return qs.stringify(params, { arrayFormat: 'repeat' })
      // }
    })
      .then(res => {
        if (res.data.meta.code === '200') {
          reslove(res)
        } else {
          reject(res)
        }
      })
      .catch(err => {
        if (err.response.data.meta.code === '2010814000') {
          // 无权限操作

          Toast({
            message: err.response.data.meta.message,
            overlay: true,
            forbidClick: true,
            duration: '0'
          })
        } else {
          reject(err)
        }

        console.log('==请求失败==', err)
      })
  })
}

/**
 * 上传文件获取文件id
 * @param param
 * @returns {AxiosPromise}
 */
export function uploadFile(params,data) {
  console.log('params',data)
  return axios({
    url: '/proof-derate-api/gov_easy/api/v1/upload_file',
    method: 'POST',
    params: params,
    data: data
  })
}

// base64导出文件并下载
export function dataURLtoDownload(dataurl, name) {
  var bstr = atob(dataurl) // 解析 base-64 编码的字符串
  var n = bstr.length
  var u8arr = new Uint8Array(n) // 创建初始化为0的，包含length个元素的无符号整型数组
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n) // 返回字符串第一个字符的 Unicode 编码
  }
  const blob = new Blob([u8arr]) // 转化成blob
  const url = URL.createObjectURL(blob) // 这个新的URL 对象表示指定的 File 对象或 Blob 对象
  const a = document.createElement('a') // 创建一个a标签
  a.href = url
  a.download = name
  a.click()
  URL.revokeObjectURL(a.href) // 释放之前创建的url对象
}