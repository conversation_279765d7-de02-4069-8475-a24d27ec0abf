import axios from 'axios'


axios.defaults.withCredentials = true
// create an axios instance
const service = axios.create({
    baseURL: '', // api 的 base_url
    timeout: 20000 // request timeout
})

// request interceptor
service.interceptors.request.use(
  config => {
    // 此处进行鉴权信息配置
    let appkey = sessionStorage.getItem('x-license-appkey');
    let timestamp = sessionStorage.getItem('x-license-timestamp');
    let signature = sessionStorage.getItem('x-license-sign');
    let requestId = sessionStorage.getItem('x-license-requestid');
    let jwtToken = sessionStorage.getItem('jwtToken');
    if (appkey && timestamp && signature && requestId) {
      config.headers = {
        "x-license-appkey": appkey,
        "x-license-timestamp": timestamp,
        "x-license-sign": signature,
        "x-license-requestid": requestId
      };
    }else if(jwtToken){
      config.headers = {
        "GOV-EASY-JWT-AUTHORIZATION": jwtToken
      };
    }
    
    return config
  },
  error => {
    // Do something with request error
    Promise.reject(error)
  }
)

// response interceptor
service.interceptors.response.use(
  // response => {

  //   return response
  // },
  /**
   * 下面的注释为通过在response里，自定义code来标示请求状态
   * 当code返回如下情况则说明权限有问题，登出并返回到登录页
   * 如想通过 xmlhttprequest 来状态码标识 逻辑可写在下面error中
   * 以下代码均为样例，请结合自生需求加以修改，若不需要，则可删除
   */
  response => {
    const res = response.data
    if (res.status === 200) {
      return Promise.reject(response)
    } else {
      return response
    }
  },
  err => {
    // endLoading()
    return Promise.reject(err)
  }
)


export default service
