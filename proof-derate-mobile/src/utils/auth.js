import axios from '@/utils/http'
import qs from 'qs'
import {
  authThirdPartyApi,
  authGovEasyUserApi
} from "@/api/assist";

/**
 * 根据鉴权参数刷新token值
 * 
 * @param {*} appKey  app标识
 * @param {*} timestamp  时间戳
 * @param {*} signature  签名值
 * @param {*} requestId  请求唯一标识
 * @param {*} userToken  用户信息
 */
export function setToken(appKey, timestamp, signature, requestId) {
  let params = {
    "x-license-appkey": appKey,
    "x-license-timestamp": timestamp,
    "x-license-sign": signature,
    "x-license-requestid": requestId
  };
  sessionStorage.setItem('x-license-appkey', "");
  sessionStorage.setItem('x-license-timestamp', "");
  sessionStorage.setItem('x-license-sign', "");
  sessionStorage.setItem('x-license-requestid', "");
  return new Promise((reslove, reject) => {
    axios({
      url: authThirdPartyApi,
      method: 'get',
      headers: params,
      params: {"x-license-appkey": appKey},
      paramsSerializer: params => {
        return qs.stringify(params, { arrayFormat: 'repeat' })
      }
    })
      .then(res => {
        if (res.data.meta.code === '200') {
          sessionStorage.setItem('x-license-appkey', appKey);
          sessionStorage.setItem('x-license-timestamp', timestamp);
          sessionStorage.setItem('x-license-sign', signature);
          sessionStorage.setItem('x-license-requestid', requestId);
          reslove(res);
        }
        else {
          reject(res)
        }
      })
      .catch(err => {
        reject(err);
      })
  });
}


/**
 * 根据鉴权token获取用户id
 * 
 * @param {*} token
 * @param {*} assistSerislNumber
 */
export function checkToken(token, assistSerialNumber) {
  sessionStorage.removeItem('x-license-appkey');
  sessionStorage.removeItem('x-license-timestamp');
  sessionStorage.removeItem('x-license-sign');
  sessionStorage.removeItem('x-license-requestid');
  return new Promise((reslove, reject) => {
    axios({
      url: authGovEasyUserApi,
      method: 'get',
      params: {"token": token, "assistSerialNumber": assistSerialNumber},
      paramsSerializer: params => {
        return qs.stringify(params, { arrayFormat: 'repeat' })
      }
    })
      .then(res => {
        if (res.data.meta.code === '200') {
          reslove(res);
        }
        else {
          reject(res)
        }
      })
      .catch(err => {
        reject(err);
      })
  });
}