<template>
  <div>
        <div class="search">
          <van-sticky>
            <van-search
                v-model="keyVal"
                shape="round"
                background="#4293F4"
                placeholder="请输入搜索证件关键词"
                @input="onSearch"
                @cancel="onCancel"
                show-action
            />
          </van-sticky>

        </div>
        <div class="data">
          <van-cell-group v-if="dataList.length > 0">
            <van-cell v-for="item in filterData" :title="item.label" @click="change(item)">
              <template #right-icon>
                <van-button type="info" size="mini">选择</van-button>
              </template>
            </van-cell>
          </van-cell-group>
        </div>

  </div>

</template>

<script>
export default {
  name: "LicenseSearch",
  props: {
    dataList: {
      type: Array,
      default: []
    },
    value:{
      type: Object,
      default: {}
    }
  },
  data: function () {
    return {
      keyVal: '',
      filterData:this.dataList
    }
  },
  created() {
    if((typeof this.value=="object")){
      if(Object.keys(this.value).length>0){
        this.keyVal = this.value.label;
        this.onSearch();
      }
    }
  },
  methods: {
    onSearch() {
      if (this.keyVal != "") {
        setTimeout(() => {
          if(this.dataList.length > 0){
            this.filterData = this.dataList.filter((item) => {
              return item.label.indexOf(this.keyVal) > -1;
            });
          }
        }, 500);
      }else{
        this.filterData = this.dataList;
      }
    },
    onCancel(){
      this.$emit("change", {label:'',value:''});
    },
    change(e){
      this.$emit("change",e);
    },
  },
}
</script>

<style scoped>

.search{
  width: 100%;
}
.data{
  margin-top: 10px;
}
/deep/ .data .van-cell{
  text-align: left;
}
</style>