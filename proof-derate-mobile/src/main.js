import Vue from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'

import Vant from 'vant';
import 'vant/lib/index.css';
Vue.use(Vant);

import {
    Button,
    Form,
    FormItem,
    Input,
    Col,
    Table,
    TableColumn,
    Tag,
    Pagination,
    Row,
    Radio,
    RadioGroup,
    Upload,
    Dialog,
    Image,
    Select,
    Option,
    Message,
    Icon,
    Tabs,
    TabPane,
    Card,
    Divider
} from 'element-ui';
import 'element-ui/lib/theme-chalk/index.css';

Vue.use(Button);
Vue.use(Form);
Vue.use(FormItem);
Vue.use(Input);
Vue.use(Col);
Vue.use(Table);
Vue.use(TableColumn);
Vue.use(Tag);
Vue.use(Pagination);
Vue.use(Row);
Vue.use(Radio);
Vue.use(RadioGroup);
Vue.use(Upload);
Vue.use(Dialog);
Vue.use(Image);
Vue.use(Select);
Vue.use(Option);
Vue.use(Icon);
Vue.use(Tabs);
Vue.use(TabPane);
Vue.use(Card);
Vue.use(Divider);
Vue.prototype.$message = Message
Vue.component(Message.name, Message) //解决默认弹出Message 提示框


Vue.config.productionTip = false

new Vue({
    router,
    store,
    render: h => h(App)
}).$mount('#app')