<template>
  <div class="content-container preview">
    <el-card>
      <div class="top-content">
        <div class="select">
          <el-select v-model="licenseName" clearable placeholder="请选择" @change="licenseChange">
            <el-option v-for="(item,idx) in licenseList" :key="idx" :label="item.licenseName" :value="item.licenseName" />
          </el-select>
          <span class="explain">点击左侧下拉框查验其他证照</span>
        </div>
        <!-- <div class="right-btn">
          <el-button @click="toBack">返回上一步</el-button>
          <el-button type="primary" @click="toScan">查验完成</el-button>
        </div>-->
      </div>
      <div class="header">
        <h3>{{ imgReviewData[0].licenseName || licenseName }}（{{ imgReviewData[0].licenseNumber }}）</h3>
      </div>
      <el-tabs v-model="activeName" type="border-card">
        <el-tab-pane v-loading="imgLoading" label="照面" name="pdf">
          <img-review :data="imgReviewData" />
        </el-tab-pane>
        <el-tab-pane v-loading="dataLoading" label="数据" name="data">
          <data-review :data="dataReviewData" />
        </el-tab-pane>
      </el-tabs>
    </el-card>
    <!-- <success /> -->
  </div>
</template>

<script>
// import { getPdf, getDataItem } from '@/api/licenseManage'

export default {
  components: {
    ImgReview: () => import('./components/imgReview.vue'), // pdf预览
    DataReview: () => import('./components/dataReviiew.vue') // 数据预览
  },
  data() {
    return {
      activeName: 'pdf',
      licenseName: '',
      licenseList: [],
      imgReviewData: [
        {
          licenseName: '中华人民共和国结婚证',
          licenseNumber: '100071801553612461440000'
        }
      ],
      dataReviewData: [
        { keyName: '证照名称', keyData: '中华人民共和国结婚证' },
        { keyName: '证照名称', keyData: '中华人民共和国结婚证' },
        { keyName: '证照名称', keyData: '中华人民共和国结婚证' },
        { keyName: '证照名称', keyData: '中华人民共和国结婚证' },
        { keyName: '证照名称', keyData: '中华人民共和国结婚证' },
        { keyName: '证照名称', keyData: '中华人民共和国结婚证' },
        { keyName: '证照名称', keyData: '中华人民共和国结婚证' },
        { keyName: '证照名称', keyData: '中华人民共和国结婚证' },
        { keyName: '证照名称', keyData: '中华人民共和国结婚证' },
        { keyName: '证照名称', keyData: '中华人民共和国结婚证' },
        { keyName: '证照名称', keyData: '中华人民共和国结婚证' },
        { keyName: '证照名称', keyData: '中华人民共和国结婚证' }
      ],
      visible: false,
      createLoading: false,
      currentLicense: {},
      authzToken: '',
      imgLoading: false,
      dataLoading: false
    }
  },
  created() {
    // const _licenseList = JSON.parse(sessionStorage.getItem('licenseList'))
    // this.licenseList = _licenseList.list
    // this.authzToken = _licenseList.authzToken
    // this.currentLicense = this.licenseList[0]
    // this.licenseName = this.licenseList[0].licenseName
  },
  mounted() {
    // this.getPdf()
    // this.getDataItem()
  },
  methods: {
    licenseChange() {
      this.currentLicense = this.licenseList.find(item => {
        if (item.licenseName === this.licenseName) {
          return item
        }
      })
      this.getPdf()
      this.getDataItem()
    },
    getPdf() {
      this.imgLoading = true
      const par = {
        authzToken: this.authzToken,
        catalogCode: this.currentLicense.catalogCode,
        licenseCode: this.currentLicense.licenseCode
      }
      getPdf(par)
        .then(res => {
          this.imgLoading = false
          this.imgReviewData = res.data.list
        })
        .catch(() => {
          this.imgLoading = false
        })
    },
    getDataItem() {
      this.dataLoading = true
      const par = {
        authzToken: this.authzToken,
        catalogCode: this.currentLicense.catalogCode,
        licenseCode: this.currentLicense.licenseCode
      }
      getDataItem(par)
        .then(res => {
          this.dataLoading = false
          this.dataReviewData = res.data.list
        })
        .catch(() => {
          this.dataLoading = false
        })
    },
    toBack() {
      this.$router.back()
      customElements
    },
    toScan() {
      this.$router.push({ name: 'InspectionScan' })
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@/style/variables.scss';
.top-content {
  display: flex;
  justify-content: space-between;
  .explain {
    // line-height: 40px;
    font-size: 14px;
    color: #888;
    padding-left: 8px;
  }
  .right-btn {
    margin-right: 10px;
  }
}

.header {
  height: 44px;
  font-size: 16px;
  color: #666;
  display: flex;
  align-items: center;
  border-bottom: 1px solid $contentBorderColor;
  margin-top: 20px;
  .icon {
    width: 48px;
    height: 49px;
    display: flex;
    justify-content: center;
    align-items: center;
    img {
      width: 24px;
      height: 24px;
    }
  }
}
.preview-box {
  padding: 20px 30px;
  .pagination {
    font-size: 14px;
  }
  .img-box {
    .carousel-img {
      width: 100%;
      // height: 100%;
    }
    /* display: flex;
      justify-content: center;
      align-items: center; */
  }
}
</style>
<style lang="scss">
.preview {
  margin-top: 50px;
  .preview-box {
    .el-carousel__container {
      height: auto !important;
    }
  }
  .el-tabs__nav-scroll {
    display: flex;
    justify-content: center;
  }
}
</style>
