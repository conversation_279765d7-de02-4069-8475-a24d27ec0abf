<template>
  <section class="preview-box">
    <div class="pagination">第{{ currentNum }}页，共{{ imgDataList.length }}页</div>
    <div class="down">
      <el-button size="small" type="info" @click="down">下载</el-button>
    </div>
    <div class="img-box">
      <!-- <el-carousel indicator-position="outside" :autoplay="false" arrow="always" @change="carouselChange">
        <el-carousel-item v-for="(item,idx) in imgDataList" :key="idx">
          <img :src="'data:image/jpg;base64,'+item.fileData" alt="" srcset="" class="carousel-img" no-repeat>
        </el-carousel-item>
        <img :src="'data:image/jpg;base64,'+imgDataList[0].fileData" alt="" srcset="" style="width:100%;opacity:0;">
      </el-carousel> -->
    </div>
  </section>
</template>
<script>
// import { dataURLtoDownload } from '@/utils'

export default {
  props: {
    data: {
      type: Array,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      imgDataList: [],
      currentNum: 1
    }
  },
  watch: {
    data: {
      handler(val) {
        this.initData(val)
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    initData(val) {
      this.imgDataList = val
    },
    carouselChange(index) {
      this.currentNum = index + 1
    },
    down() {
      if (this.imgDataList[this.currentNum - 1].fileData === undefined) return
      // dataURLtoDownload(this.imgDataList[this.currentNum - 1].fileData, this.imgDataList[this.currentNum - 1].fileData)
    }

  }
}
</script>
<style lang="scss" scoped>
.down{
  // background: #888;
  position: absolute;
  top:30px;
  right: 50px;
}
</style>
