<template>
  <!-- 协查结果页 -->
  <div class="content">
    <!-- <van-row class="title">提交结果</van-row> -->
    <div id="warp">
      <van-row>
        <van-col span="24" :style="style">
          <div class="welcome">欢迎您</div>
        </van-col>
        <van-col span="24">
          <van-cell value="更多" is-link @click="toPage('homeTodoList')">
            <!-- 使用 title 插槽来自定义标题 -->
            <template #title>
              <div class="todo">
                <div class="tag"></div>&nbsp;
                <span class="desc">我的待办（{{total}}）</span>
              </div>
            </template>
          </van-cell>
        </van-col>
        <template v-if="todoInfo.length > 0">
          <van-col
            span="24"
            class="todoInfo"
            v-for="(detail,i) in todoInfo"
            :key="i"
            @click="toDetail(detail)"
          >
            <van-row class="infoName">
              <van-col span="12">{{detail.from_assist_user_name}}</van-col>
            </van-row>
            <van-row class="item">
              <van-col span="10">材料名称</van-col>
              <van-col span="14">{{detail.material_name}}</van-col>
            </van-row>
            <van-row class="item">
              <van-col span="10">发起部门</van-col>
              <van-col span="14">{{detail.from_assist_org_name}}</van-col>
            </van-row>
            <van-row type="flex" justify="center" class="goAssist">
              <van-col>去协查</van-col>
            </van-row>
          </van-col>
        </template>
        <template v-else>
          <van-col span="24">
            <van-divider>暂无待办信息</van-divider>
          </van-col>
        </template>
        <van-col span="24">
          <van-cell>
            <!-- 使用 title 插槽来自定义标题 -->
            <template #title>
              <div class="todo">
                <div class="tag"></div>&nbsp;
                <span class="desc">协查服务</span>
              </div>
            </template>
          </van-cell>
        </van-col>
        <van-col span="24" class="serve">
          <van-cell @click="toPage('homeArchivesList')">
            <!-- 使用 title 插槽来自定义标题 -->
            <template #icon>
              <div class="serve-icon">
                <van-image fit="fill" width="3rem" :src="require('@/assets/assist-icon.png')" />
              </div>
            </template>
            <template #title>
              <div class="serve-title">
                <span>协查档案</span>
              </div>
            </template>
            <template #label>
              <div class="serve-desc">
                <span>查询协查档案</span>
              </div>
            </template>
          </van-cell>
        </van-col>
      </van-row>
    </div>
  </div>
</template>
<script>
import { getUserInfo, getTodoList } from "@/api/assist";
import { Dialog, Toast } from "vant";
export default {
  data() {
    return {
      userid: "",
      total: 0,
      todoInfo: [
        // {
        //   to_user_name: "李小花",
        //   to_assist_org_name: "市房管局",
        //   material_name: "无犯罪记录证明"
        // },
        //    {
        //   to_user_name: "李小花1",
        //   to_assist_org_name: "市房管局1",
        //   material_name: "无犯罪记录证明1"
        // }
      ],
      style: {
        height: document.documentElement.clientWidth > 750 ? "290px" : "145px",
        background:
          document.documentElement.clientWidth > 750 ? "#ffffff" : "#f9f9f9",
        backgroundImage: "url(" + require("@/assets/banner.png") + ")",
        backgroundSize: "100%"
      },
      fullWidth: document.documentElement.clientWidth,
      type: "success",
      message: "", // 提交成功
      description: "" // 描述提示
    };
  },
  beforeRouteEnter(to, from, next) {
    next(vm => {
      // 通过 `vm` 访问组件实例
      vm.showAudit = false;
      if (from.name === "homeTodoList" || from.name === "homeArchivesList" || from.name === "certEasyAssist") {
        vm.getTodoList({
          user_id: vm.$route.query["user_id"],
          assist_result: "WAIT",
          page_direction: "DESC",
          page_number: 1,
          page_size: 2
        });
      }
    });
    // 结果页面返回刷新数据
  },
  created() {
    // ip端口/assist-tool/certEasy/assist?user_id=xxxxx&assist_serial_number=xxxx
    this.userid = this.$route.query["user_id"];

    if (this.userid) {
      this.getTodoList({
        user_id: this.userid,
        assist_result: "WAIT",
        page_direction: "DESC",
        page_number: 1,
        page_size: 3
      });
    }
  },
  mounted() {
    window.addEventListener("resize", this.handleResize);
  },
  methods: {
    getTodoList(params) {
      getTodoList(params)
        .then(res => {
          console.log("==获取待办列表成功==", res);

          if (res.data.data && res.data.data.content.length > 0) {
            let data = res.data.data.content;
            
            this.todoInfo = data;
          } else {
            this.todoInfo = [];
          }
          this.total = res.data.data.totalElements;
        })
        .catch(err => {
          Toast(err.data.meta.message || "");
          console.log("==获取待办列表失败==", err);
        });
    },
    toPage(name) {
      this.$router.push({
        name: name,
        query: {
          user_id: this.userid
        }
      });
    },
    handleResize(event) {
      this.fullWidth = document.documentElement.clientWidth;
    },
    toDetail(info) {
      this.$router.push({
        name: "certEasyAssist",
        query: {
          user_id: this.userid,
          assist_serial_number: info.assist_serial_number
        }
      });
    }
  }
};
</script>
<style lang="less" scoped>
.content {
  background: #ffffff;
  // height: 100vh;
}
#warp {
  max-width: 1000px;
  height: 100vh;
  margin: 0 auto;
  background: #f7f8fa;
  overflow-x: hidden;
}

.welcome {
  position: relative;
  top: 20px;
  left: 20px;
  font-size: 20px;
  color: #ffffff;
  background: "pink";
}
.todo {
  display: flex;
}
.tag {
  // height: 44px;
  width: 4px;
  height: 80%;
  background: blue;
  padding: 10px 0;
  border-radius: 4px;
}
.desc {
  font-size: 16px;
  font-weight: 500;
  color: #000000;
}
.todoInfo {
  // margin-top: 16px;
  // margin-left: 16px;
  margin: 2%;
  width: 96%;
  border: 2px solid #ebedf0;
  min-height: 190px;
  background: #ffffff;
}
.infoName {
  padding: 16px 20px;
  font-size: 18px;
  font-weight: 900;
}
.item {
  margin: 5px 16px;
}
.item .van-col:first-child {
  color: gray;
}

.goAssist {
  border-top: 1px solid #ebedf0;
  margin-top: 20px;
  padding: 15px 0px;
  font-weight: 500;
  color: #3479ec;
  cursor: pointer;
}

.serve {
  margin-bottom: 20px;
  padding: 0px 16px;
  cursor: pointer;
  background: #ffffff;
}

.serve-icon {
  padding: 10px 20px 0 10px;
}

.serve-title {
  padding: 8px 10px 0px 10px;
  font-size: 18px;
}
.serve-desc {
  padding-left: 10px;
  font-size: 15px;
}
</style>