<template>
  <!-- 首页-协查档案 -->
  <div class="content">
    <!-- <van-row class="title">协查档案列表</van-row> -->
    <div id="warp">
      <template v-if="total > 0">
        <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
          <van-list
            v-model:loading="loading"
            :finished="finished"
            finished-text="没有更多了"
            @load="onLoad"
          >
            <!-- <van-cell v-for="item in list" :key="item" :title="item" /> -->
            <Item :dataInfo="todoInfo" @click="toDetail" isLink />
          </van-list>
        </van-pull-refresh>
      </template>
      <template v-else>
        <van-row class="margin-10">
          <van-empty description="暂无协查待办任务" />
        </van-row>
      </template>
    </div>
  </div>
</template>
<script>
import Item from "@/views/components/Item";
import { getTodoList } from "@/api/assist";

export default {
  components: { Item },
  data() {
    return {
      list: [],
      loading: false,
      finished: false,
      refreshing: false,
      userid: "",
      assist_result: "WAIT",
      total: 1,
      page_direction: "DESC",
      page_number: 1,
      page_size: 10,
      todoInfo: [
        {
          title: "",
          data: [
            // {
            //   id: 1,
            //   title: "李小花",
            //   value: "待协查",
            //   status: "success",
            //   description: "发起部门: 国土局 发起时间: 2021-06-18 10:30"
            // },
            // {
            //   id: 2,
            //   title: "协查结果",
            //   value: "符合",
            //   status: "not",
            //   subTitle: "一些次要展示的标题，也可以不要",
            //   description: "2021-06-18 10:30"
            // }
          ]
        }
      ],
      auditDict: [
        { value: "WAIT", label: "待协查" },
        { value: "SUCCESS", label: "符合" },
        { value: "FAIL", label: "不符合" }
      ]
    };
  },
  beforeRouteEnter(to, from, next) {
    next(vm => {
      // 通过 `vm` 访问组件实例
      vm.userid = vm.$route.query["user_id"];
      vm.todoInfo[0].data = [];
      vm.page_number = 1;
      vm.total = 1;
      vm.getTotal();
      vm.onRefresh("init");
    });

    // 结果页面返回刷新数据
  },
  async create() {
    // 获取协查详情
    this.userid = this.$route.query["user_id"];
    await getTodoList({
      user_id: this.userid,
      assist_result: this.assist_result,
      page_direction: this.page_direction,
      page_number: this.page_number,
      page_size: this.page_size
    }).then(res => {
      this.total = res.data.data.totalElements;
    });
  },

  methods: {
    async getTotal() {
      // 获取协查详情
      await getTodoList({
        user_id: this.userid,
        assist_result: this.assist_result,
        page_direction: this.page_direction,
        page_number: this.page_number,
        page_size: this.page_size
      }).then(res => {
        this.total = res.data.data.totalElements;
        this.loading = false;
        this.finished = false;
        this.refreshing = false;
      });
    },
    onLoad() {
      console.log("onLoad");
      if (this.refreshing) {
        this.todoInfo[0].data = [];
        this.refreshing = false;
      }
      // this.page_number = this.page_number+1;
      getTodoList({
        user_id: this.userid,
        assist_result: this.assist_result,
        page_direction: this.page_direction,
        page_number: this.page_number,
        page_size: this.page_size
      })
        .then(res => {
          console.log("==获取协查历史成功==", res);
          setTimeout(() => {
            let data = res.data.data.content;
            if (data.length > 0) {
              let itemData = data.map(i => {
                let statusItem = this.auditDict.filter(
                  j => j.value === i.audit_result
                );
                let temp = {
                  id: i.assist_serial_number,
                  assist_serial_number: i.assist_serial_number,
                  title: i.from_assist_user_name || "--",
                  value:
                    statusItem.length > 0
                      ? statusItem[0].label
                      : i.audit_result,
                  // status: i.audit_result.toLowerCase(),
                  status: i.audit_result,
                  description:
                    "发起部门: " +
                    i.from_assist_org_name +
                    " 发起时间: " +
                    i.assist_time
                };
                return temp;
              });
              itemData.forEach(k => {
                this.todoInfo[0].data.push(k);
              });
            }

            this.loading = false;
            // 数据全部加载完成
            if (this.todoInfo[0].data.length >= this.total) {
              this.finished = true;
            } else {
              this.page_number = this.page_number + 1;
            }
          }, 1000);
        })
        .catch(err => {
          this.finished = true;
          Toast(err.data.meta.message || "");
          console.log("==获取协查历史失败==", err);
        });
    },
    onRefresh(type) {
      if (type === "init") {
        // 清空列表数据
        this.finished = false;

        // 重新加载数据
        // 将 loading 设置为 true，表示处于加载状态
        this.loading = true;
      }
    },
    toDetail(info) {
      this.$router.push({
        name: "certEasyAssist",
        query: {
          user_id: this.userid,
          assist_serial_number: info.assist_serial_number
        }
      });
    }
  }
};
</script>

<style lang="less" scoped>
.content {
  background: #f9f9f9;
  //  background: pink;
  // height: 100vh;
}
#warp {
  max-width: 1000px;
  margin: 0 auto;
  height: auto;
}
.title {
  padding: 10px 0;
  background: #3278ea;
  font-size: 18px;
  font-weight: 500;
  text-align: center;
  color: #ffffff;
}

/deep/ .van-tab {
  font-size: 16px;
}
/deep/ .van-tabs__line {
  width: 23%;
  background: #3278ea;
}
/deep/ .van-tab--active {
  color: #3278ea;
}

.margin-16 {
  margin: 16px 0;
}

.margin-10 {
  margin: 10px 0;
}

.tips {
  padding: 1px 16px 16px 16px;
  font-size: 12px;
  font-weight: 400;
  text-align: left;
  color: #7a7a7a;
  line-height: 22px;
  background: #ffffff;
}
/deep/ .form .van-cell-group__title,
.form-web .van-cell-group__title {
  padding: 20px 16px;
  font-size: 24px;
  font-weight: 500;
  text-align: left;
  color: #262626;
  background: #ffffff;
}
/deep/ .form .van-cell,
.form-web .van-cell {
  padding: 10px 16px;
  color: #7a7a7a;
  font-size: 16px;
}

.submit-btn {
  margin: 20px 0;
}

.label {
  height: 30px;
  // background: pink;
  text-align: middle;
  line-height: 30px;
  font-size: 15px;
  font-weight: 400;
}
/deep/ .form .van-form .van-cell,
.form-web .van-form .van-cell {
  display: block;
  // line-height: 52px;
  padding: 16px;
  background-color: #ffffff;
}

/deep/ .form-web .van-field__label {
  padding-left: 50px;
  font-size: 18px;
  background-color: #ffffff;
}

/deep/ .form-web .van-field__control {
  padding: 16px 0 0 50px;
  max-width: 310px;
  font-size: 18px;
  background-color: #ffffff;
}

/deep/ .van-button--normal {
  font-size: 16px;
  font-weight: 600;
}
/deep/ .form .van-cell--required::before {
  padding: 5px 0px;
}
/deep/ .form-web .van-cell--required::before {
  padding-left: 50px;
}

// ::v-deep .van-overlay {
//   position: absolute;
// }
// ::v-deep .van-popup {
//   position: absolute;
// }
</style>