<template>
  <!-- 移动端业务员列表页 -->
  <div class="content">
    <div id="warp">
      <van-loading v-show="loading" type="spinner" color="#1989fa" size="32px">加载中...</van-loading>
      <!-- <van-row align="center">
        <van-col span="24">清远粤证易中转页</van-col>
        <van-col span="24">跳转url：{{currUrl}}</van-col>
        <van-col span="24">错误信息：{{err}}</van-col>
       </van-row> -->
    </div>
  </div>
</template>
<script>
import { Toast } from 'vant'
import { getUserInfo, getAccountByUserId, getPersonAuthUrl, getGovEasyUserId, personPageCodeToUserInfo } from '@/api/assist'
export default {
  data() {
    return {
      loading: true,
      loginType: '',
      code: '',
      currUrl: '',
      err: '',
      appid: '', // 如果是授权失败 重新进入页面会带appid参数
    }
  },
  created() {
    console.log(this.$route.name)
    // 新鉴权
    // ip:端口/assist-tools/index/weixin?code=xxxxxxxx
    sessionStorage.clear()
    this.code = this.$route.query['code']
    this.loginType = this.$route.params['loginType']
    this.currUrl = this.$route.fullPath
    // 正常第一次授权的情况
    if (this.loginType && this.loginType === 'weixin' && this.code &&this.$route.name==='index') {
      this.initGetUserInfo()
      // this.getAccountByUserId()
    } else if(this.$route.name ==='authorizeIndex') {
      console.log(123123)
        // 重新粤政易授权后进来
        this.personPageCodeToUserInfo(this.code)
    }
    else if (this.code===''||!this.code) {
      // 没有传code 或者授权后没有带code的情况下
      this.loading = false
      this.$message({
        message: '无效的访问,重新打开应用',
        center: true,
        type: 'error',
        offset: 300,
        duration: 3000,
      })
    }
  },
  methods: {
    async initGetUserInfo() {
      this.loading = false
      // 根据链接地址带的code 获取userid
      await getUserInfo({ code: this.code })
        .then((res) => {
          console.log('getUserInfo', res)
          // sessionStorage.setItem('jwtToken', res.data.data.jwtToken)

          const userId = res.data.data.userId
          // 通过userid 获取用户信息
          this.getAccountByUserId(userId)
        })
        .catch((err) => {
          console.log('getUserInfo error', err)
          this.err = JSON.stringify(res.data)
          Toast(err.data.meta.message || '')
        })
    },
    // 通过userid 获取用户信息
    getAccountByUserId(userId) {
      getAccountByUserId({ userId: userId })
        .then((res) => {
          console.log('getAccountByUserId', res)
          // 如果有用户信息 说明授权成功
          if (res.data.meta.code === '200') {
            // 用userid 获取最新的token 和 userid 进入业务页面
            this.getGovEasyUserId(userId)
          } else {
            // 无权限操作 跳转粤政易
            this.getPersonAuthUrl()
          }
        })
        .catch(() => {
          // 无权限操作 跳转粤政易
          this.getPersonAuthUrl()
        })
    },
    // 获取粤政易授权的url
    getPersonAuthUrl() {
      getPersonAuthUrl().then((res) => {
        console.log('getPersonAuthUrl', res)
        if (res.data.data) {
          window.open(`${res.data.data}`, '_self')
        } else {
        }
      })
    },
    // 根据userId获取用最新的token 和userdi
    getGovEasyUserId(userId) {
      getGovEasyUserId({ userId: userId }).then((res) => {
        console.log('getGovEasyUserId', res)
        if (res.data.data) {
          sessionStorage.setItem('jwtToken', res.data.data.jwtToken)
          this.$router.replace({
            name: 'home',
            query: {
              user_id: res.data.data.userId,
            },
          })
        }
      })
    },
    // 重新授权后用code获取用户信息
    personPageCodeToUserInfo(code) {
      personPageCodeToUserInfo({ code: code }).then((res) => {
        console.log('personPageCodeToUserInfo',res)
        if(res.data.data){
          const userId=  res.data.data.gov_easy_user_id
          this.getGovEasyUserId(userId)
        }
      })
    },
  },
}
</script>
<style lang="less" scoped>
.content {
  // background: #f9f9f9;
  height: 100%;
  text-align: center;
  line-height: 100vh;
}
</style>