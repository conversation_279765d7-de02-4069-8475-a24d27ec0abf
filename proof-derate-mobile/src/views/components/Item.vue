<template>
  <!-- 协查处理item组件-->
  <div :class="{'item-web':fullWidth > 750,'item-mobile':fullWidth < 750}">
    <template v-if="dataInfo.length > 0 ">
      <van-row class="margin-10" v-for="(detail,i) in dataInfo" :key="i">
        <van-cell
          :border="false"
          v-for="(info,j) in detail['data']"
          :key="j"
          :title="info.title"
          @click="toDetail(info)"
          :is-link="isLink"
        >
          <template>
            <span :class="'assist-'+ info.status +'-result'">{{info.value}}</span>
          </template>
          <template #label v-if="info['subTitle'] || (info['description'])">
            <div
              class="slot-cell-label-subTitle"
              v-show="info['subTitle'] != ''"
            >{{ info.subTitle }}</div>
            <div
              class="slot-cell-label-desc"
              v-show="info['description'] != ''"
            >{{ info.description }}</div>
          </template>
        </van-cell>
      </van-row>
    </template>
    <template v-else>
      <van-row class="margin-10">
        <van-empty description="暂无协查历史" />
      </van-row>
    </template>
  </div>
</template>
<script>
export default {
  name: "Item",
  props: {
    dataInfo: {
      type: Array,
      default: function() {
        return [
          {
            title: "",
            data: [
              {
                title: "协查结果",
                value: "未通过",
                status: "not",
                subTitle: "一些次要展示的标题，也可以不要",
                description: "2021-06-18 10:30"
              }
            ]
          }
        ];
      }
    },
    isLink: {
      type: Boolean,
      default: false
    }
  },
  created() {
    window.addEventListener("resize", this.handleResize);
  },
  data() {
    return {
      fullWidth: document.documentElement.clientWidth
    };
  },
  methods: {
    handleResize(event) {
      this.fullWidth = document.documentElement.clientWidth;
    },
    toDetail(info) {
      this.$emit("click", info);
    }
  }
};
</script>
<style lang="less" scoped>
.detail {
  background: #f9f9f9;
}

.margin-10 {
  margin: 10px 0;
}
.item-mobile .van-cell,
.item-web .van-cell {
  margin-bottom: 10px;
  padding: 16px 16px 10px 16px;
  color: #262626;
  font-size: 16px;
}

.item-mobile .van-cell__title {
  text-align: left;
  flex: 0.7;
}
.item-mobile .van-cell__value {
  text-align: right;
  color: #262626;
  flex: 0.3;
}

.item-web .van-cell__title {
  padding-left: 50px;
  text-align: left;
  flex: 0.8;
}
.item-web .van-cell__value {
  padding-right: 50px;
  text-align: right;
  flex: 0.2;
  color: #262626;
}
.assist-wait-result {
  color: #262626;
}
.assist-success-result {
  color: #4b9efe;
}
.assist-fail-result {
  color: #fa5151;
}

.item-mobile,
.item-web {
  height: 100%;
  .slot-cell-label-subTitle {
    margin: 8px 0;
    font-size: 15px;
    color: #262626;
    flex: 0.8;
  }
  .slot-cell-label-desc {
    margin: 8px 0;
    font-size: 12px;
    flex: 0.8;
  }
}
</style>