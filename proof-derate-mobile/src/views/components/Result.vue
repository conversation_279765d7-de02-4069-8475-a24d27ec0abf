<template>
  <!-- 协查处理result组件-->
  <div class="c-result" :style="{'background': background}">
    <van-row type="flex" justify="center" class="c-result-image">
      <van-col span="6" >
        <van-image width="96" height="96" :src="require('@/assets/mobile/result_ic_'+ type +'.png')" />
      </van-col>
    </van-row>
    <van-row type="flex" justify="center" class="c-result-tip" v-if="message"><van-col span="22" >{{ message }}</van-col></van-row>
    <van-row type="flex" justify="center" class="c-result-desc" v-if="description"><van-col span="22" >{{ description }}</van-col></van-row>
    <van-row type="flex" justify="center">
      <van-col span="22" class="submit-btn" v-show="infoBtn">
        <van-button type="info" block @click="infoClick">{{infoBtnText}}</van-button>
      </van-col>
       <van-col span="22" class="submit-btn margin-15" v-show="defaultBtn">
        <van-button type="default" block @click="defaultClick">{{defaultBtnText}}</van-button>
      </van-col>
    </van-row>
  </div>
</template>
<script>
export default {
  name: "Result",
  props: {
    background: {
      type: String,
      default: 'pink'
    },
    type: {
      type: String,
      default: 'success'
    },
    message: {
      type: String,
      default: ''
    },
    description: {
      type: String,
      default: ''
    },
    infoBtn: {
      type: Boolean,
      default: false
    },
    infoBtnText: {
      type: String,
      default: '主要按钮'
    },
    defaultBtn: {
      type: Boolean,
      default: false
    },
    defaultBtnText: {
      type: String,
      default: '次要按钮'
    },
  },
  methods: {
    infoClick() {
      this.$emit("info-click")
    },
    defaultClick() {
      this.$emit("default-click")
    },
  }
};
</script>
<style lang="less" scoped>

.margin-15 {
  margin-top: 15px;
}

.c-result-image{
  margin:50px 0px;
}
.c-result-tip{
  // height: 67px;
  font-size: 24px;
  font-weight: 500;
  text-align: center;
  color: #262626;
}
.c-result-desc{
  margin: 10px 0 30px 0;
  font-size: 15px;
  font-weight: 400;
  text-align: center;
  color: #999999;
}
.submit-btn .van-button--normal {
  font-size: 16px;
  font-weight: 600;
}

.c-result .van-col {
  text-align: center;
}

</style>