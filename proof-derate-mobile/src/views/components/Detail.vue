<template>
  <!-- 协查处理详情页组件-->
  <div :class="{ 'detail-web': fullWidth > 750, 'detail-mobile': fullWidth < 750 }" class="van-hairline--bottom">
    <template v-if="dataInfo.length > 0" class="margin-10">
      <van-row class="margin-top-10" v-for="(detail, i) in dataInfo" :key="i">
        <van-cell-group :border="false" :title="detail.title">
          <van-cell v-if="detail.applytitle != undefined && detail.applytitle != ''" class="custom-title">
            <template #title>
              <span>{{ detail.applytitle }}</span>
            </template>
          </van-cell>
					<van-cell v-if="detail.itemList?.length>0" class="custom-title">
						<van-grid :column-num="2">
							<van-cell v-if="detail.applytitle != undefined && detail.applytitle != ''" class="custom-title">
								<template #title>
									<span>{{ detail.applytitle }}</span>
								</template>
							</van-cell>
						</van-grid>
          </van-cell>
          <van-cell v-for="(info, j) in detail['data']" :key="j" :border="false" :title="info.title">
            <template>
              <span :class="'assist-' + info.status + '-result'" class="assist-SUCCESS-result" v-if="info.type === 'download'" @click="downloadFile(info.assistAttachmentId)">{{
                info.value
              }}</span>
              <span :class="'assist-' + info.status + '-result'" v-else>{{ info.value }}</span>
            </template>
          </van-cell>
        </van-cell-group>
      </van-row>


			<van-row >
				<van-cell-group class="custom-cell-group">
					<div 
						v-for="(item, index) in dataInfo[0].itemList" 
						:key="index"
						:class="['custom-cell', item.cols === '1' ? 'full-width' : 'half-width']"
					>
						<van-cell :title="`${item.key}`"  :value="item.value"/>
					</div>
				</van-cell-group>
			</van-row>


			
      <div v-if="doubleDataInfo.length > 0">
        <van-grid :border="false" :column-num="4" v-for="(doubleData, key) in parseInt(doubleDataInfo.length / 2)" :key="key">
          <van-grid-item class="text-left">
            {{ doubleDataInfo[key * 2].title }}
          </van-grid-item>
          <van-grid-item class="text-left item-value">
            {{ doubleDataInfo[key * 2].value }}
          </van-grid-item>
          <van-grid-item class="text-left1">
            {{ doubleDataInfo[(key + 1) * 2 - 1].title }}
          </van-grid-item>
          <van-grid-item class="text-left1 item-value">
            {{ doubleDataInfo[(key + 1) * 2 - 1].value }}
          </van-grid-item>
        </van-grid>
      </div>
      <div v-for="(detail, i) in dataInfo">
        <van-row class="tips" v-if="detail['tips'] && detail['tips'].length > 0">
          <van-divider />
          <van-col :span="24">
            <div v-for="(desc, k) in detail['tips']" :key="k">{{ desc }}</div>
          </van-col>
        </van-row>
      </div>
    </template>
    <template v-else>
      <van-row class="margin-10">
        <van-empty description="暂无协查信息" />
      </van-row>
    </template>
  </div>
</template>
<script>
export default {
  name: 'Detail',
  props: {
    dataInfo: {
      type: Array,
      default: function () {
        return [
          {
            title: '',
            data: [],
            tips: [],
          },
        ]
      },
    },
    doubleDataInfo: {
      type: Array,
      default: function () {
        return []
      },
    },
    title: {
      type: String,
      default: '',
    },
  },
  mounted() {
		window.addEventListener( 'resize', this.handleResize )
		console.log('dataInfo',this.dataInfo)
  },
  data() {
    return {
      fullWidth: document.documentElement.clientWidth,
    }
  },
  methods: {
    handleResize(event) {
      this.fullWidth = document.documentElement.clientWidth
    },
    downloadFile(assist_attachment_id) {
      this.$emit('downloadFile', assist_attachment_id)
    },
  },
}
</script>
<style lang="less" scoped>
.title {
  // height: 44px;
  padding: 10px 0;
  background: #3278ea;
  font-size: 18px;
  font-weight: 500;
  text-align: center;
  color: #ffffff;
}
.margin-16 {
  margin: 16px 0;
}

.margin-10 {
  margin: 10px 0;
}
.margin-top-10 {
  margin-top: 10px;
}
.tips {
  padding: 1px 16px 16px 16px;
  // width: 670px;
  // height: 22px;
  font-size: 12px;
  font-weight: 400;
  text-align: left;
  color: #7a7a7a;
  line-height: 22px;
  background: #ffffff;
}
.detail-mobile .van-cell-group__title,
.detail-web .van-cell-group__title {
  padding: 20px 16px;
  font-size: 24px;

  font-weight: 500;
  text-align: left;
  color: #262626;
  background: #ffffff;
  line-height: 1.2;
}
.detail-mobile .van-cell,
.detail-web .van-cell {
  padding: 10px 16px;
  color: #7a7a7a;
  font-size: 16px;
}

.detail-web .van-cell__title {
  padding-left: 50px;
  text-align: left;
  flex: 0.3;
}
.detail-web .van-cell__value {
  text-align: left;
  flex: 0.7;
}

.detail-mobile .van-cell__title {
  text-align: left;
}

.detail-mobile .van-cell__value {
  text-align: left;
}
.assist-wait-result,
.assist-WAIT-result {
  color: #262626;
}
.assist-success-result,
.assist-SUCCESS-result {
  color: #4b9efe;
}
.assist-fail-result,
.assist-FAIL-result {
  color: #fa5151;
}

.submit-btn {
  margin: 20px 0;
}

.label {
  height: 30px;
  text-align: middle;
  line-height: 30px;
  font-size: 15px;
  font-weight: 400;
}
.custom-title .van-cell__title {
  font-size: 20px;
  text-align: center;
  font-weight: 550;
}

.text-left /deep/ .van-grid-item__content {
  align-items: start;
  padding-left: 16px;
  color: #7a7a7a;
  font-size: 16px;
}
.text-left1 /deep/ .van-grid-item__content {
  align-items: start;
  padding-left: 0px;
  color: #7a7a7a;
  font-size: 16px;
}
.item-value /deep/ .van-grid-item__content {
  color: #969799;
}

.custom-cell-group {
  display: flex;
  flex-wrap: wrap;
	&::after{
		border: none !important;
	}
}

.custom-cell {
  box-sizing: border-box;
}

.half-width {
  width: 50%;  /* 一行两列 */
}

.full-width {
  width: 100%;  /* 一行一列 */
}

</style>