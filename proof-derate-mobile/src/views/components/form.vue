<template>
  <div>
    <van-form ref="formData">
      <template v-if="fullWidth < 750">
        <van-field
          v-model="assistResultLabel"
          type="text"
          placeholder="请选择"
          label-width="100%"
          right-icon="arrow-down"
          @click="errorReasonChange"
          required
          readonly
        >
          <template #label>
            <div class="label">协查结果</div>
          </template>
        </van-field>
        <van-popup
          v-model="showPicker"
          round
          position="bottom"
          :lock-scroll="false"
          get-container="warp"
          :style="{ maxWidth: '1000px' }"
        >
          <van-picker
            show-toolbar
            :columns="errorReasonData"
            :default-index="0"
            @cancel="showPicker = false"
            @confirm="onConfirm"
          />
        </van-popup>
      </template>
      <template v-else>
        <van-field
          v-model="assistResultLabel"
          type="text"
          label="协查结果"
          placeholder="请选择"
          label-width="100%"
          @click="errorReasonChange"
          required
          readonly
        >
          <template #input>
            <el-select
              v-model="formData.assist_result"
              placeholder="请选择"
              style="width: 300px"
            >
              <el-option
                v-for="item in errorReason"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </template>
        </van-field>
      </template>

      <van-field
        v-model="nowAuditRelation.audit_suggestion"
        placeholder="请填写"
        :rules="rules.assist_opinion"
        label-width="100%"
        required
        center
      >
        <template #label>
          <div class="label">协查意见</div>
        </template>
      </van-field>
      <van-field
        v-model="nowAuditRelation.assist_from_user"
        placeholder="请填写"
        label-width="100%"
        center
        disabled
      >
        <template #label>
          <div class="label">协查人</div>
        </template>
      </van-field>
      <van-field
        v-model="nowAuditRelation.assist_to_org_name"
        placeholder=""
        label-width="100%"
        center
        disabled
      >
        <template #label>
          <div class="label">协查部门</div>
        </template>
      </van-field>
      <van-field
        v-model="auditOrgName"
        placeholder="请填写"
        label-width="100%"
        center
        disabled
      >
        <template #label>
          <div class="label">下级审批部门所属区划</div>
        </template>
      </van-field>
      <van-field
        v-model="assistDepLabel"
        type="text"
        label="下级审批部门"
        placeholder="请选择"
        label-width="100%"
        required
        readonly
      >
        <template #input>
          <el-select
            v-model="auditOrgCode"
            placeholder="请选择"
            style="width: 300px"
            multiple
            @change="auditOrgChange"
          >
            <el-option
              v-for="item in subAuditObj"
              :key="item.audit_org_code"
              :label="item.audit_org_name"
              :value="item.audit_org_code"
            ></el-option>
          </el-select>
        </template>
      </van-field>
      <van-field name="uploader" label="文件上传">
        <template v-if="uploader.length == 0" #input>
          <van-uploader
            v-model="uploader"
            :after-read="afterRead"
            :preview-image="false"
            :multiple="false"
          >
            <van-button type="info" size="small">上传文件</van-button>
          </van-uploader>
        </template>
        <template v-else #input>
          <van-notice-bar
            mode="closeable"
            wrapable
            :scrollable="false"
            @close="closeNoticeBar"
            >{{ uploadFile.file.name }}</van-notice-bar
          >
        </template>
      </van-field>
    </van-form>
  </div>
</template>

<script>
export default {
  data() {
    return {
      assistResultLabel: "",
      assistDepLabel: "",
      showPicker: false,
      auditOrgName: "",
      errorReason: [
        { value: "SUCCESS", label: "符合" },
        { value: "FAIL", label: "不符合" },
      ],
      formData: {
        assist_result: "", // 协查结果
        assist_opinion: "", // 协查意见
        assist_from_user: "", // 协查人
        assist_deparment: "", // 协查部门
      },
      // 当前审核对象
      nowAuditRelation: {
        assist_attachment_name: "",
        raw: "",
      },
    };
  },
  computed: {
    errorReasonData: function () {
      let errorReasonArr = [];
      this.errorReason.forEach((i) => {
        errorReasonArr.push(i.label);
      });
      return errorReasonArr;
    },
  },
  methods: {
    /**
     * 原因类型选择
     */
    errorReasonChange(event) {
      this.showPicker = true;
    },
    onConfirm(value) {
      console.log("onConfirm", value);
      this.assistResultLabel = value;
      this.nowAuditRelation.assist_audit_result = this.errorReason.find(
        (item) => {
          return item.label == this.assistResultLabel;
        }
      ).value;
      this.showPicker = false;
      console.log(
        "this.nowAuditRelation.assist_audit_result",
        this.nowAuditRelation.assist_audit_result
      );
    },
  },
};
</script>

<style>
</style>