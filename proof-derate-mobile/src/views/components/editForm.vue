<template>
    <div>
        <van-form ref="formData">
            <template v-if="fullWidth < 750">
                <van-field v-model="assistResultLabel" type="text" placeholder="请选择" label-width="100%"
                    right-icon="arrow-down" @click="errorReasonChange" required readonly>
                    <template #label>
                        <div class="label">协查结果</div>
                    </template>
                </van-field>
                <van-popup v-model="showPicker" round position="bottom" :lock-scroll="false" get-container="warp"
                    :style="{ 'maxWidth': '1000px' }">
                    <van-picker show-toolbar :columns="errorReasonData" :default-index="0" @cancel="showPicker = false"
                        @confirm="onConfirm" />
                </van-popup>
            </template>
            <template v-else>
                <van-field v-model="assistResultLabel" type="text" label="协查结果" placeholder="请选择" label-width="100%"
                    @click="errorReasonChange" required readonly>
                    <template #input>
                        <el-select v-model="formData.assist_result" placeholder="请选择" style="width:300px">
                            <el-option v-for="item in errorReason" :key="item.value" :label="item.label"
                                :value="item.value"></el-option>
                        </el-select>
                    </template>
                </van-field>
            </template>

            <van-field v-model="formData.assist_opinion" placeholder="请填写" :rules="rules.assist_opinion"
                label-width="100%" required center>
                <template #label>
                    <div class="label">协查意见</div>
                </template>
            </van-field>
            <van-field v-model="formData.assist_name" placeholder="请填写" label-width="100%" center disabled>
                <template #label>
                    <div class="label">协查人</div>
                </template>
            </van-field>
            <van-field v-model="formData.assist_deparment" placeholder="请填写" label-width="100%" center disabled>
                <template #label>
                    <div class="label">协查部门</div>
                </template>
            </van-field>
        </van-form>
    </div>
</template>

<script>
export default {
    props: {
        formData: {
            type: Object,
            default: function () {
                return [
                    {
                        assist_result: '', // 协查结果
                        assist_opinion: '', // 协查意见
                        assist_name: '', // 协查人
                        assist_deparment: '' // 协查部门
                    }
                ];
            }
        },
    },
    computed: {
        errorReasonData: function () {
            let errorReasonArr = []
            this.errorReason.forEach(i => {
                errorReasonArr.push(i.label)
            })
            return errorReasonArr
        }
    },
    data() {
        return {
            fullWidth: document.documentElement.clientWidth,
            assistResultLabel: '',
            // formData: {
            //     assist_result: '', // 协查结果
            //     assist_opinion: '', // 协查意见
            //     assist_name: '', // 协查人
            //     assist_deparment: '' // 协查部门
            // },
            rules: {
                assist_result: [{ required: true }],
                assist_opinion: [{ required: true }]
            },
            errorReason: [
                { value: 'SUCCESS', label: '符合' },
                { value: 'FAIL', label: '不符合' }
            ],
            auditDict: [
                { value: 'WAIT', label: '待协查' },
                { value: 'SUCCESS', label: '符合' },
                { value: 'FAIL', label: '不符合' }
            ],
            showPicker: false,
        }
    },
    methods: {
        onConfirm(value) {
            this.assistResultLabel = value
            this.formData.assist_result = this.errorReason.find(item => {
                return item.label == this.assistResultLabel
            }).value
            this.showPicker = false
        },
        /**
    * 原因类型选择
    */
        errorReasonChange(event) {
            this.showPicker = true
        },
    },
    created() {
        // this.validType = { id, tyshxydm, tempTyshxydm, zzjgdm, mobile }
        this.formData.assist_result = this.errorReason[0]['value'] // 默认选中第一个
        this.assistResultLabel = this.errorReason[0]['label'] // 默认选中第一个
        let assistSerialNumber = this.$route.query['assist_serial_number']
        this.userid = this.$route.query['user_id']
        this.assistSerialNumber = assistSerialNumber
        // 获取协查详情
        // this.getAssistDetail({
        //   assist_serial_number: assistSerialNumber
        // })
    },
}
</script>

<style></style>