<template>
  <div id="outSystemLink">
    <div class="outSystemLink_title"></div>
    <div class="outSystemLink_wrap">
      <div class="outSystemLink_wrap_center">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>查询时间：{{sharData.searchDate}}</span>
          </div>
          <div class="wraplist" v-if="sharData.searchDataList!=undefined&&sharData.searchDataList.length!=0">
            <div class="wrap-content" v-for="(i,key) in sharData.searchDataList" :key="key">
              <p v-for="(k,key1) in i" :key="key1">
                <span class="key">{{k.display_name}}</span>：
                <span class="value">{{k.display_value}}</span>
              </p>
              <el-divider></el-divider>
            </div>
          </div>
          <div class="nodata" v-else>
            <i class="el-icon-warning"></i>
            <span>{{sharData.errormes}}</span>
          </div>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script>
import { getDataSharedData } from '@/api/assist'
export default {
  data() {
    return {
      sharData: {
        errormes: ''
      }
    }
  },

  mounted() {
    this.getDataSharedData()
  },

  methods: {
    getDataSharedData() {
      const config = {
        headers: {
          'x-license-requestid': this.$route.query['x-license-requestid'],
          'x-license-appkey': this.$route.query['x-license-appkey'],
          'x-license-timestamp': this.$route.query['x-license-timestamp'],
          'x-license-sign': this.$route.query['x-license-sign']
        }
      }
      const parms = {
        item_code: this.$route.query['item_code'],
        material_id: this.$route.query['material_id'],
        material_name: this.$route.query['material_name'],
        identity_name: this.$route.query['identity_name'],
        identity_type: this.$route.query['identity_type'],
        identity_number: this.$route.query['identity_number'],
        org_name: this.$route.query['org_name'],
        org_type: this.$route.query['org_type'],
        org_code: this.$route.query['org_code'],
        item_name: this.$route.query['item_name'],
        impl_org_name: this.$route.query['impl_org_name'],
        credit_code: this.$route.query['credit_code'],
        division_code: this.$route.query['division_code']
      }
      // console.log(parms,config)
      getDataSharedData(parms,config)
        .then(res => {
          if (res.data.meta.code === '200' && res.data.data != null) {
            this.sharData = res.data.data
            // console.log('this.sharData', this.sharData)
            if (this.sharData.searchDataList.length === 0) {
              this.sharData.errormes = '暂无数据'
            }
          } else if (res.data.data === null && res.data.meta.code === '200') {
            this.sharData.errormes = '暂无数据'
          } else {
            this.sharData.errormes = '接口报错，服务异常'
          }
        })
        .catch(err => {
          if (err.data.meta.code === '200' && err.data.data != null) {
            this.sharData = res.data.data
            // console.log('this.sharData', this.sharData)
            if (this.sharData.searchDataList.length === 0) {
              this.sharData.errormes = '暂无数据'
            }
          } else if (err.data.data === null && err.data.meta.code === '200') {
            this.sharData.errormes = '暂无数据'
          } else {
            this.sharData.errormes = '接口报错，服务异常'
          }
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.outSystemLink_title {
  height: 50px;
  background: #13a2d1;
}
.outSystemLink_wrap {
  padding: 15px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.outSystemLink_wrap_center {
  width: 800px;
}
.wrap-content {
  display: flex;
  min-height: 90px;
  width: 100%;
  /* flex-direction: column; */
  flex-wrap: wrap;
}
.wrap-content p {
  width: 50%;
}
.key {
  color: #666;
}
.value {
  color: #333;
}
.nodata {
  display: flex;
  justify-content: center;
  align-items: center;
  span {
    margin-left: 5px;
  }
}
</style>
