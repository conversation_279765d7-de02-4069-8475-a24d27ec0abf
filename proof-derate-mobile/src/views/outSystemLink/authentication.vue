<template>
  <div id="authentication"></div>
</template>

<script>
import { setToken } from '@/utils/auth'
export default {
  data() {
    return {}
  },

  mounted() {},

  methods: {
    async initData() {
      console.log('综受鉴权初始')
      try {
        await setToken(this.appKey, this.timestamp, this.sign, this.requestId).then(res => {
          console.log('鉴权成功', res)
          this.$router.replace({
            name: 'generalAcceptAssist',
            query: {
              assist_serial_number: this.assistSerialNumber
            }
          })
        })
      } catch (err) {
        // this.loading = false;
        //      this.$message({
        //     message: err.data.meta.message,
        //     center: true,
        //     type: "error",
        //     offset: 300,
        //     duration: 300000,
        //   });
        Toast(err.data.meta.message || '')
        console.log('==获取token失败==', err)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
</style>