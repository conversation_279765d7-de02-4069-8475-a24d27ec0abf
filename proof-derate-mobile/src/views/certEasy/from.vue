<template>
    <van-form ref="formData">
        <template>
            <van-field v-model="formData.assistResultLabel" type="text" placeholder="请选择" label-width="100%"
                right-icon="arrow-down" @click="errorReasonChange" required readonly>
                <template #label>
                    <div class="label">协查结果</div>
                </template>
            </van-field>
            <van-popup v-model="formData.showPicker" round position="bottom" :lock-scroll="false" get-container="warp"
                :style="{ 'maxWidth': '1000px' }">
                <van-picker show-toolbar :columns="errorReasonData" :default-index="0"
                    @cancel="formData.showPicker = false" @confirm="onConfirm" />
            </van-popup>
        </template>
        <van-field v-model="formData.nowAuditRelation.audit_suggestion" placeholder="请填写" :rules="rules.assist_opinion"
            label-width="100%" required center>
            <template #label>
                <div class="label">协查意见</div>
            </template>
        </van-field>
        <van-field v-model="formData.nowAuditRelation.assist_from_user" placeholder="请填写" label-width="100%" center
            disabled>
            <template #label>
                <div class="label">协查人</div>
            </template>
        </van-field>
        <van-field v-model="formData.nowAuditRelation.assist_to_org_name" placeholder="" label-width="100%" center
            disabled>
            <template #label>
                <div class="label">协查部门</div>
            </template>
        </van-field>
        <van-field v-model="formData.auditOrgName" placeholder="请填写" label-width="100%" center disabled>
            <template #label>
                <div class="label">下级审批部门所属区划</div>
            </template>
        </van-field>
        <van-field v-model="formData.assistDepLabel" type="text" label="下级审批部门" placeholder="请选择" label-width="100%"
            required readonly>
            <template #input>
                <el-select v-model="formData.auditOrgCode" placeholder="请选择" style="width:300px" multiple
                    @change="auditOrgChange">
                    <el-option v-for="item in formData.subAuditObj" :key="item.audit_org_code"
                        :label="item.audit_org_name" :value="item.audit_org_code"></el-option>
                </el-select>
            </template>
        </van-field>
        <van-field name="uploader" label="文件上传">
            <template v-if="formData.uploader.length == 0" #input>
                <van-uploader v-model="formData.uploader" :after-read="afterRead" :preview-image="false"
                    :multiple="false">
                    <van-button type="info" size="small">上传文件</van-button>
                </van-uploader>

            </template>
            <template v-else #input>
                <van-notice-bar mode="closeable" wrapable :scrollable="false" @close="closeNoticeBar">{{
                    formData.uploadFile.file.name }}</van-notice-bar>
            </template>

        </van-field>
    </van-form>
</template>

<script>
export default {
    data() {

    },
    methods: {
        /**
 * 原因类型选择
 */
        errorReasonChange(event) {
            this.showPicker = true
        },
    }
}
</script>

<style></style>