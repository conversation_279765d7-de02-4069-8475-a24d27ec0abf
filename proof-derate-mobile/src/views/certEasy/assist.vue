<template>
  <!-- 协查首页 -->
  <div class="content">
    <!-- <van-row class="title">协查处理</van-row> -->
    <div id="warp">
      <van-tabs v-model="tabIndex" @click="onClick">
        <van-tab title="协查信息" :class="{ 'form-web': fullWidth > 750, form: fullWidth < 750 }">
          <detail :dataInfo="applyInfo" :title="applyInfoTitle" :doubleDataInfo="doubleDataInfo" />
          <van-row class="margin-10">
            <detail :dataInfo="detailInfo" :title="detailInfoTitle" />
          </van-row>
          <template v-if="detailInfo.length > 0 && showAudit">
            <van-row class="margin-10">
              <van-cell-group :border="false" title="审核信息">
                <!-- <van-form ref="formData" v-if="fullWidth < 750"> -->
                <van-steps direction="vertical" :active="handleActive" v-if="showStep">
                  <van-step>
                    <span>{{ auditList[0] }}</span>
                    <van-form ref="formData" v-if="nowExamineLeve === 1 && auditResult == 'WAIT_FOR_AUDIT'">
                      <template v-if="fullWidth < 750">
                        <van-field
                          v-model="assistResultLabel"
                          type="text"
                          placeholder="请选择"
                          label-width="100%"
                          right-icon="arrow-down"
                          @click="errorReasonChange"
                          required
                          readonly
                        >
                          <template #label>
                            <div class="label">协查结果</div>
                          </template>
                        </van-field>
                        <van-popup v-model="showPicker" round position="bottom" :lock-scroll="false" get-container="warp" :style="{ maxWidth: '1000px' }">
                          <van-picker show-toolbar :columns="errorReasonData" :default-index="0" @cancel="showPicker = false" @confirm="onConfirm" />
                        </van-popup>
                      </template>
                      <template v-else>
                        <van-field v-model="assistResultLabel" type="text" label="协查结果" placeholder="请选择" label-width="100%" @click="errorReasonChange" required readonly>
                          <template #input>
                            <el-select v-model="formData.assist_result" placeholder="请选择" style="width: 300px">
                              <el-option v-for="item in errorReason" :key="item.value" :label="item.label" :value="item.value"></el-option>
                            </el-select>
                          </template>
                        </van-field>
                      </template>

                      <van-field v-model="nowAuditRelation.audit_suggestion" placeholder="请填写" :rules="rules.assist_opinion" label-width="100%" required center>
                        <template #label>
                          <div class="label">协查意见</div>
                        </template>
                      </van-field>
                      <van-field v-model="nowAuditRelation.assist_from_user" placeholder="请填写" label-width="100%" center disabled>
                        <template #label>
                          <div class="label">协查人</div>
                        </template>
                      </van-field>
                      <van-field v-model="nowAuditRelation.audit_org_name" placeholder="" label-width="100%" center disabled>
                        <template #label>
                          <div class="label">协查部门</div>
                        </template>
                      </van-field>
                      <van-field v-model="auditOrgName" placeholder="请填写" label-width="100%" center disabled>
                        <template #label>
                          <div class="label">下级审批部门所属区划</div>
                        </template>
                      </van-field>
                      <van-field v-model="assistDepLabel" type="text" label="下级审批部门" placeholder="请选择" label-width="100%" required readonly>
                        <template #input>
                          <el-select v-model="auditOrgCode" placeholder="请选择" style="width: 300px" multiple @change="auditOrgChange">
                            <el-option v-for="item in subAuditObj" :key="item.audit_org_code" :label="item.audit_org_name" :value="item.audit_org_code"></el-option>
                          </el-select>
                        </template>
                      </van-field>
                      <van-field name="uploader" label="文件上传">
                        <template v-if="uploader.length == 0" #input>
                          <van-uploader v-model="uploader" accept=".doc, .pdf, .jpg, .png, .docx" :after-read="afterRead" :preview-image="false" :multiple="false">
                            <van-button type="info" size="small">上传文件</van-button>
                          </van-uploader>
                        </template>
                        <template v-else #input>
                          <van-notice-bar mode="closeable" wrapable :scrollable="false" @close="closeNoticeBar">{{ uploadFile.file.name }}</van-notice-bar>
                        </template>
                      </van-field>
                    </van-form>
                    <div v-else>
                      <div v-for="(i, examinekey) in haxAuditRelationExamine1" :key="examinekey">
                        <detail v-if="i[0].need_audit" :dataInfo="i" :title="''" @downloadFile="downloadFile" />
                      </div>
                    </div>
                  </van-step>
                  <van-step v-if="nowExamineLeve > 1">
                    <span>{{ auditList[1] }}</span>
                    <van-form ref="formData" v-if="nowExamineLeve === 2 && auditResult == 'WAIT_FOR_AUDIT'">
                      <template v-if="fullWidth < 750">
                        <van-field
                          v-model="assistResultLabel"
                          type="text"
                          placeholder="请选择"
                          label-width="100%"
                          right-icon="arrow-down"
                          @click="errorReasonChange"
                          required
                          readonly
                        >
                          <template #label>
                            <div class="label">协查结果</div>
                          </template>
                        </van-field>
                        <van-popup v-model="showPicker" round position="bottom" :lock-scroll="false" get-container="warp" :style="{ maxWidth: '1000px' }">
                          <van-picker show-toolbar :columns="errorReasonData" :default-index="0" @cancel="showPicker = false" @confirm="onConfirm" />
                        </van-popup>
                      </template>
                      <template v-else>
                        <van-field v-model="assistResultLabel" type="text" label="协查结果" placeholder="请选择" label-width="100%" @click="errorReasonChange" required readonly>
                          <template #input>
                            <el-select v-model="formData.assist_result" placeholder="请选择" style="width: 300px">
                              <el-option v-for="item in errorReason" :key="item.value" :label="item.label" :value="item.value"></el-option>
                            </el-select>
                          </template>
                        </van-field>
                      </template>

                      <van-field v-model="nowAuditRelation.audit_suggestion" placeholder="请填写" :rules="rules.assist_opinion" label-width="100%" required center>
                        <template #label>
                          <div class="label">协查意见</div>
                        </template>
                      </van-field>
                      <van-field v-model="nowAuditRelation.assist_from_user" placeholder="请填写" label-width="100%" center disabled>
                        <template #label>
                          <div class="label">协查人</div>
                        </template>
                      </van-field>
                      <van-field v-model="nowAuditRelation.audit_org_name" placeholder="" label-width="100%" center disabled>
                        <template #label>
                          <div class="label">协查部门</div>
                        </template>
                      </van-field>
                      <van-field v-model="auditOrgName" placeholder="请填写" label-width="100%" center disabled v-if="audit_relation_list.length > 2">
                        <template #label>
                          <div class="label">下级审批部门所属区划</div>
                        </template>
                      </van-field>
                      <van-field
                        v-model="assistDepLabel"
                        type="text"
                        label="下级审批部门"
                        placeholder="请选择"
                        label-width="100%"
                        required
                        readonly
                        v-if="audit_relation_list.length > 2"
                      >
                        <template #input>
                          <el-select v-model="auditOrgCode" placeholder="请选择" style="width: 300px" multiple @change="auditOrgChange">
                            <el-option v-for="item in subAuditObj" :key="item.audit_org_code" :label="item.audit_org_name" :value="item.audit_org_code"></el-option>
                          </el-select>
                        </template>
                      </van-field>
                      <van-field name="uploader" label="文件上传">
                        <template v-if="uploader.length == 0" #input>
                          <van-uploader v-model="uploader" accept=".doc, .pdf, .jpg, .png, .docx" :after-read="afterRead" :preview-image="false" :multiple="false">
                            <van-button type="info" size="small">上传文件</van-button>
                          </van-uploader>
                        </template>
                        <template v-else #input>
                          <van-notice-bar mode="closeable" wrapable :scrollable="false" @close="closeNoticeBar">{{ uploadFile.file.name }}</van-notice-bar>
                        </template>
                      </van-field>
                    </van-form>
                    <div v-else>
                      <div v-for="(i, examinekey) in haxAuditRelationExamine2" :key="examinekey" v-show="haxAuditRelationExamine2.length !== 0">
                        <detail v-if="i[0].need_audit" :dataInfo="i" :title="''" @downloadFile="downloadFile" />
                      </div>
                    </div>
                  </van-step>
                  <van-step v-if="nowExamineLeve > 2">
                    <span>{{ auditList[2] }}</span>
                    <van-form ref="formData" v-if="nowExamineLeve === 3 && auditResult == 'WAIT_FOR_AUDIT'">
                      <template v-if="fullWidth < 750">
                        <van-field
                          v-model="assistResultLabel"
                          type="text"
                          placeholder="请选择"
                          label-width="100%"
                          right-icon="arrow-down"
                          @click="errorReasonChange"
                          required
                          readonly
                        >
                          <template #label>
                            <div class="label">协查结果</div>
                          </template>
                        </van-field>
                        <van-popup v-model="showPicker" round position="bottom" :lock-scroll="false" get-container="warp" :style="{ maxWidth: '1000px' }">
                          <van-picker show-toolbar :columns="errorReasonData" :default-index="0" @cancel="showPicker = false" @confirm="onConfirm" />
                        </van-popup>
                      </template>
                      <template v-else>
                        <van-field v-model="assistResultLabel" type="text" label="协查结果" placeholder="请选择" label-width="100%" @click="errorReasonChange" required readonly>
                          <template #input>
                            <el-select v-model="formData.assist_result" placeholder="请选择" style="width: 300px">
                              <el-option v-for="item in errorReason" :key="item.value" :label="item.label" :value="item.value"></el-option>
                            </el-select>
                          </template>
                        </van-field>
                      </template>

                      <van-field v-model="nowAuditRelation.audit_suggestion" placeholder="请填写" :rules="rules.assist_opinion" label-width="100%" required center>
                        <template #label>
                          <div class="label">协查意见</div>
                        </template>
                      </van-field>
                      <van-field v-model="nowAuditRelation.assist_from_user" placeholder="请填写" label-width="100%" center disabled>
                        <template #label>
                          <div class="label">协查人</div>
                        </template>
                      </van-field>
                      <van-field v-model="nowAuditRelation.audit_org_name" placeholder="" label-width="100%" center disabled>
                        <template #label>
                          <div class="label">协查部门</div>
                        </template>
                      </van-field>
                      <van-field v-model="auditOrgName" placeholder="请填写" label-width="100%" center disabled v-if="audit_relation_list.length > 3">
                        <template #label>
                          <div class="label">下级审批部门所属区划</div>
                        </template>
                      </van-field>
                      <van-field
                        v-model="assistDepLabel"
                        type="text"
                        label="下级审批部门"
                        placeholder="请选择"
                        label-width="100%"
                        required
                        readonly
                        v-if="audit_relation_list.length > 3"
                      >
                        <template #input>
                          <el-select v-model="auditOrgCode" placeholder="请选择" style="width: 300px" multiple @change="auditOrgChange">
                            <el-option v-for="item in subAuditObj" :key="item.audit_org_code" :label="item.audit_org_name" :value="item.audit_org_code"></el-option>
                          </el-select>
                        </template>
                      </van-field>
                      <van-field name="uploader" label="文件上传">
                        <template v-if="uploader.length == 0" #input>
                          <van-uploader v-model="uploader" accept=".doc, .pdf, .jpg, .png, .docx" :after-read="afterRead" :preview-image="false" :multiple="false">
                            <van-button type="info" size="small">上传文件</van-button>
                          </van-uploader>
                        </template>
                        <template v-else #input>
                          <van-notice-bar mode="closeable" wrapable :scrollable="false" @close="closeNoticeBar">{{ uploadFile.file.name }}</van-notice-bar>
                        </template>
                      </van-field>
                    </van-form>
                    <div v-else>
                      <div v-for="(i, examinekey) in haxAuditRelationExamine3" :key="examinekey" v-show="haxAuditRelationExamine3.length !== 0">
                        <detail v-if="i[0].need_audit" :dataInfo="i" :title="''" @downloadFile="downloadFile" />
                      </div>
                    </div>
                  </van-step>

                  <van-step v-if="nowExamineLeve > 3">
                    <span>{{ auditList[3] }}</span>
                    <van-form ref="formData" v-if="nowExamineLeve === 4 && auditResult == 'WAIT_FOR_AUDIT'">
                      <template v-if="fullWidth < 750">
                        <van-field
                          v-model="assistResultLabel"
                          type="text"
                          placeholder="请选择"
                          label-width="100%"
                          right-icon="arrow-down"
                          @click="errorReasonChange"
                          required
                          readonly
                        >
                          <template #label>
                            <div class="label">协查结果</div>
                          </template>
                        </van-field>
                        <van-popup v-model="showPicker" round position="bottom" :lock-scroll="false" get-container="warp" :style="{ maxWidth: '1000px' }">
                          <van-picker show-toolbar :columns="errorReasonData" :default-index="0" @cancel="showPicker = false" @confirm="onConfirm" />
                        </van-popup>
                      </template>
                      <template v-else>
                        <van-field v-model="assistResultLabel" type="text" label="协查结果" placeholder="请选择" label-width="100%" @click="errorReasonChange" required readonly>
                          <template #input>
                            <el-select v-model="formData.assist_result" placeholder="请选择" style="width: 300px">
                              <el-option v-for="item in errorReason" :key="item.value" :label="item.label" :value="item.value"></el-option>
                            </el-select>
                          </template>
                        </van-field>
                      </template>

                      <van-field v-model="nowAuditRelation.audit_suggestion" placeholder="请填写" :rules="rules.assist_opinion" label-width="100%" required center>
                        <template #label>
                          <div class="label">协查意见</div>
                        </template>
                      </van-field>
                      <van-field v-model="nowAuditRelation.assist_from_user" placeholder="请填写" label-width="100%" center disabled>
                        <template #label>
                          <div class="label">协查人</div>
                        </template>
                      </van-field>
                      <van-field v-model="nowAuditRelation.audit_org_name" placeholder="" label-width="100%" center disabled>
                        <template #label>
                          <div class="label">协查部门</div>
                        </template>
                      </van-field>
                      <van-field v-model="auditOrgName" placeholder="请填写" label-width="100%" center disabled v-if="audit_relation_list.length > 4">
                        <template #label>
                          <div class="label">下级审批部门所属区划</div>
                        </template>
                      </van-field>
                      <van-field
                        v-model="assistDepLabel"
                        type="text"
                        label="下级审批部门"
                        placeholder="请选择"
                        label-width="100%"
                        required
                        readonly
                        v-if="audit_relation_list.length > 4"
                      >
                        <template #input>
                          <el-select v-model="auditOrgCode" placeholder="请选择" style="width: 300px" multiple @change="auditOrgChange">
                            <el-option v-for="item in subAuditObj" :key="item.audit_org_code" :label="item.audit_org_name" :value="item.audit_org_code"></el-option>
                          </el-select>
                        </template>
                      </van-field>
                      <van-field name="uploader" label="文件上传">
                        <template v-if="uploader.length == 0" #input>
                          <van-uploader v-model="uploader" accept=".doc, .pdf, .jpg, .png, .docx" :after-read="afterRead" :preview-image="false" :multiple="false">
                            <van-button type="info" size="small">上传文件</van-button>
                          </van-uploader>
                        </template>
                        <template v-else #input>
                          <van-notice-bar mode="closeable" wrapable :scrollable="false" @close="closeNoticeBar">{{ uploadFile.file.name }}</van-notice-bar>
                        </template>
                      </van-field>
                    </van-form>
                    <div v-else>
                      <div v-for="(i, examinekey) in haxAuditRelationExamine4" :key="examinekey" v-show="haxAuditRelationExamine4.length !== 0">
                        <detail v-if="i[0].need_audit" :dataInfo="i" :title="''" @downloadFile="downloadFile" />
                      </div>
                    </div>
                  </van-step>

                  <van-step v-if="nowExamineLeve > 4">
                    <span>{{ auditList[4] }}</span>
                    <van-form ref="formData" v-if="nowExamineLeve === 5 && auditResult == 'WAIT_FOR_AUDIT'">
                      <template v-if="fullWidth < 750">
                        <van-field
                          v-model="assistResultLabel"
                          type="text"
                          placeholder="请选择"
                          label-width="100%"
                          right-icon="arrow-down"
                          @click="errorReasonChange"
                          required
                          readonly
                        >
                          <template #label>
                            <div class="label">协查结果</div>
                          </template>
                        </van-field>
                        <van-popup v-model="showPicker" round position="bottom" :lock-scroll="false" get-container="warp" :style="{ maxWidth: '1000px' }">
                          <van-picker show-toolbar :columns="errorReasonData" :default-index="0" @cancel="showPicker = false" @confirm="onConfirm" />
                        </van-popup>
                      </template>
                      <template v-else>
                        <van-field v-model="assistResultLabel" type="text" label="协查结果" placeholder="请选择" label-width="100%" @click="errorReasonChange" required readonly>
                          <template #input>
                            <el-select v-model="formData.assist_result" placeholder="请选择" style="width: 300px">
                              <el-option v-for="item in errorReason" :key="item.value" :label="item.label" :value="item.value"></el-option>
                            </el-select>
                          </template>
                        </van-field>
                      </template>

                      <van-field v-model="nowAuditRelation.audit_suggestion" placeholder="请填写" :rules="rules.assist_opinion" label-width="100%" required center>
                        <template #label>
                          <div class="label">协查意见</div>
                        </template>
                      </van-field>
                      <van-field v-model="nowAuditRelation.assist_from_user" placeholder="请填写" label-width="100%" center disabled>
                        <template #label>
                          <div class="label">协查人</div>
                        </template>
                      </van-field>
                      <van-field v-model="nowAuditRelation.audit_org_name" placeholder="" label-width="100%" center disabled>
                        <template #label>
                          <div class="label">协查部门</div>
                        </template>
                      </van-field>
                      <van-field v-model="auditOrgName" placeholder="请填写" label-width="100%" center disabled v-if="audit_relation_list.length > 4">
                        <template #label>
                          <div class="label">下级审批部门所属区划</div>
                        </template>
                      </van-field>
                      <van-field
                        v-model="assistDepLabel"
                        type="text"
                        label="下级审批部门"
                        placeholder="请选择"
                        label-width="100%"
                        required
                        readonly
                        v-if="audit_relation_list.length > 5"
                      >
                        <template #input>
                          <el-select v-model="auditOrgCode" placeholder="请选择" style="width: 300px" multiple @change="auditOrgChange">
                            <el-option v-for="item in subAuditObj" :key="item.audit_org_code" :label="item.audit_org_name" :value="item.audit_org_code"></el-option>
                          </el-select>
                        </template>
                      </van-field>
                      <van-field name="uploader" label="文件上传">
                        <template v-if="uploader.length == 0" #input>
                          <van-uploader v-model="uploader" accept=".doc, .pdf, .jpg, .png, .docx" :after-read="afterRead" :preview-image="false" :multiple="false">
                            <van-button type="info" size="small">上传文件</van-button>
                          </van-uploader>
                        </template>
                        <template v-else #input>
                          <van-notice-bar mode="closeable" wrapable :scrollable="false" @close="closeNoticeBar">{{ uploadFile.file.name }}</van-notice-bar>
                        </template>
                      </van-field>
                    </van-form>
                    <div v-else>
                      <div v-for="(i, examinekey) in haxAuditRelationExamine5" :key="examinekey" v-show="haxAuditRelationExamine5.length !== 0">
                        <detail v-if="i[0].need_audit" :dataInfo="i" :title="''" @downloadFile="downloadFile" />
                      </div>
                    </div>
                  </van-step>
                </van-steps>
                <!-- v-else-if="showStep !== true && auditResult !== 'SUCCESS'" -->

                <div v-else>
                  <van-form ref="formData" v-if="auditResult !== 'SUCCESS'">
                    <template v-if="fullWidth < 750">
                      <van-field
                        v-model="assistResultLabel"
                        type="text"
                        placeholder="请选择"
                        label-width="100%"
                        right-icon="arrow-down"
                        @click="errorReasonChange"
                        required
                        readonly
                      >
                        <template #label>
                          <div class="label">协查结果</div>
                        </template>
                      </van-field>
                      <van-popup v-model="showPicker" round position="bottom" :lock-scroll="false" get-container="warp" :style="{ maxWidth: '1000px' }">
                        <van-picker show-toolbar :columns="errorReasonData" :default-index="0" @cancel="showPicker = false" @confirm="onConfirm" />
                      </van-popup>
                    </template>
                    <template v-else>
                      <van-field v-model="assistResultLabel" type="text" label="协查结果" placeholder="请选择" label-width="100%" @click="errorReasonChange" required readonly>
                        <template #input>
                          <el-select v-model="formData.assist_result" placeholder="请选择" style="width: 300px">
                            <el-option v-for="item in errorReason" :key="item.value" :label="item.label" :value="item.value" @change="setNowAuditRelationStatus"></el-option>
                          </el-select>
                        </template>
                      </van-field>
                    </template>

                    <van-field v-model="nowAuditRelation.audit_suggestion" placeholder="请填写" :rules="rules.assist_opinion" label-width="100%" required center>
                      <template #label>
                        <div class="label">协查意见</div>
                      </template>
                    </van-field>
                    <van-field v-model="nowAuditRelation.to_user_name" placeholder="请填写" label-width="100%" center disabled>
                      <template #label>
                        <div class="label">协查人</div>
                      </template>
                    </van-field>
                    <van-field v-model="nowAuditRelation.audit_org_name" placeholder="" label-width="100%" center disabled>
                      <template #label>
                        <div class="label">协查部门</div>
                      </template>
                    </van-field>

                    <van-field name="uploader" label="文件上传">
                      <template v-if="uploader.length == 0" #input>
                        <van-uploader v-model="uploader" accept=".doc, .pdf, .jpg, .png, .docx" :after-read="afterRead" :preview-image="false" :multiple="false">
                          <van-button type="info" size="small">上传文件</van-button>
                        </van-uploader>
                      </template>
                      <template v-else #input>
                        <van-notice-bar mode="closeable" wrapable :scrollable="false" @close="closeNoticeBar">{{ uploadFile.file.name }}</van-notice-bar>
                      </template>
                    </van-field>
                  </van-form>
                  <detail
                    v-else
                    :dataInfo="i"
                    :title="''"
                    @downloadFile="downloadFile"
                    v-for="(i, examinekey) in haxAuditRelationExamine1"
                    :key="examinekey"
                    v-show="haxAuditRelationExamine1.length !== 0"
                  />
                </div>
                <!-- 
									-- ZZ24DZZZ-282
								<van-row type="flex" justify="center">
                  <div style="text-align: center"><van-button type="info" v-if="liccItemAuthCode" @click="downloadAttachment">下载电子证明</van-button></div>
                </van-row> 
								-->
              </van-cell-group>
            </van-row>
            <!-- auditResult !== 'SUCCESS' && auditResult !== 'AUDITING -->

            <van-row type="flex" justify="center" v-if="auditResult == 'WAIT_FOR_AUDIT'">
              <van-col :span="fullWidth > 750 ? 16 : 22" class="submit-btn">
                <van-button type="info" block @click="onSubmit">提交</van-button>
              </van-col>
            </van-row>
          </template>
        </van-tab>
        <van-tab title="协查历史">
          <Item :dataInfo="itemInfo" @click="toDetail" />
        </van-tab>
      </van-tabs>
    </div>
  </div>
</template>
<script>
import Detail from '@/views/components/Detail'
import Item from '@/views/components/Item'
import editForm from '@/views/components/editForm'
import { getAssistDetail, getAssistHistory, postAssistAudit, getAccountByGovEasyUser, downloadFile, downloadAttachment } from '@/api/assist'
import { id, tyshxydm, tempTyshxydm, zzjgdm, mobile } from '@/utils/validType'
import { Dialog, Toast } from 'vant'
import { uploadFile, dataURLtoDownload } from '@/utils/util'
export default {
  components: { Detail, Item, editForm },
  data() {
    return {
      assistSerialNumber: '',
      userid: '',
      fullWidth: document.documentElement.clientWidth,
      activeName: 'second',
      tabIndex: 0,
      applyInfo: [],
      doubleDataInfo: [],
      detailInfo: [],
      itemInfo: [],
      applyInfoTitle: '申请信息',
      detailInfoTitle: '协查人发起信息',
      assistResultLabel: '',
      assistDepLabel: '', // 协查部门
      formData: {
        assist_result: '', // 协查结果
        assist_opinion: '', // 协查意见
        assist_from_user: '', // 协查人
        assist_deparment: '', // 协查部门
      },
      rules: {
        assist_result: [{ required: true }],
        assist_opinion: [{ required: true }],
      },
      validType: {}, //正则校验
      showPicker: false,
      errorReason: [
        { value: 'SUCCESS', label: '符合' },
        { value: 'FAIL', label: '不符合' },
      ],
      auditDict: [
        { value: 'WAIT', label: '待协查' },
        { value: 'SUCCESS', label: '符合' },
        { value: 'FAIL', label: '不符合' },
      ],

      // showAudit: false

      showAudit: true,
      examineLevelshow: true, // 多级审核是否展示
      showStep: true,
      userdata: {},
      // 多层审核对象
      auditRelationList: [],
      // 当前审核对象
      nowAuditRelation: {
        assist_attachment_name: '',
        raw: '',
        audit_record: false,
      },
      onlyshowExaminedTable: false,
      auditOrgName: '', // 下级审核区划
      subAuditObj: [], // 下级审核部门
      sub_audit_obj_1: {}, //一级审核对象
      sub_audit_obj_2: {}, //二级审核对象
      sub_audit_obj_3: {}, //三级审核对象
      auditOrgCode: [],
      auditDiviCode: [],
      auditDiviLabel: [],
      haxAuditRelation: [], // 已审核对象
      auditResult: '', // 审核进度（SUCCESS:已完成）
      haxAuditRelationExamine1: [], // 第一级已审核数据
      haxAuditRelationExamine2: [],
      haxAuditRelationExamine3: [],
      haxAuditRelationExamine4: [],
      haxAuditRelationExamine5: [],
      uploader: [],
      uploadFile: '',
      handleActive: 0,
      addForm: {
        audit_result: 'SUCCESS',
        audit_suggestion: '',
        audit_time: '',
        fileName: '',
        raw: '',
        audit_id: '',
        to_user_id: '',
        to_user_name: '',
        to_assist_org_name: '',
        to_assist_credit_code: '',
        examineDept: '',
        audit_record: false,
      },
      audit_relation_list: [],
      auditList: [],
      liccItemAuthCode: null, // 开具的电子证明编码
    }
  },
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      // 通过 `vm` 访问组件实例
      // vm.showAudit = false
      vm.showAudit = true
      if (from.name === 'certEasyResult') {
        vm.getAssistDetail({
          assist_serial_number: vm.assistSerialNumber,
        })
      }
    })
    // 结果页面返回刷新数据
  },
  async created() {
    this.validType = { id, tyshxydm, tempTyshxydm, zzjgdm, mobile }
    this.formData.assist_result = this.errorReason[0]['value'] // 默认选中第一个
    this.assistResultLabel = this.errorReason[0]['label'] // 默认选中第一个
    let assistSerialNumber = this.$route.query['assist_serial_number']
    this.userid = this.$route.query['user_id']
    this.assistSerialNumber = assistSerialNumber
    await this.getAccountByGovEasyUser({
      user_id: this.userid,
    })
    await this.getAssistDetail({
      assist_serial_number: assistSerialNumber,
    })
  },
  mounted() {
    window.addEventListener('resize', this.handleResize)
    // 获取协查详情
  },
  computed: {
    errorReasonData: function () {
      let errorReasonArr = []
      this.errorReason.forEach((i) => {
        errorReasonArr.push(i.label)
      })
      return errorReasonArr
    },
  },
  watch: {
    assistResultLabel: function () {
      let chooseReason = this.errorReason.find((item) => {
        return item.label == this.assistResultLabel
      })
    },
  },
  methods: {
    handleResize(event) {
      this.fullWidth = document.documentElement.clientWidth
    },
    toDetail(info) {
      this.$router.push({
        name: 'certEasyDetail',
        query: {
          assistSerialNumber: info.assistSerialNumber,
        },
      })
    },
    onClick() {
      if (this.tabIndex === 1) {
        this.getAssistList()
      }
    },
    licenseChange(e) {
      this.license = e
      this.formData.license_item_code = e.value
      this.formData.license_name = e.label
      this.licenseItemLabel = e.label
      this.showLicensePicker = false
    },
    onConfirm(value) {
      console.log('onConfirm', value)
      this.assistResultLabel = value
      this.nowAuditRelation.assist_audit_result = this.errorReason.find((item) => {
        return item.label == this.assistResultLabel
      }).value
      this.showPicker = false
      console.log('this.nowAuditRelation.assist_audit_result', this.nowAuditRelation.assist_audit_result)
    },
    afterRead(file) {
      console.log('file', file)
      this.uploadFile = file
      const query = {
        relation: this.addForm.audit_id,
      }
      this.nowAuditRelation.assist_attachment_name = file.file.name
      const fd = new FormData()
      fd.append('file', this.uploadFile.file)
      uploadFile(query, fd).then((res) => {
        console.log(res)
        if (res.data.meta.code === '200' && res.data.data !== null) {
          this.nowAuditRelation.assist_attachment_id = res.data.data
          Toast('上传成功')
        }
        console.log('this.nowAuditRelation.assist_attachment_name', this.nowAuditRelation.assist_attachment_name)
        console.log('this.nowAuditRelation.assist_attachment_id', this.nowAuditRelation.assist_attachment_id)
      })
    },
    closeNoticeBar() {
      this.uploader = []
    },
    // 下级审核部门选中，修改下一级部门审核是否需要审核
    auditOrgChange() {
      this.subAuditObj.forEach((e) => {
        if (this.auditOrgCode.indexOf(e.audit_org_code) !== -1) {
          e.need_audit = true
          e.audit_record = true
        } else {
          e.need_audit = false
        }
      })
      console.log('subAuditObj', this.subAuditObj)
    },
    onSubmit() {
      console.log('this.auditOrgCode', this.auditOrgCode)
      if (this.nowAuditRelation.assist_result === '') {
        // Dialog.alert({
        //   message: "请选择协查结果"
        // });
        Toast('请选择协查结果')
      } else if (this.nowAuditRelation.audit_suggestion === '') {
        // Dialog.alert({
        //   message: "请填写核查意见"
        // });
        Toast('请填写核查意见')
      } else if (this.auditOrgCode.length === 0 && this.nowExamineLeve !== 3 && this.showStep && this.audit_relation_list.length !== 2) {
        Toast('请选择下级审核部门')
      } else {
        Dialog.confirm({
          title: '请确认是否提交该审核意见？',
          confirmButtonColor: '#1989fa',
        }).then(() => {
          this.apply()
        })
      }
    },
    getAccountByGovEasyUser(params) {
      return getAccountByGovEasyUser(params).then((res) => {
        console.log(res)
        if (res.data.meta.code === '200' && res.data.data !== null) {
          this.userdata = res.data.data
          console.log('this.userdata', this.userdata)
        }
      })
    },
    /**
     * 获取协查结果
     */
    getAssistDetail(params) {
      getAssistDetail(params)
        .then((res) => {
          console.log('getAssistDetail12346', res.data.data)
          this.setAuditRelation(res.data)
          let data = res.data.data
          let assistData = []
          let assistData1 = []
          let assistDataInfo = []
          if (res.data.data) {
            if (data.assist_data_list) {
							assistData = data.assist_data_list[0].item_list
            }
            console.log('assistDataInfo', assistDataInfo)
            this.detailInfo = [
              {
                // title: assistDataInfo.title || "--",
                title: '协查发起人信息',
                data: [
                  {
                    title: '协查发起人',
                    value: data.from_assist_user_name || '--',
                  },
                  {
                    title: '协查发起人联系方式',
                    value: data.from_assist_contain || '--',
                  },
                  {
                    title: '协查发起人部门',
                    value: data.from_assist_org_name || '--',
                  },
                  { title: '协查发起时间', value: data.creation_time || '--' },
                ],
                // tips: [assistDataInfo.bottom],
              },
            ]
            console.log('assistData', assistData)
            this.applyInfo = [
              {
                // title: assistDataInfo.title || "--",
                title: '申请信息',
                applytitle: assistDataInfo.title || null,
                data: [
                  { title: '业务流水号', value: data.business_serial_number },
                  { title: '协查单号', value: data.assist_serial_number },
                  { title: '办事人姓名', value: data.handle_affairs_name },
                  {
                    title: '证件类型',
                    value: data.handle_affairs_identity_type_name,
                  },
                  {
                    title: '证件号码',
                    value: data.handle_affairs_identity_number,
                  },
                  { title: '事项名称', value: data.item_name },
                  { title: '材料名称', value: data.material_name },
                  { title: '协查需求描述', value: data.from_demand },
                ],
								itemList:[...assistData],
                tips: assistDataInfo.bottom ? [assistDataInfo.bottom] : '',
              },
            ]
            // this.doubleDataInfo = assistData1
            if (data.handle_affairs_type === 'LEGAL_PERSON') {
              this.applyInfo[0].data[3].value = data.legal_person_identity_type
              if (data.legal_person_identity_type === null) {
                this.applyInfo[0].data[3].value = '统一社会信用代码'
              }
            }
            if (data.audit_result === 'WAIT') {
              this.showAudit = true
            } else {
            }

            this.formData.assist_from_user = data.assist_from_user || '--'
            this.formData.assist_deparment = data.toAssistOrgName
            this.addForm.audit_id = data.id
          } else {
            this.detailInfo = []
          }
        })
        .catch((err) => {
          console.log(err)
          Toast(err.data.meta.message || '')
        })
    },
    getAssistList() {
      getAssistHistory({
        assist_serial_number: this.assistSerialNumber,
      })
        .then((res) => {
          let data = res.data.data
          if (data.length > 0) {
            let itemData = data.map((i) => {
              let temp = {
                id: i.id,
                assistSerialNumber: i.assist_serial_number,
                title: i.handle_affairs_name || '--',
                value: this.auditDict.find((j) => j.value === i.audit_result).label,
                status: i.audit_result.toLowerCase(),
                description: (i.from_demand || '--') + ' ' + (i.audit_time || '--'),
              }
              return temp
            })
            this.itemInfo = [
              {
                title: '',
                data: itemData,
              },
            ]
            console.log(this.itemInfo, 2345678)
          } else {
            this.itemInfo = []
          }
        })
        .catch((err) => {})
    },
    /**
     * 原因类型选择
     */
    errorReasonChange(event) {
      this.showPicker = true
    },

    /**
     * 协查审核
     */
    apply() {
      Toast.loading({
        duration: 1000,
        message: '正在提交...',
        forbidClick: true,
      })

      // let sendData = {
      //   assist_serial_number: this.assistSerialNumber,
      //   assist_comment: this.formData.assist_opinion,
      //   audit_status: this.formData.assist_result,
      //   audit_user_id: this.userid
      // }
      const data = {
        assist_serial_number: this.assistSerialNumber,
        audit_id: this.addForm.audit_id,
        audit_relation_list: this.auditRelationList,
        user_id: this.userid,
      }
      postAssistAudit(data)
        .then((res) => {
          this.$router.push({
            name: 'certEasyResult',
            query: {
              type: 'success',
              message: '提交成功',
              assistSerialNumber: res.data.data,
            },
          })
          return
        })
        .catch((err) => {
          this.buttonLoading = false
          this.$router.push({
            name: 'certEasyResult',
            query: {
              type: 'fail',
              message: '提交失败',
              description: err.data.meta.message,
            },
          })
        })
    },
    // 设置对应数据格式
    setAuditRelation(res) {
      const userdata = this.userdata
      // const accountInfo = JSON.parse(sessionStorage.getItem('accountInfo'))
      const audit_relation_list = res.data.audit_relation_list
      this.audit_relation_list = res.data.audit_relation_list
      this.liccItemAuthCode = res.data.licc_item_auth_code
      console.log('liccItemAuthCode',res.data)
      this.auditResult = res.data.audit_result
      this.auditRelationListCopy = res.data.audit_relation_list
      this.auditList = audit_relation_list.map((i) => {
        return i.level_desc
      })
      console.log('res.data.audit_result', res.data.audit_result, audit_relation_list)
      let auditLevel1 = []
      let auditLevel2 = []
      let auditLevel3 = []
      let auditLevel4 = []
      let auditLevel5 = []
      let isHasAudit = false
      if (this.auditResult !== 'SUCCESS') {
        // this.auditRelationList =  res.data.sub_audit_obj
        this.subAuditObj = []
        // 如果是只有一级审核，不显示下级审核区划和下级审核部门
        if (audit_relation_list.length == 1) {
          if (audit_relation_list[0].audit_level == 1) {
            this.examineLevelshow = false
            this.showStep = false
          }
        }
        audit_relation_list.forEach((e0, index) => {
          // 如果是多级审核，获取下一级审核的部门
          if (this.examineLevelshow) {
            if (e0.audit_status === 'WAIT_FOR_AUDIT') {
              if (audit_relation_list[index + 1] !== undefined) {
                this.subAuditObj = audit_relation_list[index + 1].sub_audit_obj
              } else {
                this.examineLevelshow = false
              }
            }
          }

          e0.sub_audit_obj.forEach((e1) => {
            this.auditRelationList.push(e1)
            console.log(e0.audit_status === 'WAIT_FOR_AUDIT', userdata.credit_code, e1.audit_org_code, userdata.credit_code === e1.audit_org_code, 5555)
            // 如果匹配到未审核
            if (e0.audit_status === 'WAIT_FOR_AUDIT' && userdata.credit_code === e1.audit_org_code) {
              if (!isHasAudit) {
                // 如果多级审核进来，匹配中用户编码切状态要不为审核过才进入待审核流程
                if (e1.assist_audit_result !== 'SUCCESS') {
                  this.nowAuditRelation = e1
                  this.nowExamineLeve = this.nowAuditRelation.audit_level
                  this.nowAuditRelation.audit_record = true
                  this.onlyshowExaminedTable = false
                  this.auditResult = 'WAIT_FOR_AUDIT' // 待审核
                  isHasAudit = true
                } else {
                  console.log('进入待审核没有权限')
                  this.onlyshowExaminedTable = true // 只显示列表展示数据
                  this.nowExamineLeve = e0.audit_level
                  this.auditResult = 'AUDITING' // 等待另外部门审核
                  isHasAudit = true
                }
              }
            } else if (e0.audit_status === 'WAIT_FOR_AUDIT' && userdata.credit_code !== e1.audit_org_code) {
              if (!isHasAudit) {
                console.log('进入待审核没有权限')
                this.onlyshowExaminedTable = true // 只显示列表展示数据
                this.nowExamineLeve = e0.audit_level
                this.auditResult = 'AUDITING' // 等待另外部门审核
                if (e1.audit_level === this.auditRelationListCopy.length) {
                  this.nowExamineLeve = e0.audit_level - 1
                }
              }
            }
            // 如果匹配到其中审核了一个部门还有剩余部门未审核
            else if (e0.audit_status === 'AUDITING' && userdata.credit_code === e1.audit_org_code) {
              this.nowAuditRelation = e1
              this.nowExamineLeve = this.nowAuditRelation.audit_level
              this.onlyshowExaminedTable = true // 只显示列表展示数据
              this.auditResult = 'AUDITING' // 等待另外部门审核
            } else {
            }
            if (e1.audit_level === 1) {
              auditLevel1.push(e1)
              this.haxAuditRelation[0] = auditLevel1
            } else if (e1.audit_level === 2) {
              auditLevel2.push(e1)
              this.haxAuditRelation[1] = auditLevel2
            } else if (e1.audit_level === 3) {
              auditLevel3.push(e1)
              this.haxAuditRelation[2] = auditLevel3
            } else if (e1.audit_level === 4) {
              auditLevel4.push(e1)
              this.haxAuditRelation[3] = auditLevel4
            } else if (e1.audit_level === 5) {
              auditLevel5.push(e1)
              this.haxAuditRelation[4] = auditLevel5
            }
          })
        })
        this.nowAuditRelation.to_user_account = userdata.account_name
        this.nowAuditRelation.to_user_name = userdata.user_name
        this.nowAuditRelation.assist_audit_result = 'SUCCESS'
        this.subAuditObj.forEach((e) => {
          this.auditDiviCode.push(e.audit_divi_code)
          this.auditDiviLabel.push(e.audit_divi_name)
        })
        if (this.auditDiviLabel.length !== 0) {
          this.auditOrgName = this.auditDiviLabel.join(',')
        }
        this.handleActive = this.nowExamineLeve - 1
        this.haxAuditRelation.forEach((i) => {
          i.forEach((e) => {
            let data = {
              title: '',
              need_audit: e.need_audit,
              data: [
                {
                  title: '协查处理人',
                  value: e.to_user_name || '--',
                },
                {
                  title: '协查部门',
                  value: e.audit_org_name || '--',
                },
                {
                  title: '审核结果',
                  value: e.assist_audit_result === 'SUCCESS' ? '通过' : e.assist_audit_result === 'WAIT' ? '待审核' : '不通过' || '--',
                },
                { title: '附件', value: e.assist_attachment_name || '--', type: 'download', assistAttachmentId: e.assist_attachment_id },
                { title: '协查意见', value: e.audit_suggestion || '--' },
                { title: '协查时间', value: e.audit_time || '--' },
              ],
            }
            if (e.audit_level === 1) {
              this.haxAuditRelationExamine1.push([data])
            } else if (e.audit_level === 2) {
              this.haxAuditRelationExamine2.push([data])
            } else if (e.audit_level === 3) {
              this.haxAuditRelationExamine3.push([data])
            } else if (e.audit_level === 4) {
              this.haxAuditRelationExamine4.push([data])
            } else if (e.audit_level === 5) {
              this.haxAuditRelationExamine5.push([data])
            }
          })
        })
        console.log('this.haxAuditRelationExamine1', this.haxAuditRelationExamine1)
        console.log('this.haxAuditRelationExamine2', this.haxAuditRelationExamine2)
        console.log('this.haxAuditRelationExamine3', this.haxAuditRelationExamine3)
        // this.initData(res.data)
      } else {
        this.nowExamineLeve = audit_relation_list[audit_relation_list.length - 1].audit_level
        this.handleActive = this.nowExamineLeve - 1
        if (audit_relation_list.length === 1) {
          this.showStep = false
          this.examineLevelshow = false
        }
        this.onlyshowExaminedTable = true
        let auditDiviName2 = []
        let auditDiviName3 = []
        let auditDiviName4 = []
        let auditDiviName5 = []
        let auditOrgName2 = []
        let auditOrgName3 = []
        let auditOrgName4 = []
        let auditOrgName5 = []
        // 当审核完成时，获取下级审核部门和审核行政区划
        audit_relation_list.forEach((e0, index) => {
          if (audit_relation_list[index + 1]) {
            audit_relation_list[index + 1].sub_audit_obj.forEach((e2) => {
              if (e2.audit_level === 2) {
                auditDiviName2.push(e2.audit_divi_name)
                auditOrgName2.push(e2.audit_org_name)
              }
              if (e2.audit_level === 3) {
                auditDiviName3.push(e2.audit_divi_name)
                auditOrgName3.push(e2.audit_org_name)
              }
              if (e2.audit_level === 4) {
                auditDiviName4.push(e2.audit_divi_name)
                auditOrgName4.push(e2.audit_org_name)
              }
              if (e2.audit_level === 5) {
                auditDiviName5.push(e2.audit_divi_name)
                auditOrgName5.push(e2.audit_org_name)
              }
            })
          }
        })
        audit_relation_list.forEach((e0, index) => {
          e0.sub_audit_obj.forEach((e1) => {
            if (e1.audit_level === 1) {
              if (auditDiviName2.length != 0) {
                e1.auditDiviName = auditDiviName2.join(',')
              }
              if (auditOrgName2.length != 0) {
                e1.auditOrgName = auditOrgName2.join(',')
              }
              auditLevel1.push(e1)
              // this.haxAuditRelation.push(auditLevel1)
            } else if (e1.audit_level === 2) {
              if (auditDiviName3.length != 0) {
                e1.auditDiviName = auditDiviName3.join(',')
              }
              if (auditOrgName3.length != 0) {
                e1.auditOrgName = auditOrgName3.join(',')
              }
              auditLevel2.push(e1)
              // this.haxAuditRelation.push(auditLevel2)
            } else if (e1.audit_level === 3) {
              if (auditDiviName4.length != 0) {
                e1.auditDiviName = auditDiviName4.join(',')
              }
              if (auditOrgName4.length != 0) {
                e1.auditOrgName = auditOrgName4.join(',')
              }
              auditLevel3.push(e1)
              // this.haxAuditRelation.push(auditLevel3)
            } else if (e1.audit_level === 4) {
              if (auditDiviName5.length != 0) {
                e1.auditDiviName = auditDiviName5.join(',')
              }
              if (auditOrgName5.length != 0) {
                e1.auditOrgName = auditOrgName5.join(',')
              }
              auditLevel4.push(e1)
              // this.haxAuditRelation.push(auditLevel3)
            } else if (e1.audit_level === 5) {
              auditLevel5.push(e1)
              // this.haxAuditRelation.push(auditLevel3)
            }
          })
        })
        if (auditLevel1.length != 0) {
          this.haxAuditRelation.push(auditLevel1)
        }
        if (auditLevel2.length != 0) {
          this.haxAuditRelation.push(auditLevel2)
        }
        if (auditLevel3.length != 0) {
          this.haxAuditRelation.push(auditLevel3)
        }
        if (auditLevel4.length != 0) {
          this.haxAuditRelation.push(auditLevel4)
        }
        if (auditLevel5.length != 0) {
          this.haxAuditRelation.push(auditLevel5)
        }
        // this.haxAuditRelation.push(auditLevel1, auditLevel2, auditLevel3)
        this.haxAuditRelation.forEach((i) => {
          i.forEach((e) => {
            let data = {
              // title: assistDataInfo.title || "--",
              title: '',
              need_audit: e.need_audit,
              data: [
                {
                  title: '协查处理人',
                  value: e.to_user_name || '--',
                },
                {
                  title: '协查部门',
                  value: e.audit_org_name || '--',
                },
                {
                  title: '审核结果',
                  value: e.assist_audit_result === 'SUCCESS' ? '通过' : e.assist_audit_result === 'WAIT' ? '待审核' : '不通过' || '--',
                },
                { title: '附件', value: e.assist_attachment_name || '--', type: 'download', assistAttachmentId: e.assist_attachment_id },
                { title: '协查意见', value: e.audit_suggestion || '--' },
                { title: '协查时间', value: e.audit_time || '--' },
              ],
              // tips: [assistDataInfo.bottom]
            }
            if (e.audit_level === 1) {
              this.haxAuditRelationExamine1.push([data])
            } else if (e.audit_level === 2) {
              this.haxAuditRelationExamine2.push([data])
            } else if (e.audit_level === 3) {
              this.haxAuditRelationExamine3.push([data])
            } else if (e.audit_level === 4) {
              this.haxAuditRelationExamine4.push([data])
            } else if (e.audit_level === 5) {
              this.haxAuditRelationExamine5.push([data])
            }
          })
        })
      }
    },
    // 对匹配上，要审核的数据进行设值和控制
    setAuditRelationWaitRecord() {},
    // 对匹配上或匹配不上当前用户的数据，进行控制和赋值展示
    setAuditRelationFinishedRecord() {},

    // 对当前审匹配到的审核对象记录修改审核状态设值
    setNowAuditRelationStatus() {
      this.nowAuditRelation.audit_record = true
    },
    // 下载附件
    downloadFile(assist_attachment_id) {
      console.log('assist_attachment_id', assist_attachment_id)
      const config = { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
      downloadFile({ id: assist_attachment_id }, config).then((res) => {
        console.log(res)
        dataURLtoDownload(res.data.data.file_data_base64, res.data.data.file_name)
      })
    },
    // 下载电子证明
    downloadAttachment() {
      downloadAttachment({ assist_serial_number: this.assistSerialNumber }).then((res) => {
        // console.log(res)
        if(res){
          dataURLtoDownload(res.data.data.file_data_base64, res.data.data.file_name)
        }
      })
    },
  },
}
</script>
<style lang="less" scoped>
.content {
  background: #f9f9f9;
  // height: 100vh;
  height: 100%;
}

#warp {
  max-width: 1000px;
  margin: 0 auto;
  height: auto;
}

.title {
  padding: 10px 0;
  background: #3278ea;
  font-size: 18px;
  font-weight: 500;
  text-align: center;
  color: #ffffff;
}

/deep/ .van-tab {
  font-size: 16px;
}

/deep/ .van-tabs__line {
  width: 23%;
  background: #3278ea;
}

/deep/ .van-tab--active {
  color: #3278ea;
}

.margin-16 {
  margin: 16px 0;
}

.margin-10 {
  margin: 10px 0;
}

.tips {
  padding: 1px 16px 16px 16px;
  font-size: 12px;
  font-weight: 400;
  text-align: left;
  color: #7a7a7a;
  line-height: 22px;
  background: #ffffff;
}

/deep/ .form .van-cell-group__title,
.form-web .van-cell-group__title {
  padding: 20px 16px;
  // font-size: 24px;
  font-size: 20px;
  font-weight: 500;
  text-align: left;
  color: #262626;
  background: #ffffff;
}

/deep/ .form .van-cell,
.form-web .van-cell {
  padding: 10px 16px;
  color: #7a7a7a;
  font-size: 16px;
}

.submit-btn {
  margin: 20px 0;
}

.label {
  height: 30px;
  // background: pink;
  text-align: middle;
  line-height: 30px;
  font-size: 15px;
  font-weight: 400;
}

/deep/ .form .van-form .van-cell,
.form-web .van-form .van-cell {
  display: block;
  // line-height: 52px;
  padding: 16px;
  background-color: #ffffff;
}

/deep/ .form-web .van-field__label {
  padding-left: 50px;
  font-size: 18px;
  background-color: #ffffff;
}

/deep/ .form-web .van-field__control {
  padding: 16px 0 0 50px;
  max-width: 310px;
  font-size: 18px;
  background-color: #ffffff;
}

/deep/ .van-button--normal {
  font-size: 16px;
  font-weight: 600;
}

/deep/ .form .van-cell--required::before {
  padding: 5px 0px;
}

/deep/ .form-web .van-cell--required::before {
  padding-left: 50px;
}

/deep/ .form .van-notice-bar {
  width: 100%;
}
/deep/ .van-step--vertical:not(:last-child)::after {
  border-bottom-width: 0px;
}
// ::v-deep .van-overlay {
//   position: absolute;
// }
// ::v-deep .van-popup {
//   position: absolute;
// }
</style>
