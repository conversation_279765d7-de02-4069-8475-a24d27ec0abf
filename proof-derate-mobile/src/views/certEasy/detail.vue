<template>
  <!-- 协查处理详情页 -->
  <div class="content">
    <!-- <van-row class="title">协查处理</van-row> -->
    <div id="warp">
      <detail :dataInfo="dataInfo" />
    </div>
  </div>
</template>
<script>
import Detail from "@/views/components/Detail";
import { getAssistHistoryDetail } from "@/api/assist";
export default {
  components: { Detail },
  data() {
    return {
      assistSerialNumber: "",
      dataInfo: [],
      auditDict: [
        { value: "WAIT", label: "待协查" },
        { value: "SUCCESS", label: "符合" },
        { value: "FAIL", label: "不符合" }
      ]
    };
  },
  created() {
    this.assistSerialNumber = this.$route.query["assistSerialNumber"];
    console.log("toDetail", this.assistSerialNumber);
    // 获取协查详情
    this.getAssistHistoryDetail({
      assist_serial_number: this.assistSerialNumber
    });
  },
  methods: {
    /**
     * 获取协查历史详情
     */
    getAssistHistoryDetail(params) {
      getAssistHistoryDetail(params)
        .then(res => {
          console.log("==获取协查历史详情成功==", res);

          let data = res.data.data;
          let assistDataInfo = data.assist_data_list[0];
          let assistData = assistDataInfo["item_list"].map(i => {
            return { title: i.key, value: i.value };
          });
          this.dataInfo = [
            {
              title: assistDataInfo.title || "--",
              data: [
                {
                  title: "协查发起人",
                  value: data.from_assist_user_name || "--"
                },
                {
                  title: "协查发起人联系方式",
                  value: data.from_assist_contain || "--"
                },
                {
                  title: "协查发起人部门",
                  value: data.from_assist_org_name || "--"
                },
                { title: "协查发起时间", value: data.creation_time || "--" },
                ...assistData
              ],
              tips: [assistDataInfo.bottom]
            },
            {
              title: "审核信息",
              data: [
                {
                  title: "协查结果",
                  value: this.auditDict.find(i => i.value === data.audit_result)
                    .label,
                  status: data.audit_result.toLowerCase()
                },
                { title: "协查意见", value: data.audit_suggestion || "--"},
                { title: "协查人", value: data.toUserName || "--"},
                { title: "协查部门", value: data.toAssistOrgName || "--"}
              ]
            }
          ];
        })
        .catch(err => {
          console.log("==获取协查历史详情==", err);
        });
    }
  }
};
</script>
<style lang="less" scoped>
.content {
  background: #f9f9f9;
  height: 100vh;
}
#warp {
  max-width: 1000px;
  margin: 0 auto;
}

.title {
  // height: 44px;
  padding: 10px 0;
  background: #3278ea;
  font-size: 18px;
  font-weight: 500;
  text-align: center;
  color: #ffffff;
}
.margin-16 {
  margin: 16px 0;
}

.margin-10 {
  margin: 10px 0;
}

.tips {
  padding: 1px 16px 16px 16px;
  // width: 670px;
  // height: 22px;
  font-size: 12px;
  font-weight: 400;
  text-align: left;
  color: #7a7a7a;
  line-height: 22px;
  background: #ffffff;
}
/deep/ .van-cell-group__title {
  padding: 20px 16px;
  font-size: 24px;
  font-weight: 500;
  text-align: left;
  color: #262626;
  background: #ffffff;
}
/deep/ .van-cell {
  padding: 10px 16px;
  color: #7a7a7a;
  font-size: 16px;
}

.audit-result {
  color: #262626;
}
.assist-pass-result {
  color: #4b9efe;
}
.assist-not-result {
  color: #fa5151;
}

.submit-btn {
  margin: 20px 0;
}

.label {
  height: 30px;
  text-align: middle;
  line-height: 30px;
  font-size: 15px;
  font-weight: 400;
}
</style>