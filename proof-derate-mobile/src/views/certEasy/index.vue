<template>
  <!-- 移动端业务员列表页 -->
  <div class="content">
    <div id="warp">
      <van-loading v-show="loading" type="spinner" color="#1989fa" size="32px">加载中...</van-loading>
      <!-- <h1>粤证易跳转首页 </h1>
      <div>跳转url：{{currUrl}}</div>
      <div>错误信息：{{err}}</div>-->
    </div>
  </div>
</template>
<script>
import { Toast } from "vant";
import { checkToken } from "@/utils/auth";
import { getAssistEasyAccount, getUserInfo } from "@/api/assist";
export default {
  data() {
    return {
      loading: true,
      loginType: "",
      token: "",
      code: "",
      status: "",
      assistSerialNumber: "",
      currUrl: "",
      err: ""
    };
  },
  created() {
    // 旧鉴权
    // ip:端口/assist-tool/certEasy/index/1642736988762dbf5a/shanwei?token=eyJ0eXAiOi
    // 新鉴权
    // ip:端口/assist-tool/certEasy/index/1642736988762dbf5a/weixin?code=u_C-rr7-LeOZ
    sessionStorage.clear();
    this.token = this.$route.query["token"];
    this.status = this.$route.query["status"];
    this.code = this.$route.query["code"];
    this.loginType = this.$route.params["loginType"];
    this.assistSerialNumber = this.$route.params["assistSerialNumber"];
    this.currUrl = this.$route.fullPath;

    if (
      this.loginType &&
      this.loginType === "shanwei" &&
      this.token &&
      this.assistSerialNumber
    ) {
      this.initCheckToken();
    } else if (this.loginType && this.loginType === "weixin" && this.code) {
      this.initGetUserInfo();
    } else {
      this.loading = false;
      this.$message({
        message: "无效的访问",
        center: true,
        type: "error",
        offset: 300,
        duration: 3000
      });
    }
  },
  methods: {
    async initCheckToken() {
      try {
        await checkToken(this.token, this.assistSerialNumber).then(res => {
          console.log("粤证易鉴权成功", res);

          getAssistEasyAccount({ token: this.token })
            .then(res => {
              sessionStorage.setItem('jwtToken', res.data.data.jwtToken);
              this.$router.replace({
                name: "certEasyAssist",
                query: {
                  user_id: res.data.data.userId,
                  assist_serial_number: this.assistSerialNumber
                }
              });
            })
            .catch(err => {
              console.log("getAssistEasyAccount error", err);
              Toast(err.data.meta.message || "");
            });
        });
      } catch (err) {
        this.loading = false;
        this.err = JSON.stringify(err.data);
        Toast(err.data.meta.message || "");
        console.log("==获取token失败==", err);
      }
    },
    async initGetUserInfo() {
      this.loading = false;
      await getUserInfo({ code: this.code })
        .then(res => {
          sessionStorage.setItem('jwtToken', res.data.data.jwtToken);
          this.$router.replace({
            name: "certEasyAssist",
            query: {
              user_id: res.data.data.userId,
              assist_serial_number: this.assistSerialNumber
            }
          });
        })
        .catch(err => {
          console.log("getUserInfo error", err);
          this.err = JSON.stringify(res.data);
          Toast(err.data.meta.message || "");
        });
    }
  }
};
</script>
<style lang="less" scoped>
.content {
  // background: #f9f9f9;
  height: 100%;
}
#warp {
  max-width: 1000px;
  text-align: center;
  line-height: 80vh;
}
</style>