<template>
  <!-- 协查首页 -->
  <div class="content">
    <!-- <van-row class="title">协查处理</van-row> -->
    <div id="warp">
      <van-tabs v-model="tabIndex" @click="onClick">
        <van-tab title="协查信息" :class="{'form-web':fullWidth > 750,'form':fullWidth < 750}">
          <detail :dataInfo="detailInfo" />
          <!-- <van-row class="margin-10">
            <van-cell-group :border="false" title="审核信息">
              <van-form ref="formData">
                <template v-if="fullWidth < 750">
                  <van-field
                    v-model="assistResultLabel"
                    type="text"
                    placeholder="请选择"
                    label-width="100%"
                    right-icon="arrow-down"
                    @click="errorReasonChange"
                    required
                    readonly
                  >
                    <template #label>
                      <div class="label">协查结果</div>
                    </template>
                  </van-field>
                  <van-popup
                    v-model="showPicker"
                    round
                    position="bottom"
                    :lock-scroll="false"
                    get-container="warp"
                    :style="{'maxWidth':'1000px'}"
                  >
                    <van-picker
                      show-toolbar
                      :columns="errorReasonData"
                      :default-index="0"
                      @cancel="showPicker = false"
                      @confirm="onConfirm"
                    />
                  </van-popup>
                </template>
                <template v-else>
                  <van-field
                    v-model="assistResultLabel"
                    type="text"
                    label="协查结果"
                    placeholder="请选择"
                    label-width="100%"
                    @click="errorReasonChange"
                    required
                    readonly
                  >
                    <template #input>
                      <el-select
                        v-model="formData.assist_result"
                        placeholder="请选择"
                        style="width:300px"
                      >
                        <el-option
                          v-for="item in errorReason"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                        ></el-option>
                      </el-select>
                    </template>
                  </van-field>
                </template>

                <van-field
                  v-model="formData.assist_opinion"
                  placeholder="请填写"
                  :rules="rules.assist_opinion"
                  label-width="100%"
                  required
                  center
                >
                  <template #label>
                    <div class="label">协查意见</div>
                  </template>
                </van-field>
                <van-field
                  v-model="formData.assist_name"
                  placeholder="请填写"
                  label-width="100%"
                  center
                  disabled
                >
                  <template #label>
                    <div class="label">协查人</div>
                  </template>
                </van-field>
                <van-field
                  v-model="formData.assist_deparment"
                  placeholder="请填写"
                  label-width="100%"
                  center
                  disabled
                >
                  <template #label>
                    <div class="label">协查部门</div>
                  </template>
                </van-field>
              </van-form>
            </van-cell-group>
          </van-row>
          <van-row type="flex" justify="center">
            <van-col :span="fullWidth > 750 ? 16 : 22" class="submit-btn">
              <van-button type="info" block @click="onSubmit">提交</van-button>
            </van-col>
          </van-row>-->
        </van-tab>
        <van-tab title="协查历史">
          <Item :dataInfo="itemInfo" @click="toDetail" />
        </van-tab>
      </van-tabs>
    </div>
  </div>
</template>
<script>
import Detail from "@/views/components/Detail";
import Item from "@/views/components/Item";
import {
  getAssistDetailForGeneralAccept,
  getAssistHistoryForGeneralAccept,
  postAssistAudit
} from "@/api/assist";
import { id, tyshxydm, tempTyshxydm, zzjgdm, mobile } from "@/utils/validType";
import { Dialog, Toast } from "vant";
export default {
  components: { Detail, Item },
  data() {
    return {
      assistSerialNumber: "",
      fullWidth: document.documentElement.clientWidth,
      activeName: "second",
      tabIndex: 0,
      detailInfo: [
        // {
        //   title: "汕尾职工住房申请",
        //   data: [
        //     { title: "协查发起人", value: "李小风" },
        //     { title: "协查发起人联系方式", value: "138****0000" },
        //     { title: "协查发起人部门", value: "工商部门" },
        //     { title: "协查发起时间", value: "2022-01-28" },
        //     { title: "办事人姓名", value: "李四" },
        //     { title: "是否享受住房补贴", value: "是" }
        //   ],
        //   tips: [
        //     "1.这是表单对应说明文字这是表单对应说明文字这是表单说明",
        //     "2.这是表单对应说d明文字这是表单对应说明文字这是表单说明"
        //   ]
        // },
        // {
        //   title: "汕尾职工住房申请",
        //   data: [
        //     { title: "审核结果", value: "待协查", status: "wait" },
        //     { title: "协查结果", value: "已通过", status: "pass" },
        //     { title: "协查结果", value: "未通过", status: "not" },
        //     { title: "审核结果", value: "申请符合条件，同意" },
        //     { title: "协查人", value: "张三丰" },
        //     { title: "协查结果", value: "协查部门" }
        //   ]
        // }
      ],
      itemInfo: [
        //   {
        //     title: "汕尾职工住房申请",
        //     data: [
        //       {
        //         id: 1,
        //         title: "审核结果",
        //         value: "待协查",
        //         status: "wait",
        //         description: "张三丰 国土局 2021-06-18 10:30"
        //       },
        //       {
        //         id: 2,
        //         title: "协查结果",
        //         value: "未通过",
        //         status: "not",
        //         subTitle: "一些次要展示的标题，也可以不要",
        //         description: "2021-06-18 10:30"
        //       }
        //     ]
        //   }
      ],
      assistResultLabel: "",
      formData: {
        assist_result: "", // 协查结果
        assist_opinion: "", // 协查意见
        assist_name: "", // 协查人
        assist_deparment: "" // 协查部门
      },
      rules: {
        assist_result: [{ required: true }],
        assist_opinion: [{ required: true }]
      },
      validType: {}, //正则校验
      showPicker: false,
      errorReason: [
        { value: "SUCCESS", label: "符合" },
        { value: "FAIL", label: "不符合" }
      ],
      auditDict: [
        { value: "WAIT", label: "待协查" },
        { value: "SUCCESS", label: "符合" },
        { value: "FAIL", label: "不符合" }
      ]
    };
  },
    beforeRouteEnter(to, from, next) {
    next(vm => {
      // 通过 `vm` 访问组件实例
      if (from.name === "generalAcceptResult") {
        vm.getAssistDetailForGeneralAccept({
          assist_serial_number: vm.assistSerialNumber
        });
      }
    });
    // 结果页面返回刷新数据
  },
  created() {
    this.validType = { id, tyshxydm, tempTyshxydm, zzjgdm, mobile };

    let assistSerialNumber = this.$route.query["assist_serial_number"];
    this.assistSerialNumber = assistSerialNumber;
    // 获取协查详情
    this.getAssistDetailForGeneralAccept({
      assist_serial_number: assistSerialNumber
    });
  },
  mounted() {
    window.addEventListener("resize", this.handleResize);
  },
  computed: {
    errorReasonData: function() {
      let errorReasonArr = [];
      this.errorReason.forEach(i => {
        errorReasonArr.push(i.label);
      });
      return errorReasonArr;
    }
  },
  watch: {
    assistResultLabel: function() {
      let chooseReason = this.errorReason.find(item => {
        return item.label == this.assistResultLabel;
      });
      // this.formData.assist_result = chooseReason.value;
      // this.licenseRequired = chooseReason.value == "ERROR" ? true : false;
      // this.rules.license_id_code[0].required = this.licenseRequired;
      // this.$refs["formData"].resetValidation();
    }
  },
  methods: {
    handleResize(event) {
      this.fullWidth = document.documentElement.clientWidth;
    },
    toDetail(info) {
      console.log("toDetail", info);
      this.$router.push({
        name: "generalAcceptDetail",
        query: {
          assistSerialNumber: info.assistSerialNumber
        }
      });
    },
    onClick() {
      if (this.tabIndex === 1) {
        this.getAssistList();
      }
    },
    licenseChange(e) {
      this.license = e;
      this.formData.license_item_code = e.value;
      this.formData.license_name = e.label;
      this.licenseItemLabel = e.label;
      this.showLicensePicker = false;
    },
    onConfirm(value) {
      this.assistResultLabel = value;
      this.formData.assist_result = this.errorReason.find(item => {
        return item.label == this.assistResultLabel;
      }).value;
      this.showPicker = false;
    },
    onSubmit() {
      if (this.formData.assist_result === "") {
        // Dialog.alert({
        //   message: "请选择协查结果"
        // });
        Toast("请选择协查结果");
      } else if (this.formData.assist_opinion === "") {
        // Dialog.alert({
        //   message: "请填写核查意见"
        // });
        Toast("请填写核查意见");
      } else {
        Dialog.confirm({
          title: "请确认是否提交该审核意见？",
          confirmButtonColor: "#1989fa"
        }).then(() => {
          this.apply();
        });
      }
    },

    /**
     * 获取协查结果
     */
    getAssistDetailForGeneralAccept(params) {
      getAssistDetailForGeneralAccept(params)
        .then(res => {
          console.log("==获取协查详情成功==", res);

          let data = res.data.data;
          if (res.data.data) {
            let assistDataInfo = data.assist_data_list[0];
            let assistData = [];
            if (assistDataInfo["item_list"] != null && assistDataInfo["item_list"]) {
              assistData = assistDataInfo["item_list"].map(i => {
                return { title: i.key, value: i.value };
              });
            }
            this.detailInfo = [
              {
                title: assistDataInfo.title || "--",
                data: [
                  {
                    title: "协查发起人",
                    value: data.from_assist_user_name || "--"
                  },
                  {
                    title: "协查发起人联系方式",
                    value: data.from_assist_contain || "--"
                  },
                  {
                    title: "协查发起人部门",
                    value: data.from_assist_org_name || "--"
                  },
                  { title: "协查发起时间", value: data.creation_time || "--" },
                  ...assistData
                ],
                tips: [assistDataInfo.bottom]
              },
              {
                title: "审核信息",
                data: [
                  {
                    title: "审核结果",
                    value: this.auditDict.find(
                      i => i.value === data.audit_result
                    ).label,
                    status: data.audit_result.toLowerCase()
                  },
                  { title: "协查意见", value: data.audit_suggestion || "--" },
                  { title: "协查人", value: data.to_user_name || "--" },
                  { title: "协查部门", value: data.to_assist_org_name || "--" }
                ]
              }
            ];
            this.formData.assist_name = data.to_user_name || "--";
            this.formData.assist_deparment = data.to_assist_org_name;
          } else {
            this.detailInfo = [];
          }
        })
        .catch(err => {
          // this.$message({
          //   message: err.data.meta.message,
          //   center: true,
          //   type: "error",
          //   offset: 300,
          //   duration: 3000
          // });
          Toast(err.data.meta.message || "");
          console.log("==获取协查详情失败==", err);
        });
    },
    getAssistList() {
      getAssistHistoryForGeneralAccept({
        assist_serial_number: this.assistSerialNumber
      })
        .then(res => {
          let data = res.data.data;
          console.log("==获取协查历史成功==", data);
          if (data.length > 0) {
            let itemData = data.map(i => {
              let temp = {
                id: i.id,
                assistSerialNumber: i.assist_serial_number,
                title: i.audit_suggestion || "--",
                value: this.auditDict.find(j => j.value === i.audit_result)
                  .label,
                status: i.audit_result.toLowerCase(),
                description:
                  (i.to_user_name || "--") +
                  " " +
                  i.to_assist_org_name +
                  " " +
                  (i.audit_time || "--")
              };
              return temp;
            });
            this.itemInfo = [
              {
                title: "",
                data: itemData
              }
            ];
          } else {
            this.itemInfo = [];
          }
        })
        .catch(err => {
          console.log("==获取协查历史失败==", err);
        });
    },
    /**
     * 原因类型选择
     */
    errorReasonChange(event) {
      this.showPicker = true;
    },

    /**
     * 调用纠错上报接口
     */
    apply() {
      Toast.loading({
        duration: 0,
        message: "正在提交...",
        forbidClick: true
      });
      console.log("==纠错上报请求参数==", this.formData);

      postAssistAudit(this.formData)
        .then(res => {
          console.log("==提交审核成功==", res);
          this.$router.push({
            name: "generalAcceptResult",
            query: {
              type: "success",
              message: "提交成功",
              description: "描述提示信息"
            }
          });
          return;
        })
        .catch(err => {
          console.log("==提交审核失败==", err);
          this.buttonLoading = false;
          this.$router.push({
            name: "generalAcceptResult",
            query: {
              type: "fail",
              message: "提交失败",
              description: "描述提示信息"
            }
          });
        });
    }
  }
};
</script>
<style lang="less" scoped>
.content {
  background: #f9f9f9;
  height: 100%;
}
#warp {
  max-width: 1000px;
  margin: 0 auto;
  height: auto;
}
.title {
  padding: 10px 0;
  background: #3278ea;
  font-size: 18px;
  font-weight: 500;
  text-align: center;
  color: #ffffff;
}

/deep/ .van-tab {
  font-size: 16px;
}
/deep/ .van-tabs__line {
  width: 23%;
  background: #3278ea;
}
/deep/ .van-tab--active {
  color: #3278ea;
}

.margin-16 {
  margin: 16px 0;
}

.margin-10 {
  margin: 10px 0;
}

.tips {
  padding: 1px 16px 16px 16px;
  font-size: 12px;
  font-weight: 400;
  text-align: left;
  color: #7a7a7a;
  line-height: 22px;
  background: #ffffff;
}
/deep/ .form .van-cell-group__title,
.form-web .van-cell-group__title {
  padding: 20px 16px;
  font-size: 24px;
  font-weight: 500;
  text-align: left;
  color: #262626;
  background: #ffffff;
}
/deep/ .form .van-cell,
.form-web .van-cell {
  padding: 10px 16px;
  color: #7a7a7a;
  font-size: 16px;
}

.submit-btn {
  margin: 20px 0;
}

.label {
  height: 30px;
  // background: pink;
  text-align: middle;
  line-height: 30px;
  font-size: 15px;
  font-weight: 400;
}
/deep/ .form .van-form .van-cell,
.form-web .van-form .van-cell {
  display: block;
  // line-height: 52px;
  padding: 16px;
  background-color: #ffffff;
}

/deep/ .form-web .van-field__label {
  padding-left: 50px;
  font-size: 18px;
  background-color: #ffffff;
}

/deep/ .form-web .van-field__control {
  padding: 16px 0 0 50px;
  max-width: 310px;
  font-size: 18px;
  background-color: #ffffff;
}

/deep/ .van-button--normal {
  font-size: 16px;
  font-weight: 600;
}
/deep/ .form .van-cell--required::before {
  padding: 5px 0px;
}
/deep/ .form-web .van-cell--required::before {
  padding-left: 50px;
}

// ::v-deep .van-overlay {
//   position: absolute;
// }
// ::v-deep .van-popup {
//   position: absolute;
// }
</style>