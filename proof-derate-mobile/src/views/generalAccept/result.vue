<template>
  <!-- 协查结果页 -->
  <div class="content">
    <!-- <van-row class="title">提交结果</van-row> -->
    <div id="warp">
      <van-row>
        <van-col span="24" :style="style">
          <result
            :background="fullWidth > 750 ? '#ffffff': '#f9f9f9'"
            :infoBtn="false"
            :type="type"
            @info-click="infoEvent"
            :message="message"
            :description="description"
          />
        </van-col>
      </van-row>
    </div>
  </div>
</template>
<script>
import Result from "@/views/components/Result";
export default {
  components: { Result },
  data() {
    return {
      style: {
        'height': document.documentElement.clientWidth > 750 ? '300px' : 'auto',
        'background': document.documentElement.clientWidth > 750 ? '#ffffff' : '#f9f9f9'
      },
      fullWidth: document.documentElement.clientWidth,
      type: "success",
      message: "", // 提交成功
      description: "" // 描述提示
    };
  },
  created() {
    console.log(this.$route.query);
    this.type = this.$route.query["type"];
    this.message = this.$route.query["message"];
    this.description = this.$route.query["description"];
  },
  mounted() {
    window.addEventListener("resize", this.handleResize);
  },
  methods: {
    handleResize(event) {
      this.fullWidth = document.documentElement.clientWidth;
    },
    infoEvent() {
      console.log("infoEvent");
    },
    defaultEvent() {
      console.log("defaultEvent");
    }
  }
};
</script>
<style lang="less" scoped>
.content {
  background: #f9f9f9;
  height: 100vh;
}
#warp {
  max-width: 1000px;
  margin: 0 auto;
}

.result{
  height:300px;
  background: 'pink';
}

.title {
  // height: 44px;
  padding: 10px 0;
  background: #3278ea;
  font-size: 18px;
  font-weight: 500;
  text-align: center;
  color: #ffffff;
}
.margin-16 {
  margin: 16px 0;
}

.margin-10 {
  margin: 10px 0;
}

.tips {
  padding: 1px 16px 16px 16px;
  // width: 670px;
  // height: 22px;
  font-size: 12px;
  font-weight: 400;
  text-align: left;
  color: #7a7a7a;
  line-height: 22px;
  background: #ffffff;
}
/deep/ .van-cell-group__title {
  padding: 20px 16px;
  font-size: 24px;
  font-weight: 500;
  text-align: left;
  color: #262626;
  background: #ffffff;
}
/deep/ .van-cell {
  padding: 10px 16px;
  color: #7a7a7a;
  font-size: 16px;
}

.audit-result {
  color: #262626;
}
.assist-pass-result {
  color: #4b9efe;
}
.assist-not-result {
  color: #fa5151;
}

.submit-btn {
  margin: 20px 0;
}

.label {
  height: 30px;
  text-align: middle;
  line-height: 30px;
  font-size: 15px;
  font-weight: 400;
}
</style>