<template>
  <!-- 移动端业务员列表页 -->
  <div class="content">
    <div id="warp">
      <van-loading v-show="loading" type="spinner" color="#1989fa" size="32px">加载中...</van-loading>
    </div>
  </div>
</template>
<script>
import { setToken } from '@/utils/auth'
import { Toast } from 'vant'
export default {
  data() {
    return {
      loading: true,
      timestamp: '',
      sign: '',
      appKey: '',
      requestId: '',
      assistSerialNumber: ''
    }
  },
  created() {
    // 综受中转页
    sessionStorage.clear()
    this.timestamp = this.$route.query['x-license-timestamp']
    this.sign = encodeURI(this.$route.query['x-license-sign'])
    this.appKey = this.$route.query['x-license-appkey']
    this.requestId = this.$route.query['x-license-requestid']
    this.assistSerialNumber = this.$route.query['assist_serial_number']
    if (this.timestamp && this.sign && this.appKey && this.requestId) {
      this.initData()
    } else {
      this.loading = false
      this.$message({
        message: '无效的访问',
        center: true,
        type: 'error',
        offset: 300,
        duration: 3000
      })
    }
  },
  methods: {
    async initData() {
      console.log('综受鉴权初始')
      try {
        await setToken(this.appKey, this.timestamp, this.sign, this.requestId).then(res => {
          console.log('鉴权成功', res)
          this.$router.replace({
            name: 'generalAcceptAssist',
            query: {
              assist_serial_number: this.assistSerialNumber
            }
          })
        })
      } catch (err) {
        // this.loading = false;
        //      this.$message({
        //     message: err.data.meta.message,
        //     center: true,
        //     type: "error",
        //     offset: 300,
        //     duration: 300000,
        //   });
        Toast(err.data.meta.message || '')
        console.log('==获取token失败==', err)
      }
    }
  }
}
</script>
<style lang="less" scoped>
.content {
  // background: #f9f9f9;
  height: 100%;
}
#warp {
  max-width: 1000px;
  text-align: center;
  line-height: 80vh;
}
</style>