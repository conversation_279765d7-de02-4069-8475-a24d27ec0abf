import router from './router'
import { asyncRoutes } from '@/router'

/* import router, { createRouter } from '@/router'
import { constantRoutes } from '@/router' */

import store from './store'
import { Message } from 'element-ui'
import NProgress from 'nprogress' // progress bar
import 'nprogress/nprogress.css' // progress bar style
import { TokenKey, getToken } from '@/utils/auth' // get token from cookie
import { collectLastChildrenRoutes } from '@/utils' // get token from cookie
import getPageTitle from '@/utils/get-page-title'
import {
  filterAsyncRouter
} from '@/store/modules/permission'

NProgress.configure({ showSpinner: false }) // NProgress Configuration

// const whiteList = ['/login'] // no redirect whitelist
const whiteList = [
  '/login',
  '/white/login',
  '/white/register',
  '/white/apply',
  '/white/check'
] // no redirect whitelist

// 将其他项目的菜单全路径找出来
function processRoutes(routes, parentPath = '/') {
  routes.forEach((route) => {
    // 累积当前路由的完整路径
    let fullPath = ''
    // if (!route.isInternalLink) {
    /* if (route.isOutherPath) {
      const path = route.path.split('/').slice(1).join('/')
      fullPath = `${parentPath}${path}`
      console.log(route.path, path, fullPath, 9)
      // 如果当前路由是叶子节点（没有children），则更新meta
      route.meta.otherPath = fullPath
      // route.meta.otherProjectNmae = path[1]
    } else {
      fullPath = `${parentPath}${
        route.path.startsWith('/') ? route.path.slice(1) : route.path
      }`
    } */
    parentPath = parentPath === '/' ? '' : parentPath
    // console.log('parentPath', parentPath)
    fullPath = `${parentPath}${
      route.path.startsWith('/') ? route.path.slice(1) : route.path
    }`
    if (route.children) {
      processRoutes(route.children, fullPath + '/')
    }

    /* if (!route.children) {
      route.meta.otherPath = fullPath
    } else {
      // 递归处理子路由
      processRoutes(route.children, fullPath + '/')
    } */
  })
}

router.beforeEach(async(to, from, next) => {
  // start progress bar
  NProgress.start()
  // set page title
  document.title = getPageTitle(to.meta.title)

  // determine whether the user has logged in
  const hasToken = getToken()
  // 获取项目基础配置信息。暂先隐藏
  const isNewSystem = store.getters.isNewSystem
  if (!isNewSystem) {
    store.dispatch('settings/changeSetting')
  }
  // const hasToken = 'ceshi'
  if (hasToken) {
    if (to.path === '/login') {
      // if is logged in, redirect to the home page
      next({ path: '/' })
      NProgress.done()
    } else if (to.path === '/white/passwd') {
      next()
      NProgress.done()
    } else {
      // const hasGetUserInfo = store.getters.name
      const hasGetPermission = store.getters.permission
      if (hasGetPermission) {
        const currentRoutes = store.state.permission.currentRoutes
        // 如果是外部项目的菜单则自动寻找下一个不是外部项目的菜单
        if (
          to.meta.projectName &&
          to.meta.projectName !== process.env.VUE_APP_BASE_PROJECT_NAME &&
          Object.keys(to.meta).length !== 0
        ) {
          if (currentRoutes.children !== undefined) {
            const allRoutes = collectLastChildrenRoutes(currentRoutes.children)
            const newRoutes = allRoutes.filter(
              (route) => !route.isOutherPath && !route.isCatalogue
            )
            // 如果当前一级目录下所有的三级菜单都是外部项目
            if (newRoutes.length < 1) {
              const threeRoutes = allRoutes.filter(
                (route) => !route.isCatalogue
              )
              const projectName =
                threeRoutes.length > 0
                  ? threeRoutes[0].meta.projectName
                  : allRoutes[0].meta.projectName
              const path =
                threeRoutes.length > 0
                  ? threeRoutes[0].path
                  : allRoutes[0].path
              const url =
                window.location.origin + '/' + projectName + '/white/check'
              const par =
                '?tokenName=' +
                TokenKey +
                '&path=' +
                path +
                '&type=inOutherLink'
              // console.log('window.open', url + par)
              window.open(url + par, '_self')
            } else {
              next(newRoutes[0].meta.fullPath)
            }
          }
        } else {
          // console.log('原逻辑走')
          next()
        }
        /* if (to.matched.length === 0) {
          next('/') // 判断此跳转路由的来源路由是否存在，存在的情况跳转到来源路由，否则跳转到/页面
        } else {
        } */
      } else {
        try {
          // get user permission
          const menuTreeList =
            (await store.dispatch('user/getCurrentUserInfo')) || []
          // const sdata = JSON.parse(JSON.stringify(menuTreeList))
          const rdata = JSON.parse(JSON.stringify(menuTreeList))

          // store.dispatch('permission/generatelocalitRoutes', rdata).then((res) => {
          // const rewriteRoutes = res
          const lastLevelRoutes = collectLastChildrenRoutes(asyncRoutes)
          const rewriteRoutes = filterAsyncRouter(
            rdata,
            false,
            true,
            asyncRoutes,
            lastLevelRoutes
          )
          processRoutes(rewriteRoutes)
          // 判断所有路由第一级是否自带有'/，如无则自动加上
          rewriteRoutes.forEach((e) => {
            e.path = e.path.startsWith('/') ? e.path : '/' + e.path
          })
          // const rewriteRoutes = rdata
          // rewriteRoutes.push({ path: '*', redirect: '/', hidden: true })
          // console.log('过滤后的动态router', rewriteRoutes)
          rewriteRoutes.push({ path: '*', redirect: '/404', hidden: true })
          store
            .dispatch('permission/GenerateRoutes', rewriteRoutes)
            .then(() => {
              // 存储路由
              router.addRoutes(rewriteRoutes) // 动态添加可访问路由表
              next({ ...to, replace: true })
            })
          // })
          /* await store.dispatch('permission/generateRoutes', menuTreeList).then((res) => {
                          router.addRoutes(res)
                          next({ ...to, replace: true })
                        }) */
        } catch (error) {
          // remove token and go to login page to re-login
          await store.dispatch('user/resetToken')
          Message.error(error || 'Has Error')
          next(`/white/login?redirect=${to.fullPath}`)
          NProgress.done()
        }
      }
    }
  } else {
    /* has no token*/
    if (whiteList.indexOf(to.path) !== -1) {
      // in the free login whitelist, go directly
      next()
    } else {
      // other pages that do not have permission to access are redirected to the login page.
      // next(`/login?redirect=${to.path}`)
      next(`/white/login?redirect=${to.fullPath}`)

      NProgress.done()
    }
  }
})

router.afterEach(() => {
  // finish progress bar
  NProgress.done()
})
