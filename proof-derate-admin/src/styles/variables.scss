// sidebar
$menuText:#bfcbd9;
$menuActiveText:#409EFF;
$subMenuActiveText:block;

$menuBg:#304156;
$menuHover:#263445;

$subMenuBg:#1f2d3d;
$subMenuHover:#001528;

$sideBarWidth: 210px;
$topBarHeight: 56px;
$contentHeight: calc(100vh - 56px);

/*
  蓝色主体
*/
$BcolorPrimary: #2697FF; // 主题色

// 顶部一级导航
$BtopmenuText:rgba(255,255,255,1);  //顶部导航字体颜色
$BtopmenuLogText:#fff; //logo字体颜色
$BtopmenuBg:#2697FF;  //顶部导航背景色
$BmenuActiveText:#2697FF;
$BmenuBtnActiveBg:#fff;
$BmenuBtnHover:rgba(255,255,255,0.2);

// 侧边二级导航
$BsubMenuText:#9199A5; //字体颜色
$BsubMenuBg:#fff; //侧边导航背景色
$BsubMenuBtnBg:#fff; //三级导航背景色
$BsubMenuActiveTwoText:#333333; //二级导航标题色
$BsubMenuHoverBtn:#fff;  //
$BsubMenuActiveBtn:#fff;  //
$BsubMenuHoverThrText:#2697FF;//三级导航标题色
$BsubMenuActiveThrText:#2697FF;
$BsubMenuActiveThrBg:#f0f6ff;

// 按钮
$BbtnText:#fff;  //按钮字体颜色
//$BbtnBg:#4381E6;  //按钮背景色
$BbtnHover:#0078E5;  //按钮hover颜色

// 搜索按钮
$BseachBtnText:#2697FF; //字体颜色
$BseachBtnBg:#e0f0ff; //背景色
$BseachBtnBorder:#99ceff; //边框色
$BseachBtnHoverBg:#c2e2ff; //
$BseachBtnHoverBorder:#80c2ff; //

// 重置按钮
$BseachBtnText:#9199A5; //字体颜色
$BseachBtnBg:#fff; //背景色
$BseachBtnBorder:#e5e5e5; //背景色
$BseachBtnHoverText:#2697FF; //字体颜色
$BseachBtnHoverBg:#e0f0ff; //背景色
$BseachBtnHoverBorder:#99ceff; //背景色





/* $colorDanger: #F84040; // 危险色
$colorSuccess: #29B551; // 成功色
$colorWarning: #E6A23C; // 警告色
$colorStrong: #000000;
$colorHeavy: #303133; // 用于一级标题
$colorNormal: #606266;  // 用于二级状态
$colorLight: #909399; // 用于辅助字、备注字 */



// the :export directive is the magic sauce for webpack
:export {
  menuText: $menuText;
  menuActiveText: $menuActiveText;
  subMenuActiveText: $subMenuActiveText;
  menuBg: $menuBg;
  menuHover: $menuHover;
  subMenuBg: $subMenuBg;
  subMenuHover: $subMenuHover;
  sideBarWidth: $sideBarWidth;

  BcolorPrimary:$BcolorPrimary;
  BtopmenuText:$BtopmenuText;
  BtopmenuLogText:$BtopmenuLogText;
  BtopmenuBg:$BtopmenuBg;
  BmenuActiveText:$BmenuActiveText;
  BmenuBtnActiveBg:$BmenuBtnActiveBg;
  BmenuBtnHover:$BmenuBtnHover;
  BsubMenuText:$BsubMenuText;
  BsubMenuBg:$BsubMenuBg;
  BsubMenuBtnBg:$BsubMenuBtnBg;
  BsubMenuActiveTwoText:$BsubMenuActiveTwoText;
  BsubMenuHoverBtn:$BsubMenuHoverBtn;
  BsubMenuActiveBtn:$BsubMenuActiveBtn;
  BsubMenuHoverThrText:$BsubMenuHoverThrText;
  BsubMenuActiveThrText:$BsubMenuActiveThrText;
  BsubMenuActiveThrBg:$BsubMenuActiveThrBg;
  BbtnText:$BbtnText;
  BbtnHover:$BbtnHover;
  BseachBtnText:$BseachBtnText;
  BseachBtnBg:$BseachBtnBg;
  BseachBtnBorder:$BseachBtnBorder;
  BseachBtnHoverBg:$BseachBtnHoverBg;
  BseachBtnHoverBorder:$BseachBtnHoverBorder;
  BseachBtnText:$BseachBtnText;
  BseachBtnBg:$BseachBtnBg;
  BseachBtnBorder:$BseachBtnBorder;
  BseachBtnHoverText:$BseachBtnHoverText;
  BseachBtnHoverBg:$BseachBtnHoverBg;
  BseachBtnHoverBorder:$BseachBtnHoverBorder;
}
