@import './variables';
.top-nav {
  // margin-left: $sideBarWidth;
  width: 100%;
  background-color: $BtopmenuBg;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1001;
  overflow: hidden;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  .log {
    padding: 0 20px 0 7px;
    line-height: 56px;
    font-size: 24px;
    font-weight: bold;
    color: $BtopmenuLogText;
    float: left;
  }
  .el-menu {
    // flex: 1;
    // float: left;
    /* display: flex;
      justify-content: flex-start;
      align-items: center; */
    border: none !important;
    background-color: $BtopmenuBg;
    .nav-item {
      display: inline-block;
      .el-menu-item {
        // width: 90px;
        height: 32px;
        text-align: center !important;
        line-height: 32px !important;
        border-radius: 16px;
        color: $BtopmenuText;
        padding: 0 13px;
        margin: 0 20px;
        background: transparent;
        &:hover {
          background-color: $BmenuBtnHover !important;
          color: #fff;
        }
        &:focus {
          // 一级菜单focus样式,设置了,点击外链会有focus样式
          // background-color: $BmenuBtnActiveBg !important;
          // color: $subMenuActiveText !important;
          color: #fff;
        }
        &.is-active {
          background-color: $BmenuBtnActiveBg !important;
        }
      }
    }
  }
  .right-menu {
    float: right;
    height: 100%;
    &:focus {
      outline: none;
    }
    .right-menu-item {
      display: inline-block;
      padding: 0 8px;
      height: 100%;
      font-size: 18px;
      color: #5a5e66;
      vertical-align: text-bottom;
      &.hover-effect {
        cursor: pointer;
        transition: background 0.3s;
        &:hover {
          background: rgba(0, 0, 0, 0.025);
        }
      }
    }
    .avatar-container {
      margin-right: 30px;
      .avatar-wrapper {
        margin-top: 5px;
        position: relative;
        .user-avatar {
          cursor: pointer;
          width: 40px;
          height: 40px;
          border-radius: 10px;
        }
        .el-icon-caret-bottom {
          cursor: pointer;
          position: absolute;
          right: -20px;
          top: 25px;
          font-size: 12px;
        }
      }
    }
  }

  .float-right {
    position: absolute;
    right: 0;
    display: flex;
    align-items: center;
  }
}
.left-app-wrapper {
  .top-nav {
    flex-direction: column;
    .right-menu {
      height: 60px;
      margin-bottom: 70px;
      .avatar-container {
        margin-right: 0px;
      }
    }
  }
  .el-menu {
    .nav-item {
      .el-menu-item {
        width: 100px;
        height: 90px;
        padding: 0 35px;
        // line-height: 90px !important;
        border-radius: 0;
        // color:transparent;
        display: flex;
        align-items: center;
        flex-direction: column;
        justify-content: center;
        &.is-active {
          background-color: #0a54c5 !important;
        }
      }
    }
    // flex: 0;
  }
  .greenTheme .nav-item .el-menu-item {
    &.is-active {
      background-color: #1c8e68 !important;
    }
  }
  .lightBlueTheme .nav-item .el-menu-item {
    &.is-active {
      background-color: #0a54c5 !important;
    }
  }
  .darkBlueTheme .nav-item .el-menu-item {
    &.is-active {
      background-color: #064095 !important;
    }
  }
  .defaultTheme .nav-item .el-menu-item {
    &.is-active {
      background-color: #0a54c5 !important;
    }
  }
}
