// cover some element-ui styles
.el-breadcrumb__inner,
.el-breadcrumb__inner a {
    font-weight: 400 !important;
}

.el-upload {
    input[type="file"] {
        display: none !important;
    }
}

.el-upload__input {
    display: none;
}

.el-dialog {
    transform: none;
    left: 0;
    position: relative;
    margin: 0 auto;
}

// refine element ui upload
.upload-container {
    .el-upload {
        width: 100%;
        .el-upload-dragger {
            width: 100%;
            height: 200px;
        }
    }
}

// dropdown
.el-dropdown-menu {
    a {
        display: block
    }
}

// to fix el-date-picker css style
.el-range-separator {
    box-sizing: content-box;
}

// .el-form-item__label {
//   font-size: 16px;
//   color: #888;
// }
// .el-radio__label{
//   color: #666666;
//   font-size: 16px;
//   font-family: Microsoft YaHei, Microsoft YaHei-Regular;
// }
.el-pagination {
    float: right;
}

.el-table th {
    background: #f8f8f8;
}
.el-table tr{
    height: 47px;
}

/*
  重定义element-ui的样式
*/

// 复选框选中的字体颜色
.el-checkbox__input.is-checked+.el-checkbox__label {
    color: inherit;
}

// 单选框选中字体颜色
.el-radio__input.is-checked+.el-radio__label {
    color: inherit;
}

//单选框禁用颜色
.el-radio__input.is-disabled+span.el-radio__label {
    color: #888888;
}

.el-tree-node__content{
  height: 32px;
}
.el-table__cell{
  padding: 3px 0 !important;
}

.el-button.is-disabled,.el-button.is-disabled:hover,.el-button.is-disabled:focus{
  color: #c0c4cc !important;
}
