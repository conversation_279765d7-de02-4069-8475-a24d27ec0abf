@import 'variables';
#app {
  .main-container {
    // min-height: 100%;
    height: $contentHeight;
    transition: margin-left 0.28s;
    margin-left: $sideBarWidth;
    margin-top: $topBarHeight;
    position: relative;
  }
  .sidebarHide {
    margin-left: 0 !important;
  }
  .sidebar-container {
    transition: width 0.28s;
    width: $sideBarWidth !important;
    background-color: $BsubMenuBg;
    box-shadow: 3px 3px 6px 0px rgba(0, 0, 0, 0.05);
    // height: 100%;
    height: $contentHeight;
    position: fixed;
    font-size: 0px;
    // top: 0;
    top: $topBarHeight;
    bottom: 0;
    left: 0;
    z-index: 1001;
    overflow: hidden;
    // reset element-ui css
    .horizontal-collapse-transition {
      transition: 0s width ease-in-out, 0s padding-left ease-in-out, 0s padding-right ease-in-out;
    }
    .scrollbar-wrapper {
      overflow-x: hidden !important;
    }
    .el-scrollbar__bar.is-vertical {
      right: 0px;
    }
    .el-scrollbar {
      height: 100%;
    }
    &.has-logo {
      .el-scrollbar {
        height: calc(100% - 50px);
      }
    }
    .is-horizontal {
      display: none;
    }
    a {
      display: inline-block;
      width: 100%;
      overflow: hidden;
    }
    .svg-icon {
      margin-right: 10px;
    }
    .el-menu {
      border: none;
      height: 100%;
      width: 100% !important;
    }
    // 侧边导航二级导航 hover
    .submenu-title-noDropdown,
    .el-submenu__title {
      font-weight: bold;
      color: $BsubMenuActiveTwoText !important;
      &:hover {
        background-color: #fff !important;
      }
    }
    .is-active > .el-submenu__title > svg {
      // color: #d3dce6;
    }
    /* .is-active>.el-menu-item {
      background-color: $BsubMenuActiveThrBg !important;
      span{
        font-size: 50px;
      }

    } */
    & .el-submenu .is-active.el-menu-item {
      background-color: $BsubMenuActiveThrBg !important;
    }
    & .nest-menu .el-submenu > .el-submenu__title,
    & .el-submenu .el-menu-item {
      min-width: $sideBarWidth !important;
      background-color: $BsubMenuBg !important;
      &:hover {
        // color: $BsubMenuHoverThrText !important;
        background-color: $BsubMenuHoverBtn !important;
      }
    }
  }
  #sidebar-container-left {
    .greenTheme {
      .router-link-active {
        border-right: 3px solid #1f9e73;
      }
    }
    .darkBlueTheme {
      .router-link-active {
        border-right: 3px solid #0747a6;
      }
    }
    .defaultTheme {
      .router-link-active {
        border-right: 3px solid #3c8ef0;
      }
    }
    .router-link-active {
      border-right: 3px solid #3c8ef0;
    }
    & .nest-menu .el-submenu > .el-submenu__title,
    & .el-submenu .el-menu-item {
      padding-left: 20px !important;
      // min-width: $sideBarWidth !important;
      // background-color: $BsubMenuBg !important;
    }
  }
  .hideSidebar {
    .sidebar-container {
      width: 54px !important;
    }
    #sidebar-container-left {
      width: 54px !important;
    }
    .main-container {
      margin-left: 54px;
    }
    #main-container-left {
      margin-left: 155px;
    }
    .submenu-title-noDropdown {
      padding: 0 !important;
      position: relative;
      .el-tooltip {
        padding: 0 !important;
        .svg-icon {
          margin-left: 20px;
        }
      }
    }
    .el-submenu {
      overflow: hidden;
      & > .el-submenu__title {
        padding: 0 !important;
        .svg-icon {
          margin-left: 20px;
        }
        .el-submenu__icon-arrow {
          display: none;
        }
      }
    }
    .el-menu--collapse {
      .el-submenu {
        & > .el-submenu__title {
          & > span {
            height: 0;
            width: 0;
            overflow: hidden;
            visibility: hidden;
            display: inline-block;
          }
        }
      }
    }
  }
  .el-menu--collapse .el-menu .el-submenu {
    min-width: $sideBarWidth !important;
  }
  // mobile responsive
  .mobile {
    .main-container {
      margin-left: 0px;
    }
    .sidebar-container {
      transition: transform 0.28s;
      width: $sideBarWidth !important;
    }
    &.hideSidebar {
      .sidebar-container {
        pointer-events: none;
        transition-duration: 0.3s;
        transform: translate3d(-$sideBarWidth, 0, 0);
      }
    }
  }
  .withoutAnimation {
    .main-container,
    .sidebar-container {
      transition: none;
    }
  }
  .left-app-wrapper {
    .sidebar-container {
      left: 105px;
      height: calc(100vh - 0px);
      top: 0;
      // width: 185px !important;
      width: 240px !important;
      .el-submenu__title{
        font-weight: 500;
      }
    }
    .main-container {
      // margin-left: 288px;
      margin-left: 345px;
      margin-top: 0px;
    }
  }
}

// when menu collapsed
.el-menu--vertical {
  & > .el-menu {
    .svg-icon {
      margin-right: 16px;
    }
  }
  .nest-menu .el-submenu > .el-submenu__title,
  .el-menu-item {
    &:hover {
      // you can use $subMenuHover
      // background-color: $menuHover !important;
    }
  }
  // the scroll bar appears when the subMenu is too long
  > .el-menu--popup {
    max-height: 100vh;
    overflow-y: auto;
    &::-webkit-scrollbar-track-piece {
      background: #d3dce6;
    }
    &::-webkit-scrollbar {
      width: 6px;
    }
    &::-webkit-scrollbar-thumb {
      background: #99a9bf;
      border-radius: 20px;
    }
  }
}
