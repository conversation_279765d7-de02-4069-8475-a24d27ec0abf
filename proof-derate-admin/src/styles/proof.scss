// : #01a463;
$skinColor: var(--skinColor);
$cellbackColor: var(--skinColor);
$borderColor: var(--borderColor);
//
.el-tabs--border-card > .el-tabs__header .el-tabs__item.is-active {
  border-radius: 10px 10px 0px 0px;
  color: $skinColor;
}
.el-tabs--border-card {
  border-radius: 10px 10px 0px 0px;
  border: 0;
  box-shadow: none;
  // font-size: 18px;
}
.el-tabs--border-card > .el-tabs__header {
  border-radius: 10px 10px 0px 0px;
}
.content-wrapper {
  background: #f2f2f2;
}
.el-tabs--border-card > .el-tabs__header {
  background: #fff !important;
}
.el-card__body,
.el-main {
  // padding: 20px 10px 0 !important;
}
.el-tabs--border-card > .el-tabs__header .el-tabs__item {
  width: 154px;
  text-align: center;
  height: 54px;
  line-height: 54px;
  font-size: 16px;
}
.el-tabs__item :hover {
  background: $skinColor;
}
.el-table th.el-table__cell {
  // background: #f0faf6;
  background: $cellbackColor;
  //   background: #01a4632d;
  color: #333333;
  font-weight: 500;
}
.el-table th.el-table__cell.is-leaf {
  // border-bottom: 1px solid rgba(1, 164, 99, 0.5);
  border-bottom: 1px solid $borderColor;
}
.el-table td.el-table__cell div {
  // color: #333333;
}
.el-button--primary:hover,
.el-button--primary:focus {
  background: $skinColor;
}
.el-button--primary {
  background: $skinColor;
  border-color: $skinColor;
}
.el-button--text {
  color: $skinColor;
}
.el-button--text:focus,
.el-button--text:hover {
  color: $skinColor;
}
.el-checkbox__input.is-checked + .el-checkbox__label {
  color: $skinColor;
}
.el-checkbox__input.is-checked .el-checkbox__inner,
.el-checkbox__input.is-indeterminate .el-checkbox__inner {
  color: $skinColor;
}
.el-checkbox__input.is-checked .el-checkbox__inner {
  background-color: $skinColor;
  border-color: $skinColor;
}
.el-input.is-active .el-input__inner,
.el-input__inner:focus {
  border-color: $skinColor;
  outline: 0;
}
.el-cascader .el-input .el-input__inner:focus,
.el-cascader .el-input.is-focus .el-input__inner {
  border-color: $skinColor;
}
.el-range-editor.is-active,
.el-range-editor.is-active:hover,
.el-select .el-input.is-focus .el-input__inner {
  border-color: $skinColor;
}
.el-select .el-input__inner:focus {
  border-color: $skinColor;
}
.el-select-dropdown__item.selected {
  color: $skinColor;
  font-weight: 700;
}
.el-tabs--border-card > .el-tabs__header .el-tabs__item:not(.is-disabled):hover {
  color: $skinColor;
}
.el-pagination.is-background .el-pager li:not(.disabled).active {
  background-color: $skinColor;
  color: #fff;
}
.el-pagination.is-background .el-pager li:not(.disabled):hover {
  color: $skinColor;
  // color: #fff;
}

.el-card {
  border: 0;
}

.el-radio__input.is-checked .el-radio__inner {
  border-color: $skinColor;
  background: $skinColor;
}
.el-radio__input.is-checked + .el-radio__label {
  color: $skinColor;
}
.el-cascader-node.in-active-path,
.el-cascader-node.is-active,
.el-cascader-node.is-selectable.in-checked-path {
  color: $skinColor;
}
.el-radio__inner:hover {
  border-color: $skinColor;
}
.el-pagination__sizes .el-input .el-input__inner:hover {
  border-color: $skinColor;
}
.el-descriptions__header {
  margin-bottom: 10px;
}
.el-descriptions__title {
  font-size: 16px;
  color: #333333;
  font-weight: 500;
}
.el-descriptions :not(.is-bordered) .el-descriptions-item__cell {
  padding-bottom: 12px;
  min-width: 140px;
  width: 630px;
}
.el-descriptions__title {
  font-size: 14px;
}
.dashed-line {
  border-top: 1px dashed #f2f2f2;
}

.el-transfer-panel__item:hover {
  color: $skinColor;
}
.el-checkbox__input.is-focus .el-checkbox__inner {
  border-color: $skinColor;
}
.el-button--primary.is-disabled,
.el-button--primary.is-disabled:active,
.el-button--primary.is-disabled:focus,
.el-button--primary.is-disabled:hover {
  color: #fff;
  background-color: rgba(1, 164, 99, 0.5);
  border-color: rgba(1, 164, 99, 0.5);
}
.el-button--default:focus,
.el-button--default:hover {
  color: #606266;
  background: #fff;
  border: 1px solid #dcdfe6;
  // color: #FFF;
  // border-color: rgba(1, 164, 99, 0.5);
  // background-color: rgba(1, 164, 99, 0.5);
}

.el-button--primary.is-plain {
  color: $skinColor;
  background: #f0f9eb;
  border-color: $skinColor;
}
.el-button--primary.is-plain:focus,
.el-button--primary.is-plain:hover {
  color: $skinColor;
  background: #f0f9eb;
  border-color: $skinColor;
  // color: #FFF;
  // border-color: rgba(1, 164, 99, 0.5);
  // background-color: rgba(1, 164, 99, 0.5);
}
.el-checkbox__inner:hover {
  border-color: $skinColor;
}
.el-form-item__content .el-table tr {
  line-height: 10px;
}
.colorText {
  color: $borderColor;
}

.content-header > h1 {
  padding: 13px 0 9px;
}
.title-algin-midle {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.el-menu-item.isActive {
  color: $skinColor !important;
  background-color: #e5f6ef !important;
}
.el-menu-item:focus,
.el-menu-item:hover {
  outline: 0;
  background-color: #e5f6ef !important;
}
.el-submenu__title:hover {
  background-color: #e5f6ef !important;
}

.item-add {
  border-left: 1px dashed $borderColor !important;
  border-right: 1px dashed $borderColor !important;
  border-bottom: 1px dashed $borderColor !important;
  color: $borderColor !important;
}

.padding-10 {
  padding: 10px;
}

.el-tabs__active-bar {
  // width: 50px !important;
}
.el-tabs--top .el-tabs__item.is-top {
  padding-right: 20px !important;
  width: 150px;
  text-align: center;
  font-size: 16px;
}
.el-tabs__nav-wrap .is-top {
  padding-right: 20px !important;
  width: 150px;
  text-align: center;
  font-size: 16px;
}
.el-tabs__header {
  margin: 0 0 0px !important;
  padding: 0 0 15px;
  background: #fff;
}
.el-tabs__nav-scroll {
  background: #fff;
}
.el-tabs--top > .el-tabs__content {
  padding: 15px;
  background: #fff;
}
