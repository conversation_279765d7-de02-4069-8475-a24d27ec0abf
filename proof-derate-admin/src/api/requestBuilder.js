import axios from 'axios'
import store from '@/store/index.js'
import Vue from 'vue'

axios.defaults.withCredentials = false

const builder = function(opts) {
  const service = axios.create(Object.assign({
    timeout: 900000
  }, opts))

  service.interceptors.request.use(
    config => {
      if (config.vue) {
        config.vue.loading = true
        config.vue.loadfailed = false
      }

      config.params = {
        // t: new Date().getTime(),
        ...config.params
      }

      const fn = Vue.prototype.get_token
      if (typeof fn === 'function') {
        // 整合到工作台时，通过这个方法获取用户token
        const token = fn()
        if (token.length != 0) {
          config.headers['JWT-AUTHORIZATION'] = token
        }
      } else {
        // 应用模块开发时，直接调用store获取用户token
        if (Object.keys(store.getters.token).length != 0) {
          config.headers['JWT-AUTHORIZATION'] = store.getters.token
        }
      }

      return config
    },
    error => {
      return Promise.reject(error)
    }
  )

  // response interceptor
  service.interceptors.response.use(
    response => {
      if (response && response.config && response.config.vue) {
        response.config.vue.loading = false
        response.config.vue.loadfailed = false
      }
      const res = response.data
      return res
    },
    err => {
      if (err && err.response) {
        switch (err.response.status) {
          case 400:
            err.message = '错误请求'
            break
          case 403:
            err.message = '拒绝访问'
            break
          case 404:
            err.message = '请求错误，未找到资源'
            break
          default:
            break
        }
      }
      if (err && err.config && err.config.vue) {
        err.config.vue.loading = false
        err.config.vue.loadfailed = true
      }
      return Promise.reject(err)
    }
  )

  return service
}

export default builder
