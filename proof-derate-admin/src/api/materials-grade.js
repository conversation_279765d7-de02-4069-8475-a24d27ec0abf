import request from '@/api/requests/proofDerateRequest'

/**
 * 获取材料目录等级列表
 * @param {*} data
 * @returns
 */
export function getCatalogueMaterialList(data) {
  return request({
    url: '/catalog/v1/common/list',
    method: 'post',
    data
  })
}

/**
 * 编辑材料目录信息
 * @param {*} data
 * @returns
 */
export function editCatalogueMaterial(data) {
  return request({
    url: '/archives/v1/common/catalog_rank/edit',
    method: 'post',
    data
  })
}
/**
 * 材料档案复用日志列表查询
 * @param {*} data
 * @returns
 */
export function getReuseLogList(data) {
  return request({
    url: '/material/webapi/v1/common/reuse_log/list',
    method: 'post',
    data
  })
}
/**
 * 材料档案-日志管理-复用日志详情
 * @param {*} data
 * @returns
 */
export function getReuseLogDetail(data) {
  return request({
    url: '/material/webapi/v1/common/reuse_log/get',
    method: 'post',
    data
  })
}
/**
 * 获取查询界面采集系统
 * @param {*} data
 * @returns
 */
export function getReuseLogSystems(data) {
  return request({
    url: '/material/webapi/v1/common/reuse_log/systems',
    method: 'post',
    data
  })
}

/**
 * 查询材料档案详情
 * @param {*} data
 * @returns
 */
export function getMaterialInfo(data) {
  return request({
    url: '/material/webapi/v1/common/material_info/get',
    method: 'post',
    data
  })
}
/**
 * 查询材料生命周期
 * @param {*} data
 * @returns
 */
export function getLiveCycle(data) {
  return request({
    url: '/material/webapi/v1/common/material_info/live_cycle/check',
    method: 'post',
    data
  })
}
/**
 * 查询材料档案附件列表
 * @param {*} data
 * @returns
 */
export function getMaterialInfoList(data) {
  return request({
    url: '/material/webapi/v1/common/material_info/attachment/list',
    method: 'post',
    data: data
  })
}

/**
 * 材料档案使用日志列表查询
 * @param {*} data
 * @returns
 */
export function getUseLogList(data) {
  return request({
    url: '/material/webapi/v1/common/use_log/list',
    method: 'post',
    data: data
  })
}
// 水印管理>水印样式管理>水印列表
export function getWatermarkPage(data) {
  return request({
    url: '/license/webapi/v1/common/watermark/page',
    method: 'post',
    data: data
  })
}
// 水印管理>水印样式管理>水印详情
export function getWatermarkDetail(id) {
  return request({
    url: `/license/webapi/v1/common/watermark/view/${id}`,
    method: 'get'
  })
}
// 水印管理>水印样式管理>预览
export function getWatermarkPreview(id) {
  return request({
    url: `/license/webapi/v1/common/watermark/preview/${id}`,
    method: 'get'
  })
}
// 水印管理>水印样式管理>新增预览
export function getWatermarkNewPreview(data) {
  return request({
    url: `/license/webapi/v1/common/watermark/preview`,
    method: 'post',
    data: data
  })
}
// 水印管理>水印内容信息项>查看列表
export function getWatermarkItemList(data) {
  return request({
    url: '/license/webapi/v1/common/watermark_item/list',
    method: 'get',
    data: data
  })
}
// 水印管理>水印内容信息项>删除
export function getWatermarkItemDelete(data) {
  return request({
    url: '/license/webapi/v1/common/watermark_item/delete',
    method: 'post',
    data: data
  })
}
// 水印管理>水印内容信息项>删除
export function getWatermarkItemCreate(data) {
  return request({
    url: '/license/webapi/v1/common/watermark_item/create',
    method: 'post',
    data: data
  })
}
// 水印管理>新增水印>创建
export function getWatermarkCreate(data) {
  return request({
    url: '/license/webapi/v1/common/watermark/create',
    method: 'post',
    data: data
  })
}
// 水印管理>新增水印>编辑
export function getWatermarkEdit(data) {
  return request({
    url: '/license/webapi/v1/common/watermark/edit',
    method: 'post',
    data: data
  })
}
// 水印管理>水印样式管理>删除
export function getWatermarkDelete(data) {
  return request({
    url: '/license/webapi/v1/common/watermark/delete',
    method: 'post',
    data: data
  })
}

// 根据唯一标识查询目录和水印关系列表
export function getFillElement(id) {
  return request({
    url: `license/webapi/v1/common/fill_element/get/${id}`,
    method: 'GET'
  })
}
// 更新目录和水印关系
export function updateFillElement(data) {
  return request({
    url: `/license/webapi/v1/common/fill_element/update`,
    method: 'POST',
    data: data
  })
}
// 水印样式 -> 查询全部
export function getWatermarkList() {
  return request({
    url: `/license/webapi/v1/common/watermark/list`,
    method: 'get'
  })
}

// 水印样式 -> 全部目录列表
export function getAllList() {
  return request({
    url: `/license/webapi/v1/common/fill_element/catalog_list`,
    method: 'POST'
  })
}

// 归档
export function archive(data) {
  return request({
    url: `/material/webapi/v1/common/material_info/live_cycle/archive`,
    method: 'POST',
    data
  })
}

// 免证管理>水印管理>水印应用管理>证明目录列表页面
export function getCatalogList() {
  return request({
    url: `/exempt/webapi/v1/common/water_mark/catalog_list`,
    method: 'GET'
  })
}

// 免证管理>水印管理>水印应用管理>绑定水印证明目录关系
export function bindAssociationCatalog(param) {
  return request({
    url: `/exempt/webapi/v1/common/water_mark/bind_association_catalog`,
    method: 'GET',
    params: param
  })
}

// 免证管理>水印管理>水印应用管理>证明目录分页页面
export function associationCatalogPage(param) {
  return request({
    url: `/exempt/webapi/v1/common/water_mark/association_catalog_page`,
    method: 'GET',
    params: param
  })
}

// 免证管理>水印管理>水印应用管理>删除水印证明目录关系
export function deleteAssociationCatalog(param) {
  return request({
    url: `/exempt/webapi/v1/common/water_mark/delete_association_catalog`,
    method: 'GET',
    params: param
  })
}

// 免证管理>水印管理>水印应用管理>获取所有已绑定的水印
export function hasCatalogList() {
  return request({
    url: `/exempt/webapi/v1/common/water_mark/has_catalog_list`,
    method: 'GET'
  })
}
