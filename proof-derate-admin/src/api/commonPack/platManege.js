import request from '@/api/requests/liccRequest'

/* 材料目录分页条件查询 */
export function getDepartmentList(data) {
  return request({
    url: '/auth/webapi/v1/common/organization/page',
    method: 'POST',
    data: data
  })
}

/* 查看部门 */
export function getOrganizationView(id) {
  return request({
    url: `/auth/webapi/v1/common/organization/view/${id}`,
    method: 'get'
  })
}
/*  获取部门列表  */
export function getOrganizationList(data) {
  return request({
    url: '/auth/webapi/v1/common/organization/list',
    method: 'POST',
    data: data
  })
}
// 编辑部门
export function saveOrganizationEdit(data) {
  return request({
    url: '/auth/webapi/v1/common/organization/edit',
    method: 'POST',
    data: data
  })
}
// 新建部门
export function saveOrganizationCreate(data) {
  return request({
    url: '/auth/webapi/v1/common/organization/create',
    method: 'POST',
    data: data
  })
}
// 删除部门
export function deleteOrganization(id) {
  return request({
    url: `/auth/webapi/v1/common/organization/del/${id}`,
    method: 'POST'
  })
}
// 获取部门树
export function getOrganizationTree(data) {
  return request({
    url: '/auth/webapi/v1/common/organization/tree',
    method: 'get',
    data: data
  })
}

/*
  用户与权限内
*/
// 获取用户列表
export function getAccountList(data) {
  return request({
    url: '/auth/webapi/v1/common/account/page',
    method: 'post',
    data: data
  })
}
// 重置密码
export function resetPassword(id) {
  return request({
    url: `/auth/webapi/v1/common/account/reset_password/${id}`,
    method: 'post'
  })
}
// 变更状态
export function changeStatus(data) {
  return request({
    url: '/auth/webapi/v1/common/account/change_status',
    method: 'post',
    data
  })
}
// 查看用户
export function getAccountView(id) {
  return request({
    url: `/auth/webapi/v1/common/account/view/${id}`,
    method: 'get'
  })
}
// 编辑用户
export function editAccount(data) {
  return request({
    url: `/auth/webapi/v1/common/account/edit`,
    method: 'post',
    data
  })
}
// 新增用户
export function createAccount(data) {
  return request({
    url: `/auth/webapi/v1/common/account/create`,
    method: 'post',
    data
  })
}
// 初始化密码
export function initPassword(data) {
  return request({
    url: '/auth/webapi/v1/common/account/init_password',
    method: 'get',
    data
  })
}

/*
菜单管理
*/
// 获取菜单分页列表
export function getMenuPage(data) {
  return request({
    url: '/auth/webapi/v1/common/menu/page',
    method: 'post',
    data: data
  })
}
// 获取菜单列表
export function getMenuList(data) {
  return request({
    url: '/auth/webapi/v1/common/menu/list',
    method: 'post',
    data: data
  })
}
// 查看菜单
export function getMenuView(id) {
  return request({
    url: `/auth/webapi/v1/common/menu/view/${id}`,
    method: 'get'
  })
}
// 删除菜单
export function delMenu(id) {
  return request({
    url: `/auth/webapi/v1/common/menu/del/${id}`,
    method: 'post'
  })
}
// 编辑菜单
export function EditMenu(data) {
  return request({
    url: '/auth/webapi/v1/common/menu/edit',
    method: 'post',
    data: data
  })
}
// 创建菜单
export function createMenu(data) {
  return request({
    url: '/auth/webapi/v1/common/menu/create',
    method: 'post',
    data: data
  })
}
// 菜单tree
export function getMenuTree(data) {
  return request({
    url: '/auth/webapi/v1/common/menu/tree',
    method: 'get',
    data: data
  })
}
// 菜单导出
/* export function exportMenu(data) {
  return request({
    url: '/auth/webapi/v1/common/menu/export',
    method: 'post',
    data: data,
    responseType: 'blod',
    headers: {
      ContentType: 'application/json'
    }
  })
} */
export function exportMenu(data) {
  return '/auth/webapi/v1/common/menu/export'
}
// 菜单导入
export function importMenu(data) {
  return request({
    url: '/auth/webapi/v1/common/menu/import',
    method: 'post',
    data: data
  })
}

/*
日志管理
*/
// 获取操作日志列表
export function getLogOperationPage(data) {
  return request({
    url: '/auth/webapi/v1/common/access_log/list',
    method: 'post',
    data: data
  })
}
// 操作日志-详情
export function getOperationDetail(data) {
  return request({
    url: `/auth/webapi/v1/common/access_log/view/${data.year}/${data.id}`,
    method: 'get'
  })
}
/*
角色
*/
// 获取角色列表
export function getRoleList(data) {
  return request({
    url: '/auth/webapi/v1/common/role/list',
    method: 'post',
    data
  })
}

// 角色管理 > 分页列表
export function getRolePage(data) {
  return request({
    url: `/auth/webapi/v1/common/role/page`,
    method: 'POST',
    data: data
  })
}

// 角色管理 > 分页列表
export function getBindMenu(params) {
  return request({
    url: `/auth/webapi/v1/common/role/bind_menu`,
    method: 'GET',
    params: params
  })
}

// 角色管理 > 查看角色
export function getRoleView(id) {
  return request({
    url: `/auth/webapi/v1/common/role/view/${id}`,
    method: 'GET'
  })
}
// 角色管理 > 编辑角色
export function editRole(data) {
  return request({
    url: `/auth/webapi/v1/common/role/edit`,
    method: 'post',
    data: data
  })
}
// 角色管理 > 自动生成角色代码
export function getAutoCode() {
  return request({
    url: `/auth/webapi/v1/common/role/auto_code`,
    method: 'GET'
  })
}
// 角色管理 > 创建角色
export function createRoleData(data) {
  return request({
    url: `/auth/webapi/v1/common/role/create`,
    method: 'POST',
    data: data
  })
}
// 角色管理 > 检测是否绑定用户
export function checkBindUser(id) {
  return request({
    url: `/auth/webapi/v1/common/role/check_bind_user/${id}`,
    method: 'GET'
  })
}
// 角色管理 > 变更状态
export function changeRoleStatus(data) {
  return request({
    url: `/auth/webapi/v1/common/role/change_status`,
    method: 'POST',
    data: data
  })
}

/*  所有区划 */
export function getAlldivision(data) {
  return request({
    url: '/auth/webapi/v1/common/division/get',
    method: 'GET'
  })
}
/*  获取注册部门  */
export function getOrganization(data) {
  return request({
    url: '/auth/webapi/v1/common/organization/list',
    method: 'POST',
    data: data
  })
}

// 接入应用系统 > 分页列表
export function getAccessSyspage(data) {
  return request({
    url: `/auth/webapi/v1/common/access_sys/page`,
    method: 'POST',
    data: data
  })
}

// 接入应用系统 > 生成appKey
export function getAutoKey() {
  return request({
    url: `/auth/webapi/v1/common/access_sys/auto_key`,
    method: 'GET'
  })
}

// 接入应用系统 > 生成password
export function getAutoPassword() {
  return request({
    url: `/auth/webapi/v1/common/access_sys/auto_password`,
    method: 'GET'
  })
}

// 接入应用系统 > 创建
export function createAccessSys(data) {
  return request({
    url: `/auth/webapi/v1/common/access_sys/create`,
    method: 'POST',
    data: data
  })
}

// 接入应用系统 > 查看
export function getAccessSysData(id) {
  return request({
    url: `/auth/webapi/v1/common/access_sys/view/${id}`,
    method: 'get'
  })
}
// 接入应用系统 > 编辑
export function editAccessSysData(data) {
  return request({
    url: `/auth/webapi/v1/common/access_sys/edit`,
    method: 'POST',
    data: data
  })
}
// 接口管理 > 分页列表
export function getapiManagePage(data) {
  return request({
    url: `/auth/webapi/v1/common/api_manage/page`,
    method: 'POST',
    data: data
  })
}

// 接口管理 > 未选中列表
export function getUncheckedList() {
  return request({
    url: `/auth/webapi/v1/common/api_manage/unchecked_list`,
    method: 'POST'
  })
}
// 接口管理 > 选中列表
export function getCheckedList() {
  return request({
    url: `/auth/webapi/v1/common/api_manage/checked_list`,
    method: 'POST'
  })
}
// 接口管理 > 创建
export function createApiManage(data) {
  return request({
    url: `/auth/webapi/v1/common/api_manage/create`,
    method: 'POST',
    data: data
  })
}
// 接入应用系统 > 重置密码
export function resetAccessSysPassword(id) {
  return request({
    url: `/auth/webapi/v1/common/access_sys/reset_password/${id}`,
    method: 'POST'
  })
}
// 接入应用系统 > 禁用
export function disableAccessSys(id) {
  return request({
    url: `/auth/webapi/v1/common/access_sys/disable/${id}`,
    method: 'POST'
  })
}
// 接入应用系统 >  恢复
export function recoveryAccessSys(id) {
  return request({
    url: `/auth/webapi/v1/common/access_sys/recovery/${id}`,
    method: 'POST'
  })
}

// 接口管理  > 禁用
export function disableApiManage(id) {
  return request({
    url: `/auth/webapi/v1/common/api_manage/disable/${id}`,
    method: 'POST'
  })
}
// 接口管理  >  恢复
export function recoveryApiManage(id) {
  return request({
    url: `/auth/webapi/v1/common/api_manage/recovery/${id}`,
    method: 'POST'
  })
}
// 接口管理 > 删除
export function delectApiManage(id) {
  return request({
    url: `/auth/webapi/v1/common/api_manage/del/${id}`,
    method: 'POST'
  })
}
// 接口管理 > 详情
export function getApiManageDetail(id) {
  return request({
    url: `/auth/webapi/v1/common/api_manage/view/${id}`,
    method: 'GET'
  })
}

// 字典管理 > 列表
export function getDictList(data) {
  return request({
    url: `/auth/webapi/v1/common/dict/page`,
    method: 'POST',
    data: data
  })
}

// 字典管理 > 详情
export function getDictDetail(data) {
  return request({
    url: `/auth/webapi/v1/common/dict_detail/page`,
    method: 'POST',
    data: data
  })
}

//  字典管理 > 新建
export function addDict(data) {
  return request({
    url: `/auth/webapi/v1/common/dict/create`,
    method: 'POST',
    data: data
  })
}

//  字典管理 > 编辑
export function editDict(data) {
  return request({
    url: `/auth/webapi/v1/common/dict/edit`,
    method: 'POST',
    data: data
  })
}

// 字典管理 > 删除
export function deleteDict(id) {
  return request({
    url: `/auth/webapi/v1/common/dict/del/${id}`,
    method: 'POST'
  })
}

// 字典明细 > 新增
export function addDictDetail(data) {
  return request({
    url: `/auth/webapi/v1/common/dict_detail/create`,
    method: 'POST',
    data: data
  })
}

// 字典明细 > 编辑
export function editDictDetail(data) {
  return request({
    url: `/auth/webapi/v1/common/dict_detail/edit`,
    method: 'POST',
    data: data
  })
}

// 字典明细 > 删除
export function deleteDictDetail(id) {
  return request({
    url: `/auth/webapi/v1/common/dict_detail/del/${id}`,
    method: 'POST'
  })
}
// 字典明细 > 查看字典
export function getDictDetailView(id) {
  return request({
    url: `auth/webapi/v1/common/dict/view/${id}`,
    method: 'GET'
  })
}

// 行政区划管理 > 分页列表
export function getDivisionlPage(data) {
  return request({
    url: `/auth/webapi/v1/common/division/page`,
    method: 'POST',
    data: data
  })
}
// 行政区划管理 > 创建行政区划
export function divisionCreate(data) {
  return request({
    url: `/auth/webapi/v1/common/division/create`,
    method: 'POST',
    data: data
  })
}
// 行政区划管理 > 下拉列表
export function getDivisionList(params) {
  return request({
    url: `/auth/webapi/v1/common/division/get`,
    method: 'get',
    params: params
  })
}
// 行政区划管理 > 下拉列表 （树）
export function getDivisionListTree(params) {
  return request({
    url: `/auth/webapi/v1/common/division/get_trees`,
    method: 'get',
    params: params
  })
}

// 行政区划管理 -> 删除行政区划
export function delDivision(code) {
  return request({
    url: `/auth/webapi/v1/common/division/del/${code}`,
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  })
}
// 行政区划管理 -> 编辑行政区划
export function editDivision(data) {
  return request({
    url: `/auth/webapi/v1/common/division/edit`,
    method: 'POST',
    data: data
  })
}
// 行政区划管理 > 区划树
export function getDivisionTree() {
  return request({
    url: `/auth/webapi/v1/common/division/tree`,
    method: 'GET'
  })
}
// 行政区划管理 -> 导入
export function importDivision(data, params) {
  return request({
    url: `auth/webapi/v1/common/division/import`,
    method: 'POST',
    data: data,
    params,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 系統設置 > 詳情
export function getSystemSettings() {
  return request({
    url: `/auth/webapi/v1/common/sys/view`,
    method: 'GET'
  })
}

// 系统设置 > 密码有效天数
export function editPasswordValidate(data) {
  return request({
    url: `/auth/webapi/v1/common/sys/security_edit`,
    method: 'POST',
    data: data
  })
}

// 系统设置 > 皮肤设置
export function editSystemColor(data) {
  return request({
    url: `/auth/webapi/v1/common/sys/skin_edit`,
    method: 'POST',
    data: data
  })
}

// 系统设置 > 系统设置
export function editSystemSettings(data) {
  // return request({
  //   headers: { 'Content-Type': 'multipart/form-data' },
  //   url: `/auth/webapi/v1/common/sys/system_edit?id=${params.id}&name=${params.name}&icon=${params.icon}&navigate_layout=${params.navigate_layout}&web_icon=${params.web_icon}`,
  //   method: 'POST',
  //   data: data
  // })

  return request({
    url: `/auth/webapi/v1/common/sys/system_edit`,
    method: 'POST',
    data: data
  })
}

/**
 * 获取在线用户列表
 * @param {*} data
 * @returns
 */
export function getOnlineUserList(data) {
  return request({
    url: `/auth/webapi/v1/common/online_user/page`,
    method: 'POST',
    data: data
  })
}

/**
 * 强退在线用户
 * @param {*} data
 */
export function kickOutOnlineUser(data) {
  return request({
    url: `/auth/webapi/v1/common/online_user/kick_out`,
    method: 'POST',
    data
  })
}
/**
 * 测试启动
 * @param {*} data
 */
export function opsText(data) {
  return request({
    url: `/executor-license-inter-provincial-ops/index`,
    method: 'GET'
  })
}

/**
 * 获取外部账号列表
 * @param {*} params
 */
export function getThirdAccountList(params) {
  return request({
    url: `/auth/webapi/v1/common/third_account/page`,
    method: 'POST',
    data: params
  })
}
/**
 * 第三方账号管理-删除
 * @param {*} id
 * @returns
 */
export function deleteThirdAccount(id) {
  return request({
    url: `/auth/webapi/v1/common/third_account/del/${id}`,
    method: 'GET'
  })
}

/**
 * 第三方账号管理-创建
 * @param {*} params
 * @returns
 */
export function createThirdAccount(params) {
  return request({
    url: `/auth/webapi/v1/common/third_account/create`,
    method: 'POST',
    data: params
  })
}

/**
 * 第三方账号管理-下拉查询
 * @param {*} params
 * @returns
 */
export function getSelectAccount(params) {
  return request({
    url: `/auth/webapi/v1/common/third_account/select_list`,
    method: 'GET'
  })
}

// 下载
export function exportsDown(url, data) {
  return request({
    url: `${url}`,
    method: 'POST'
  })
}

/**
 * 接口管理-接入应用系统-导入
 * @param {*} data
 * @returns
 */
export function accessApplicationSystemImport(data, params) {
  return request({
    url: `auth/webapi/v1/common/access_sys/import`,
    method: 'POST',
    data: data,
    params,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 平台管理-部门信息管理-导入
 * @param {*} data
 * @returns
 */
export function departmentImport(data, params) {
  return request({
    url: `/auth/webapi/v1/common/organization/import`,
    method: 'POST',
    data: data,
    params,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 平台管理-记录外部链接
 * @param {*} data
 * @returns
 */
export function setExternallinksLog(params) {
  return request({
    url: `/auth/webapi/v1/common/access_log/external_links`,
    method: 'GET',
    params
  })
}

/**
 * 接口管理 > 获取接口管理分类类型
 * @param {*} data
 * @returns
 */
export function getApiGroupOptions(params) {
  return request({
    url: `/auth/webapi/v1/common/api_manage/api_group`,
    method: 'GET',
    params
  })
}

/**
 * 接口管理 > 接口分组编辑
 */
export function editApiGroup(data) {
  return request({
    url: `/auth/webapi/v1/common/api_manage/api_group/edit`,
    method: 'post',
    data
  })
}

/**
 * 接口管理 > 接口分组删除
 */
export function delApiGroup(data) {
  return request({
    url: `/auth/webapi/v1/common/api_manage/api_group/del`,
    method: 'post',
    data
  })
}

/**
 * 接口管理 > 编辑
 */
export function editApi(id, data) {
  return request({
    url: `/auth/webapi/v1/common/api_manage/edit/${id}`,
    method: 'post',
    data
  })
}

/**
 * 接口管理 > 下载模板
 */
export function downloadApiManageTemplate(id) {
  return `/auth/webapi/v1/common/api_manage/download_template`
}

// 接口管理 > 导出
export function exportApiManage(data) {
  return `/auth/webapi/v1/common/api_manage/export`
  /*  return request({
    url: `/auth/webapi/v1/common/api_manage/export`,
    method: 'POST',
    data: data
  }) */
}
// 接口管理 > 导入
export function importApiManage(data, params) {
  return request({
    url: `/auth/webapi/v1/common/api_manage/import`,
    method: 'POST',
    data: data,
    params
  })
}
// 导出excel导入的异常清单
export function exportErrorInv() {
  return `/common/webapi/v1/common/file/export_error_inv`
}

// 部门管理-导入-下载模版
export function deparmentDownTemplate() {
  return '/auth/webapi/v1/common/organization/download_template'
}

// 行政区划管理-导入-下载模版
export function divisionDownTemplate() {
  return '/auth/webapi/v1/common/division/download_template'
}

// 行政区划管理-导入-下载模版
export function accessSysDownTemplate() {
  return '/auth/webapi/v1/common/access_sys/download_template'
}

// 平台管理-系统设置-平台设置 >
export function editSysPlatform(data) {
  return request({
    url: `/auth/webapi/v1/common/sys/platform_edit`,
    method: 'POST',
    data: data
  })
}

// 平台管理-角色管理-删除角色
export function deleteRole(data) {
  return request({
    url: `/auth/webapi/v1/common/role/delete`,
    method: 'POST',
    data: data
  })
}

// 平台管理-角色管理-删除账号
export function deleteAccount(data) {
  return request({
    url: `/auth/webapi/v1/common/account/delete`,
    method: 'POST',
    data: data
  })
}

// 数据范围枚举
export function getDataScope() {
  return request({
    url: `/common/webapi/v1/common/selector/get_data_scope`,
    method: 'get'
  })
}
// 获取app接口数据范围
export function getDataScopeApp() {
  return request({
    url: `/common/webapi/v1/common/selector/get_data_scope/app`,
    method: 'get'
  })
}
