import request from '@/api/requests/adminRequest'
import store from '@/store/index.js'

/**********************
 * 
 * 行政区划接口服务
 * 
 **********************/
//获取下级区划
export function getDivision(divCode) {
	return request({
		url: '/commonObj/divisions/' + divCode,
		method: 'get',
		headers: { 'Content-Type': 'application/json' }
	})
}

//获取当前区划
export function getDivisionByCode(divCode) {
	return request({
		url: '/commonObj/getDivisionByCode/' + divCode,
		method: 'get',
		headers: { 'Content-Type': 'application/json' }
	})
}

//获取全部区划
export function getAllDivisions() {
	return request({
		url: '/commonObj/divisions/getAllDivisions',
		method: 'get',
		headers: { 'Content-Type': 'application/json' }
	})
}

//获取全部父级行政区划
export function getAllParentValue(key) {
	return request({
		url: '/commonObj/getAllParentValue/'+ key,
		method: 'get',
		headers: { 'Content-Type': 'application/json' }
	})
}
//获取下级区划
export function childDivisions(divCode) {
	return request({
		url: '/commonObj/childDivisions/' + divCode,
		method: 'get',
		headers: { 'Content-Type': 'application/json' }
	})
}
//从本地缓存里面获取行政区划信息
export function getDivisionByCodeFromStore(divCode) {
	return new Promise((reslove) => {
		let resultData = null;
		let divisionData = store.getters.divisionData;
		for (let i = 0; i < divisionData.length; i++) {
			if (divisionData[i].code == divCode) {
				resultData = divisionData[i];
				break;
			}
		}
		if (resultData || !divCode || divCode.length<1) {
			reslove(resultData);
		} else {
			getDivisionByCode(divCode).then((response) => {
				if (response) {
					resultData = {};
					resultData.code = response.value;
					resultData.name = response.name;
					store.dispatch("add_divisionData", resultData);
					reslove(resultData);
				}
			}).catch((err) => {
				reslove(null);
			});
		}

	});
}