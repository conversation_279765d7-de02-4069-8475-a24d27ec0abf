import request from '@/api/requests/adminRequest'
import axios from 'axios'
import store from '@/store/index.js'
import Vue from 'vue'

//获取单位列表
export function getOrg(divCode) {
  return request({
    url: '/managerCommon/org/selectItem/' + divCode,
    method: 'get',
    headers: { 'Content-Type': 'application/json' },
  })
}

//获取所有机构
export function allOrgList() {
  return request({
    url: '/managerCommon/org/selectItem/allOrgList',
    method: 'get',
    headers: { 'Content-Type': 'application/json' },
  })
}

//组织机构查询列表接口
export function getOrgList(params) {
  return request({
    url: '/org/list',
    method: 'post',
    data: params,
    headers: { 'Content-Type': 'application/json' },
  })
}


//组织机构查询列表接口无需鉴权
export function getOrgListNoAuth(params) {
  return request({
    url: '/api/org/list',
    method: 'post',
    data: params,
    headers: { 'Content-Type': 'application/json' },
  })
}

//根据id查询组织机构接口
export function getOrgById(params) {
  return request({
    url: '/org/getById',
    method: 'get',
    params: { id: params },
    headers: { 'Content-Type': 'application/json' },
  })
}

//新增组织机构
export function saveOrg(params) {
  return request({
    url: '/org/add',
    method: 'post',
    data: params,
    headers: { 'Content-Type': 'application/json' },
  })
}

//修改组织机构
export function editOrg(params) {
  return request({
    url: '/org/edit',
    method: 'post',
    data: params,
    headers: { 'Content-Type': 'application/json' },
  })
}

//删除组织机构
export function delOrgById(params) {
  return request({
    url: '/org/deleteById',
    method: 'get',
    params: { id: params },
    headers: { 'Content-Type': 'application/json' },
  })
}

//废置组织机构
export function disOrgById(params) {
  return request({
    url: '/org/discardById',
    method: 'get',
    params: { id: params },
    headers: { 'Content-Type': 'application/json' },
  })
}

//行政区划按条件查询
export function codeItemList(params) {
  return request({
    url: '/api/codeItem/list',
    method: 'post',
    data: params,
    headers: { 'Content-Type': 'application/json' },
  })
}

//下载组织机构的excel模板
export function importExcelDownload() {
  let fn = Vue.prototype.get_token;
  let t_token = '';
  if (typeof fn === 'function') {
    // 整合到工作台时，通过这个方法获取用户token
    t_token = fn();
  } else {
    // 应用模块开发时，直接调用store获取用户token
    if (Object.keys(store.getters.token).length != 0) {
      t_token = store.getters.token;
    }
  }
  return axios({
    baseURL: request.defaults.baseURL,
    url: '/org/importExcelDownload',
    method: 'get',
    headers: { 'Content-Type': 'application/octet-stream', 'JWT-AUTHORIZATION': t_token },
    responseType: 'blob'
  }).then(response => {
    if (!response) {
      this.$message.error("下载文件失败");
      return false;
    }
    //let fileName = response.headers['content-disposition'].split('=')[1];
    // 获取文件名
    let objectUrl = URL.createObjectURL(new Blob([response.data]));
    // 文件地址
    const link = document.createElement('a');
    link.download = "批量导入组织机构模板.xls";
    link.href = objectUrl;
    link.click();
  })
}

//导入组织机构
export function importOrg(data) {
  return request({
    url: '/org/batchImport',
    method: 'post',
    data: data,
    headers: { "Content-Type" : "multipart/form-data" },
  })
}

//获取同级及下级行政区划单位列表
export function getCurrentAndLowerOrgs(divCode) {
  return request({
    url: '/managerCommon/org/getCurrentAndLowerOrgs/' + divCode,
    method: 'get',
    headers: { 'Content-Type': 'application/json' },
  })
}

//组织机构导出
export function toExport(callback) {
  return request({
    url: '/org/toExport',
    method: 'post', //请求类型
    procgress: true,
    back: callback,
    responseType: 'blob'  //响应类型，避免乱码
  })
}