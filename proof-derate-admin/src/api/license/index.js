import request from '@/api/requests/proofDerateRequest'
/**
 * 根据全省目录名称，获取全省目录名称与基本码与行业部门 请求证照目录分页接口
 * @param license_item_name
 * @returns {AxiosPromise}
 */
export function getCatalogPage(param) {
  return request({
    url: '/license/webapi/v1/common/catalog/page',
    method: 'get',
    params: param
  })
}

/**
 * 根据全省目录名称，获取全省目录名称与基本码与行业部门 请求组合目录分页接口
 * @param license_item_name
 * @returns {AxiosPromise}
 */
export function getUnionCatalogPage(param) {
  return request({
    url: '/license/webapi/v1/common/union_catalog/page',
    method: 'get',
    params: param
  })
}

/**
 * 获取电子证明目录
 * @param license_item_name
 * @returns {AxiosPromise}
 */
export function getProofCatalogPage(param) {
  return request({
    url: '/license/webapi/v1/common/proof_catalog/page',
    method: 'get',
    params: param
  })
}
