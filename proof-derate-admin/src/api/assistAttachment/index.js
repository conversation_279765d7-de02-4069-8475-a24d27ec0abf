import request from '@/api/requests/proofDerateRequest'

// 附件上传
export function upload(param, data) {
  return request({
    url: '/assist/webapi/v1/assist_attachment/upload',
    method: 'POST',
    data: data,
    params: param,
    headers: { 'Content-Type': 'multipart/form-data' }
  })
}
// 根据关联标识获取文件
export function download(param) {
  return request({
    url: '/assist/webapi/v1/assist_attachment/download',
    method: 'get',
    params: param
  })
}

// 下载协查电子证明归档文件
export function downloadAttachment(param) {
  return request({
    url: `/assist/webapi/v1/common/license_item/download_attachment`,
    method: 'get',
    params: param
  })
}