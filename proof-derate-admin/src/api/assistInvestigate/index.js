import request from '@/api/requests/proofDerateRequest'

// 协查服务通用查询 分页页面
export function getList(param) {
  return request({
    url: '/assist/webapi/v1/assist_investigate/page',
    method: 'GET',
    params: param
  })
}
// 根据事项编码获取材料下拉对象
export function getitemMaterial(param) {
  return request({
    url: '/assist/webapi/v1/assist_investigate/item_material',
    method: 'GET',
    params: param
  })
}
// 根据材料id获取协查部门下拉对象
export function getassistOrg(param) {
  return request({
    url: '/assist/webapi/v1/assist_investigate/assist_org',
    method: 'GET',
    params: param
  })
}
// 协查申请
export function apply(data) {
  return request({
    url: '/assist/webapi/v1/assist_investigate/apply',
    method: 'POST',
    data: data
  })
}
// 协查处理
export function handle(data) {
  return request({
    url: '/assist/webapi/v1/assist_investigate_handle',
    method: 'POST',
    data: data
  })
}

/*
协查申请
*/

// 协查申请查询 分页页面
export function getApplyList(param) {
  return request({
    url: '/assist/webapi/v1/assist_investigate/apply/page',
    method: 'GET',
    params: param
  })
}

// 协查申请编辑页面 材料下拉列表
export function getApplyitemMaterial(param) {
  return request({
    url: '/assist/webapi/v1/assist_investigate/apply/item_material',
    method: 'GET',
    params: param
  })
}

// 协查申请编辑页面 协查部门下拉列表
export function getApplyassistOrg(param) {
  return request({
    url: '/assist/webapi/v1/assist_investigate/apply/assist_org',
    method: 'GET',
    params: param
  })
}
// 协查申请编辑页面 事项分页页面
export function getApplyItemPage(param) {
  return request({
    url: '/assist/webapi/v1/assist_investigate/apply/item_page',
    method: 'get',
    params: param
  })
}
// 提交协查申请
export function applySave(data) {
  return request({
    url: '/assist/webapi/v1/assist_investigate/apply/save',
    method: 'POST',
    data: data
  })
}
// 查看协查申请 根据id查询具体数据
export function applyView(id) {
  return request({
    url: `/assist/webapi/v1/assist_investigate/apply/view/${id}`,
    method: 'GET'
  })
}

/*
协查处理
*/
// 协查处理 分页页面
export function getHandleList(param) {
  return request({
    url: '/assist/webapi/v1/assist_investigate_handle/page',
    method: 'GET',
    params: param
  })
}
// 提交协查处理
export function handleSave(data) {
  return request({
    url: '/assist/webapi/v1/assist_investigate_handle/save',
    method: 'POST',
    data: data
  })
}
// 查看协查处理 根据id查询具体数据
export function handleView(id) {
  return request({
    url: `/assist/webapi/v1/assist_investigate_handle/view/${id}`,
    method: 'GET'
  })
}
/*
协查档案
*/
// 协查档案 分页页面面
export function getArchivesList(param) {
  return request({
    url: '/assist/webapi/v1/assist_investigate_archives/page',
    method: 'GET',
    params: param
  })
}
// 提交协查处理
export function archivesSave(data) {
  return request({
    url: '/assist/webapi/v1/assist_investigate_archives/save',
    method: 'POST',
    data: data
  })
}
// 查看协查档案 根据id查询具体数据
export function archivesView(id) {
  return request({
    url: `/assist/webapi/v1/assist_investigate_archives/view/${id}`,
    method: 'GET'
  })
}

/*
协查人员
*/
// 协查人员查询 分页页面
export function userPage(param) {
  return request({
    url: '/assist/webapi/v1/assist_investigate_user/page',
    method: 'GET',
    params: param
  })
}
// 查看协查人员 根据id查询
export function getUserByid(id) {
  return request({
    url: `/assist/webapi/v1/assist_investigate_user/view/${id}`,
    method: 'GET'
  })
}

// 证明目录查询
export function proofCatalogPage(param) {
  return request({
    url: '/assist/webapi/v1/assist_investigate_user/proof_catalog_page',
    method: 'GET',
    params: param
  })
}
// 创建
export function create(data) {
  return request({
    url: '/assist/webapi/v1/assist_investigate_user/create',
    method: 'POST',
    data: data
  })
}
// 修改
export function edit(data, id) {
  return request({
    url: `/assist/webapi/v1/assist_investigate_user/edit/${id}`,
    method: 'POST',
    data: data
  })
}
// 删除
export function deleteUser(id) {
  return request({
    url: `assist/webapi/v1/assist_investigate_user/delete/${id}`,
    method: 'POST',
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })
}
// 获取导入模板
export function getUserTemplate(param) {
  return request({
    url: '/assist/webapi/v1/assist_investigate_user/down_import_assist_user_template',
    method: 'GET',
    params: param
  })
}

// 导入 协查人员附件
export function importFile(data) {
  return request({
    url: '/assist/webapi/v1/assist_investigate_user/import_assist_user',
    method: 'POST',
    data: data,
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })
}

/**
 * 协查查看接口
 * @param param
 * @returns {AxiosPromise}
 */
export function getViewBySerialNumber(assist_serial_number) {
  return request({
    url: `/assist/webapi/v1/assist_investigate_handle/view_by_serial_number/${assist_serial_number}`,
    method: 'GET'
  })
}
/**
 * 免证办-协查发起部门查询接口
 * @param param
 * @returns {AxiosPromise}
 */
export function getAssistOrg(param) {
  return request({
    url: `/exempt/webapi/v1/exempt_certificates/manager/investigation/assist/org`,
    method: 'GET',
    params: param
  })
}

/**
 * 免证办-协查发起接口
 * @param param
 * @returns {AxiosPromise}
 */
export function getAssist(param, serial_number) {
  return request({
    url: `/exempt/webapi/v1/exempt_certificates/manager/investigation/assist/${serial_number}`,
    method: 'POST',
    params: param,
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })
}

// 协查申请编辑页面 证明目录下拉列表
export function getItemProofCatalog(param) {
  return request({
    url: '/assist/webapi/v1/assist_investigate/apply/item_proof_catalog',
    method: 'get',
    params: param
  })
}

// 获取协查单号
export function getAssistSerialNumber() {
  return request({
    url: '/assist/webapi/v1/assist_investigate/apply/assist_serial_number',
    method: 'get'
  })
}

// 查看协查人员 根据id查询 非脱敏接口
export function getViewWithoutMaskField(id) {
  return request({
    url: `/assist/webapi/v1/assist_investigate_user/view_without_mask_field/${id}`,
    method: 'get'
  })
}

// 协查模块查询证明详情
export function getProofCatalogById(id) {
  return request({
    url: `/assist/webapi/v1/proof_catalog/find/${id}`,
    method: 'get',
  })
}


