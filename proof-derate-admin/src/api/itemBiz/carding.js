import request from '@/api/requests/proofDerateRequest'

/**
 * 事项清单分页查询
 * @param actualize 实施区划
 * @returns {AxiosPromise}
 */
export function getOProofListPage(param = {}) {
  return request({
    url: '/proof_list/page',
    method: 'get',
    params: param
  })
}

/**
 * 事项清单详情
 * @param actualize 实施区划
 * @returns {AxiosPromise}
 */
export function getProofListFormView(id, param = {}) {
  return request({
    url: '/proof_list/formView/' + id,
    method: 'get',
    params: param,
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })
}
/**
 * 证照跳转
 * @param code 证照代码
 * @returns {AxiosPromise}
 */
export function goLicenseItemView(code) {
  return request({
    url: '/proof_list/licenseItemView/' + code,
    method: 'get'
  })
}

/**
 * 事项清单梳理确认
 * @param data
 * @returns {AxiosPromise}
 */
export function proofListConfirmCreate(param = {}) {
  return request({
    url: '/proof_list_confirm/create',
    method: 'post',
    data: param
  })
}

