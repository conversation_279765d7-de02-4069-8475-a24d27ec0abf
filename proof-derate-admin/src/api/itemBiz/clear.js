import request from '@/api/requests/proofDerateRequest'

/**
 * 获取事项列表分页
 * @param param
 * @returns {AxiosPromise}
 */
export function getItemList(param = {}) {
  return request({
    url: '/item/webapi/v1/page',
    method: 'get',
    params: param
  })
}

/**
 * 事项总数查询 事项
 * @param param
 * @returns {AxiosPromise}
 */
export function getPageCount(param = {}) {
  return request({
    url: '/item/webapi/v1/pageCount',
    method: 'get',
    params: param
  })
}

/**
 * 事项清理分页 查询
 * @param param
 * @returns {AxiosPromise}
 */
export function getWaitForCleanPage(param = {}) {
  return request({
    url: '/item/webapi/v1/wait_for_clean/page',
    method: 'get',
    params: param
  })
}

/**
 * 事项清理分页 分页
 * @param param
 * @returns {AxiosPromise}
 */
export function getWaitForCleanPageCount(param = {}) {
  return request({
    url: '/item/webapi/v1/wait_for_clean/pageCount',
    method: 'get',
    params: param
  })
}

/**
 * 事项梳理分页 查询
 * @param param
 * @returns {AxiosPromise}
 */
export function getCardingUnconFirmedPage(param = {}) {
  return request({
    url: '/item/webapi/v1/carding_unconfirmed/page',
    method: 'get',
    params: param
  })
}

/**
 * 事项梳理分页 查询
 * @param param
 * @returns {AxiosPromise}
 */
export function getCardingUnconFirmedPageCount(param = {}) {
  return request({
    url: '/item/webapi/v1/carding_unconfirmed/pageCount',
    method: 'get',
    params: param
  })
}

/**
 * 事项审核分页 查询
 * @param param
 * @returns {AxiosPromise}
 */
export function getCardingConFirmedPage(param = {}) {
  return request({
    url: '/item/webapi/v1/carding_confirmed/page',
    method: 'get',
    params: param
  })
}

/**
 * 事项审核分页 分页
 * @param param
 * @returns {AxiosPromise}
 */
export function getCardingConFirmedPageCount(param = {}) {
  return request({
    url: '/item/webapi/v1/carding_confirmed/pageCount',
    method: 'get',
    params: param
  })
}

/**
 * 事项清单分页 查询
 * @param param
 * @returns {AxiosPromise}
 */
export function getProoListPage(param = {}) {
  return request({
    url: '/item/webapi/v1/proof_list/page',
    method: 'get',
    params: param
  })
}

/**
 * 事项清单分页 分页
 * @param param
 * @returns {AxiosPromise}
 */
export function getProoListPageCount(param = {}) {
  return request({
    url: '/item/webapi/v1/proof_list/pageCount',
    method: 'get',
    params: param
  })
}

/**
 * 无需清理列表的查看事项和该事项绑定的材料详情
 * @param param
 * @returns {AxiosPromise}
 */
export function getItemView(itemCode, param = {}) {
  return request({
    url: '/item/webapi/v1/view/' + itemCode,
    method: 'get',
    params: param
  })
}

/**
 * 获取事项清单详情
 * @param param
 * @returns {AxiosPromise}
 */
export function getProofListFormView(id) {
  return request({
    url: '/catalog/webapi/v1/proof_list/formView/' + id,
    method: 'get'
  })
}

/**
 * 获取事项清单详情之已绑定的材料列表
 * @param param
 * @returns {AxiosPromise}
 */
export function getProofListView(id) {
  return request({
    url: '/catalog/webapi/v1/proof_list/view/' + id,
    method: 'get'
  })
}

/**
 * 编辑为无需清理
 * @param param
 * @returns {AxiosPromise}
 */
export function doNotClean(param) {
  return request({
    url: '/item/webapi/v1/do_not_clean',
    method: 'post',
    params: param,
    headers: { 'Content-Type': 'application/json' }
  })
}

/**
 * 创建证明清理材料
 * @param param
 * @returns {AxiosPromise}
 */
export function proofListCreate(itemId, param) {
  return request({
    url: '/catalog/webapi/v1/proof_list/create_proof?item_code=' + itemId,
    method: 'post',
    data: param,
    headers: { 'Content-Type': 'application/json' }
  })
}

/**
 * 暂存证明清理材料-新
 * @param param
 * @returns {AxiosPromise}
 */
export function proofListCreateDraft(itemId, param) {
  return request({
    url: '/catalog/webapi/v1/proof_list/create_proof_draft?item_code=' + itemId,
    method: 'post',
    data: param,
    headers: { 'Content-Type': 'application/json' }
  })
}

/**
 * 修改证明清理材料-新
 * @param param
 * @returns {AxiosPromise}
 */
export function proofListUpdate(itemId, param, is_revision) {
  let url = '/catalog/webapi/v1/proof_list/update_item_proof_list?item_code=' + itemId + '&is_revision=false'
  if (is_revision) {
    url = '/catalog/webapi/v1/proof_list/update_item_proof_list?item_code=' + itemId + '&is_revision=true'
  }
  return request({
    // url: '/proof_list/update_proof_list',
    // url: '/proof_list/update_item_proof_list?item_code=' + itemId,
    url: url,
    method: 'post',
    data: param,
    headers: { 'Content-Type': 'application/json' }
  })
}

/**
 * 承诺书文件上传url
 * @param param
 * @returns {AxiosPromise}
 */
export function proofListCommitAttachmentt(file) {
  return request({
    url: '/catalog/webapi/v1/proof_list/commit_attachment',
    method: 'post',
    data: file,
    headers: { 'Content-Type': 'multipart/form-data' }
  })
}

/**
 * 事项对比管理-事项对比清单分页
 * @param param
 * @returns {AxiosPromise}
 */
export function getComparePage(data) {
  return request({
    url: '/item/webapi/v1/record/compare/page',
    method: 'post',
    data: data,
    headers: { 'Content-Type': 'application/json' }
  })
}

/**
 * 事项对比管理-事项对比清单详情
 * @param param
 * @returns {AxiosPromise}
 */
export function getCompareDetail(data) {
  return request({
    url: '/item/webapi/v1/record/compare/detail',
    method: 'post',
    data: data,
    headers: { 'Content-Type': 'application/json' }
  })
}

/**
 * 事项对比管理-事项对比清单详情-更新
 * @param param
 * @returns {AxiosPromise}
 */
export function updateCompare(data) {
  return request({
    url: '/item/webapi/v1/record/compare/detail/update',
    method: 'post',
    data: data,
    headers: { 'Content-Type': 'application/json' }
  })
}

/**
 * 事项对比管理-事项对比清单-导出
 * @param param
 * @returns {AxiosPromise}
 */
export function exportCompareList(data) {
  return request({
    url: '/item/webapi/v1/record/compare/export',
    method: 'post',
    data: data,
    headers: { 'Content-Type': 'application/json' }
  })
}

/**
 * 事项对比管理-事项对比清单-最近同步时间
 * @param param
 * @returns {AxiosPromise}
 */
export function getCompareMaxDate(data) {
  return request({
    url: '/item/webapi/v1/record/compare/max_date',
    method: 'post',
    data: data,
    headers: { 'Content-Type': 'application/json' }
  })
}
