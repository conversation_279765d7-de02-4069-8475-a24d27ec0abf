import request from '@/api/requests/proofDerateRequest'

/**
 * 事项清单分页查询
 * @param actualize 实施区划
 * @returns {AxiosPromise}
 */
export function getOProofListPage(param = {}) {
  return request({
    url: '/catalog/webapi/v1/proof_list/page',
    method: 'get',
    params: param
  })
}

/**
 * 事项清单分页查询
 * @param actualize 实施区划
 * @returns {AxiosPromise}
 */
export function getArchiveProofListPage(param = {}) {
  return request({
    url: '/catalog/webapi/v1/proof_list/archive/page',
    method: 'get',
    params: param
  })
}

/**
 * 事项清单详情
 * @param actualize 实施区划
 * @returns {AxiosPromise}
 */
export function getProofListFormView(id, param = {}) {
  return request({
    url: '/catalog/webapi/v1/proof_list/formView/' + id,
    method: 'get',
    params: param,
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })
}

/**
 * 事项清单详情-新
 * @param actualize 实施区划
 * @returns {AxiosPromise}
 */
export function getProofListFormViewCode(id, param = {}) {
  return request({
    url: '/catalog/webapi/v1/proof_list/formViewCode/' + id,
    method: 'get',
    params: param,
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })
}

/**
 * 根据证明清单id和证明目录id，获取替换后的证明清单
 * @param actualize
 * @returns {AxiosPromise}
 */
export function getCatalogReplace(param = {}) {
  return request({
    url: '/catalog/webapi/v1/proof_list/catalog_replace',
    method: 'get',
    params: param
  })
}
/**
 * 事项清单详情初稿
 * @param actualize 实施区划
 * @returns {AxiosPromise}
 */
export function getProofListFirstDrafts(id, param = {}) {
  return request({
    url: '/preliminary_drafts/view/' + id,
    method: 'get',
    params: param,
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })
}

/**
 * 事项清单详情初稿-新
 * @param itemCode 事项item_code
 * @returns {AxiosPromise}
 */
export function getProofListFirstDraftsView(itemCode) {
  return request({
    url: '/catalog/webapi/v1/preliminary_drafts/viewByCode/' + itemCode,
    method: 'get'
  })
}

/**
 * 证照跳转
 * @param code 证照代码
 * @returns {AxiosPromise}
 */
export function goLicenseItemView(code) {
  return request({
    url: '/catalog/webapi/v1/proof_list/licenseItemView/' + code,
    method: 'get'
  })
}

/**
 * 电子证明跳转
 * @param code 电子证明跳转
 * @returns {AxiosPromise}
 */
export function licenseProofItemView(code) {
  return request({
    url: '/catalog/webapi/v1/proof_list/license_proof_item_view/' + code,
    method: 'get'
  })
}

/**
 * 事项清单梳理确认
 * @param data
 * @returns {AxiosPromise}
 */
export function proofListConfirmCreate(param = {}) {
  return request({
    url: '/catalog/webapi/v1/proof_list_confirm/create',
    method: 'post',
    data: param
  })
}

/**
 * 事项清单梳理确认-新
 * @param data
 * @returns {AxiosPromise}
 */
export function proofListConfirmCreateByCode(param = {}) {
  return request({
    url: '/catalog/webapi/v1/proof_list_confirm/createByCode',
    method: 'post',
    data: param
  })
}

/**
 * 事项清单仍需清理-新
 * @param data
 * @returns {AxiosPromise}
 */
export function proofListConfirmRemainClear(itemCode, param = {}) {
  return request({
    url: '/catalog/webapi/v1/proof_list_confirm/remain_clear_item?item_code=' + itemCode,
    method: 'post',
    data: param,
    headers: { 'Content-Type': 'application/json' }
  })
}

/**
 * 获取创建清单记录
 * @param proof_list_id	 事项清单id
 * @returns {AxiosPromise}
 */
export function getProofSubmitInfo(itemCode, param = {}) {
  return request({
    url: '/proof_list_submit/submit_info',
    method: 'get',
    params: param
  })
}

/**
 * 获取梳理记录
 * @param proof_list_id	 事项清单id
 * @returns {AxiosPromise}
 */
export function getProofConfirmInfo(param = {}) {
  return request({
    url: '/catalog/webapi/v1/proof_list_confirm/confirm_info',
    method: 'get',
    params: param
  })
}

/**
 * 获取审核记录
 * @param proof_list_id	 事项清单id
 * @returns {AxiosPromise}
 */
export function getProofAuditInfo(param = {}) {
  return request({
    url: '/proof_list_audit/audit_info',
    method: 'get',
    params: param
  })
}

/**
 * 获取创建清单记录-新
 * @param item_code	 事项id
 * @returns {AxiosPromise}
 */
export function getProofSubmitInfoByCode(param = {}) {
  return request({
    url: '/proof_list_submit/submit_info_by_code',
    method: 'get',
    params: param
  })
}

/**
 * 获取梳理记录-新
 * @param item_code	 事项id
 * @returns {AxiosPromise}
 */
export function getProofConfirmInfoByCode(param = {}) {
  return request({
    url: '/catalog/webapi/v1/proof_list_confirm/confirm_info_by_code',
    method: 'get',
    params: param
  })
}

/**
 * 获取审核记录-新
 * @param item_code	 事项id
 * @returns {AxiosPromise}
 */
export function getProofAuditInfoByCode(param = {}) {
  return request({
    url: '/proof_list_audit/audit_info_by_code',
    method: 'get',
    params: param
  })
}

/**
 * 获取审核记录
 * @param param
 * @returns {AxiosPromise}
 */
export function exportProofList(param = {}) {
  return '/catalog/webapi/v1/proof_list/export_proof_list'
  /* return request({
    url: '/catalog/webapi/v1/proof_list/export_proof_list',
    method: 'get',
    responseType: 'blob',
    params: param,
    timeout: 1000 * 60 * 15 // 15分钟
  }) */
}

/**
 * 根据事项编码获取提交的清单记录
 * @param param
 * @returns {AxiosPromise}
 */
export function getProcedureLog(itemCode) {
  return request({
    url: `/catalog/webapi/v1/procedure_log/code/${itemCode}`,
    method: 'get'
  })
}
