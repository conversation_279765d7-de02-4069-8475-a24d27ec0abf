import request from '@/api/requests/proofDerateRequest'
/**
 * 免证办服务-一键核验
 * @param param
 * @returns {AxiosPromise}
 */
export function oneKeyValidate(data) {
  return request({
    url: `/exempt/webapi/v1/exempt_certificates/one_key_validate`,
    method: 'post',
    data: data
    // headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })
}

/**
 * 免证办服务，处理
 * @param data
 * @returns {AxiosPromise}
 */
export function handle(data) {
  return request({
    url: '/exempt/webapi/v1/exempt_certificates/manager_service/handle',
    method: 'post',
    data: data
  })
}

/**
 * 免证办服务查询事项接口
 * @param param
 * @returns {AxiosPromise}
 */
export function getServiceItemPage(param) {
  return request({
    url: '/exempt/webapi/v1/exempt_certificates/service_item_page',
    method: 'get',
    params: param
  })
}
/**
 * 免证办管理查询分页接口
 * @param param
 * @returns {AxiosPromise}
 */
export function getManagerPage(data) {
  return request({
    url: '/exempt/webapi/v1/exempt_certificates/manager_page',
    method: 'post',
    data: data,
    headers: { 'Content-Type': 'application/json' }
  })
}
/**
 * 免证办管理查看详情
 * @param param
 * @returns {AxiosPromise}
 */
export function getManagerDetail(serial_number) {
  return request({
    url: `/exempt/webapi/v1/exempt_certificates/manager_detail/${serial_number}`,
    method: 'get'
  })
}

/**
 * 免证办管理，处理
 * @param param
 * @returns {AxiosPromise}
 */
export function managerHandle(param = {}, serial_number) {
  return request({
    url: `/v1/exempt_certificates/manager_handle/${serial_number}`,
    method: 'get',
    params: param,
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })
}

/**
 * 免证办管理详情，归档
 * @param param
 * @returns {AxiosPromise}
 */
export function attachmentArchiving(param, serial_number) {
  return request({
    url: `/exempt/webapi/v1/exempt_certificates/manager/license/attachment_archiving/${serial_number}`,
    method: 'get',
    params: param
  })
}

/**
 * 免证办管理详情，电子证照查看
 * @param param
 * @returns {AxiosPromise}
 */
export function getView(param, serial_number) {
  return request({
    url: `/exempt/webapi/v1/exempt_certificates/manager/license/view/${serial_number}`,
    method: 'get',
    params: param
  })
}

/**
 * 免证办管理详情,文件上传
 * @param param
 * @returns {AxiosPromise}
 */
export function uploadFile(data) {
  return request({
    url: `/exempt/webapi/v1/exempt_certificates/manager/upload_file`,
    method: 'POST',
    data: data,
    headers: { 'Content-Type': 'multipart/form-data' }
  })
}

/**
 * 免证办管理详情,文件上传
 * @param param
 * @returns {AxiosPromise}
 */
export function holdUp(data) {
  return request({
    url: `/exempt/webapi/v1/exempt_certificates/manager_service/hold_up`,
    method: 'POST',
    data: data
  })
}
/**
 * 免证办管理-替代方式文件下载接口
 * @param param
 * @returns {AxiosPromise}
 */
export function download(param) {
  return request({
    url: `/exempt/webapi/v1/exempt_certificates/manager/attachment/download`,
    method: 'get',
    params: param
  })
}

/**
 * 免证办服务-一键核验上下文流程（第一步）
 * @param param
 * @returns {AxiosPromise}
 */
export function oneKeyValidateContext(param) {
  return request({
    url: `/exempt/webapi/v1/exempt_certificates/one_key_validate_context`,
    method: 'POST',
    params: param,
    timeout: 1000 * 60 * 10
  })
}
/**
 * 根据关联日志获取日志记录
 * @param param
 * @returns {AxiosPromise}
 */
export function association(association) {
  return request({
    url: `/catalog/webapi/v1/procedure_log/operator_log/association/${association}`,
    method: 'GET'
  })
}

/**
 * 免证办服务，数据共享方式，查看数据
 * @param param
 * @returns {AxiosPromise}
 */
export function getDataSharedData(data) {
  return request({
    url: `/exempt/webapi/v1/exempt_certificates/manager_service/get_data_shared_data`,
    method: 'POST',
    data: data
  })
}

/**
 * 免证办服务，提交开具电子证明
 * @param data
 * @returns {AxiosPromise}
 */
export function issueLicenseApply(data) {
  return request({
    url: `/exempt/webapi/v1/exempt_certificates/issue_license_apply`,
    method: 'POST',
    data: data,
    headers: { 'Content-Type': 'application/json' }
  })
}

/**
 * 查看电子证明
 * @param data
 * @returns {AxiosPromise}
 */
export function getLicenseViewUrl(auth_code) {
  return request({
    url: `/exempt/webapi/v1/exempt_certificates/get_license_view_url/${auth_code}`,
    method: 'GET',
    headers: { 'Content-Type': 'application/json' }
  })
}

/**
 * 免证办管理详情，粤省事/粤商通 电子证照查看
 * @param data
 * @returns {AxiosPromise}
 */
export function getYssYstView(serial_number, param) {
  return request({
    url: `/exempt/webapi/v1/exempt_certificates/manager/license/yss_yst/view/${serial_number}`,
    method: 'GET',
    params: param
    // headers: { 'Content-Type': 'application/json' }
  })
}

/**
 * 免证办管理详情，粤省事/粤商通 一键归档
 * @param data
 * @returns {AxiosPromise}
 */
export function getViewPdf(serial_number, param) {
  return request({
    url: `/exempt/webapi/v1/exempt_certificates/manager/license/yss_yst/view_pdf/${serial_number}`,
    method: 'GET',
    params: param
    // headers: { 'Content-Type': 'application/json' }
  })
}

/**
 * 免证办管理详情，粤省事/粤商通 电子证照编辑
 * @param data
 * @returns {AxiosPromise}
 */
export function yssYstUpdate(serial_number, param, data) {
  return request({
    url: `/exempt/webapi/v1/exempt_certificates/manager/license/yss_yst/update/${serial_number}`,
    method: 'POST',
    params: param,
    data: data
    // headers: { 'Content-Type': 'application/json' }
  })
}

/**
 * 用证日志分页查询
 * @param data
 * @returns {AxiosPromise}
 */
export function getUseViewLogPage(param) {
  return request({
    url: `/exempt/webapi/v1/common/use_view_log/page`,
    method: 'GET',
    params: param
  })
}
/**
 * 用证日志详情查询
 * @param data
 * @returns {AxiosPromise}
 */
export function getUseViewLogDetail(param) {
  return request({
    url: `/exempt/webapi/v1/common/use_view_log/detail`,
    method: 'GET',
    params: param
  })
}

/**
 * 免证办服务，获取数据共享配置
 * @param data
 * @returns {AxiosPromise}
 */
export function getDataSharedConfig(param) {
  return request({
    url: `/exempt/webapi/v1/exempt_certificates/manager_service/get_data_shared_config`,
    method: 'GET',
    params: param
  })
}

/**
 * 免证办服务，数据共享方式，归档pdf
 * @param data
 * @returns {AxiosPromise}
 */
export function getDataSharedDataArchivist(data) {
  return request({
    url: `/exempt/webapi/v1/exempt_certificates/manager_service/get_data_shared_data_archivist`,
    method: 'POST',
    data: data
  })
}

/**
 * 免证办服务-归档-生成zip文件
 * @param data
 * @returns {AxiosPromise}
 */
export function getReplaceWayArchivist(params) {
  return request({
    url: `/exempt/webapi/v1/exempt_certificates/manager_service/get_replace_way_archivist`,
    method: 'POST',
    params: params,
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })
}

/**
 * 免证办服务，部门协查文件获取
 * @param data
 * @returns {AxiosPromise}
 */
export function getInvestigationArchivist(data) {
  return request({
    url: `/exempt/webapi/v1/common/investigation/archiving`,
    method: 'POST',
    data: data
  })
}

/**
 * 免证办服务，电子证明归档获取
 * @param data
 * @returns {AxiosPromise}
 */
export function getLicenseItemArchivist(data) {
  return request({
    url: `/exempt/webapi/v1/common/license_item/archiving`,
    method: 'POST',
    data: data
  })
}
