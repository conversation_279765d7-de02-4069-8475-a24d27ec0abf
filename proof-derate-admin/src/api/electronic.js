import request from '@/api/requests/proofDerateRequest'

/*
电子证明
*/

// 证明档案-获取证明列表
export function getArchivesList(data) {
  return request({
    url: '/license_proof/webapi/v1/common/proof_archives/page',
    method: 'post',
    data
  })
}

// 证明档案-获取证明详情
export function getArchivesDetail(params) {
  return request({
    url: '/license_proof/webapi/v1/common/proof_archives/view',
    method: 'get',
    params
  })
}


// 证明档案-获取证明详情(非脱敏)
export function getArchivesDetailNonEncrypted(params) {
  return request({
    url: '/license_proof/webapi/v1/common/proof_archives/non_encrypted_view',
    method: 'get',
    params,
  })
}

// 证明档案-下载电子证明
// 下载证照文件
/* export function downProofArchives(versions = '') {
  return `/license_proof/webapi/v1/common/proof_archives/download`
} */
export function downProofArchives(data) {
  return request({
    url: '/license_proof/webapi/v1/common/proof_archives/download',
    method: 'post',
    data
  })
}
