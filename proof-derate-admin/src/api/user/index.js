import request from '@/api/requests/proofDerateRequest'

/* 用户管理 > 分页查询列表 */
export function getAccountPage(data) {
  return request({
    url: 'user/webapi/v1/account_info/page',
    method: 'POST',
    data: data
  })
}

/* 查询所有账号 */
export function getAllAccount() {
  return request({
    url: 'user/webapi/v1/account_info/get_all_account',
    method: 'GET'
  })
}

/* 用户管理 > 新增用户信息 */
export function create(data) {
  return request({
    url: 'user/webapi/v1/account_info/create',
    method: 'POST',
    data: data
  })
}

/* 用户管理 > 编辑用户信息 */
export function edit(data) {
  return request({
    url: 'user/webapi/v1/account_info/edit',
    method: 'POST',
    data: data
  })
}

/* 用户管理 > 删除用户信息 */
export function deleteAccountInfo(id) {
  return request({
    url: `user/webapi/v1/account_info/delete/${id}`,
    method: 'POST'
  })
}

/* 用户管理 > 查看详情 */
export function getView(id, params) {
  return request({
    url: `user/webapi/v1/account_info/view/${id}`,
    method: 'GET',
    params: params
  })
}
