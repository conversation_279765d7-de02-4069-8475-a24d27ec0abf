import request from '@/api/requests/proofDerateRequest'
/**
 * 同步记录列表页
 * @param param
 * @returns {AxiosPromise}
 */
export function synchronizeList(params) {
  return request({
    url: '/catalog/webapi/v1/synchronize/list',
    method: 'GET',
    params: params
  })
}
/**
 * 同步记录详情页
 * @param param
 * @returns {AxiosPromise}
 */
export function synchronizeDetails(params) {
  return request({
    url: '/catalog/webapi/v1/synchronize/details',
    method: 'GET',
    params: params
  })
}

/**
 * 获取同步的操作日志
 * @param param
 * @returns {AxiosPromise}
 */
export function getLog(params) {
  return request({
    url: '/catalog/webapi/v1/synchronize/operator/journal',
    method: 'GET',
    params: params
  })
}
/**
 * 同步操作
 * @param param
 * @returns {AxiosPromise}
 */
export function operate(params) {
  return request({
    url: '/catalog/webapi/v1/synchronize/operate',
    method: 'POST',
    params: params
  })
}
/**
 * 忽略操作
 * @param param
 * @returns {AxiosPromise}
 */
export function ignore(params) {
  return request({
    url: '/catalog/webapi/v1/synchronize/ignore',
    method: 'POST',
    params: params
  })
}
/**
 * 更新操作
 * @param param
 * @returns {AxiosPromise}
 */
export function update(params) {
  return request({
    url: '/catalog/webapi/v1/synchronize/update',
    method: 'POST',
    params: params
  })
}

/**
 * 根据请求参数查询总条数
 * @param param
 * @returns {AxiosPromise}
 */
export function listCount(params) {
  return request({
    url: '/catalog/webapi/v1/synchronize/listCount',
    method: 'get',
    params: params
  })
}
