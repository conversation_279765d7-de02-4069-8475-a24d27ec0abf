import request from '@/api/requests/proofDerateRequest'
/**
 * 上传事项证明材料导入
 * @param param
 * @returns {AxiosPromise}
 */
export function commit_attachment(file) {
  return request({
    url: '/item/webapi/v1/commit_attachment',
    method: 'post',
    data: file,
    headers: { 'Content-Type': 'multipart/form-data' }
  })
}

/**
 * 上传事项证明材料(材料空白表格、材料样例)
 * @param param
 * @returns {AxiosPromise}
 */
export function prove_attachment(file) {
  return request({
    url: '/item/webapi/v1/manager/commit_attachment',
    method: 'post',
    data: file,
    headers: { 'Content-Type': 'multipart/form-data' }
  })
}

/**
 *新增证明目录
 * @param param
 * @returns {AxiosPromise}
 */
export function itemProveCreate(param) {
  return request({
    url: '/item/webapi/v1/manager/create',
    method: 'post',
    data: param
  })
}

/**
 *编辑 证明目录
 * @param param
 * @returns {AxiosPromise}
 */
export function itemProveEdit(id, param = {}) {
  return request({
    url: '/item/webapi/v1/manager/edit/' + id,
    method: 'post',
    data: param
  })
}

/**
* 下载事项证明材料导入模板
* @param param
* @returns {AxiosPromise}
*/
export function down_import_template(param) {
  return request({
    url: '/item/webapi/v1/down_import_template',
    method: 'get',
    data: param
  })
}

/**
* 事项列表查询
* @param param
* @returns {AxiosPromise}
*/
export function list(param) {
  return request({
    url: '/item/webapi/v1/list',
    method: 'get',
    data: param
  })
}

/**
* 事项分页查询
* @param actualize
* @returns {AxiosPromise}
*/
export function page(param = {}) {
  return request({
    url: '/item/webapi/v1/manager/page',
    method: 'get',
    params: param
  })
}

/**
 * 查询 根据证明目录id，获取证明目录以及所有的关联的数据
 * @param id 事项清单id
 * @returns {AxiosPromise}
 */
export function getProveInfoById(id) {
  return request({
    url: '/item/webapi/v1/manager/' + id,
    method: 'get'
  })
}

/**
 *查询 根据证明目录id，获取证明目录更变日志
 * @param id
 * @returns {AxiosPromise}
 */
export function getProveChangeLog(id) {
  return request({
    url: '/item/webapi/v1/manager/change_log/' + id,
    method: 'get'
  })
}
