import request from '@/api/requests/proofDerateRequest'

/**
 * 承诺书下载
 * @param param
 * @returns {AxiosPromise}
 */
export function getAttachmentBase64(param) {
  return request({
    url: '/proof_list/down_attachment',
    method: 'get',
    params: param
  })
}

/**
 * 初稿承诺书下载
 * @param param
 * @returns {AxiosPromise}
 */
export function getPreliminaryDraftsAttachment(param) {
  return request({
    url: '/preliminary_drafts/down_attachment',
    method: 'get',
    params: param
  })
}

/**
 * 证明材料管理，下载材料样例文件接口
 * @param param
 * @returns {AxiosPromise}
 */
export function downloadSampleFile(param) {
  return request({
    url: '/item/manager/download_sample_file',
    method: 'get',
    params: param
  })
}

/**
 * 证明材料管理，下载材料空白表格接口
 * @param param
 * @returns {AxiosPromise}
 */
export function downloadBlankFile(param) {
  return request({
    url: '/item/manager/download_blank_file',
    method: 'get',
    params: param
  })
}

/**
 * 文件下载
 * @param param
 * @returns {AxiosPromise}
 */
export function downloadFile(params) {
  return request({
    url: '/common/webapi/v1/download_file',
    method: 'POST',
    params: params,
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })
}
