import request from '@/api/requests/proofDerateRequest'

/**
 * 获取证照列表
 * @param license_item_name
 * @returns {AxiosPromise}
 */
export function getLicenseItemName(param) {
  return request({
    url: '/common/license_item/name',
    method: 'get',
    params: param
  })
}

/**
 * 证照跳转
 * @param code 证照代码
 * @returns {AxiosPromise}
 */
export function goLicenseItemView(code) {
  return request({
    url: '/proof_list/licenseItemView/' + code,
    method: 'get'
  })
}

/**
 * 根据全省目录名称，获取全省目录名称与基本码与行业部门
 * @param license_item_name
 * @returns {AxiosPromise}
 */
export function getIndustryDept(param) {
  return request({
    url: '/common/webapi/v1/license_item/industry_dept',
    method: 'get',
    params: param
  })
}

/**
 * 获取电子证明系统跳转地址
 * @param param
 * @returns {AxiosPromise}
 */
export function getProofLoginLicenseUrl() {
  return request({
    url: '/license/webapi/v1/common/proof_login_license_url',
    method: 'get'
  })
}
