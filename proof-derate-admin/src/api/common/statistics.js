import request from '@/api/requests/proofDerateRequest'

/**
 * 首页统计-取消证明情况统计
 * @returns {AxiosPromise}
 */
export function getProofListStat() {
  return request({
    url: '/index/webapi/v1/proof_list_stat/index',
    method: 'get'
  })
}

/**
 * 首页统计-事项梳理情况统计
 * @returns {AxiosPromise}
 */
export function getItemStat() {
  return request({
    url: '/index/webapi/v1/item_stat/index',
    method: 'get'
  })
}

/**
 * 首页统计-待办事项情况统计
 * @returns {AxiosPromise}
 */
export function getProofListStatItemIndex() {
  return request({
    url: '/index/webapi/v1/proof_list_stat/item_index',
    method: 'get'
  })
}

/**
 * 首页统计-区域清理事项情况
 * @returns {AxiosPromise}
 */
export function getCleanStatusTopFiveArea() {
  return request({
    url: '/index/webapi/v1/item_stat/clean_status_top_five_area',
    method: 'get'
  })
}

/**
 * 首页统计免证替代方式
 * @returns {AxiosPromise}
 */
export function getReplaceWayTopFive() {
  return request({
    url: '/index/webapi/v1/proof_list_stat/replace_way_top_five',
    method: 'get'
  })
}

/**
 * 首页协查任务列表
 * @returns {AxiosPromise}
 */
export function getAssistInvestigate(param) {
  return request({
    url: '/assist/webapi/v1/assist_investigate_handle/page',
    method: 'get',
    params: param
  })
}
/**
 * 首页统计-待办事项情况统计
 * @returns {AxiosPromise}
 */
export function getWaitForCleanItem() {
  return request({
    url: '/index/webapi/v1/proof_list_stat/wait_for_clean_item_index',
    method: 'get'
  })
}

/**
 * 首页协查任务统计
 * @returns {AxiosPromise}
 */
export function getAssistTask() {
  return request({
    url: '/index/webapi/v1/proof_list_stat/assist_task_index',
    method: 'get'
  })
}

/**
 * 首页统计-待确认事项情况统计
 * @returns {AxiosPromise}
 */
export function getCardingUnconfirmedItem() {
  return request({
    url: '/index/webapi/v1/proof_list_stat/carding_unconfirmed_item_index',
    method: 'get'
  })
}

/**
 * 首页统计-首页统计-事项审核情况统计
 * @returns {AxiosPromise}
 */
export function getApprovedItem() {
  return request({
    url: '/index/webapi/v1/proof_list_stat/approved_item_index',
    method: 'get'
  })
}
