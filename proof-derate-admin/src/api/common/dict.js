import request from '@/api/requests/proofDerateRequest'
import adminRequest from '@/api/requests/adminRequest'

/**
 * 获取实施区划列表
 * @param param
 * @returns {AxiosPromise}
 */
export function getActualizeList(param = {}) {
  return request({
    url: '/common/webapi/v1/division',
    method: 'get'
  })
}

/**
 * 通过实施区划查询实施机构
 * @param actualize 实施区划
 * @returns {AxiosPromise}
 */
export function getOrganizationList(actualize) {
  return request({
    url: '/common/webapi/v1/organization',
    method: 'get'
  })
}

/**
 * 获取事项标准类型
 * @param param
 * @returns {AxiosPromise}
 */
export function getStandardTypeList(param = {}) {
  return request({
    url: '/common/webapi/v1/item/standard_type_by_catalog',
    method: 'get'
  })
}

/**
 * 获取事项标准类型
 * @param param
 * @returns {AxiosPromise}
 */
export function getStandardTypeListCatalog(param = {}) {
  return request({
    url: '/common/webapi/v1/item/standard_type_by_catalog',
    method: 'get'
  })
}

/**
 * 获取事项类型
 * @param param
 * @returns {AxiosPromise}
 */
export function getMattersTypeList(param = {}) {
  return request({
    url: '/common/webapi/v1/item/type',
    method: 'get'
  })
}

/**
 * 获取事项证明状态
 * @param param
 * @returns {AxiosPromise}
 */
export function getProofStatusList(param = {}) {
  return request({
    url: '/common/webapi/v1/item/proof_type',
    method: 'get'
  })
}

/**
 * 获取事项类型
 * @param param
 * @returns {AxiosPromise}
 */
export function getItemTypeList(param = {}) {
  return request({
    url: '/common/webapi/v1/item/type',
    method: 'get'
  })
}

/**
 * 获取所属行业部门列表
 * @param param
 * @returns {AxiosPromise}
 */
export function getDepartmentList(param = {}) {
  return request({
    url: '/common/webapi/v1/organization/industry',
    method: 'get',
    params: param
  })
}

/**
 * 获取证明开具单位类型
 * @param param
 * @returns {AxiosPromise}
 */
export function getUnitTypeList(param = {}) {
  return request({
    url: '/common/webapi/v1/organization/proof_provide_type',
    method: 'get',
    params: param
  })
}

/**
 * 获取证明清理类型
 * @param param
 * @returns {AxiosPromise}
 */
export function getProofClearType() {
  return request({
    url: '/common/webapi/v1/proof_list/proof_clear_type',
    method: 'get'
  })
}

/**
 * 获取替代取消方式
 * @param param
 * @returns {AxiosPromise}
 */
export function getReplaceCancelWay() {
  return request({
    url: '/common/webapi/v1/proof_list/replace_cancel_way',
    method: 'get'
  })
}

/**
 * 获取材料类型
 * @param param
 * @returns {AxiosPromise}
 */
export function getItemMaterialType() {
  return request({
    url: '/common/webapi/v1/item_material/material_type',
    method: 'get'
  })
}
/**
 * 获取材料标准
 * @param param
 * @returns {AxiosPromise}
 */
export function getIMaterialSource() {
  return request({
    url: '/common/webapi/v1/item_material/material_source',
    method: 'get'
  })
}

/**
 * 获取证明标准类型
 * @param param
 * @returns {AxiosPromise}
 */
export function getItemMaterialStandardType() {
  return request({
    url: '/common/webapi/v1/item_material/standard_type_by_catalog',
    method: 'get'
  })
}

/**
 * 获取证明标准类型(查询条件)
 * @param param
 * @returns {AxiosPromise}
 */
export function getItemMaterialStandardTypeByCatalog() {
  return request({
    url: '/common/webapi/v1/item_material/standard_type_by_catalog',
    method: 'get'
  })
}

// 获取单位
export function getCodeItemList(params) {
  return adminRequest({
    url: '/code/list',
    method: 'post',
    data: {
      pageNumber: 0,
      pageSize: 10,
      standardNumber: '',
      name: '行业部门'
    },
    headers: { 'Content-Type': 'application/json' }
  })
}

// 行业部门查询列表接口
export function getCodeItemListByCodeId(params) {
  return adminRequest({
    url: '/codeItem/listByCodeId',
    method: 'post',
    data: params,
    headers: { 'Content-Type': 'application/json' }
  })
}
/**
 * 获取免证办证件类型
 * @param param
 * @returns {AxiosPromise}
 */
export function getExemptIdentityType() {
  return request({
    url: '/common/webapi/v1/exempt_identity_type',
    method: 'get'
  })
}

/**
 * 获取企业证件类型
 * @param param
 * @returns {AxiosPromise}
 */
export function getEnterpriseIdentityType() {
  return request({
    url: '/common/webapi/v1/enterprise_identity_type',
    method: 'get'
  })
}

/**
 * 获取当前账号和用户信息
 * @param param
 * @returns {AxiosPromise}
 */
export function getCurrentAccount() {
  return request({
    url: '/common/webapi/v1/account_and_info/current_account',
    method: 'get'
  })
}

/**
 * 获取事项同步状态
 * @param param
 * @returns {AxiosPromise}
 */
export function getSynchronizeStatus() {
  return request({
    url: '/common/webapi/v1/synchronize_status',
    method: 'get'
  })
}

/**
 * 获取证明目录-数据共享-值1
 * @param param
 * @returns {AxiosPromise}
 */
export function getConditionKeySelectItem() {
  return request({
    url: '/common/webapi/v1/data_shared/condition_key_select_item',
    method: 'get'
  })
}

/**
 * 获取主题显示排序规则枚举
 * @param param
 * @returns {AxiosPromise}
 */
export function getDataSharedListSortRule() {
  return request({
    url: '/common/webapi/v1/catalog/enums/data_shared_list_sort_rule',
    method: 'get'
  })
}

/**
 * 获取主题显示排序类型
 * @param param
 * @returns {AxiosPromise}
 */
export function getDataSharedListSortType() {
  return request({
    url: '/common/webapi/v1/catalog/enums/data_shared_list_sort_type',
    method: 'get'
  })
}

/**
 * 获取证明目录-数据共享-条件操作符
 * @param param
 * @returns {AxiosPromise}
 */
export function getQueryFilterOperationSelectItem() {
  return request({
    url: '/common/webapi/v1/data_shared/query_filter_operation_select_item',
    method: 'get'
  })
}

/**
 * 获取证件类型
 * @param param
 * @returns {AxiosPromise}
 */
export function accountInfoIdentityType() {
  return request({
    url: '/common/webapi/v1/account_info_identity_type',
    method: 'get'
  })
}

/**
 * 获取行政区划相关部门树形结构
 * @param param
 * @returns {AxiosPromise}
 */
export function getOrgTreeList(data) {
  console.log('params',data)
  return request({
    url: '/common/webapi/v1/get_org_tree_list',
    method: 'get',
    params: data
  })
}
/**
 * 上传文件获取文件id
 * @param param
 * @returns {AxiosPromise}
 */
export function uploadFile(params,data) {
  console.log('params',data)
  return request({
    url: '/common/webapi/v1/upload_file',
    method: 'POST',
    params: params,
    data: data
  })
}
