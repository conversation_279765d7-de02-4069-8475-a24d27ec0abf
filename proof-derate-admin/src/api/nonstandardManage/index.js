import request from '@/api/requests/proofDerateRequest'

/**
 * 查询
 * @param param
 * @returns {AxiosPromise}
 */
export function restList(param) {
  return request({
    url: '/customWzmDocument/restList',
    method: 'post',
    data: param
  })
}
/**
 * 修改
 * @param param
 * @returns {AxiosPromise}
 */
export function editFbDocument(param) {
  return request({
    url: '/customWzmDocument/editFbDocument',
    method: 'post',
    data: param
  })
}
/**
 * 删除
 * @param param
 * @returns {AxiosPromise}
 */
export function deleteFbSubject(param) {
  return request({
    url: '/customWzmDocument/deleteFbSubject',
    method: 'get',
    params: param
  })
}
/**
 * 删除证明材料
 * @param param
 * @returns {AxiosPromise}
 */
export function deleteDocument(param) {
  return request({
    url: '/customWzmDocument/deleteDocument',
    method: 'get',
    params: param
  })
}

