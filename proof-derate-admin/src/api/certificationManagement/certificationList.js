import request from '@/api/requests/proofDerateRequest'

/**
 * 事项分页查询
 * @param actualize
 * @returns {AxiosPromise}
 */
export function getGetproofCatalogPage(param = {}) {
  return request({
    url: '/catalog/webapi/v1/proof_catalog/page',
    method: 'get',
    params: param
  })
}

/**
 *新增证明目录
 * @param param
 * @returns {AxiosPromise}
 */
export function getGetproofCatalogCreate(param) {
  return request({
    url: '/catalog/webapi/v1/proof_catalog/create',
    method: 'post',
    data: param
  })
}

/**
 *编辑 证明目录
 * @param param
 * @returns {AxiosPromise}
 */
export function getGetproofCatalogEdit(id, param = {}) {
  return request({
    url: '/catalog/webapi/v1/proof_catalog/edit/' + id,
    method: 'post',
    data: param
  })
}

/**
 * 查询 根据证明目录id，获取证明目录以及所有的关联的数据
 * @param id 事项清单id
 * @returns {AxiosPromise}
 */
export function getGetproofCatalogFind(id) {
  return request({
    url: '/catalog/webapi/v1/proof_catalog/find/' + id,
    method: 'get'
  })
}

/**
 *查询 根据证明目录id，获取证明目录更变日志
 * @param id
 * @returns {AxiosPromise}
 */
export function getFind_catalog_log(id) {
  return request({
    url: '/catalog/webapi/v1/proof_catalog/find_catalog_log/' + id,
    method: 'get'
  })
}
/**
 *承诺书文件保存到session
 * @param param
 * @returns {AxiosPromise}
 */
export function getCommit_Attachment(param) {
  return request({
    url: '/catalog/webapi/v1/proof_catalog/commit_attachment',
    method: 'post',
    data: param
  })
}
/**
 *承诺书文件session清除
 * @param param
 * @returns {AxiosPromise}
 */
export function getClean_Commit_Attachment(param) {
  return request({
    url: '/catalog/webapi/v1/proof_catalog/clean_commit_attachment',
    method: 'post',
    data: param,
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })
}
/**
 *下载承诺书文件接口
 * @param param
 * @returns {AxiosPromise}
 */
export function getDown_Attachment(param) {
  return request({
    url: '/catalog/webapi/v1/proof_catalog/down_attachment',
    method: 'get',
    data: param
  })
}

/**
 *分页 证明管理>证明目录管理>证明目录详情>关联证明
 * @param actualize
 * @returns {AxiosPromise}
 */
export function getCatalogRelationPage(param = {}) {
  return request({
    url: '/catalog/webapi/v1/proof_list/catalog_relation_page',
    method: 'get',
    params: param
  })
}
/**
 *下载材料清理excel模板接口
 * @param actualize
 * @returns {AxiosPromise}
 */
export function downloadCleanResultTemplate(param) {
  return request({
    url: '/catalog/webapi/v1/proof_list/download_clean_result_template',
    method: 'get',
    params: param
  })
}

/**
 *导入事项清理结果
 * @param actualize
 * @returns {AxiosPromise}
 */
export function importCleanResultByExcel(data) {
  return request({
    url: '/catalog/webapi/v1/proof_list/import_clean_result_by_excel',
    method: 'POST',
    data: data,
    headers: { 'Content-Type': 'multipart/form-data' }
  })
}

/**
 *查询数据主题分页
 * @param actualize
 * @returns {AxiosPromise}
 */
export function getThemePage(param) {
  return request({
    url: '/catalog/webapi/v1/proof_catalog/data_shared/theme_page',
    method: 'get',
    params: param
  })
}
/**
 *查询数据共享系统查询列表
 * @param actualize
 * @returns {AxiosPromise}
 */
export function getSystemSelectItem(param) {
  return request({
    url: '/catalog/webapi/v1/proof_catalog/data_shared/system_select_item',
    method: 'get',
    params: param
  })
}
/**
 *根据系统编码与数据主题获取配置信息
 * @param actualize
 * @returns {AxiosPromise}
 */
export function getConfigDetail(param) {
  return request({
    url: '/catalog/webapi/v1/proof_catalog/data_shared/config_detail',
    method: 'get',
    params: param
  })
}
/**
 *查看数据共享配置信息
 * @param actualize
 * @returns {AxiosPromise}
 */
export function getConfigDetailByid(catalog_id) {
  return request({
    url: `/catalog/webapi/v1/proof_catalog/data_shared/config_detail/${catalog_id}`,
    method: 'get'
  })
}
