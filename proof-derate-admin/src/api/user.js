import request from '@/api/requests/liccRequest'
import {
  SYSTEM_INFO_API,
  SYSTEM_BG_IMG_API,
  CAPTCHA_API,
  LOGIN
} from '@/utils/constant'
// 获取验证码
export function getCaptcha() {
  return request({
    url: CAPTCHA_API,
    method: 'get'
  })
}

export function login(data) {
  return request({
    url: LOGIN,
    method: 'post',
    data
  })
}

export function getInfo(token) {
  return request({
    url: '/vue-admin-template/user/info',
    method: 'get',
    params: { token }
  })
}

export function logout() {
  return request({
    url: '/logout',
    method: 'get'
  })
}
// 修改初始密码
export function updateInitPassword(data) {
  return request({
    url: '/auth/webapi/v1/common/auth/update_init_password',
    method: 'POST',
    data
  })
}
// 修改非初始密码
export function updatePassword(data) {
  return request({
    url: '/auth/webapi/v1/common/account/update_password',
    method: 'post',
    data
  })
}
// 获取项目配置信息
export function getSystemInfo() {
  return request({
    // url: '/auth/webapi/v1/common/auth/login',
    url: SYSTEM_INFO_API,
    method: 'get'
  })
}
// 获取权限
export function getPermission() {
  return request({
    url: '/auth/webapi/v1/common/auth/menu_per_tree',
    method: 'get'
  })
}

// 获取当前账号信息-用户信息、机构信息、权限tree
export function getCurrentUserInfo() {
  return request({
    url: '/auth/webapi/v1/common/auth/current_account',
    method: 'get'
  })
}

// c
export function getbgImg() {
  return request({
    url: SYSTEM_BG_IMG_API,
    method: 'get'
  })
}

// 粤政易退出登录
export function yzyLogout(account) {
  return request({
    url: `/auth/webapi/v1/yzy/security_center/logout/${account}`,
    method: 'get'
  })
}

// 第三方登录回调换取本系统的token
export function getThirdAuthorization(data) {
  return request({
    url: '/auth/webapi/v1/common/auth/third_party/callback/login',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json',
      'Context-Api-Path': process.env.VUE_APP_BASE_API
    }
  })
}
