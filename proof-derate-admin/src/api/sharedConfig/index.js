import request from '@/api/requests/proofDerateRequest'

/**
 * 证明目录->添加数据共享-》选择数据主题
 * @param param
 * @returns {AxiosPromise}
 */
export function getCatalogDataSharedConfigPage(param) {
  return request({
    url: '/catalog/webapi/v1/proof_derate/catalog_data_shared_config/page',
    method: 'GET',
    params: param
  })
}

/**
 * 创建 数据主题
 * @param data
 * @returns {AxiosPromise}
 */
export function create(data) {
  return request({
    url: '/catalog/webapi/v1/proof_derate/catalog_data_shared_config/create',
    method: 'POST',
    data: data
  })
}

/**
 * 编辑 数据主题
 * @param data
 * @returns {AxiosPromise}
 */
export function edit(data) {
  return request({
    url: '/catalog/webapi/v1/proof_derate/catalog_data_shared_config/edit',
    method: 'POST',
    data: data
  })
}

/**
 * 查询 根据数据共享id，获取数据共享主题配置
 * @param data
 * @returns {AxiosPromise}
 */
export function find(id) {
  return request({
    url: `/catalog/webapi/v1/proof_derate/catalog_data_shared_config/find/${id}`,
    method: 'GET'
  })
}

/**
 * 数据共享主题导入
 * @param data
 * @returns {AxiosPromise}
 */
export function themeImport(data) {
  return request({
    url: `/catalog/webapi/v1/proof_derate/catalog_data_shared_config/import`,
    method: 'POST',
    data: data,
    headers: { 'Content-Type': 'multipart/form-data' }
  })
}

/**
 * 下载数据共享配置模板文件接口
 * @param data
 * @returns {AxiosPromise}
 */
export function downTemplate(param) {
  return request({
    url: `/catalog/webapi/v1/proof_derate/catalog_data_shared_config/down_template`,
    method: 'GET',
    params: param
  })
}

/**
 * 删除根据数据共享id
 * @param data
 * @returns {AxiosPromise}
 */
export function catalogDataSharedConfigDelete(param) {
  return request({
    url: `/catalog/webapi/v1/proof_derate/catalog_data_shared_config/delete`,
    method: 'GET',
    params: param
  })
}
