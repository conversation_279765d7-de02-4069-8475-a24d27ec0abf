<template>
  <div id="papeTitle">
    <div class="content-header title-algin-midle">
      <h1 v-if="titleName">
        <i v-if="isHasBack" class="el-icon-arrow-left" @click="goToList()" />
        <span>{{ titleName }}</span>
      </h1>
      <span>
        <template>
          <slot />
        </template>
      </span>
    </div>
  </div>
</template>

<script>
export default {
  name: 'PapeTitle',
  props: {
    titleName: { type: String, default: '' },
    isHasBack: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {}
  },
  mounted() {},

  methods: {
    goToList() {
      this.$emit('goToList')
    }
  }
}
</script>

<style lang="scss" scoped>
.content-header > h1 {
  padding: 13px 0 9px;
}
.title-algin-midle {
  display: flex;
  align-items: center;
  justify-content: space-between;
  i {
    cursor: pointer;
  }
}
</style>
