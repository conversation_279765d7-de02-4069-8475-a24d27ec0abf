<template>
  <div ref="content" v-resize="visibilityChange" :class="[className, { 'ellipsis-tooltip': tooltipFlag }]">
    <el-tooltip
      ref="tlp"
      v-bind="this.$attrs"
      effect="dark"
      :disabled="!tooltipFlag"
      :placement="placement"
    >
      <template #content>
        <div v-html="text" />
      </template>
      <span :id="uuid" ref="spanText" class="span-text" v-html="text" />
    </el-tooltip>
  </div>
</template>

<script>

export default {
  name: 'EllipsisTooltip',
  props: {
    text: { type: [String, Number], default: '' }, // 字符内容
    placement: { type: String, default: 'top-start' },
    className: { type: String, default: 'text' } // class
  },
  data() {
    return {
      disabledTip: false,
      tooltipFlag: true,
      uuid: this.getUuid()
    }
  },
  methods: {
    // tooltip的可控
    visibilityChange() {
      this.tooltipFlag = true
      this.$nextTick(() => {
        const ev = this.$refs.spanText
        ev.style.display = ''
        let className = ev.getAttribute('class')
        className = className.replace('ellipsis-tooltip', '')
        ev.setAttribute('class', className)
        const ev_width = ev.offsetWidth // 文本的实际宽度
        const content_width = this.$refs.tlp.$el.parentElement.clientWidth // 文本容器宽度
        if (content_width < ev_width) {
          // 实际内容宽度 > 文本宽度 =》内容溢出
          this.tooltipFlag = true // NameIsIncludeWord ? true : !!false
          ev.style.display = 'block'
          ev.style.width = content_width + 'px'
          ev.setAttribute('class', 'ellipsis-tooltip')
        } else {
          // 否则为不溢出
          this.tooltipFlag = false
        }
      })
    },
    getUuid() {
      return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        const r = (Math.random() * 16) | 0
        const v = c === 'x' ? r : (r & 0x3) | 0x8
        return v.toString(16)
      })
    }
  }
}
</script>

<style lang="scss" scoped>
  .ellipsis-tooltip{
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-all;//英文数字折行
    white-space: nowrap;
    padding: 0;
  }
  .span-text{
    padding: 0;
    margin: 0;
  }
</style>
