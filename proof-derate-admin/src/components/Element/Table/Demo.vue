<!-- <template>
  <div class="app-container">
    <custom-table
      ref="table"
      :is-card-type="false"
      :table-data="tableData"
      :table-header="tableHeader"
      :table-tools="tableTools"
      :show-input="true"
      stripe
      input-placeholder="请输入姓名"
      @refresh="query(1)"
      @query="query"
      @delete="deleteData"
    >
      <template #status="{ row }">
        {{ row.status === 1 ? '激活' : '冻结' }}
      </template>
    </custom-table>
  </div>
</template>

<script>
import CustomTable from './Element/Table'

export default {
  name: 'Demo',
  components: {
    CustomTable
  },
  data() {
    return {
      tableData: {
        content: [], // 表格数据
        loading: false, // 控制表格加载效果
        total: 0, // 分页查询时的总数，去掉该属性，不显示分页条
        currentPage: 1, // 当前页码
        pageSize: 15, // 每页显示的数量
        isShowSelection: true // 是否显示多选框，默认false
      },
      // 表格左上方按钮列表，可为空
      tableTools: [
        { name: 'refresh', displayName: '刷新', icon: 'fa fa-refresh' }
      ],
      // 表头配置
      tableHeader: [
        { label: '状态', slot: 'status', prop: 'status', width: '80px' }, // 配置slot属性，可支持使用插槽
        { label: '姓名', prop: 'name', minWidth: '80px' },
        { label: '性别', prop: 'sex', minWidth: '80px',
          formatter: (row, col, val) => {
            return val === 1 ? '男' : '女'
          }
        },
        {
          label: '操作',
          prop: 'operateColumn', // prop为“operateColumn”时，可配置actions按钮列表
          width: '90px',
          fixed: 'right',
          actions: [
            {
              label: '删除',
              action: 'delete' // 按钮该按钮时，派发事件的名称
            }
          ]
        }
      ]
    }
  },
  created() {
    this.query(1)
  },
  methods: {
    // currentPage: 当前页码
    query(currentPage) {
      if (_.isNumber(currentPage)) {
        this.tableData.currentPage = currentPage
      }
      const keyword = this.$refs.table ? this.$refs.table.searchInput : ''
      this.tableData.loading = true
      // 假数据
      setTimeout(() => {
        const data = [
          { id: 1, status: 1, name: '张三', sex: 1 },
          { id: 2, status: 1, name: '李四', sex: 0 },
          { id: 3, status: 0, name: '王麻子', sex: 1 }
        ]
        this.tableData.content = _.filter(data, item => item.name.includes(keyword))
        this.tableData.total = 3
        this.tableData.loading = false
      }, 1000)
    },
    // row: 当前记录的数据
    deleteData(row) {
      const index = _.findIndex(this.tableData, { id: row.id })
      this.tableData.content.splice(index, 1)
    }
  }
}
</script>

<style scoped>

</style> -->
