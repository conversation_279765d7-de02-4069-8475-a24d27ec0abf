<template>
  <el-card :class="[isCardType ? 'gt-table-card' : 'gt-table-box']">
    <div v-if="showTableTitleWrap" class="gt-table-title clearfix">
      <template v-if="tableTitle">
        <i class="gt-table-title-text" />
        {{ tableTitle || "" }}
      </template>
      <div class="gt-func-box">
        <span class="gt-func-buttons">
          <template v-for="item in tableTools">
            <slot v-if="item.slot" :name="item.slot" />
            <template v-else>
              <el-button
                v-if="!item.hidden"
                :key="item.name"
                :disabled="item.disabled"
                :loading="item.loading"
                :type="item.type"
                :size="item.size"
                :code="item.code"
                :plain="item.plain"
                :class="item.className"
                :title="item.title"
                @click="toolClick(item.name)"
              >
                <i :class="item.icon" />
                <span>{{ item.displayName }}</span>
              </el-button>
            </template>
          </template>
        </span>
        <el-form :inline="true" class="gt-form" @submit.native.prevent>
          <el-form-item v-if="showInput">
            <el-input
              v-model="searchInput"
              :style="rightInputStyle"
              prefix-icon="el-icon-search"
              :placeholder="inputPlaceholder"
              clearable
              @change="inputChange"
              @keyup.enter.native="keyupEnter"
            />
          </el-form-item>
          <el-form-item v-if="$slots.tableUpperRight">
            <slot name="tableUpperRight" />
          </el-form-item>
        </el-form>
      </div>
      <slot name="tableTitle" />
    </div>
    <el-table
      :key="tableKey"
      ref="table"
      v-loading="tableConfig.loading"
      class="gt-el-table"
      :border="tableConfig.border"
      :data="tableConfig.content"
      :max-height="tableConfig.maxHeight || null"
      :highlight-current-row="tableConfig.isHighlightCurrentRow"
      :height="tableConfig.total ? null : (tableConfig.height || null)"
      :row-key="tableConfig.rowKey || 'id'"
      :tree-props="tableConfig.treeProps || { children: 'children', hasChildren: 'hasChildren' }"
      v-bind="$attrs"
      empty-text="暂无数据"
      @cell-click="cellClick"
      @cell-dblclick="cellDblclick"
      @row-dblclick="rowDbClick"
      @selection-change="handleSelectionChange"
      @select="select"
      @select-all="selectAll"
      @sort-change="sortChange"
      @cell-mouse-enter="cellMouseEnter"
      @cell-mouse-leave="cellMouseLeave"
      @row-click="rowClick"
    >
      <el-table-column v-if="tableConfig.content.length > 0 && expand" type="expand">
        <template slot-scope="scope">
          <slot name="expand" :row="scope.row" :$index="scope.$index" :column="scope.column" :store="scope.store" />
        </template>
      </el-table-column>
      <template v-if="tableConfig.content.length > 0">
        <el-table-column
          v-if="tableConfig.multiple && tableConfig.isShowSelection && tableHeader.length > 0"
          type="selection"
          width="36px"
          :show-overflow-tooltip="tableConfig.selectionShowOverflowTooltip"
          :selectable="selectEnable"
          :reserve-selection="tableConfig.reserveSelection"
        />
        <el-table-column v-if="!tableConfig.multiple && tableConfig.isShowSelection && tableHeader.length > 0" width="40px">
          <template slot-scope="scope">
            <div style="overflow: hidden">
              <el-radio v-model="radioValue" :label="scope.row.id" @change="radioChange(scope.row)">
                <span />
              </el-radio>
            </div>
          </template>
        </el-table-column>
        <el-table-column v-if="tableConfig.isShowIndex" :index="indexMethod" type="index" label="序号" width="60px" />
      </template>
      <template v-for="(item, index) in tableHeader">
        <template v-if="!item.hidden">
          <template v-if="item.prop === 'operateColumn'">
            <el-table-column
              v-if="!item.hidden && tableConfig.content.length > 0"
              :key="index"
              :label="item.label"
              :min-width="item.minWidth"
              :width="item.width"
              :fixed="item.fixed ? item.fixed : null"
              :align="item.align || 'center'"
            >
              <template slot-scope="scope">
                <template v-for="(n,i) in item.actions">
                  <el-button
                    v-if="n.isShowFunc ? n.isShowFunc(scope.row, scope.$index) : true"
                    :key="index + '_' + i"
                    :size="n.size || 'small'"
                    :type=" n.type || 'text'"
                    :disabled="n.disabledFunc ? n.disabledFunc(scope.row, scope.$index) : false"
                    @click="handleBtn(n.action, scope.row)"
                  >{{ n.formatter ? n.formatter(scope.row, scope.$index) : n.label }}</el-button>
                </template>
              </template>
            </el-table-column>
          </template>
          <el-table-column
            v-else-if="item.slot"
            :key="index"
            :prop="item.prop"
            :label="item.label"
            :min-width="item.minWidth"
            :width="item.width"
            :align="item.align || 'center'"
            :show-overflow-tooltip="item.slot !=='operate' && item.showOverflowTooltip !== false"
            :sortable="item.sortable"
            :sort-method="(a, b) => item.sortMethod ? item.sortMethod(a, b) : sortMethod(a, b, item.prop)"
          >
            <template slot-scope="scope">
              <slot :name="item.slot" :row="scope.row" :$index="scope.$index" :column="scope.column" :store="scope.store" />
            </template>
          </el-table-column>
          <el-table-column
            v-else
            :key="index"
            :prop="item.prop"
            :label="item.label"
            :min-width="item.minWidth"
            :width="item.width"
            :align="item.align || 'center'"
            :show-overflow-tooltip="item.showOverflowTooltip !== false"
            :sortable="item.sortable"
            :sort-method="(a, b) => item.sortMethod ? item.sortMethod(a, b) : sortMethod(a, b, item.prop)"
          >
            <template slot-scope="scope">
              <div
                :style="item.style"
                :class="[item.class, 'test-class']"
                v-html="item.formatter ? item.formatter(scope.row, scope.column, scope.row[item.prop], scope.$index) : scope.row[item.prop]"
              />
            </template>
          </el-table-column>
        </template>
      </template>
    </el-table>
    <div v-if="tableConfig.total" class="gt-pagination-wp">
      <br>
      <el-pagination
        :small="paginationSmall"
        :current-page="tableConfig.currentPage"
        :page-sizes="[10, 20, 30, 50]"
        :page-size="tableConfig.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :pager-count="5"
        :total="tableConfig.total"
        background
        :disabled="isPaginationDisabled"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </el-card>
</template>

<script>
import functionUtil from '@/utils/functionUtil'
export default {
  name: 'CustomTable',
  components: {},
  props: {
    // 是否显示卡片样式
    isCardType: {
      type: Boolean,
      default: true
    },
    // 表格标题
    tableTitle: {
      type: String,
      default: ''
    },
    showTableTitleWrap: {
      type: Boolean,
      default: true
    },
    /**
     * 表头上的按钮
     * 示例：
     * [{ name: 'createDialog', icon: 'el-icon-circle-plus', displayName: '新增' }]
     */
    tableTools: {
      type: Array,
      default: () => {
        return []
      }
    },
    /**
     * 表头的配置
     * 最基本的配置：[{ label: '测试', prop: 'test' }]
     */
    tableHeader: {
      type: Array,
      default: function() {
        return []
      }
    },
    tableData: {
      type: Object,
      default: function() {
        return {}
      }
    },
    expand: {
      type: Boolean,
      default: false
    },
    /**
     * 是否使用小型分页样式
     */
    paginationSmall: {
      type: Boolean,
      default: false
    },
    /**
     * 是否显示输入框
     */
    showInput: {
      type: Boolean,
      default: false
    },
    /**
     * 表格右上方输入框的提示语
     */
    inputPlaceholder: {
      type: String,
      default: '请输入'
    },
    /**
     * 表格右上方输入框的宽度
     */
    rightInputWidth: {
      type: String,
      default: null
    },
    /**
     * 分页是否禁用
     */
    isPaginationDisabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      tableConfig: {
        rowKey: 'id', // 行数据的 Key，用来优化 Table 的渲染；在使用 reserve-selection 功能与显示树形数据时，该属性是必填的
        loading: false, // true：表格具有加载状态
        border: true, // false： 表格不显示边框
        content: [], // 表格数据
        total: 0, // 表格数据的总数，去掉该属性，不显示分页条
        currentPage: 1, // 当前页数
        pageSize: 15, // 一页所显示的数量
        maxHeight: null, // 表格的最大高度，超过这个高度会显示滚动条
        height: null, // 表格的高度，超过这个高度会显示滚动条
        isHighlightCurrentRow: true, // 是否要高亮当前行
        treeProps: null,
        isShowSelection: false, // 是否显示多选框
        reserveSelection: false, // 仅对 type=selection 的列有效，类型为 Boolean，为 true 则会在数据更新之后保留之前选中的数据（需指定 row-key）
        isShowIndex: false, // 是否显示序号列
        multiple: true, // 是否多选
        disabledcCondition: [], // 禁止选择框的条件
        selectionShowOverflowTooltip: true // 复选框禁止出现省列号
      },
      selectedData: [], // 所勾选的数据
      radioValue: '',
      searchInput: '',
      debounce: null, // 函数防抖
      tableKey: new Date().getTime()
    }
  },
  computed: {
    rightInputStyle() {
      return `width: ${this.rightInputWidth};`
    }
  },
  watch: {
    tableData: {
      handler(val) {
        if (!val) return
        // 把父组件传过来的tableData赋值给tableConfig
        Object.keys(val).forEach(prop => {
          if (val[prop] !== undefined) {
            this.$set(this.tableConfig, prop, val[prop])
          }
        })
      },
      deep: true,
      immediate: true
    }
  },
  mounted() {
    // 防止火狐浏览器拖拽的时候以新标签打开
    document.body.ondrop = function(event) {
      event.preventDefault()
      event.stopPropagation()
    }
  },
  created() {
    this.debounce = functionUtil.debounce(this.inputChange, 500, false)
  },
  beforeDestroy() {
    this.debounce = null
  },
  methods: {
    // 序号
    indexMethod(index) {
      return index + 1 + (this.tableData.currentPage - 1) * this.tableData.pageSize
    },
    // 表头按钮的点击事件
    toolClick(name) {
      this.$emit(name, this.selectedData)
    },
    // 操作列中按钮的点击事件
    handleBtn(name, row) {
      this.$emit(name, row)
    },
    // 当某个单元格被点击时会触发该事件
    cellClick(row, column, cell) {
      this.$emit('cell-click', row, column, cell)
    },
    // 当某个单元格被双击时会触发该事件
    cellDblclick(row, column, cell) {
      this.$emit('cell-dblclick', row, column, cell)
    },
    // 当某一行被双击时会触发该事件
    rowDbClick(row, column) {
      this.$emit('row-dblclick', row, column)
    },
    // 当某一行被点击时会触发该事件
    rowClick(row, column) {
      this.$emit('row-click', row, column)
    },
    // 当选择项发生变化时会触发该事件
    handleSelectionChange(val) {
      this.selectedData = val
      this.$emit('selection-change', val)
    },
    // 当用户手动勾选数据行的 Checkbox 时触发的事件
    select(selection, row) {
      this.$emit('select', selection, row)
    },
    // 当用户手动勾选全选 Checkbox 时触发的事件
    selectAll(selection) {
      this.$emit('select-all', selection)
    },
    handleSizeChange(val) {
      this.tableData.pageSize = val
      this.tableData.currentPage = 1
      this.$emit('query', val)
    },
    handleCurrentChange(val) {
      this.tableData.currentPage = val
      this.$emit('query', val)
    },
    radioChange(row) {
      this.selectedData = [row]
      this.$emit('selection-change', this.selectedData)
    },
    /**
     * 默认勾选
     * @param row 需要勾选或去掉勾选的记录
     * @param isSelect true: 勾选；false：不勾选
     */
    defaultTickRow(row, isSelect) {
      this.$nextTick(() => {
        if (!this.$refs['table']) return
        this.$refs['table'].toggleRowSelection(row, isSelect)
      })
    },
     /**
     * 默认勾选(用于单选)
     * @param row 需要勾选或去掉勾选的记录
     * @param isSelect true: 勾选；false：不勾选
     */
    radioTickRow(row) {
      this.$nextTick(() => {
        if (!this.$refs['table']) return
        this.$refs['table'].setCurrentRow(row)
      })
    },
    // 用于多选表格，清空用户的选择
    clearSelection() {
      this.$refs.table.clearSelection()
    },
    selectEnable(row, rowIndex) {
      const disabledcCondition = this.tableConfig.disabledcCondition
      if (disabledcCondition.length > 0) {
        const hasField = disabledcCondition.every(i => {
          return row[i.field]
        })
        const conditionResult = []
        disabledcCondition.forEach(i => {
          let value = null
          if (i.value === 'true') {
            value = true
          } else if (i.value === 'null') {
            value = null
          }
          if (i.condition === 'eq') {
            conditionResult.push(row[i.field] === value)
          } else if (i.condition === 'neq') {
            conditionResult.push(row[i.field] != value)
          }
        })
        const finalResult = conditionResult.every(i => i === true)

        return !(hasField && finalResult)
      } else {
        return true
      }
    },
    sortChange({ column, prop, order }) {
      this.$emit('sort-change', { column, prop, order })
    },
    // 当单元格 hover 进入时会触发该事件
    cellMouseEnter(row, column, cell, event) {
      this.$emit('cell-mouse-enter', row, column, cell, event)
    },
    // 当单元格 hover 退出时会触发该事件
    cellMouseLeave(row, column, cell, event) {
      this.$emit('cell-mouse-leave', row, column, cell, event)
    },
    sortMethod(a, b, prop) {
      if (_.isEmpty(a[prop]) && _.isEmpty(b[prop])) {
        return 0
      } else if (_.isEmpty(a[prop])) {
        return -1
      } else if (_.isEmpty(b[prop])) {
        return 1
      }
      return a[prop].toLowerCase().localeCompare(b[prop].toLowerCase())
    },
    inputChange() {
      this.$emit('query', this.searchInput)
    },
    keyupEnter() {
      if (window.navigator.msSaveBlob) {
        // IE以及IE内核的浏览器
        this.debounce()
      }
    }
  }
}
</script>
<style lang="scss">
</style>
<style lang="scss" scoped>
@import '~@/styles/mixin.scss';
.el-card.gt-table-box {
  border: 0;
  &.is-always-shadow {
    box-shadow: none;
  }
  .el-card__body {
    padding: 0;
  }
}
.cols-select-popover {
  .el-checkbox-group {
    margin-bottom: -4px;
    .el-checkbox {
      display: block;
      margin-bottom: 4px;
    }
  }
}
.expand-content-class {
  margin-left: 40px;
}
.test-class {
  white-space: nowrap;
  text-overflow: ellipsis; /* ellipsis:显示省略符号来代表被修剪的文本  string:使用给定的字符串来代表被修剪的文本*/
  overflow: hidden; /*超出部分隐藏*/
}
/* .column-count-2 {
  @include webkit(column-count, 2);
  @include webkit(column-gap, 24);
}
.column-count-1 {
  @include webkit(column-count, 1);
  @include webkit(column-gap, 24);
} */
.only-icon {
  font-size: 25px;
  padding: 0;
}
.checkboxClass {
  background-color: chocolate;
}

</style>
