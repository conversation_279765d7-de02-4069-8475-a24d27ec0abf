<template>
  <div>
    <el-dialog :visible.sync="dialogVisible" :width="width" :center="isCenter">
      <div class="dialog-content">
        <img src="~@/assets/images/info_icon.png" />
        <div class="title">{{ title }}</div>
        <div>{{ desc }}</div>
      </div>
      <span slot="footer" class="dialog-footer" v-if="showFooter">
        <slot></slot>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: "TipsDialog",
  props: {
    dialogVisible: {
      type: Boolean,
      default: false,
    },
    width: {
        type:String,
        default:"20%"
    },
    icon: {
      type: String,
      default: require("../../../assets/images/info_icon.png"),
    },
    title: {
      type: String,
      default: "",
    },
    desc: {
      type: String,
      default: "",
    },
    isCenter: {
      type: Boolean,
      default: true,
    },
    showFooter: {
      type: Boolean,
      default: true,
    },
  },
};
</script>

<style lang='scss' scoped>
.dialog-content {
  text-align: center;
  color: #666666;
  font-size: 16px;
  img {
    width: 56px;
    height: 56px;
  }
  .title {
    margin: 20px 0 10px 0;
    font-weight: 700;
  }
}
::v-deep .el-dialog__header {
  padding: 0;
}
</style>