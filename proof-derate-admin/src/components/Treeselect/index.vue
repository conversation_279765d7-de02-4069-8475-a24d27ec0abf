<template>
  <div id="tree-select">
    <div class="tree-select-left">
      <div class="tree-select-left-title">菜单分配</div>
      <div class="tree-select-left-content">
        <!-- height: 465px -->
        <el-scrollbar style="height: 555px">
          <eltreeCustom
            ref="tree"
            :data="treeData"
            :check-strictly="checkStrictlyChil"
            show-checkbox
            node-key="menuId"
            default-expand-all
            :props="defaultProps"
            @check="treeCheck"
          >
            <!--   @check-change="checkChange" -->
            <span slot-scope="{ node, data }" class="custom-tree-node">
              <span class="custom-tree-node-text">{{ node.label }}</span>
              <span v-if="data.scopes!=null" class="custom-tree-node-select">
                <el-form v-if="data.bindMenu" :ref="'from'+ data.menuId" class="el-form" :model="data">
                  <el-form-item label prop="bindScope" :rules="[{ required: true,message: '请选择数据范围', trigger: 'change' }]">
                    <el-select v-model="data.bindScope" placeholder="请选择" clearable :disabled="ifEdit">
                      <el-option v-for="item in data.scopesOptions" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                  </el-form-item>
                </el-form>
                <el-select v-if="!data.bindMenu" v-model="data.bindScope" placeholder="请选择" clearable :disabled="ifEdit">
                  <el-option v-for="item in data.scopesOptions" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </span>
            </span>
          </eltreeCustom>
        </el-scrollbar>
      </div>
    </div>
  </div>
</template>

<script>
import eltreeCustom from '@/components/tree/src/tree'
export default {
  components: {
    eltreeCustom
  },
  props: {
    treeData: {
      type: Array,
      default: () => {
        return []
      }
    },
    checkStrictly: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      defaultProps: {
        children: 'children',
        label: 'menuName',
        disabled: this.isdisabledFn
      },
      options: [
        {
          value: 'ALL',
          label: '全局'
        },
        {
          value: 'CURRENT',
          label: '本级'
        },
        {
          value: 'CURRENT_SUB',
          label: '本级及下级'
        },
        {
          value: 'CURRENT_LAST',
          label: '本级及子孙级'
        },
        {
          value: 'ORG',
          label: '本部门'
        },
        {
          value: 'ORG_SUB',
          label: '本部门及下级部门'
        },

        {
          value: 'ORG_LAST',
          label: '本部门及子孙级部门'
        },
        {
          value: 'ONLY_SUB_ORG',
          label: '仅下级部门'
        },
        {
          value: 'ONLY_LAST_ORG',
          label: '仅子孙级部门'
        }
      ],
      value: '',
      selectKeyList: [],
      validateTreeList: [],
      selectTrueList: [],
      ifEdit: false,
      checkStrictlyChil: true,
      thisCheckParent: []
    }
  },
  watch: {
    treeData: {
      deep: true,
      handler(newVal, oldVal) {
        setTimeout(() => {
          this.checkStrictlyChil = false
        }, 500)
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.setfillerOptions(this.treeData)
      // console.log('this.treeData', this.treeData)
    })
  },

  methods: {
    isdisabledFn() {
      return this.ifEdit
    },
    treeCheck(val, val1) {
      this.setAllChosefalse(this.treeData)
      if (val1.halfCheckedNodes.length != 0) {
        val1.halfCheckedNodes.forEach(e => {
          e.bindMenu = true
        })
      }
      if (val1.checkedNodes.length != 0) {
        val1.checkedNodes.forEach(e => {
          e.bindMenu = true
        })
      }

      console.log(val1, val)
      // })
    },
    checkChange(val, val2, val3) {
      this.selectTrueList = []
      this.thisCheckParent = []
      console.log(val, val2, val3)
      this.findParentChecked(this.treeData, val)
      if (this.thisCheckParent.length != 0) {
        this.thisCheckParent[0].bindMenu = true
      }
      this.getAllChoseID(this.treeData)
      setTimeout(() => {
        this.setSelectkey(this.selectTrueList)
      }, 100)
    },
    checkChange1(val1, val2, val3) {
      this.selectTrueList = []
      if (val2) {
        console.log(val2)
        this.setAllChoseTrue([val1])
      } else {
        this.setAllChosefalse([val1])
      }
      this.getAllChoseID(this.treeData)
      // console.log('this.selectTrueList', this.selectTrueList)
      // this.setSelectkey(this.selectTrueList)
      setTimeout(() => {
        // console.log(this.getChecked())
        this.setSelectkey(this.selectTrueList)
        // this.selectTrueList = []
      }, 100)
    },

    setSelectkey(list) {
      this.$refs.tree.setCheckedKeys(list)
    },
    setfillerOptions(data) {
      data.forEach(e => {
        if (e.scopes != null) {
          e.scopesOptions = []
          this.options.forEach(e1 => {
            if (e.scopes.indexOf(e1.value) !== -1) {
              e.scopesOptions.push(e1)
              e.bindScope = e.bindScope === null ? e.scopes.length ? e.scopes[0] : 'ALL' : e.bindScope
            }
          })
        }
        if (e.children !== null) {
          this.setfillerOptions(e.children)
        }
      })
    },
    setAllChosefalse(data) {
      data.forEach(e => {
        e.bindMenu = false
        if (e.children != null) {
          this.setAllChosefalse(e.children)
        }
      })
    },
    setAllChoseTrue(data) {
      data.forEach(e => {
        e.bindMenu = true
        if (e.children != null) {
          this.setAllChoseTrue(e.children)
        }
      })
    },
    getAllChoseID(data) {
      data.forEach(e => {
        if (e.bindMenu === true) {
          this.selectTrueList.push(e.menuId)
        }
        if (e.children != null) {
          this.getAllChoseID(e.children)
        }
      })
    },
    getChecked() {
      //   console.log(this.$refs.tree.getHalfCheckedKeys())
      return this.$refs.tree.getHalfCheckedKeys().concat(this.$refs.tree.getCheckedKeys(false))
    },
    validateTree(data) {
      data.forEach(e => {
        // console.log(this.$refs['from' + e.menuId])
        if (this.$refs['from' + e.menuId] != undefined) {
          this.$refs['from' + e.menuId].validate((valid, val) => {
            this.validateTreeList.push(valid)
          })
        }
        if (e.children != null) {
          this.validateTree(e.children)
        }
      })
    },
    // 根据父级id找到对应的上级对象
    findParentChecked(data, val) {
      const checkParent = data.filter(item => item.menuId === val.parent)
      if (checkParent.length === 0) {
        for (const e of data) {
          if (e.children != null) {
            const checkParent = e.children.filter(item => item.menuId === val.parent)
            if (checkParent.length != 0) {
              this.thisCheckParent.push(checkParent[0])
              break
            }
            this.findParentChecked(e.children, val)
          }
        }
      } else {
        this.thisCheckParent.push(checkParent[0])
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.tree-select {
  &-left {
    max-width: 485px;
    &-title {
      height: 49px;
      border: 1px solid #e9e9e9;
      background: #f8f8f8;
      font-size: 14px;
      color: #666666;
      line-height: 49px;
      padding-left: 20px;
      // display: flex;
      // align-items: center;
    }
    &-content {
      border: 1px solid #e9e9e9;
      padding: 0 20px;
      // height: 465px;
      // overflow-y: auto;
    }
  }
}
::v-deep .el-scrollbar__wrap {
  overflow-x: hidden;
}
.detail-role-card::v-deep .el-divider--horizontal {
  margin: 9px 0 21px;
}
.custom-tree-node {
  width: calc(100% - 4px);
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  min-width: auto;
  padding: 4px 2px;
  border-radius: 4px;
  display: flex;
  align-items: center;
}
.custom-tree-node-select {
  margin-left: 15px;
}
.custom-tree-node-text {
  max-width: 190px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.detail-role-card::v-deep .el-tree-node__content {
  // height: 40px;
  height: 100%;
  margin-bottom: 4px;
}
.detail-role-card::v-deep .el-input__inner {
  height: 32px;
  line-height: 32px;
}
.detail-role-card::v-deep .el-input__icon {
  line-height: 32px;
}
.el-form {
  display: inline-block;
}
.el-form::v-deep .el-form-item {
  margin-bottom: 0px;
}

#tree-select .tree-select-left ::v-deep .el-form-item__error {
  left: 0;
  padding-top: 0px;
  // top: -1px;
}
</style>
