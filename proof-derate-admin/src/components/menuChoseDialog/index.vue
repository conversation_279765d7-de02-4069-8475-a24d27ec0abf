<template>
  <div class="menuChoseDialog">
    <el-dialog :title="fileDialogTitle" :visible.sync="dialogVisible" width="870px" @close="close">
      <div class="dialog-wrap" element-loading-spinner="el-icon-loading">
        <el-tree ref="tree" :data="treeData" show-checkbox node-key="value"> </el-tree>
      </div>
      <div class="dialog-footer">
        <el-button type="primary" @click="output">导出</el-button>
        <el-button @click="close">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getMenuTree } from '@/api/commonPack/platManege'
export default {
  data() {
    return {
      dialogVisible: false,
      fileDialogTitle: '导出菜单',
      defaultProps: {
        children: 'children',
        label: 'label',
      },
      treeData: [],
    }
  },
  props: {
    menuData: {
      type: Array,
      default: () => {
        return []
      },
    },
  },
  watch: {
    dialogVisible(value) {
      if (value) {
        // this.getMenuTree()
      }
    },
  },
  methods: {
    close() {
      this.dialogVisible = false
      this.$emit('close')
      //   this.$store.commit('breadcrumbBtn/SET_PLATMANAGE', { exportChosedMenu: false })
    },
    output() {
      console.log('tree', this.$refs.tree.getCheckedKeys())
      const node = this.$refs.tree.getCheckedKeys()
      this.$emit('output', node)
    },
    getMenuTree() {
      getMenuTree().then((res) => {
        this.treeData = res.data
        console.log('treeData', this.treeData)
        // this.menuId = this.treeData[0].value
        // console.log(this.menuId)
      })
    },
    // 清空勾选状态
    clearChosedNode() {
      this.$refs.tree.setCheckedKeys([])
    },
  },
  mounted() {
    this.getMenuTree()
  },
}
</script>

<style>
.dialog-footer {
  text-align: center;
}
.dialog-wrap {
  height: 300px;
  overflow-y: auto;
}
</style>