<template>
  <div>
    <el-cascader ref="cascader" :key="endIndex" v-model="value" class="cascader" :disabled="editDisabled" clearable
      :props="optionProps" :options="cascaderOptions" @change="change" v-show="!showOnlyLabel" filterable :collapse-tags="collapseTags">
      <template slot-scope="{ data }">
        <div v-if="data.label.length <= 10" class="cascader-content">{{ data.label }}</div>
        <el-tooltip v-else effect="dark" :content="data.label" placement="top">
          <div class="cascader-content-over">{{ data.label }}</div>
        </el-tooltip>
      </template>
    </el-cascader>
    <span v-show="showOnlyLabel">{{ cascaderLabel }}</span>
  </div>
</template>

<script>
import { getDivisionListTree } from '@/api/commonPack/platManege'
export default {
  props: {
    // 需要回显的具体的行政区划值， 例如： '430100'
    divisionCode: {
      type: String,
      default: () => {
        return ''
      }
    },
    // 多选的行政区划
    divisionCodeLsit: {
      type: Array,
      default: () => {
        return []
      }
    },
    // 是否禁用弹出下拉框，默认不禁用
    editDisabled: {
      type: Boolean,
      default: () => {
        return false
      }
    },
    // 账号判定的行政区划
    limitationCode: {
      type: String,
      default: () => {
        return ''
      }
    },
    // 权限代码，根据权限获取树
    permissionCode: {
      type: String,
      default: () => {
        return ''
      }
    },
    // 只显示单纯完整行政规划字段
    showOnlyLabel: {
      type: Boolean,
      default: () => {
        return false
      }
    },
    // 多选模式下是否折叠Tag
    collapseTags:{
      type: Boolean,
      default: () => {
        return true
      }
    },
    // 组件配置
    optionProps: {
      type: Object,
      default: () => {
        return {
          value: 'value',
          label: 'label',
          children: 'children',
          checkStrictly: true,
          expandTrigger: 'hover',
          multiple: false // 是否多选
        }
      }
    },
  },
  data() {
    return {
      value: '',
      loading: false,
      // optionProps: {
      //   value: 'value',
      //   label: 'label',
      //   children: 'children',
      //   checkStrictly: true,
      //   expandTrigger: 'hover',
      //   multiple: false // 是否多选
      // },
      cascaderOptions: [],
      endIndex: 0,
      cascaderLabel: ''
    }
  },
  watch: {
    divisionCode: {
      handler(val) {
        if (!val) return
        this.value = this.divisionCode
        // console.log('this.divisionCode',this.divisionCode)
        // console.log(' this.value', this.value)
        // this.value ='430100'
      },
      deep: true,
      immediate: true
    },
    divisionCodeLsit:{
      handler(val) {
        if (!val) return
        this.value = this.divisionCodeLsit
        // console.log(123123123123,this.value)
        // this.value =['441300', '440100', '440100']
      },
      deep: true,
      immediate: true
    }
  },

  mounted() {
    this.getDivisionListTree().then(() => {
      setTimeout(() => {
        // console.log('this.$refs.cascader.presentText', this.$refs.cascader.presentText)
        if (this.$refs.cascader) {
          this.cascaderLabel = this.$refs.cascader.presentText
        }
      })
    })
  },

  methods: {
    getDivisionListTree() {
      const data = {
        code: this.limitationCode,
        permissionCode: this.permissionCode
        // auth:organization:list
      }
      if (this.editDisabled && this.limitationCode === '') {
        // data.code = this.value
      } else {
        data.code = this.limitationCode
      }
      console.log('getDivisionListTree',data)
      return getDivisionListTree(data).then(res => {
        if (res.meta.code === '200' && res.data !== null) {
          // 新建-只有一条数据就自动填充
          /* if (res.data.length === 1 && !this.divisionCode) {
            this.value = this.filterDivision(res.data)[0].value
          } */
          this.cascaderOptions = res.data
        }
      })
    },
    filterDivision(data) {
      let _ = ''
      if (data.length === 1) {
        if (data[0].children && data[0].children.length > 0) {
          _ = this.filterDivision(data[0].children)
        } else {
          _ = data
        }
      }
      return _
    },
    change() {
      console.log(this.value)
      this.$nextTick(() => {
        const node = this.$refs.cascader.getCheckedNodes()
        let data = {}
        console.log('node', node)
        if (this.optionProps.multiple === false) {
          if (node.length !== 0) {
            data = {
              name: node[0].label,
              code: node[0].value
            }
          } else {
            data = {
              name: '',
              code: ''
            }
          }
        } else {
          data = []
          node.forEach(e => {
            let item = {
              name: e.label,
              code: e.value
            }
            data.push(item)
          });
        }
        // 选中值改变时返回对应的label和code值
        this.$emit('setDivisionCodeAndName', data)
      })
    },
    // 去除选中的值
    clearChose() {
      this.value = []
    }
  }
}
</script>

<style lang="scss" scoped>
.cascader {
  width: 100%;
}

.cascader-content {
  width: 125px;
}

.cascader-content-over {
  width: 125px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
