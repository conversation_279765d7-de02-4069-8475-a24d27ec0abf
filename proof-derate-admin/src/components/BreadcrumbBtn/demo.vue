<!--
第一步：新建按钮组件，命名注意要唯一性；
       路径：@components/BreadcrumbBtn

第二步：在@/layout/NavBar.vue 注册组件，注意动态引入
        参考：components: {
                QualityRulesDetail: () => import('@/components/BreadcrumbBtn/qualityRulesDetail')
              },

第三步：在注册路由的时候 添加breadcrumbBtnComponentId，注意名称要跟NavBar里的一致，在meta里面加；
        参考：meta: { title: '目录办件档案', breadcrumbBtnComponentId: 'QualityRulesDetail' }

第四步：如有交互，如编辑、停启用、删除等，可利用vuex存状态，
      路径：@/store/modules/BreadcrumbBtn
      具体可参考案例

      修改vuex的值：this.$store.commit('breadcrumbBtn/SET_STANDARDRULE', true)//this.$store.commit('breadcrumbBtn/SET_MATERIALCATALOG', { stopUseFlag: !oldValue })
      获取vuex的值：
               computed: {
                  isEdit() {
                    return this.$store.state.breadcrumbBtn.standardRule.detail
                  }
                },

 -->
