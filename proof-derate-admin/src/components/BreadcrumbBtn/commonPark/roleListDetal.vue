<template>
  <div class="btn-container">
    <!-- <el-button
      v-if="this.$store.state.breadcrumbBtn.platManage.roleStatus==='NORMAL'"
      v-permission="'auth:role:recovery'"
      type="primary"
      @click="isDisable()"
    >
      <span>禁用</span>
    </el-button>
    <el-button v-else v-permission="'auth:role:disable'" type="danger" @click="isDisable()">
      <span>恢复</span>
    </el-button>
    <el-button v-permission="'auth:role:edit'" type="primary" @click="edit()">编辑角色</el-button> -->
    <el-button @click="back()">返回列表</el-button>
  </div>
</template>
<script>
export default {
  computed: {
    roleStatus() {
      return this.$store.state.breadcrumbBtn.platManage.roleStatus
    }
  },
  methods: {
    edit() {
      this.$router.push({ name: 'RoleDetail', query: { type: 'edit', id: this.$route.query.id }})
    },
    back() {
      this.$router.push({ name: 'RoleList' })
    },
    isDisable() {
      this.$store.commit('breadcrumbBtn/SET_PLATMANAGE', { isRoleDisable: !this.$store.state.breadcrumbBtn.platManage.isRoleDisable })
    }
  }
}
</script>
