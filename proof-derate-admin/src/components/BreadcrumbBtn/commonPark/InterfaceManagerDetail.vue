<template>
  <div class="btn-container">
    <!-- <el-button type="primary" @click="del()">删除</el-button>
    <el-button v-if="interfaceManagerDetailSatus==='DISABLE'" type="primary" @click="setDisable()">恢复</el-button>
    <el-button v-if="interfaceManagerDetailSatus==='NORMAL'" type="danger" @click="setDisable()">禁用</el-button> -->
    <el-button @click="back()">返回列表</el-button>
  </div>
</template>
<script>
export default {
  computed: {
    interfaceManagerDetailSatus() {
      return this.$store.state.breadcrumbBtn.platManage.interfaceManagerDetailSatus
    }
  },
  methods: {
    back() {
      this.$router.push({ name: 'InterfaceManager' })
    },
    del() {
      this.$store.commit('breadcrumbBtn/SET_PLATMANAGE', { isInterfaceManagerDetailDel: !this.$store.state.breadcrumbBtn.platManage.isInterfaceManagerDetailDel })
    },
    setDisable() {
      this.$store.commit('breadcrumbBtn/SET_PLATMANAGE', { isInterfaceManagerDetailDisable: !this.$store.state.breadcrumbBtn.platManage.isInterfaceManagerDetailDisable })
    }
  }
}
</script>
