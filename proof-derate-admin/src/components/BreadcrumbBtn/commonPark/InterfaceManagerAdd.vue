<template>
  <div class="btn-container">
    <el-button v-permission="'app:api_manage:create'" type="primary" @click="sumbit()">新建</el-button>
    <el-button v-permission="'app:api_manage:import'" type="primary" @click="uplandData()">导入</el-button>
    <el-button v-permission="'app:api_manage:export'" type="primary" @click="exportData()">导出</el-button>
  </div>
</template>
<script>

export default {

  methods: {
    sumbit() {
      // this.$store.commit('breadcrumbBtn/SET_PLATMANAGE', { interfaceManagerAdd: true })
      this.$router.push({ name: 'InterfaceManagerAdd' })
    },
    uplandData() {
      this.$store.commit('breadcrumbBtn/SET_PLATMANAGE', { interfaceManagerUpland: true })
    },
    exportData() {
      console.log(232323)
      this.$store.commit('breadcrumbBtn/SET_PLATMANAGE', { interfaceManagerExport: true })
    }
  }
}
</script>
