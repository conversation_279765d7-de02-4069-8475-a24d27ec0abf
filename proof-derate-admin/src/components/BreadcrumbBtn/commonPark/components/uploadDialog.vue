<!-- 单文件上传组件 -->
<template>
  <div class="uploadDialog">
    <div style="margin:-23px 0 10px">{{ uploadTitle }}</div>
    <el-upload
      ref="upload"
      class="upload-demo"
      action
      :accept="limitFileType"
      :file-list="fileList"
      :on-change="handleChange"
      :show-file-list="false"
      :auto-upload="false"
      @before-upload="beforeUpload"
    >
      <el-input ref="upload" slot="trigger" v-model="input" placeholder="请选择文件" class="input" />
      <el-button slot="trigger" type="primary" class="btnFile">浏览</el-button>
      <!-- <div v-if="template" slot="tip" class="el-upload__tip" style="position: absolute;margin-bottom: 100px">
        <img src="@/assets/images/u3029.png" alt>
        温馨提示：请选择以.xlsx为后缀名的文件！
        <span>
          <a class="temp_style" @click="downloadTemplate">下载导入模板</a>
        </span>
      </div>
      <div v-if="tips_message" slot="tip" class="el-upload__tip" style="position: absolute">
        <img src="@/assets/images/u3029.png" alt>
        {{ tips_message }}
      </div> -->
    </el-upload>
  </div>
</template>
<script>
export default {
  props: {
    template: {
      type: Boolean,
      default: true
    },
    tips_message: {
      type: String,
      default: ''
    },
    limitFileType: {
      type: String,
      default: ''
    },
    limitFileSize: {
      type: String,
      default: ''
    },
    uploadTitle: {
      type: String,
      default: '请选择您要导入的数据文件'
    }
  },
  data() {
    return {
      input: '',
      file: '',
      fileList: [],
      downloadFlag: false
    }
  },
  mounted() {
    console.log('limitFileType-----', this.limitFileType)
  },
  methods: {
    beforeUpload(file) {
      console.log(this.limitFileType, this.limitFileSize)
      // this.formData.fileName = file.name
      const isSize = file.size / 1024 / 1024 < this.limitFileSize
      if (!isSize) {
        this.$message.warning('上传文件大小不能超过 ${this.limitFileSize}!')
        return false
      }
    },
    handleChange(file) {
      this.file = file
      this.input = file.name
    },
    downloadTemplate() {
      this.downloadFlag = true
      this.$emit('toFather', this.downloadFlag)
    },
    // 清空
    clearFiles() {
      // this.$refs.upload.clearFiles()
      this.input = null
    }
  }
}
</script>
<style lang="scss" scoped>
.el-upload__tip {
  margin: 20px 0;
}
.input {
  width: 83%;
  position: absolute;
  left: 3%;
  top: 39%;
}
.btnFile {
  position: absolute;
  left: 88%;
  top: 39%;
}
.el-upload__input {
  display: none !important;
}
.el-upload {
  width: 98%;
}
.temp_style {
  color: #409eff !important;
}
</style>
<style>
  .uploadDialog .el-upload__input{
    display: none !important;
  }
</style>

