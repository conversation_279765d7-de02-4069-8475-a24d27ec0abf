<template>
  <div v-if="isShow" class="btn-container">
    <el-button v-permission="'auth:menu:create'" type="primary" @click="handleAdd()">新建</el-button>
    <el-button v-permission="'auth:menu:import'" type="primary" @click="importMenu()">导入</el-button>
    <el-button v-permission="'auth:menu:export'" type="primary" @click="exportMenu()">导出</el-button>

    <!-- <el-dialog title="证明材料导入" :visible.sync="dialogForm.visible" width="50%" center>
      <uploadDialog ref="uploadDialog" @toFather="download" />
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="importFlie">上传</el-button>
        <el-button @click="dialogForm.visible = false">取 消</el-button>
      </span>
    </el-dialog>-->
    <fileDialog ref="fileDialog" :whitelist="whitelist" :file-size-limit="fileSizeLimit" :multiple="multiple" @getFilelist="getFilelist" />
  </div>
</template>
<script>
import { exportMenu, importMenu } from '@/api/commonPack/platManege'
import uploadDialog from './components/uploadDialog.vue'
import fileDialog from '@/components/fileDialog'
import { exportsDown } from '@/utils'
export default {
  components: {
    uploadDialog,
    fileDialog
  },
  data() {
    return {
      dialogForm: {
        visible: false
      },
      whitelist: ['ljson'],
      fileSizeLimit: 2,
      multiple: false
    }
  },
  computed: {
    isShow() {
      return this.$store.state.breadcrumbBtn.platManage.showMenuListButton
    }
  },
  methods: {
    handleAdd() {
      this.$store.commit('breadcrumbBtn/SET_PLATMANAGE', { isMenuAdd: true })
    },
    exportMenu() {
      /*  console.log(process.env.VUE_DOWN_API_URL)
      const url = process.env.VUE_APP_DOWN_URL + '/materials-api/auth/v1/common/menu/export'
      window.open(url, '导出菜单') */
      // exportsDown('/auth/webapi/v1/common/menu/export', {}, '导出菜单.ljson')
      exportsDown(exportMenu(), {}, '导出菜单.ljson', 'licc')
      /* exportMenu().then(res => {
        console.log(res, 234)
      }).catch((error) => {
        console.log(error, 333)
      }) */
    },
    download() {},
    importMenu() {
      // this.dialogForm.visible = true
      this.$refs.fileDialog.dialogVisible = true
    },
    getFilelist(list) {
      console.log(list, this.$refs.fileDialog.fileList)
      this.importFlie()
    },
    importFlie() {
      if (this.$refs.fileDialog.fileList.length != 0) {
        // const name = this.$refs.uploadDialog.input
        const file = this.$refs.fileDialog.fileList[0].raw
        const fd = new FormData()
        fd.append('file', file)
        importMenu(fd)
          .then(res => {
            this.$refs.fileDialog.fileList = []
            const type = res.meta.code === '200' ? 'success' : 'error'
            const data = res.data ? res.data : res.meta.message
            const message = data.split('/').join('<br/>')
            this.$message({
              type: type,
              dangerouslyUseHTMLString: true,
              message: message
            })
            if (type === 'success') {
              this.dialogForm.visible = false
              this.query(1, 'search', data)
            }
          })
          .catch(() => {
            this.$refs.fileDialog.fileList = []
          })
      } else {
        this.$message({
          message: '请选择文件，再导入！',
          type: 'warning'
        })
      }
    }
  }
}
</script>
