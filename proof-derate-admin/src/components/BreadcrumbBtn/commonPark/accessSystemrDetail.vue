<template>
  <div class="btn-container">
    <!-- <el-button type="primary" v-permission="'app:access_sys:reset_password'" @click="restPassword()">重置密码</el-button>
    <el-button type="danger" v-permission="'app:access_sys:disable'" @click="disable()" v-if="$store.state.breadcrumbBtn.platManage.isDisable">禁用</el-button>
    <el-button type="primary" v-permission="'app:access_sys:recovery'" @click="disable()" v-else>恢复</el-button>
    <el-button type="primary" v-permission="'app:access_sys:edit'" @click="edit()">编辑</el-button> -->
    <el-button @click="back()">返回列表</el-button>
  </div>
</template>
<script>
export default {
  methods: {
    disable() {
      this.$store.commit('breadcrumbBtn/SET_PLATMANAGE', { isAccessSystemDisable: true })
    },
    edit() {
      this.$router.push({ name: 'accessSystemApplicationEdit', query: { type: 'edit', id: this.$route.query.id }})
    },
    back() {
      this.$router.push({ name: 'accessSystemApplication' })
    },
    restPassword() {
      this.$store.commit('breadcrumbBtn/SET_PLATMANAGE', { isAccessSystemRestPassword: !this.$store.state.breadcrumbBtn.platManage.isAccessSystemRestPassword })
    }
  }
}
</script>
