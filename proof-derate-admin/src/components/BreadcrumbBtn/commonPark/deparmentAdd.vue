<template>
  <div class="btn-container">
    <el-button v-permission="'auth:organization:create'" type="primary" @click="addDeparment()">新建</el-button>
    <el-button v-permission="'auth:organization:import'" type="primary" @click="departmentImport()">导入</el-button>
    <el-button v-permission="'auth:organization:export'" type="primary" @click="departmentExport()">导出</el-button>
  </div>
</template>
<script>
import { exportDataByGetMethod } from '@/utils'
export default {
  methods: {
    addDeparment() {
      this.$store.commit('breadcrumbBtn/SET_PLATMANAGE', { isDeparmentAdd: true })
    },

    /**
     * 导入
     */
    departmentImport() {
      this.$store.commit('breadcrumbBtn/SET_PLATMANAGE', { isDepartmentImport: true })
    },

    /**
     * 导出
     */
    departmentExport() {
      exportDataByGetMethod('/auth/webapi/v1/common/organization/export', {}, '部门管理导出数据.xlsx', 'licc')
    }

  }
}
</script>
