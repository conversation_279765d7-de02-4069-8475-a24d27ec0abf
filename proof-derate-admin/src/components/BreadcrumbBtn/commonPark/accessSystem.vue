<template>
  <div class="btn-container">
    <el-button v-permission="'app:access_sys:create'" type="primary" @click="add()">新建</el-button>
    <el-button v-permission="'app:access_sys:import'" type="primary" @click="importData()">导入</el-button>
    <el-button v-permission="'app:access_sys:export'" type="primary" @click="exportData()">导出</el-button>
  </div>
</template>
<script>
import { exportDataByGetMethod } from '@/utils'
export default {
  data() {
    return {
    }
  },
  methods: {
    add() {
      this.$router.push({ name: 'accessSystemApplicationEdit', query: { type: 'add' }})
    },

    /**
     * 导入
     */
    importData() {
      this.$store.commit('breadcrumbBtn/SET_PLATMANAGE', { accessApplicationSystemImport: true })
    },

    /**
     * 导出
     */
    exportData() {
      exportDataByGetMethod('/auth/webapi/v1/common/access_sys/export', {}, '接入应用系统导出数据.xlsx', 'licc')
    }

  }
}
</script>
