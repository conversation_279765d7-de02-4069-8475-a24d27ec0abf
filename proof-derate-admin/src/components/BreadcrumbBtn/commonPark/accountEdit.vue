<template>
  <div class="btn-container">
    <!-- <el-button v-permission="'auth:account:reset_password'" type="primary" @click="resetPassword()">重置密码</el-button>
    <el-button v-if="accoundIdEdit" v-permission="'auth:account:edit'" type="primary" @click="addAccount()">编辑用户</el-button>
    <el-button
      v-if="this.$store.state.breadcrumbBtn.platManage.accountStatus ==='NORMAL'"
      v-permission="'auth:account:disable'"
      type="danger"
      class="table-delete"
      @click="setStatus()"
    >禁用</el-button>
    <el-button v-else v-permission="'auth:account:recovery'" type="primary" class="table-delete" @click="setStatus()">恢复</el-button> -->
    <el-button @click="back()">返回列表</el-button>
  </div>
</template>
<script>
export default {
  computed: {
    accoundIdEdit() {
      return this.$store.state.breadcrumbBtn.platManage.accoundIdEdit
    },
    accountStatus() {
      return this.$store.state.breadcrumbBtn.platManage.accountStatus
    }
  },
  methods: {
    addAccount() {
      this.$router.push({ name: 'AccountSetting', query: { id: this.accoundIdEdit, isEdit: 1 }})
    },
    resetPassword() {
      this.$store.commit('breadcrumbBtn/SET_PLATMANAGE', { isAccountResetPassword: !this.$store.state.breadcrumbBtn.platManage.isAccountResetPassword })
    },
    setStatus() {
      this.$store.commit('breadcrumbBtn/SET_PLATMANAGE', { isAccountDissable: !this.$store.state.breadcrumbBtn.platManage.isAccountDissable })
    },
    back() {
      this.$router.push({ name: 'accountList' })
    }
  }
}
</script>
