<template>
  <div class="btn-container">
    <el-button v-permission="'auth:role:edit'" type="primary" @click="edit()">编辑角色</el-button>
    <el-button @click="back()">返回列表</el-button>
  </div>
</template>
<script>
export default {
  methods: {
    edit() {
      this.$router.push({ name: 'RoleDetail', query: { type: 'edit', id: this.$route.query.id }})
    },
    back() {
      this.$router.push({ name: 'RoleList' })
    }
  }
}
</script>
