<template>
  <div class="btn-container">
    <el-button v-permission="'auth:division:create'" type="primary" @click="addConfigManage()">新建</el-button>
    <el-button v-permission="'auth:division:import'" type="primary" @click="fileImport()">导入</el-button>
    <el-button v-permission="'auth:division:export'" type="primary" @click="fileExport()">导出</el-button>
  </div>
</template>
<script>
import { exportDataByGetMethod } from '@/utils'
export default {
  data() {
    return {

    }
  },
  methods: {
    addConfigManage() {
      this.$store.commit('breadcrumbBtn/SET_PLATMANAGE', { isaddAdministrativeDivision: true })
    },
    fileImport() {
      this.$store.commit('breadcrumbBtn/SET_PLATMANAGE', { fileImportAdministrativeDivision: true })
    },
    fileExport() {
      exportDataByGetMethod('/auth/webapi/v1/common/division/export', {}, '行政区划导出数据.xlsx', 'licc')
    }
  }
}
</script>
