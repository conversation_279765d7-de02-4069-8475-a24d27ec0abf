<template>
  <div class="btn-container">
    <!-- <el-button v-permission="'auth:dict:edit'" type="primary" @click="handleEdit()">编辑</el-button>
    <el-button v-permission="'auth:dict:del'" type="danger" class="table-delete" @click="handleDelete()">删除</el-button> -->
    <el-button v-permission="'auth:dict:detail:create'" type="primary" @click="editConfigManage()">新建</el-button>
    <el-button @click="back()">返回列表</el-button>
  </div>
</template>
<script>
export default {
  methods: {
    editConfigManage() {
      this.$store.commit('breadcrumbBtn/SET_PLATMANAGE', {
        isAddEditConfigManage: true
      })
    },
    back() {
      this.$router.push({ name: 'configManageList' })
    },
    handleEdit() {
      this.$store.commit('breadcrumbBtn/SET_PLATMANAGE', { isConfigManageDetailEdit: !this.$store.state.breadcrumbBtn.platManage.isConfigManageDetailEdit })
    },
    handleDelete() {
      this.$store.commit('breadcrumbBtn/SET_PLATMANAGE', { isConfigManageDetailDel: !this.$store.state.breadcrumbBtn.platManage.isConfigManageDetailDel })
    }
  }
}
</script>
