<template>
  <div class="passwd-container">
    <el-dialog title="密码修改" :visible.sync="passwdDialogVisible" width="30%" class="passwdDialog">
      <div>请修改初始密码</div>
      <div class="passwordChange-wrap">
        <el-form
          ref="passwdForm"
          :model="passwdForm"
          status-icon
          :rules="passwdRules"
          label-width="80px"
          class="passwdForm"
        >
          <!-- <el-form-item label="账号" prop="pass">
            <el-input type="password" v-model="passwdForm.account" autocomplete="off"></el-input>
          </el-form-item>-->
          <el-form-item label="新密码" prop="new_password">
            <el-input
              :key="passwordChangeType"
              ref="passwordChange"
              v-model="passwdForm.new_password"
              :type="passwordChangeType"
              placeholder="请输入密码"
              name="passwordChange"
              tabindex="2"
              auto-complete="on"
            />
            <span class="show-pwd change" @click="showPwdChange">
              <svg-icon :icon-class="passwordChangeType === 'password' ? 'eye' : 'eye-open'" />
            </span>
          </el-form-item>
          <el-form-item label="确认密码" prop="confirmPassword">
            <el-input
              :key="confirmPasswordChangeType"
              ref="confirmPasswordChange"
              v-model="passwdForm.confirmPassword"
              :type="confirmPasswordChangeType"
              placeholder="请输入密码"
              name="confirmPasswordChange"
              tabindex="2"
              auto-complete="on"
            />
            <span class="show-pwd change" @click="shoCconfirmPwdChange">
              <svg-icon :icon-class="confirmPasswordChangeType === 'password' ? 'eye' : 'eye-open'" />
            </span>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="passwordChange-footer">
        <el-button type="primary" @click="passwordChangeSubmit">确 定</el-button>
        <el-button @click="passwdDialogVisible = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { updateInitPassword } from '@/api/user'

export default {
  props: {
    loginForm: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    const validatePassIsidentical = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请再次输入密码'))
      } else if (value !== this.passwdForm.new_password) {
        callback(new Error('两次输入密码不一致!'))
      } else {
        callback()
      }
    }
    return {
      passwdDialogVisible: false,
      passwdForm: {
        account: '',
        new_password: '',
        confirmPassword: ''
      },
      passwdRules: {
        new_password: [{ required: true, trigger: 'blur', message: '请输入密码' }],
        confirmPassword: [{ required: true, trigger: 'blur', validator: validatePassIsidentical }]
      },
      passwordChangeType: 'password',
      confirmPasswordChangeType: 'password'
    }
  },

  methods: {

    showPwdChange() {
      if (this.passwordChangeType === 'password') {
        this.passwordChangeType = ''
      } else {
        this.passwordChangeType = 'password'
      }
      this.$nextTick(() => {
        this.$refs.passwordChange.focus()
      })
    },
    shoCconfirmPwdChange() {
      if (this.confirmPasswordChangeType === 'password') {
        this.confirmPasswordChangeType = ''
      } else {
        this.confirmPasswordChangeType = 'password'
      }
      this.$nextTick(() => {
        this.$refs.confirmPasswordChange.focus()
      })
    },
    passwordChangeSubmit() {
      this.$refs.passwdForm.validate(valid => {
        if (valid) {
          const data = {
            account: this.loginForm.account,
            new_password: this.passwdForm.new_password
          }
          updateInitPassword(data).then(res => {

          })
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
@import '~@/styles/variables.scss';
.passwd-container {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
 /*  width: 100%;
  height: 100%; */
  .show-pwd {
    position: absolute;
    right: 10px;
    top: 7px;
    font-size: 16px;
    cursor: pointer;
    user-select: none;
  }
  .captchaImg {
    width: 40%;
    height: 100%;
    margin-left: 28px;
    border: 1px solid rgba(188, 188, 188, 0.6);
    cursor: pointer;
    img {
      width: 100%;
      height: 100%;
      text-align: center;
      vertical-align: middle;
      margin-top: -4px;
    }
  }
  .svg-container {
    padding-left: 15px;
    vertical-align: middle;
    width: 30px;
    display: inline-block;
    font-size: 20px;
    position: absolute;
    z-index: 1;
    &::after {
      content: '';
      position: absolute;
      display: block;
      width: 1px;
      background: #e9edee;
      height: 32px;
      top: 4px;
      right: -24px;
    }
  }
  .reg-tips {
    color: blue;
    font-size: 16px;
    text-align: center;
    .active {
      color: red;
      cursor: pointer;
    }
  }
  .passwordChange-footer {
    text-align: center;
  }
}
.passwordChangeDialog ::v-deep .el-dialog__header {
  padding-bottom: 0px;
}
.passwordChangeDialog ::v-deep .el-dialog__body {
  padding-top: 0px;
}
.passwordChangeDialog ::v-deep .el-input__suffix {
  display: none;
}
.passwordChange-wrap {
  padding: 26px 16px 0px;
  .change {
    top: 0px;
  }
}
</style>
<style lang="scss">
.passwd-container{
  .el-dialog__body{
    padding: 0 20px 30px;
  }

}
</style>
