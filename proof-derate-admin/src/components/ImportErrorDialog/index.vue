<template>
  <div>
    <el-dialog title="提示" :visible.sync="dialogVisible" width="50%" :show-close="false" top="300px">
      <div class="tips-title">
        <div><img class="error-icon" src="@/assets/commonPack_images/icon_import_error.png" alt=""></div>
        文件导入失败，{{ importFailList.length }}条数据校验失败，失败原因如下：
      </div>
      <div class="el-dialog-div">
        <div class="error-content" v-for="(item, index) in importFailList" :key="index">
          {{ item }}
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关 闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'ImportErrorDialog',
  props: {
    importFailList: {
      type: Array,
      default: []
    },
  },
  data() {
    return {
      dialogVisible: false
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-dialog__body {
  padding: 0px 20px 30px 20px;
}

::v-deep .el-dialog__footer {
  text-align: center;
}
.el-dialog-div {
  max-height: 150px;
  background-color: #F2F2F2;
  border-radius: 8px 0 0 8px;
  padding: 10px 16px;
  overflow: auto;
}

/* 自定义滚动条轨道样式 */
::-webkit-scrollbar-track {
  background-color: #f1f1f1;
  border-radius: 2px;
}

/* 自定义滚动条样式 */
::-webkit-scrollbar-thumb {
  background-color: #d9d9d9;
  border-radius: 2px;
}

/* 自定义滚动条的宽度 */
::-webkit-scrollbar {
  width: 4px;
}

.error-content {
  line-height: 22px;
}

.tips-title {
  display: flex;
  align-items: center;
  margin: 10px 0;
  color: #FF2525;
}

.error-icon {
  width: 25px;
  height: 25px;
}
</style>