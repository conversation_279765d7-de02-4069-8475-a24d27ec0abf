<template>
  <el-breadcrumb class="app-breadcrumb" separator=">">
    <transition-group name="breadcrumb">
      <el-breadcrumb-item v-for="(item, index) in levelList" :key="item.path">
        <span v-if="item.redirect === 'noRedirect' || index == levelList.length - 1" class="no-redirect">{{ item.meta.title }}</span>
        <a v-else @click.prevent="handleLink(item)">{{ item.meta.title }}</a>
      </el-breadcrumb-item>
    </transition-group>
  </el-breadcrumb>
</template>

<script>
import pathToRegexp from 'path-to-regexp'
import Cookies from 'js-cookie'
import { Base64 } from 'js-base64'
export default {
  data() {
    return {
      levelList: null
    }
  },
  watch: {
    $route(to, from) {
      // console.log('to,from',to,from)
      this.getBreadcrumb()
    }
  },
  created() {
    this.getBreadcrumb()
  },
  methods: {
    getBreadcrumb() {
      // only show routes with meta.title
      const matched = this.$route.matched.filter(item => item.meta && item.meta.title)
      // const first = matched[0]

      /* if (!this.isDashboard(first)) {
        matched = [{ path: '/dashboard', meta: { title: 'Dashboard' }}].concat(matched)
      } */
      // console.log('matched', matched)
      const matchedOther = []
      matched.forEach(item => {
        if (item.meta.originalMeta) {
          let i = {
            meta: item.meta.originalMeta,
            path: item.meta.originalMeta.path
          }
          matchedOther.push(i)
          // return item1
        }
      })

      this.levelList = matched.filter(item => item.meta && item.meta.title && item.meta.breadcrumb !== false)
      if (matchedOther.length != 0) {
        this.levelList.push(matchedOther[0])
      }

      // console.log('this.levelList', this.levelList)
      let str = this.levelList[this.levelList.length - 1].meta.title
      let base64 = Base64.encode(str)
      // console.log('base64', base64)
      Cookies.set('Menu-Name', base64)
    },
    isDashboard(route) {
      const name = route && route.name
      if (!name) {
        return false
      }
      return name.trim().toLocaleLowerCase() === 'Dashboard'.toLocaleLowerCase()
    },
    pathCompile(path) {
      const { params } = this.$route
      var toPath = pathToRegexp.compile(path)
      return toPath(params)
    },
    handleLink(item) {
      const { redirect, path } = item
      if (redirect) {
        this.$router.push({ name: redirect })
        return
      }
      console.log('this.pathCompile(path)', this.pathCompile(path))
      this.$router.push(this.pathCompile(path))
    }
  }
}
</script>

<style lang="scss" scoped>
.app-breadcrumb.el-breadcrumb {
  display: inline-block;
  font-size: 14px;
  line-height: 50px;
  margin-left: 8px;

  .no-redirect {
    color: #97a8be;
    cursor: text;
  }
}
</style>
