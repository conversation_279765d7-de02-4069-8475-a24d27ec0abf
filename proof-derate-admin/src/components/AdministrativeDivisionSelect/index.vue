<template>
  <div>
    <el-cascader
      ref="cascader"
      :key="endIndex"
      v-model="value"
      class="cascader"
      :disabled="editDisabled"
      clearable
      :props="optionProps"
      :options="cascaderOptions"
      @change="change"
    >
      <template slot-scope="{ data }">
        <div v-if="data.name.length <= 10" class="cascader-content">{{ data.name }}</div>
        <el-tooltip v-else effect="dark" :content="data.name" placement="top">
          <div class="cascader-content-over">{{ data.name }}</div>
        </el-tooltip>
      </template>
    </el-cascader>
  </div>
</template>

<script>
import { getDivisionList } from '@/api/commonPack/platManege'
export default {
  props: {
    // 需要回显的具体的行政区划值， 例如： '430100'
    divisionCode: {
      type: String,
      default: () => {
        return ''
      }
    },
    // 懒加载时获取下级时传入的参数
    levelCode: {
      type: String,
      default: () => {
        return 'SUB' // SUB 全局 CURRENT 层级
      }
    },
    // 是否禁用弹出下拉框，默认不禁用
    editDisabled: {
      type: Boolean,
      default: () => {
        return false
      }
    },
    // 账号判定的行政区划
    limitationCode: {
      type: String,
      default: () => {
        return ''
      }
    }
  },
  data() {
    return {
      value: '',
      loading: false,
      optionProps: {
        value: 'code',
        label: 'name',
        children: 'children',
        checkStrictly: true,
        lazyLevelCode: this.levelCode,
        expandTrigger: 'hover',
        lazy: true,
        // 动态加载，点击某一级加载对应的下级
        lazyLoad(node, resolve) {
          if (node.data) {
            const data = {
              code: node.data.code,
              level: this.lazyLevelCode
            }
            if (!node.data.children) {
              if (!node.data.disabled) {
                getDivisionList(data).then(res => {
                  if (res.data != null) {
                    res.data.forEach(e => {
                      Object.assign(e, { leaf: false })
                    })
                    const data = res.data
                    console.log('data', data)
                    resolve(data)
                  } else {
                    resolve([])
                  }
                })
              } else {
                resolve([])
              }
            } else {
              resolve()
            }
          }
        }
      },
      cascaderData: [],
      cascaderDataFrist: [],
      cascaderOptions: [],
      codeIndex: 0,
      endIndex: 0,
      chosedData: ''
    }
  },
  watch: {
    divisionCode: {
      handler(val) {
        if (!val) return
        this.value = this.divisionCode
      },
      deep: true,
      immediate: true
    },
    endIndex: {
      handler(val) {
        if (!val) return
        console.log('val', val)
        if (val === this.cascaderData.length - this.codeIndex) {
          const list = []
          this.cascaderData.forEach((e, index) => {
            if (this.cascaderData[index - 1]) {
              console.log('this.cascaderData[index - 1].children', this.cascaderData[index - 1].children)
              if (!this.cascaderData[index - 1].children) {
                console.log(111, this.cascaderData[index - 1])
                this.cascaderData[index - 1].children = []
                this.cascaderData[index - 1].children.push(this.cascaderData[index])
              } else {
                this.cascaderData[index - 1].children.forEach(e1 => {
                  if (e1.code === this.cascaderData[index].code) {
                    e1.children = this.cascaderData[index].children
                    console.log('e1', e1)
                  }
                })
                // this.cascaderData[index - 1].children.push(this.cascaderData[index])
              }
            }
          })

          list.push(this.cascaderData[0])
          // list.forEach(e => {
          //   Object.assign(e, { leaf: false })
          // })
          this.setLeaf(list)
          this.cascaderOptions = list

          console.log('this.cascaderOptions', this.cascaderOptions)
          this.loading = false
        }
      },
      deep: true
    }
  },

  mounted() {
    if (this.limitationCode !== '') {
      this.getDivisionListByLimitationCode()
      this.loading = true
    } else {
      this.getDivisionList()
    }
  },

  methods: {
    getDivisionList() {
      const data = {
        code: '',
        level: 'SUB'
      }
      getDivisionList(data).then(res => {
        if (res.meta.code === '200' && res.data != null) {
          this.cascaderDataFrist = res.data
          const data1 = {
            code: this.divisionCode,
            level: 'PARENT'
          }
          this.getDivisionListByCode(data1)
        }
      })
    },
    getDivisionListByCode(data) {
      getDivisionList(data).then(res => {
        if (res.meta.code === '200' && res.data != null) {
          this.cascaderData = res.data
          //   console.log('this.cascaderData.length', this.cascaderData.length, 'this.divisionCode', this.divisionCode)
          // 查询上级的结构存在一个数值，只有最外一层的组织行政区划
          if (this.cascaderData.length === 1) {
            this.cascaderDataFrist.forEach(e => {
              Object.assign(e, { leaf: false })
            })
            this.cascaderOptions = this.cascaderDataFrist
          } else if (this.cascaderData.length === 0 && this.divisionCode === '') {
            // 存在没有上级的情况，只给最外一层的组织行政区划
            this.cascaderDataFrist.forEach(e => {
              Object.assign(e, { leaf: false })
            })
            this.cascaderOptions = this.cascaderDataFrist
          } else {
            this.cascaderData.forEach((e, index) => {
              const data = {
                code: e.code,
                level: 'SUB'
              }
              getDivisionList(data).then(res => {
                const list = []
                if (res.meta.code === '200' && res.data != null) {
                  this.cascaderOptions = []
                  e.children = res.data
                  // 将查出来的每一级的children 赋上对应的下级的值
                  if (this.cascaderData.length !== 0) {
                    for (var i = this.cascaderData.length - 1; i >= 0; i--) {
                      // console.log('i', this.cascaderData[i], this.cascaderData[i - 1])
                      if (this.cascaderData[i - 1]) {
                        if (this.cascaderData[i - 1].children) {
                          this.cascaderData[i - 1].children.forEach(e => {
                            if (e.code === this.cascaderData[i].code) {
                              if (this.cascaderData[i].children) {
                                e.children = this.cascaderData[i].children
                              }
                            }
                          })
                        }
                      }
                    }

                    list.push(this.cascaderData[0])
                    this.cascaderDataFrist.forEach(e => {
                      if (e.code !== this.cascaderData[0].code) {
                        list.push(e)
                      }
                    })
                    this.setLeaf(list)
                    this.cascaderOptions = list
                    console.log('this.cascaderOptions', this.cascaderOptions)
                  }
                }
              })
            })
          }
        }
      })
    },

    getDivisionListByLimitationCode() {
      const data = {
        code: '',
        level: 'SUB'
      }
      getDivisionList(data).then(res => {
        if (res.meta.code === '200' && res.data != null) {
          this.cascaderDataFrist = res.data
          const data1 = {
            code: this.divisionCode,
            level: 'PARENT'
          }
          if (this.divisionCode === '') {
            data1.code = this.limitationCode
          }
          this.getDivisionListByCode1(data1)
        }
      })
    },
    getDivisionListByCode1(data) {
      getDivisionList(data).then(res => {
        if (res.meta.code === '200' && res.data != null) {
          this.cascaderData = res.data
          this.codeIndex = 0
          console.log('this.cascaderData', this.cascaderData)
          if (this.cascaderData.length !== 0) {
            this.cascaderData.forEach((e, index) => {
              if (e.code === this.limitationCode) {
                this.codeIndex = index
              }
            })
            console.log('this.codeIndex', this.codeIndex)
          }
          this.cascaderData.forEach((e, index) => {
            //
            if (index > this.codeIndex || index === this.codeIndex) {
              const data2 = {
                code: e.code,
                level: 'SUB'
              }
              getDivisionList(data2).then(res => {
                if (res.meta.code === '200' && res.data != null) {
                  e.children = res.data
                }
                console.log('index', index)
                this.endIndex++
              })
            }
          })
        }
      })
    },
    findByid(data, id) {
      data.forEach(item => {
        if (item.code === id) {
          this.chosedData = item
        }
        if (item.children) {
          this.findByid(item.children, id)
        }
      })
    },
    change() {
      const node = this.$refs.cascader.getCheckedNodes()
      let data = {}
      if (node.length !== 0) {
        data = {
          name: node[0].label,
          code: node[0].value
        }
      } else {
        data = {
          name: '',
          code: ''
        }
      }
      console.log(this.cascaderOptions, data.code)
      this.findByid(this.cascaderOptions, data.code)
      console.log('this.chosedData', this.chosedData)
      // this.optionProps.lazyLoad(node)
      // const i = _.filter(this.cascaderOptions, (item)=>{
      //   return item.code ===data.code
      // })
      //  console.log('i',i)
      // const loadData = {
      //   code: data.code,
      //   level: this.levelCode
      // }
      // getDivisionList(loadData).then(res => {
      //   if (res.data != null) {
      //     console.log(res)
      //     this.chosedData.children = res.data
      //     this.$set(this.chosedData, 'children', res.data)
      //     console.log('this.cascaderOptions', this.cascaderOptions)
      //   }
      // })
      // console.log(1111)
      // 选中值改变时返回对应的label和code值
      this.$emit('setDivisionCodeAndName', data)
    },
    // 去除选中的值
    clearChose() {
      this.value = []
    },
    // 设置所以的数据层级leaf
    setLeaf(data) {
      data.forEach(item => {
        item.leaf = false
        if (item.children) {
          this.setLeaf(item.children)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.cascader {
  width: 100%;
}
.cascader-content {
  width: 125px;
}
.cascader-content-over {
  width: 125px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
