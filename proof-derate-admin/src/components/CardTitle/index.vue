<template>
  <div class="tabheader box-title">
    <!-- <h3 class="box-title-h3">{{titleName}}</h3> -->
    <h3 class="box-title-h3 cursor" v-if="ifback" @click="back">
      <i class="el-icon-arrow-left" />
      {{titleName}}
    </h3>
    <h3 class="box-title-h3" v-else>{{titleName}}</h3>
    <div class="box-title-btn">
      <template>
        <slot></slot>
      </template>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {}
  },
  props: {
    // 标题数据
    titleName: { type: String, default: '' },
    ifback: { type: Boolean, default: false }
  },
  mounted() {},

  methods: {
    back() {
      this.$emit('back')
    }
  }
}
</script>

<style lang="scss" scoped>
.tabheader {
  padding: 18px 20px;
  border-bottom: 1px solid #ebeef5;
  box-sizing: border-box;
  background-color: #fff;
  box-shadow: 0 -6px 12px 0 rgb(0 0 0 / 10%);
  // border-radius: 4px;
  border-radius: 4px 4px 0px 0px;
  position: relative;
  top: 6px;
}
.box {
  &-title {
    // height: 36px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    // margin:0px 0 30px 20px;
    &-h3 {
      margin: 0;
      padding: 0;
    }
    &-btn {
      display: flex;
      align-items: center;
      .btn {
        width: 36px;
        height: 36px;
        background: #f7f7f7;
        border: 1px solid #d9d9d9;
        border-radius: 4px;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        margin-right: 12px;
        &:first-child {
          // margin-right: 12px;
        }
        &.active {
          background: #e0f0ff;
          border: 1px solid #99ceff;
        }
      }
    }
  }
}
.box-title-h3 {
  i {
    padding-right: 5px;
  }
}
.cursor {
  cursor: pointer;
}
</style>