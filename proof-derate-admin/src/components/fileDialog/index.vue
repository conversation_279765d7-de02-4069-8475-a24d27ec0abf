<template>
  <div id="fileDialog">
    <el-dialog :title="fileDialogTitle" :visible.sync="dialogVisible" width="870px" @close="close">
      <div class="dialog-wrap" v-loading="loading" element-loading-text="正在导入，请稍等" element-loading-spinner="el-icon-loading">
        <el-upload
          :auto-upload="false"
          :show-file-list="false"
          class="upload-demo"
          action
          :on-change="handleChange"
          :file-list="fileList"
          :accept="acceptFile"
        >
          <el-button type="primary" @click="changefile">选择文件</el-button>
        </el-upload>
        <p>
          <i class="el-icon-warning-outline"></i>
          <span v-if="applicationType === 'divisionCode'">支持格式：{{this.whitelist.join('/')}}，最大可上传{{this.fileSizeLimit}}M。</span>
          <span v-else>支持格式：{{this.whitelist.join('，')}}总和不超过{{this.fileSizeLimit}}M。</span>
          <el-button v-if="isShowTemple" type="text" @click="downTemple">下载导入模板</el-button>
        </p>
        <div class="dialog-wrap-list">
          <el-upload
            v-if="fileTableList.length==0"
            class="upload-drag"
            drag
            :auto-upload="false"
            :show-file-list="false"
            :on-change="handleChange"
            :file-list="fileList"
            :accept="acceptFile"
            action
          >
            <img src="~@/assets/images/Frame.png" alt />
            <div class="el-upload__text">点击上方”选择文件"或将文件拖拽到此区域</div>
          </el-upload>
          <!-- :data="fileList"  -->
          <el-table :data="fileTableList" height="200" border style="width: 100%" v-else>
            <el-table-column prop="name" label="文件名称"></el-table-column>
            <el-table-column label="操作">
              <template slot-scope="scope">
                <el-upload
                  :auto-upload="false"
                  :show-file-list="false"
                  class="upload-change"
                  action
                  :on-change="handleChange1"
                  :accept="acceptFile"
                >
                  <span style="color:#2697FF; margin-right:10px;" @click="changefile(scope.$index)">替换</span>
                </el-upload>
                <span style="color:#FF2B2B;cursor: pointer;" @click="delectFile(scope.$index)">删除</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
      <div class="dialog-footer" v-if="fileList.length!=0">
        <el-button type="primary" @click="updateFile">确定上传</el-button>
        <el-button @click="dialogVisible = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getIsWhitelist, hasSpecialCharacters } from '@/utils/index.js'
export default {
  props: {
    whitelist: {
      type: Array,
      default: []
    },
    fileSizeLimit: {
      type: Number,
      default: 0
    },
    multiple: {
      type: Boolean,
      default: true
    },
    isShowTemple: {
      type: Boolean,
      default: false
    },
    fileDialogTitle: {
      type: String,
      default: '上传示例'
    },
    isCheckFileName: {//是否检查文件文件名
      type:Boolean,
      default: false
    },
    applicationType: {//应用类型，行政区划divisionCode导入需要更改文字,默认是不需要更改的
      type:String,
      default:'default'
    }
  },
  data() {
    return {
      dialogVisible: false,
      fileList: [],
      // fileTableList: [],
      fileIndex: '',
      // whitelist: ['doc', 'docx', 'pdf', 'ofd', 'png', 'bmp', 'xls', 'xlsx', 'jpg', 'png', 'jpeg', 'rar', 'zip', 'gif'],
      acceptFile: '',
      // fileSizeLimit: 20
      loading: false
    }
  },
  computed: {
    fileTableList() {
      const list = []
      this.fileList.map(i => {
        if (i.id === undefined) {
          list.push(i)
        }
      })
      // console.log(list)
      return list
    }
  },
  mounted() {
    this.getaAcceptFile()
  },

  watch: {
    fileList: (newData, oldData) => {
      // console.log(newData, oldData)
    },
    fileTableList: (newData, oldData) => {
      console.log(newData, oldData)
    }
  },

  methods: {
    close() {
      // console.log('close')
      this.$emit('dialogClose')
    },
    getaAcceptFile() {
      this.acceptFile = '.' + this.whitelist.join(',.')
    },
    handleChange(file, list) {
      const isCheckFileName = this.isCheckFileName
      const hasSpecialCharacter = hasSpecialCharacters(file.name)
      const isLt20m = file.size / (1024 * 1024) < this.fileSizeLimit
      if (!isLt20m) {
        this.$message.error(`文件大小不能超过${this.fileSizeLimit}m！`)
        list.pop()
      } else if (!getIsWhitelist(file.name, this.whitelist)) {
        this.$message.error(`请重新选择以${this.whitelist.join(',')}为后缀名的文件！`)
        list.pop()
      } else if(isCheckFileName && hasSpecialCharacter) {
        this.$message.error(`导入失败，文件名含有非法字符！`)
        list.pop()
      }else {
        // 单选
        if (!this.multiple) {
          this.fileList = [file]
        } else {
          this.fileList = list
        }
      }
    },
    handleChange1(file, list) {
      const isCheckFileName = this.isCheckFileName
      const hasSpecialCharacter = hasSpecialCharacters(file.name)
      const isLt20m = file.size / (1024 * 1024) < this.fileSizeLimit
      if (!isLt20m) {
        this.$message.error(`文件大小不能超过${this.fileSizeLimit}m！`)
        list.pop()
      } else if (!getIsWhitelist(file.name, this.whitelist)) {
        this.$message.error(`请重新选择以${this.whitelist.join(',')}为后缀名的文件！`)
        list.pop()
      }  else if(isCheckFileName && hasSpecialCharacter) {
        this.$message.error(`导入失败，文件名含有非法字符！`)
        list.pop()
      }else {
        this.fileList.splice(this.fileIndex, 1, file)
      }
      // console.log('this.fileList', this.fileList)
    },
    delectFile(index) {
      let firstlistLength = this.fileList.length - this.fileTableList.length
      this.fileList.splice(firstlistLength, 1)
      this.$emit('getFilelist', this.fileList)
    },
    changefile(index) {
      this.fileIndex = index
    },
    updateFile() {
      let size = 0
      // console.log('this.fileList', this.fileList)
      this.fileList.forEach(e => {
        if (e.size == undefined) {
          size = size + e.len
        } else {
          size = size + e.size
        }
      })
      // console.log(size)
      const isLt20m = size / (1024 * 1024) < this.fileSizeLimit
      if (!isLt20m) {
        this.$message.error(`文件大小不能超过${this.fileSizeLimit}m！`)
      } else {
        if(this.applicationType === 'divisionCode') {
          this.loading = true
        } else {
          this.dialogVisible = false
        }
        this.$emit('getFilelist', this.fileList)
      }
    },
    downTemple() {
      this.$emit('downTemple')
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-wrap {
  margin-bottom: 10px;
}
.dialog-footer {
  text-align: center;
}
.dialog-wrap-list {
  //   height: 200px;
}
.upload-change {
  display: inline-block;
}
.upload-drag img {
  width: 56px;
  margin-bottom: 19px;
}
.upload-drag ::v-deep .el-upload-dragger {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.upload-drag ::v-deep .el-upload--text {
  width: 100%;
}

::v-deep .el-dialog__title {
  font-weight: 700;
}

::v-deep .el-dialog__body {
  padding:10px 20px 30px 20px
}
</style>