<template>
  <div id="fileDialog">
    <el-dialog :title="fileDialogTitle" :visible.sync="dialogVisible" width="870px" @close="close">
      <div v-loading="loading" class="dialog-wrap" element-loading-text="正在导入，请稍等" element-loading-spinner="el-icon-loading">
        <el-upload
          :auto-upload="false"
          :show-file-list="false"
          class="upload-demo"
          action
          :on-change="handleChange"
          :file-list="fileList"
          :accept="accept"
          :multiple="multiple"
        >
          <el-button type="primary" @click="changefile">选择文件</el-button>
        </el-upload>
        <div>
          <i class="el-icon-warning-outline" />
          <span v-if="applicationType === 'divisionCode'">支持格式：{{ this.accep }}，最大可上传{{ this.fileSizeLimit }}M。</span>
          <span v-else>支持格式：{{ this.accept }}，</span>
          <span v-if="fileSizeLimit>0">最大可上传{{ fileSizeLimit }}M。</span>
          <el-button v-if="isShowTemple" type="text" @click="downTemple">下载导入模板</el-button>
        </div>
        <div class="import-setting">
          <div>
            <i class="el-icon-setting" />
            <span>文件导入设置：</span>
          </div>
          <div class="radio">
            <el-radio-group v-model="config">
              <el-radio label="ERROR_IGNORE">若报错则跳过</el-radio>
              <el-radio label="ERROR_HALT">若报错则结束</el-radio>
            </el-radio-group>
          </div>
          <div class="tips">
            <p>说明：若存在校验成功和校验失败的数据，则校验成功的数据入库，校验失败的数据不入库。</p>
            <p>说明：若存在校验失败的数据，则所有数据不入库。</p>
          </div>
        </div>
        <div class="dialog-wrap-list">
          <el-upload
            v-if="fileTableList.length==0"
            class="upload-drag"
            drag
            :auto-upload="false"
            :show-file-list="false"
            :on-change="handleChange"
            :file-list="fileList"
            :accept="accept"
            :multiple="multiple"
            action
          >
            <img src="~@/assets/images/Frame.png" alt>
            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            <!-- <div class="el-upload__text">点击上方”选择文件"或将文件拖拽到此区域</div> -->
          </el-upload>
          <!-- :data="fileList"  -->
          <el-table v-else :data="fileTableList" height="200" border style="width: 100%">
            <el-table-column prop="name" label="文件名称" />
            <el-table-column label="操作">
              <template slot-scope="scope">
                <el-upload
                  :auto-upload="false"
                  :show-file-list="false"
                  class="upload-change"
                  action
                  :on-change="handleChange1"
                  :accept="accept"
                  :multiple="multiple"
                >
                  <span style="color:#2697FF; margin-right:10px;" @click="changefile(scope.$index)">替换</span>
                </el-upload>
                <span style="color:#FF2B2B;cursor: pointer;" @click="delectFile(scope.$index)">删除</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
      <div v-if="fileList.length!=0" class="dialog-footer">
        <el-button type="primary" @click="updateFile">确定上传</el-button>
        <el-button @click="dialogVisible = false">取消</el-button>
      </div>
    </el-dialog>

    <el-dialog title="提示" :visible.sync="dialogVisibleStatus" width="870px" class="result-dialog">
      <div class="status-box">
        <i v-if="importResultData.successCount === importResultData.totalCount" class="icon icon1 el-icon-circle-check" />
        <i v-else-if="importResultData.successCount > 0 && importResultData.failCount > 0 && config != 'ERROR_HALT'" class="icon icon2 el-icon-warning-outline" />
        <i v-else-if="importResultData.failCount === importResultData.totalCount || config == 'ERROR_HALT'" class="icon icon3 el-icon-circle-close" />
        <h3 class="title">{{ importResultData.successCount === importResultData.totalCount ? '全部导入成功' : importResultData.failCount === importResultData.totalCount || config == 'ERROR_HALT' ? '全部导入失败' : '部分导入成功' }}</h3>
        <p v-if="config === 'ERROR_IGNORE'">本次共导入{{ importResultData.totalCount }}条数据，其中{{ importResultData.successCount }}条数据导入成功，{{ importResultData.failCount }}条数据导入失败。</p>
        <p v-if="config === 'ERROR_HALT'">本次共导入{{ importResultData.totalCount }}条数据，其中{{ importResultData.successCount }}条数据校验成功，{{ importResultData.failCount }}条数据校验失败。</p>
      </div>
      <div v-if="importResultData.failCount > 0" class="error-detail">
        <div class="title">详情信息</div>
        <ul class="error-li">
          <li v-for="(item,idx) in importResultData.failDetails" :key="idx">{{ item }}</li>
        </ul>
        <p v-if="importResultData.failCount > 10">备注：仅显示前10条报错信息。点击“导出失败情况说明”可查看全部失败原因</p>
      </div>
      <div class="dialog-footer" style="margin-top: 30px;">
        <el-button v-if="importResultData.inventoryId" type="primary" @click="exportList">导出清单</el-button>
        <el-button @click="dialogVisibleStatus = false">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getIsWhitelist2, hasSpecialCharacters, exportsDown } from '@/utils/index.js'
import { exportErrorInv } from '@/api/commonPack/platManege'

export default {
  props: {
    accept: { // 接受上传的文件类型（thumbnail-mode 模式下此参数无效）
      type: String,
      default: ''
    },
    fileSizeLimit: { // 单个文件上传大小
      type: Number,
      default: 0 //  0代表不限制
    },
    multiple: { // 是否支持多选文件
      type: Boolean,
      default: false
    },
    limit: { // 最大允许上传个数
      type: Number,
      default: 0 //  0代表不限制
    },
    isShowTemple: { // 是否显示【下载导入模版】
      type: Boolean,
      default: false
    },
    fileDialogTitle: { // 弹窗标题
      type: String,
      default: '导入文件'
    },
    isCheckFileName: { // 是否检查文件文件名
      type: Boolean,
      default: false
    },
    applicationType: { // 应用类型，行政区划divisionCode导入需要更改文字,默认是不需要更改的
      type: String,
      default: 'default'
    },
    importResultData: { // 导入之后接口返回的导入情况数据
      type: Object,
      default: function() {
        return {
          totalCount: 0, // 导入总数
          successCount: 0, // 导入成功数
          failCount: 0, // 导入失败数
          failDetails: [], // 失败详情
          inventoryId: '' // 清单唯一标识,用于导出清单
        }
      }
    }
  },
  data() {
    return {
      dialogVisible: false,
      dialogVisibleStatus: false, // 导入之后状态弹窗，如无需要显示则无需改变
      config: 'ERROR_IGNORE', // 导入设置 ERROR_IGNORE :若报错则忽略（跳过） ERROR_HALT :若报错则终止（结束）
      fileList: [],
      // fileTableList: [],
      fileIndex: '',
      // whitelist: ['doc', 'docx', 'pdf', 'ofd', 'png', 'bmp', 'xls', 'xlsx', 'jpg', 'png', 'jpeg', 'rar', 'zip', 'gif'],
      acceptFile: '',
      // fileSizeLimit: 20
      loading: false
    }
  },
  computed: {
    fileTableList() {
      const list = []
      this.fileList.map(i => {
        if (i.id === undefined) {
          list.push(i)
        }
      })
      // console.log(list)
      return list
    }
  },

  watch: {
    fileList: (newData, oldData) => {
      // console.log(newData, oldData)
    },
    fileTableList: (newData, oldData) => {
      console.log(newData, oldData)
    },
    dialogVisible: {
      handler(val) {
        if (!val) {
          this.loading = false
        }
      },
      deep: true,
      immediate: true
    }
  },
  mounted() {
    // this.getaAcceptFile()
  },

  methods: {
    close() {
      // console.log('close')
      this.$emit('dialogClose')
    },
    /* getaAcceptFile() {
      this.acceptFile = '.' + this.whitelist.join(',.')
    }, */
    handleChange(file, list) {
      const isCheckFileName = this.isCheckFileName
      const hasSpecialCharacter = hasSpecialCharacters(file.name)
      const isLt20m = this.fileSizeLimit > 0 ? file.size / (1024 * 1024) < this.fileSizeLimit : true
      if (!isLt20m) {
        this.$message.error(`文件大小不能超过${this.fileSizeLimit}m！`)
        list.pop()
      } else if (!getIsWhitelist2(file.name, this.accept.split(','))) {
        this.$message.error(`请重新选择以${this.accept}为后缀名的文件！`)
        list.pop()
      } else if (isCheckFileName && hasSpecialCharacter) {
        this.$message.error(`导入失败，文件名含有非法字符！`)
        list.pop()
      } else {
        // 单选
        console.log(this.limit)
        if (this.limit === 1) {
          this.fileList = [file]
        } else {
          this.fileList = list
        }
      }
    },
    handleChange1(file, list) {
      const isCheckFileName = this.isCheckFileName
      const hasSpecialCharacter = hasSpecialCharacters(file.name)
      const isLt20m = this.fileSizeLimit > 0 ? file.size / (1024 * 1024) < this.fileSizeLimit : true
      if (!isLt20m) {
        this.$message.error(`文件大小不能超过${this.fileSizeLimit}m！`)
        list.pop()
      } else if (!getIsWhitelist2(file.name, this.accept.split(','))) {
        this.$message.error(`请重新选择以${this.accept}为后缀名的文件！`)
        list.pop()
      } else if (isCheckFileName && hasSpecialCharacter) {
        this.$message.error(`导入失败，文件名含有非法字符！`)
        list.pop()
      } else {
        this.fileList.splice(this.fileIndex, 1, file)
      }
      // console.log('this.fileList', this.fileList)
    },
    delectFile(index) {
      const firstlistLength = this.fileList.length - this.fileTableList.length
      this.fileList.splice(firstlistLength, 1)
      this.$emit('getFilelist', this.fileList)
    },
    changefile(index) {
      this.fileIndex = index
    },
    updateFile() {
      let size = 0
      // console.log('this.fileList', this.fileList)
      this.fileList.forEach(e => {
        if (e.size == undefined) {
          size = size + e.len
        } else {
          size = size + e.size
        }
      })
      // console.log(size)
      const isLt20m = size / (1024 * 1024) < this.fileSizeLimit
      if (!isLt20m) {
        this.$message.error(`文件大小不能超过${this.fileSizeLimit}m！`)
      } else {
        /* if(this.applicationType === 'divisionCode') {
          this.loading = true
        } else {
          this.dialogVisible = false
        } */
        // this.dialogVisible = false
        this.loading = true
        const params = {
          config: this.config
        }
        this.$emit('getFilelist', this.fileList, params)
      }
    },
    // 导出清单
    exportList() {
      this.dialogVisibleStatus = false
      const params = {
        inventory_id: this.importResultData.inventoryId
      }
      exportsDown(exportErrorInv(), params)
    },
    downTemple() {
      this.$emit('downTemple')
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-wrap {
  margin-bottom: 10px;
}
.dialog-footer {
  text-align: center;
}
.dialog-wrap-list {
  //   height: 200px;
}
.upload-change {
  display: inline-block;
}
.upload-drag img {
  width: 56px;
  margin-bottom: 19px;
}
.upload-drag ::v-deep .el-upload-dragger {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.upload-drag ::v-deep .el-upload--text {
  width: 100%;
}

::v-deep .el-dialog__title {
  font-weight: 700;
}

::v-deep .el-dialog__body {
  padding:10px 20px 30px 20px
}

.import-setting{
  display: flex;
  line-height: 24px;
  margin-bottom: 10px;
  .radio{
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    // margin-left: 5px;
    & ::v-deep .el-radio-group{
      display: flex;
      flex-direction: column;
      .el-radio{
        margin-bottom: 5px;
        margin-right: 5px;
      }
    }
  }
  .tips{
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    p{
      margin: 0;
      line-height: 1;
      margin-bottom: 7px;
      color: rgb(145, 153, 165);;
    }
  }
}
.result-dialog{
  .status-box{
    display: flex;
    flex-flow: column wrap;
    align-items: center;
    .icon{
      font-size: 50px;
    }
    .icon1{
      color: #67C23A;
    }
    .icon2{
      color: #E6A23C;
    }
    .icon3{
      color: #F56C6C;
    }
    .title{
      margin: 20px 0 10px;
    }
    p{
      margin: 0px 0;
    }
  }
  .error-detail{
    // margin-bottom: 30px;
    .title{
      font-size: 16px;
      color: #303133;
    }
    .error-li{
      color: #909399;
      margin: 10px 0;
    }
    ul,li {
      list-style-type: none; /* 移除列表项前的项目符号 */
      padding-left: 0; /* 移除列表项内部的左内边距 */
      margin-left: 0; /* 移除列表项内部的左外边距 */
      line-height: 20px;
    }
    p{
      color: #606266;
    }
  }
}
</style>
