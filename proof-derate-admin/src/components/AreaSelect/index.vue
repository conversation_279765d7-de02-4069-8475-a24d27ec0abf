 /**
     * optionData
     * 具体用于绑定具体值的数组
     * 必传
     * 具体格式入下（需要多少级的组织架构，添加对应多少个数组里的对象）
     *  optionData: [
        {
          divisionlist: [], // 具体绑定的下拉框的数组
          disabled: false, // 设置当前的下拉框是否禁用
          placeholderText:'请选择', // 设置当前下拉框的占位符
          code: '', // 当前下拉框的v-model值
          name: '' // 当前下来框选中值的具体名称
        },
        {
          divisionlist: [],
          disabled: false,
          placeholderText:'请选择',
          code: '',
          name: ''
        },
        {
          divisionlist: [],
          disabled: false,
          placeholderText:'请选择',
          code: '',
          name: ''
        }
      ]
     * 
*/

/** isShowText
    * 设置组件是否只显示组织架构的文本形式（示例：长沙市雨花区雨花街道）
    * 非必传，默认不展示
*/
<template>
  <div class="area-container-sapn" v-if="isShowText">{{completeProvinceName}}</div>
  <div class="area-container" v-else>
    <el-select
      v-model="index.code"
      :placeholder="index.placeholderText"
      clearable
      class="padding-5"
      @change="selctchange"
      @clear="clearDivisionCode(key)"
      @focus="focus(key)"
      :disabled="index.disabled"
      v-for="(index,key) in optionData"
      :key="key"
    >
      <el-option v-for="item in index.divisionlist" :key="item.code" :label="item.name" :value="item.code" />
    </el-select>
  </div>
</template>

<script>
import { getDivisionList } from '@/api/commonPack/platManege'
export default {
  data() {
    return {
      selectIndex: 0,
      completeProvinceName: ''
    }
  },
  props: {
    divisionCode: { type: String, default: '' },

    optionData: {
      type: Array,
      default: () => []
    },

    isShowText: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    divisionCode(value) {
      this.initData()
      // 监听到有变化就重新获取数据
    }
  },
  created() {},

  mounted() {
    this.initData()
  },
  methods: {
    focus(val) {
      this.selectIndex = val
    },
    initData() {
      this.optionData.forEach(i => {
        i.divisionlist = []
      })
      const data = {
        code: '',
        level: 'SUB'
      }
      this.getDivisionList(data, 0)
      if (this.divisionCode != '') {
        const data1 = {
          code: this.divisionCode,
          level: 'PARENT'
        }
        // this.optionData[0].name=''
        this.getDivisionListByCode(data1)
      }
    },

    /**
     * 获取division_code
     */
    getDivisionCode() {
      for (let i = this.optionData.length - 1; i > -1; i--) {
        if (this.optionData[i].code !== '') {
          this.division_code = this.optionData[i].code

          this.division_name = this.optionData[i].divisionlist.find(item => {
            return item.code === this.optionData[i].code
          }).name
          break
        }
      }
      this.$emit('setDivisionCode', this.division_code)
      this.$emit('setDivisionName', this.division_name)
    },

    clearDivisionCode(key) {
      this.optionData.forEach((i, index) => {
        if (index > key) {
          i.code = ''
          i.divisionlist = []
        }
      })
      if (key === 0) {
        this.division_code = ''
        this.division_name = ''
      }
      this.getDivisionCode()
    },
    /**
     * 外部组件获取部门编码和部门名字
     *  @return {division_code,division_name} 返回对应编码和对应的完整组织名称
     */
    getAllCodeAndName() {
      let divisionCodeList = []
      let divisionNameList = []
      for (let index = 0; index < this.optionData.length; index++) {
        let opt = {}
        opt = this.optionData[index].divisionlist.find(item => {
          return item.code === this.optionData[index].code
        })
        if (opt !== undefined) {
          divisionCodeList.push(opt.code)
          divisionNameList.push(opt.name)
        }
      }
      return {
        division_code: divisionCodeList[divisionCodeList.length - 1],
        division_name: divisionNameList.join(',')
        // division_name:divisionNameList[divisionNameList.length - 1]
      }
    },
    /**
     * 组件重置和初始化
     */
    resetCodeAndName() {
      this.divisionCode = ''
      this.division_name = ''
      this.$emit('setDivisionCode', this.divisionCode)
      this.$emit('setDivisionName', this.division_name)
      this.initData()
    },
    selctchange(val) {
      // 点击清空按钮会触发选择改变事件，需判断是否为清空操作
      if (val !== '') {
        let opt = {}
        opt = this.optionData[this.selectIndex].divisionlist.find(item => {
          return item.code === val
        })
        if (opt !== undefined) {
          this.optionData[this.selectIndex].name = opt.name
        } else {
          this.optionData[this.selectIndex].name = ''
        }

        let data = {
          code: this.optionData[this.selectIndex].code,
          level: 'SUB'
        }
        // this.getDivisionList3(data)
        this.getDivisionList(data, this.selectIndex + 1)
        this.getDivisionCode()
      }
    },
    getDivisionListByCode(data) {
      getDivisionList(data).then(res => {
        if (res.meta.code === '200' && res.data != null) {
          const provinceNameList = []
          const divisionList = res.data
          divisionList.forEach((e, index) => {
            this.optionData[index].code = e.code
            provinceNameList.push(e.name)
            let data = {
              code: e.code,
              level: 'SUB'
            }
            this.completeProvinceName = provinceNameList.join('')
            // console.log('completeProvinceName', this.completeProvinceName)
            this.getDivisionList(data, index + 1, 'aaa')
          })
        }
      })
    },

    getDivisionList(data, index, type) {
      if (this.optionData.length > index) {
        this.optionData[index].divisionlist = []
        if (!type) {
          for (let i = 0; i < this.optionData.length - index; i++) {
            this.optionData[index + i].code = ''
          }
        }
        getDivisionList(data).then(res => {
          if (res.meta.code === '200' && res.data != null) {
            this.optionData[index].divisionlist = res.data
          }
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .padding-5 {
  width: 140px;
  padding: 5px;
}
.area-container-sapn {
  color: #888888;
}
</style>
