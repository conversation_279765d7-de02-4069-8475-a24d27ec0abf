<template>
  <div id="exhibitionTable">
    <el-table :data="tableData" border style="width: 100%">
      <el-table-column type="index" label="序号" width="100" />
      <template v-if="!isEdit">
        <el-table-column prop="name" label="中文名称" />
        <el-table-column prop="en_name" label="拼音缩写" />
        <el-table-column label="数据类型">
          <template slot-scope="scope">
            <!-- <el-input v-model="scope.row.data_type.value" placeholder="请输入中文名称"></el-input> -->
            <span v-if="scope.row.data_type!=null">{{ scope.row.data_type.desc }}</span>
            <!-- <span v-else>{{}}</span> -->
          </template>
        </el-table-column>
        <!-- <el-table-column prop="" label=""></el-table-column> -->
        <el-table-column prop="len" label="长度" />
        <el-table-column prop="standard_field" label="对应标准属性" />
        <el-table-column prop="nullable" label="可空">
          <template slot-scope="scope">
            <span v-if="scope.row.nullable">是</span>
            <span v-else>否</span>
          </template>
        </el-table-column>
        <el-table-column prop="default_value" label="默认值" />
      </template>
      <template v-else>
        <el-table-column label="中文名称">
          <template slot-scope="scope">
            <el-form :ref="'nameform'+[scope.$index]" :model="scope.row" label-width="0px">
              <el-form-item
                prop="name"
                :rules="[{ required: true, message: '请输入中文名称'}, { min: 1, max: 50, message: '长度在 1 到 50 个字数', trigger: 'blur' }]"
              >
                <el-input v-model="scope.row.name" placeholder="请输入中文名称" />
              </el-form-item>
            </el-form>
          </template>
        </el-table-column>
        <el-table-column label="拼音缩写">
          <template slot-scope="scope">
            <el-form :ref="'en_nameform'+[scope.$index]" :model="scope.row" label-width="0px" :rules="rules">
              <el-form-item prop="en_name">
                <el-input v-model="scope.row.en_name" placeholder="请输入拼音缩写" />
              </el-form-item>
            </el-form>
          </template>
        </el-table-column>
        <el-table-column label="数据类型">
          <template slot-scope="scope">
            <!-- <el-input v-model="scope.row.data_type.value" placeholder="请输入数据类型"></el-input> -->
            <el-select v-model="scope.row.data_type.value" placeholder="请选择">
              <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="长度">
          <template slot-scope="scope">
            <el-form :ref="'lenform'+[scope.$index]" :model="scope.row" label-width="0px">
              <el-form-item prop="len" :rules="[{ required: true, message: '请输入长度'},{ type: 'number', message: '必须为数字值'}]">
                <el-input v-model.number="scope.row.len" placeholder="请输入长度" />
              </el-form-item>
            </el-form>
          </template>
        </el-table-column>
        <el-table-column label="对应标准属性">
          <template slot-scope="scope">
            <!-- <el-input
              v-if="scope.row.item_type=='common_type'"
              v-model="scope.row.standard_field"
              placeholder="请输入对应标准属性"
              :disabled="true"
            />-->
            <span>{{scope.row.standard_field}}</span>
          </template>
        </el-table-column>
        <el-table-column label="可空">
          <template slot-scope="scope">
            <el-checkbox
              v-model="scope.row.nullable"
              :disabled="scope.row.item_type=='common_type'"
              @change="checkboxChange(scope.row,scope.$index)"
            />
          </template>
        </el-table-column>
        <el-table-column label="默认值">
          <template slot-scope="scope">
            <span>{{scope.row.default_value}}</span>
            <!-- <el-input v-if="scope.row.item_type=='end_date_type'" v-model="scope.row.standard_field" placeholder="请输入对应标准属性" :disabled="true" /> -->
          </template>
        </el-table-column>
        <el-table-column label="操作" width="180">
          <template slot-scope="scope">
            <span v-if="scope.row.item_type=='end_date_type'&&scope.$index>5" style="color:#FF2B2B" @click="delectData(scope.$index)">删除</span>
            <span v-else>——</span>
          </template>
        </el-table-column>
      </template>
    </el-table>
  </div>
</template>

<script>
export default {
  name: 'ExhibitionTable',
  props: {
    isEdit: {
      type: Boolean,
      default: true
    },

    tableData: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    var validateRepeat = (rule, value, callback) => {
      let validateItem = []
      validateItem = this.tableData.filter(i => {
        return i.en_name == value
      })
      // console.log('validateItem', validateItem)
      if (validateItem.length > 1) {
        callback(new Error('该拼音缩写已存在'))
      } else {
        callback()
      }
    }
    return {
      options: [
        {
          value: 0,
          label: '字符型'
        },
        {
          value: 1,
          label: '数字型'
        },
        {
          value: 2,
          label: '日期型'
        },
        {
          value: 3,
          label: '二进制'
        },
        {
          value: 4,
          label: '复合'
        }
      ],
      rules: {
        en_name: [
          { required: true, message: '请输入拼音缩写' },
          { min: 2, max: 50, message: '长度在 2 到 50 个字数', trigger: 'blur' },
          { validator: validateRepeat, trigger: 'change' }
        ]
      }
    }
  },
  watch: {
    tableData: {
      handler(newName, oldName) {
        // console.log(newName, oldName)
        this.tableData = newName
        this.tableData.forEach(e => {
          // console.log(e.data_type)
          if (e.data_type == null) {
            e.data_type = {
              value: null
            }
          }
        })
        // console.log(this.tableData)
      }
    },
    deep: true
  },
  mounted() {
    this.$nextTick(() => {
      // console.log('this.tableData', this.tableData)
    })
  },

  methods: {
    delectData(index) {
      this.tableData.splice(index, 1)
    },
    checkboxChange(val, index) {
      // console.log(val, val1)
      if (val.nullable && index === 5) {
        val.default_value = '长期有效'
      } else {
        val.default_value = ''
      }
    },
    dataValidate() {
      const lenList = []
      const en_nameList = []
      const nameList = []
      this.tableData.forEach((e, index) => {
        this.$refs['lenform' + index].validate((valid, object) => {
          if (valid) {
            lenList.push(valid)
          }
        })
        this.$refs['en_nameform' + index].validate((valid, object) => {
          if (valid) {
            en_nameList.push(valid)
          }
        })
        this.$refs['nameform' + index].validate((valid, object) => {
          if (valid) {
            nameList.push(valid)
          }
        })
      })
      if (lenList.length == this.tableData.length && en_nameList.length == this.tableData.length && nameList.length == this.tableData.length) {
        console.log('校验成功')
        return true
      } else {
        return false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
#exhibitionTable ::v-deep .el-form .is-error {
  margin-top: 15px;
  margin-bottom: 15px;
}
#exhibitionTable ::v-deep .el-form-item {
  margin-top: 0px;
  margin-bottom: 0px;
}
</style>
