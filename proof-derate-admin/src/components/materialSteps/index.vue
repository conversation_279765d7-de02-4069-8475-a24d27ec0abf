<template>
  <div class="materialSteps">
    <div class="materialStepsWrap" v-for="(i,key) in stepsList" :key="key">
      <div class="materialSteps-item">
        <img src="~@/assets/images/<EMAIL>" alt v-if="i.status=='success'" />
        <!-- <div class="materialSteps-item-number" v-else-if="i.status=='success1'">{{key+1}}</div> -->
        <!-- #9f9f9f -->
        <div class="materialSteps-item-number" v-else>{{key+1}}</div>
        <span>{{i.label}}</span>
      </div>
      <div class="materialSteps-line" v-if="key!=stepsList.length-1"></div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {}
  },
  props: {
    stepsList: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  mounted() {},

  methods: {}
}
</script>

<style lang="scss" scoped>
.materialSteps {
  display: flex;
  align-items: center;
  justify-content: center;
  .materialStepsWrap {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  img {
    width: 24px;
    margin-right: 16px;
  }
  .materialSteps-item {
    display: flex;
    align-items: center;
    span {
      color: #333333;
    }
    &-number {
      width: 24px;
      height: 24px;
      border-radius: 50%;
      background: #2697ff;
      color: #fff;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-right: 16px;
    }
  }
  .materialSteps-line {
    width: 180px;
    height: 0px;
    border: 1px solid #c5c5c5;
    margin: 0 16px;
  }
}
</style>