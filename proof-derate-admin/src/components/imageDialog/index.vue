<template>
  <div id="imageDialog">
    <el-dialog title="图片预览" :visible.sync="dialogVisible" width="30%">
      <div class="imageDialog-wrap">
        <img :src="imgSrc" alt />
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  props: {
    imgSrc: {
      type: String,
      default: () => {
        return ''
      }
    }
  },
  data() {
    return {
      dialogVisible: false
    }
  },

  mounted() {},

  methods: {}
}
</script>

<style lang="scss" scoped>
.imageDialog-wrap {
  img {
    width: 100%;
    max-height: 400px;
    object-fit: contain;
  }
}
</style>