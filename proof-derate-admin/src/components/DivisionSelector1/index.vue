<template>
  <el-cascader
    ref="division"
    :key="key2"
    :options="divisions"
    :props="divisionOptions"
    clearable
    style="width: 100%"
    :value="code"
    :disabled="disabled"
    @change="change1"
  />
</template>

<script>
import { getAllParentValue, childDivisions } from '@/api/admin/baseobject'

export default {
  name: 'DivisionSelector1',
  model: {
    prop: 'code',
    event: 'changeModel'
  },
  props: {
    code: {
      type: String,
      default: ''
    },
    code1: {
      type: String,
      default: ''
    },
    root: {
      type: String,
      default: ''
    },
    maxLevel: {
      type: Number,
      default: 5
    },
    disabled: {
      type: Boolean
    },
    org: {
      type: Boolean
    },
    pCode: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      divisions: [],
      divisionOptions: {},
      key2: +new Date(),
      sdisabled: this.disabled
    }
  },
  watch: {
    code() {
      this.loadOptionsByValue()
    }
  },
  async mounted() {
    let code = ''
    if (this.root) {
      code = this.root
      // 兼容组织机构修改、详情、删除、废置机构选择行政区划
    } else if (this.org) {
      code = this.pCode
      // 账号选择行政区划
    } else {
      code = JSON.parse(this.$store.state.user.organization).division_code
    }
    // 获取当前行政区划代码全部上级行政区划
    this.getAllParentValue(code, 1)
  },
  methods: {
    checkLeaf(list) {
      return (list || []).map(x => {
        const leaf = this.maxLevel && this.maxLevel <= x.valueLevel
        return Object.assign(x, { leaf })
      })
    },
    lazyLoad(node, resolve) {
      childDivisions(node.value).then(response => {
        if (this.checkLeaf(response)) {
          resolve(this.checkLeaf(response))
        } else {
          resolve([])
        }
      })
    },
    change1(code) {
      console.log('change call', code)
      this.getAllParentValue(code, 2)
    },
    async loadOptionsByValue() {
      const parents = this.allParentValue
      const options = this.divisions
      let match = options.find(x => parents.some(y => y.value === x.value))
      while (match) {
        if (!match.children || !match.children.length) {
          // 匹配当前机构的子机构
          match.children = await childDivisions(match.value)
        }
        match = match.children.find(x => parents.some(y => y.value === x.value))
      }
      this.key2 = +new Date()
    },
    getAllParentValue(code, num) {
      getAllParentValue(code).then(resp => {
        if (resp) {
          this.allParentValue = resp
          if (num == 1) {
            if (this.org) {
              this.divisions = [{ name: this.allParentValue[0].name, value: this.allParentValue[0].value, valueLevel: this.allParentValue[0].valueLevel }]
            } else {
              this.divisions = [
                {
                  name: this.allParentValue[this.allParentValue.length - 1].name,
                  value: this.allParentValue[this.allParentValue.length - 1].value,
                  valueLevel: this.allParentValue[this.allParentValue.length - 1].valueLevel
                }
              ]
            }
            this.divisionOptions = {
              checkStrictly: true,
              value: 'value',
              label: 'name',
              lazy: true,
              expandTrigger: 'hover',
              emitPath: false,
              lazyLoad: this.lazyLoad
            }
            this.loadOptionsByValue()
          } else if (num == 2) {
            let r
            if (code != null) {
              this.$refs.division.toggleDropDownVisible()
              if (!code || !code.length) {
                r = { code }
              } else {
                const code = JSON.parse(this.$store.state.user.organization).division_code
                r = { code }
              }
            }
            r = { code }
            this.$emit('change', r)
          }
        }
      })
    }
  }
}
</script>

<style>
</style>
