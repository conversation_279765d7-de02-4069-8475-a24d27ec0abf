import Vue from 'vue'

import 'normalize.css/normalize.css' // A modern alternative to CSS resets

import ElementUI from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'
// require(`../public/static/theme/${link}/theme/index.css`)

// import '../public/theme/greenTheme/theme/index.css'
// import locale from 'element-ui/lib/locale/lang/en' // lang i18n

import 'admin-lte/dist/css/AdminLTE.css'
import 'admin-lte/dist/css/skins/_all-skins.css'
import '@/styles/index.scss' // global css
import '@/styles/proof.scss'

import App from './App'
import store from './store'
import router from './router'
import Rsa from '@/utils/rsa.js'
import _ from 'lodash'
import '@/icons' // icon
import '@/permission' // permission control
import { loadAppConfig } from './utils/config-loader'
// Vue.use(Element, {
//   size: 'mini', // set element-ui default size
//   // locale: enLang // 如果使用中文，无需设置，请删除
// })
// set ElementUI lang to EN
// Vue.use(ElementUI, { locale })
// 如果想要中文版 element-ui，按如下方式声明
Vue.use(ElementUI)
Vue.prototype._ = _
Vue.prototype.Rsa = Rsa // 将Rsa注册为公共方法,方便其他页面调用
Vue.config.productionTip = false
Vue.prototype.$vm = new Vue()

// 按扭权限指令
Vue.directive('permission', {
  inserted: (el, binding, vnode) => {
    // console.log('vnode',vnode)
    const permissionList = vnode.context.$route.meta.permission || []
    // const permissionList = vnode.context.$route.button
    // console.log(permissionList, binding.value)
    const _ = []
    permissionList.forEach(element => {
      _.push(element.key)
    })

    if (!_.includes(binding.value)) {
      el.parentNode.removeChild(el)
    } else {
      const _value = permissionList.filter(item => item.key === binding.value)
      el.childNodes[el.childNodes.length - 1].childNodes[0].nodeValue = _value[0].value
    }
  }
})

/* async function initApp() {
  // 加载配置
  const config = await loadConfig()
  console.log('config', config)
  // 将配置挂载到 Vue 原型上
  Vue.prototype.$appConfig = config

  // 也可以作为全局属性使用
  // Vue.config.globalProperties.$appConfig = config

  new Vue({
    el: '#app',
    router,
    store,
    render: (h) => h(App),
  })
}
initApp() */

// 使用标准的异步函数
async function initApp () {
  try {
    // 1. 加载配置
    const appConfig = await loadAppConfig()
    console.log('config配置：', appConfig)

    // 2. 注入Vue原型
    Vue.prototype.$appConfig = appConfig

    // 3. 作为全局属性
    // Vue.config.globalProperties.$appConfig = appConfig

    // 4. 启动应用
    new Vue({
      el: '#app',
      router,
      store,
      render: (h) => h(App),
    })
  } catch (error) {
    console.error('应用初始化失败:', error)
    showErrorPage(error)
  }
}

// 错误显示函数
function showErrorPage(error) {
  const appEl = document.getElementById('app')
  if (appEl) {
    appEl.innerHTML = `
      <div style="padding: 20px; font-family: sans-serif; color: #721c24; background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 4px;">
        <h2 style="margin-top: 0;">应用初始化失败</h2>
        <p><strong>错误详情：</strong> ${error.message}</p>
        <button
          style="padding: 8px 16px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;"
          onclick="window.location.reload()"
        >
          重新加载
        </button>
      </div>
    `
  }
}

// 启动应用
initApp().catch(error => {
  console.error('未捕获的初始化错误:', error)
  showErrorPage(error)
})
