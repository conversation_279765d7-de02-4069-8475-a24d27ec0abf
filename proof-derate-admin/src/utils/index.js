/**
 * Parse the time to string
 * @param {(Object|string|number)} time
 * @param {string} cFormat
 * @returns {string | null}
 */
export function parseTime(time, cFormat) {
  if (arguments.length === 0 || !time) {
    return null
  }
  const format = cFormat || '{y}-{m}-{d} {h}:{i}:{s}'
  let date
  if (typeof time === 'object') {
    date = time
  } else {
    if (typeof time === 'string') {
      if (/^[0-9]+$/.test(time)) {
        // support "1548221490638"
        time = parseInt(time)
      } else {
        // support safari
        time = time.replace(new RegExp(/-/gm), '/')
      }
    }

    if (typeof time === 'number' && time.toString().length === 10) {
      time = time * 1000
    }
    date = new Date(time)
  }
  const formatObj = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay()
  }
  const time_str = format.replace(/{([ymdhisa])+}/g, (result, key) => {
    const value = formatObj[key]
    // Note: getDay() returns 0 on Sunday
    if (key === 'a') {
      return ['日', '一', '二', '三', '四', '五', '六'][value]
    }
    return value.toString().padStart(2, '0')
  })
  return time_str
}

/**
 * @param {number} time
 * @param {string} option
 * @returns {string}
 */
export function formatTime(time, option) {
  if (('' + time).length === 10) {
    time = parseInt(time) * 1000
  } else {
    time = +time
  }
  const d = new Date(time)
  const now = Date.now()

  const diff = (now - d) / 1000

  if (diff < 30) {
    return '刚刚'
  } else if (diff < 3600) {
    // less 1 hour
    return Math.ceil(diff / 60) + '分钟前'
  } else if (diff < 3600 * 24) {
    return Math.ceil(diff / 3600) + '小时前'
  } else if (diff < 3600 * 24 * 2) {
    return '1天前'
  }
  if (option) {
    return parseTime(time, option)
  } else {
    return (
      d.getMonth() +
      1 +
      '月' +
      d.getDate() +
      '日' +
      d.getHours() +
      '时' +
      d.getMinutes() +
      '分'
    )
  }
}

/**
 * @param {string} url
 * @returns {Object}
 */
export function param2Obj(url) {
  const search = decodeURIComponent(url.split('?')[1]).replace(/\+/g, ' ')
  if (!search) {
    return {}
  }
  const obj = {}
  const searchArr = search.split('&')
  searchArr.forEach((v) => {
    const index = v.indexOf('=')
    if (index !== -1) {
      const name = v.substring(0, index)
      const val = v.substring(index + 1, v.length)
      obj[name] = val
    }
  })
  return obj
}

/**
 * 文件类型转base64
 * @param {*} blob
 * @param {*} cb
 */
export function blobToDataURL(blob, cb) {
  const reader = new FileReader()
  reader.onload = function(evt) {
    const base64 = evt.target.result
    cb(base64)
  }
  reader.readAsDataURL(blob)
}
// base64 转 blob
export function base64toBlob(base64, type) {
  // 将base64转为Unicode规则编码
  const bstr = atob(base64)
  let n = bstr.length
  const u8arr = new Uint8Array(n)
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n) // 转换编码后才可以使用charCodeAt 找到Unicode编码
  }
  return new Blob([u8arr], { type })
}
// base64导出文件并下载
export function dataURLtoDownload(dataurl, name) {
  var bstr = atob(dataurl) // 解析 base-64 编码的字符串
  var n = bstr.length
  var u8arr = new Uint8Array(n) // 创建初始化为0的，包含length个元素的无符号整型数组
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n) // 返回字符串第一个字符的 Unicode 编码
  }
  const blob = new Blob([u8arr]) // 转化成blob
  const url = URL.createObjectURL(blob) // 这个新的URL 对象表示指定的 File 对象或 Blob 对象
  const a = document.createElement('a') // 创建一个a标签
  a.href = url
  a.download = name
  a.click()
  URL.revokeObjectURL(a.href) // 释放之前创建的url对象
}
// 判断是否为配置文件类型
export function getIsWhitelist(fileName, whitelist) {
  const fileSuffix = fileName.substring(fileName.lastIndexOf('.') + 1)
  console.log(fileName, fileSuffix)
  const isWhitelist = whitelist.includes(fileSuffix.toLocaleLowerCase())
  return isWhitelist
}
// 判断是否为配置文件类型(通用骨架)
export function getIsWhitelist2(fileName, whitelist) {
  let fileSuffix = fileName.substring(fileName.lastIndexOf('.'))
  console.log(fileName)
  console.log('fileSuffix',fileSuffix)
  console.log('whitelist',whitelist)
  const isWhitelist = whitelist.includes(fileSuffix.toLocaleLowerCase())
  return isWhitelist
}
/* 部分隐藏处理
 ** str 需要处理的字符串
 ** frontlen 保留的前几位
 **endLen 保留的后几位
 ** cha 替换的字符串
 ** author-hx
 */
export function plusXing(str, frontLen, endLen, cha) {
  const len = str.length - frontLen - endLen
  let xing = ''
  for (let i = 0; i < len; i++) {
    xing += cha
  }
  return str.substring(0, frontLen) + xing + str.substring(str.length - endLen)
}

/**
 * @param {Function} func
 * @param {number} wait
 * @param {boolean} immediate
 * @return {*}
 */
export function debounce(func, wait, immediate) {
  let timeout, args, context, timestamp, result

  const later = function() {
    // 据上一次触发时间间隔
    const last = +new Date() - timestamp

    // 上次被包装函数被调用时间间隔 last 小于设定时间间隔 wait
    if (last < wait && last > 0) {
      timeout = setTimeout(later, wait - last)
    } else {
      timeout = null
      // 如果设定为immediate===true，因为开始边界已经调用过了此处无需调用
      if (!immediate) {
        result = func.apply(context, args)
        if (!timeout) context = args = null
      }
    }
  }

  return function(...args) {
    context = this
    timestamp = +new Date()
    const callNow = immediate && !timeout
    // 如果延时不存在，重新设定延时
    if (!timeout) timeout = setTimeout(later, wait)
    if (callNow) {
      result = func.apply(context, args)
      context = args = null
    }

    return result
  }
}

/* 下载（导出）功能，携带请求头token
 ** url 接口地址
 ** param 请求参数
 ** xlsName 文件名称
 ** author-hx
 */
import axios from 'axios'
import { getToken } from '@/utils/auth'

export function exportsDown(url, param = {}, xlsName, type) {
  // 这是请求的地址 （自定义）
  // let _herf = window.host
  const _herf =
    type === 'licc'
      ? process.env.VUE_APP_BASE_LICC_API
      : process.env.VUE_APP_BASE_API
  // 这是请求头中需要的token （自定义）
  const token = getToken()
  axios
    .post(`/${_herf}${url}`, param, {
      responseType: 'blob',
      headers: {
        Authorization: token,
        // 这是格式，看后段要不要你传 （自定义）
        ContentType: 'application/json'
      }
    })
    .then((res) => {
      // 没传文件名就取后端返回来的文件名
      const filename =
        xlsName ||
        (res.headers['content-disposition']
          ? decodeURIComponent(
            res.headers['content-disposition'].split('=')[1]
          ).replace("utf-8''", '')
          : 'download.xls')
    console.log(filename, res, res.headers['content-disposition'])
    const blob = res.data
    const reader = new FileReader()
    reader.readAsDataURL(blob)
    reader.onload = (e) => {
      const a = document.createElement('a')
      a.download = filename
      a.href = e.target.result
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
    }
  })
    .catch((err) => {
      console.log(err.message)
    })
}

/* 判断是否有权限
 ** @param {array} permissionList 当前页面的权限集合
 ** @param {string|array} permissionCode 要验证的权限标识
 * demp isPermission(this.$route.meta.permission, key)
 ** author-hx
 */
export function isPermission(permissionList, permissionCode) {
  if (!permissionList) return false
  const _ = []
  permissionList.forEach((element) => {
    _.push(element.key ? element.key : element)
  })
  const falge = _.includes(permissionCode)
  // console.log(falge, permissionList, permissionCode, _)
  return falge
}

/**
 * @param {string} url
 * @returns {Object}
 */
export function getQueryObject(url) {
  url = url == null ? window.location.href : url
  const search = url.substring(url.lastIndexOf('?') + 1)
  const obj = {}
  const reg = /([^?&=]+)=([^?&=]*)/g
  search.replace(reg, (rs, $1, $2) => {
    const name = decodeURIComponent($1)
    let val = decodeURIComponent($2)
    val = String(val)
    obj[name] = val
    return rs
  })
  return obj
}
/**
 * 返回一个格式化文字函数
 * @param {String} char
 * @param {String} type '一个为三个值，left(添加到数字左边)，right(添加到数字右边)，both(添加到两边)'
 * @returns {Function}
 */
export function formatChar(char, type = 'left') {
  const _char = String(char)
  return function(target) {
    return type === 'left'
      ? `${_char}${target}`
      : type === 'right'
        ? `${target}${_char}`
        : type === 'both'
          ? `${_char}${target}${_char}`
          : `${target}`
  }
}
// 获取文件名和文件后缀
export function getFileType(filePath) {
  const startIndex = filePath.lastIndexOf('.')
  if (startIndex !== -1) {
    return {
      fileSuffix: filePath
        .substring(startIndex + 1, filePath.length)
        .toLowerCase(),
      fileName: filePath.substring(0, startIndex).toLowerCase()
    }
  } else {
    return ''
  }
}
// 获取一个月的日期范围
// this.getMonth(type,months)
// type为字符串类型，有两种选择，"s"代表开始,"e"代表结束，months为数字类型，0代表本月，-1代表上月，1代表下月
export function getMonth(type, months) {
  var d = new Date()
  var year = d.getFullYear()
  var month = d.getMonth() + 1
  if (Math.abs(months) > 12) {
    months = months % 12
  }
  if (months !== 0) {
    if (month + months > 12) {
      year++
      month = (month + months) % 12
    } else if (month + months < 1) {
      year--
      month = 12 + month + months
    } else {
      month = month + months
    }
  }
  month = month < 10 ? '0' + month : month
  var date = d.getDate()
  var firstday = year + '-' + month + '-' + '01'
  var lastday = ''
  if (
    month === '01' ||
    month === '03' ||
    month === '05' ||
    month === '07' ||
    month === '08' ||
    month === '10' ||
    month === '12'
  ) {
    lastday = year + '-' + month + '-' + 31
  } else if (month === '02') {
    if (
      (year % 4 === 0 && year % 100 !== 0) ||
      (year % 100 === 0 && year % 400 === 0)
    ) {
      lastday = year + '-' + month + '-' + 29
    } else {
      lastday = year + '-' + month + '-' + 28
    }
  } else {
    lastday = year + '-' + month + '-' + 30
  }
  var day = ''
  if (type === 's') {
    day = firstday
  } else {
    day = lastday
  }
  return day
}

/**
 *
 * @param {所有权限列表} allList
 * @param {需要的权限列表} list
 * @param {表格展示的数据} tableList
 */
export function getOperationPermissionList(allList, list, tableList) {
  let tableHeader = tableList
  const length = tableList.length
  if (allList && allList.length) {
    const intersection = []
    allList.forEach((x) => {
      list.forEach((y) => {
        if (x.key == y) {
          // 找到相同的就push进新的数组
          intersection.push(x.key)
        }
      })
    })
    if (intersection.length == 0) {
      tableHeader = tableList.slice(0, length - 1)
    }
  } else {
    tableHeader = tableList.slice(0, length - 1)
  }

  return tableHeader
}

/**
 * 枚举定义工具
 * 示例：
 * const STATUS = createEnum({
 *     ACTIVE: [1, 'active'],
 *     INACTIVE: [2, 'inactive']
 * })
 * 通过枚举值列表：STATUS.list
 * 获取枚举值：STATUS.ACTIVE
 * 获取枚举描述：STATUS.getDesc('ACTIVE')
 * 通过枚举值获取描述：STATUS.getDescFromValue(STATUS.INACTIVE)
 * * 通过描述获取枚举值：SEARCH_TYPE.getValueFromDesc('ACTIVE')
 */
export function createEnum(definition) {
  const strToValueMap = {}
  const numToDescMap = {}
  const list = []
  for (const enumName of Object.keys(definition)) {
    const [value, desc] = definition[enumName]
    strToValueMap[enumName] = value
    numToDescMap[value] = desc
    list.push({ label: desc, value })
  }

  return Object.freeze({
    list,
    ...strToValueMap,
    getDesc(enumName) {
      return (definition[enumName] && definition[enumName][1]) || ''
    },
    getDescFromValue(value) {
      return numToDescMap[value] || ''
    },
    getValueFromDesc(value) {
      return strToValueMap[value] || 0
    }
  })
}

/**
 * 检查文件名中是否有特殊字符
 * @param {*} fileName
 * @returns
 */
export function hasSpecialCharacters(fileName) {
  const pattern = /[\|\$\@\'\"/\.\.//\\"()+'\\\"\\http%<>;&]/g
  const fileNameNoSuffix = fileName.match(/[^\\/]*(?=\.\w+$)/)[0]
  const result = fileNameNoSuffix.match(pattern)
  if (result && result.length) {
    return true
  } else {
    return false
  }
}

export function exportDataByGetMethod(url, param = {}, xlsName, type = '') {
  // 这是请求的地址 （自定义）
  // let _herf = window.host
  // const _herf = process.env.VUE_APP_BASE_API
  const _herf =
    type === 'licc'
      ? process.env.VUE_APP_BASE_LICC_API
      : process.env.VUE_APP_BASE_API
  // 这是请求头中需要的token （自定义）
  const token = getToken()
  console.log(token)
  axios
    .get(`/${_herf}${url}`, {
      param,
      responseType: 'blob',
      headers: {
        Authorization: token,
        // 这是格式，看后段要不要你传 （自定义）
        ContentType: 'application/json'
      }
    })
    .then((res) => {
      console.log('res', res)
      const blob = res.data
      const reader = new FileReader()
      reader.readAsDataURL(blob)
      reader.onload = (e) => {
        const a = document.createElement('a')
        a.download = xlsName
        a.href = e.target.result
        document.body.appendChild(a)
        a.click()
        document.body.removeChild(a)
      }
    })
    .catch((err) => {
      console.log('err', err.message)
    })
}

/*
 * 下划线转换成小驼峰
 * hx-2024-3.15
 * 使用示例
const snakeCaseData = {
  first_name: "John",
  last_name: "Doe",
  user_info: {
    age: 30,
    email_address: "<EMAIL>",
  },
};
const camelCaseData = convertToCamelCase(snakeCaseData);
console.log(camelCaseData);
*/
export function convertToCamelCase(obj) {
  if (Array.isArray(obj)) {
    return obj.map(convertToCamelCase)
  } else if (obj !== null && obj.constructor === Object) {
    return Object.keys(obj).reduce((result, key) => {
      const camelCaseKey = toCamelCase(key)
      result[camelCaseKey] = convertToCamelCase(obj[key])
      return result
    }, {})
  }
  return obj
}
function toCamelCase(str) {
  return str.replace(/([-_][a-z])/gi, ($1) => {
    return $1.toUpperCase().replace('-', '').replace('_', '')
  })
}

/*
 * 小驼峰转换成下划线
 * hx-2024-3.15
 * 使用示例
const camelCaseObj = {
  myVariableName: "value",
  nestedObject: {
    anotherVariableName: "anotherValue",
  },
};
const snakeCaseObj = convertToSnakeCase(camelCaseObj);
console.log(snakeCaseObj);
 */
export function convertToSnakeCase(obj) {
  if (Array.isArray(obj)) {
    return obj.map(convertToSnakeCase)
  } else if (obj !== null && typeof obj === 'object') {
    return Object.keys(obj).reduce((result, key) => {
      const snakeCaseKey = key
        .replace(/([a-z0-9])([A-Z])/g, '$1_$2')
        .toLowerCase()
      result[snakeCaseKey] = convertToSnakeCase(obj[key])
      return result
    }, {})
  }
  return obj
}

/**
 * 根据版本，返回各版本的权限标识
 * 示例
 **/
export function convertBtnPermission(list, versions) {
  return list[versions]
}

/**
 * 根据版本，返回各版本的api地址
 * 示例
 **/
export function getApiUrl(url, versions) {
  return versions === 'V2' ? url.replace(/\/common\//, '/gd/') : url
}
// 将传入的路由的三级菜单取出来
export function collectLastChildrenRoutes(routes) {
  const lastLevelRoutes = []
  function traverseRoutes(routeList) {
    routeList.forEach((route) => {
      if (route.children) {
        const obj = {
          path: route.path,
          component: route.component,
          redirect: route.redirect,
          name: route.name,
          meta: route.meta,
          isOutherPath: false, // 统一处理为内部菜单
          isCatalogue: true // 标记不是菜单
        }
        // 如果当前路由有子路由，则递归遍s历子路由
        lastLevelRoutes.push(obj)
        traverseRoutes(route.children)
      } else {
        // 如果没有子路由，说明是最后一级，将其添加到结果数组中
        // route.isInternalLink = true
        lastLevelRoutes.push(route)
      }
    })
  }
  traverseRoutes(routes)
  return lastLevelRoutes
}


// 下载当前上传的文件
export function downURLfile(url, name) {
  // const blob = new Blob()
  console.log(url, name)
  const downLink = document.createElement('a')
  downLink.download = name
  // downLink.href = URL.createObjectURL(url)
  downLink.href = url
  // 触发点击
  document.body.appendChild(downLink)
  downLink.click()
  // 然后移除
  document.body.removeChild(downLink)
}

/**
 * 方法一
 *  table合并行通用
 * @param type single 开启以第一行为合并行数为标准
 * @returns {array}
 */
export function mergeTableRow1(data, merge, type) {
  if (!merge || merge.length === 0) {
    return data
  }

  merge.forEach(m => {
    const mList = {}
    const spliceLocation = merge.indexOf(m)
    data = data.map((v, index) => {
      const rowVal = v[m]
      if (mList[rowVal] && mList[rowVal].newIndex === index) {
        // flag为true的时候代表当前值的前者与上一个值相等
        /*
                  只有当当前merge之前的所有merge的值的当前index等于index-1  才允许当前merge合并
              */
        let flag = false
        const mergeSolve = merge.slice(0, spliceLocation)
        mergeSolve.slice(0, spliceLocation).forEach(mergeItem => {
          if (data[index][mergeItem] == data[index - 1][mergeItem]) {
            flag = true
          }
        })
        if (m == merge[0]) {
          flag = true
        }
        if (flag) {
          mList[rowVal]['num']++
          mList[rowVal]['newIndex']++
          data[mList[rowVal]['index']][m + '-span'].rowspan++
          v[m + '-span'] = {
            rowspan: 0,
            colspan: 0
          }
        } else {
          mList[rowVal] = {
            num: 1,
            index: index,
            newIndex: index + 1
          }
          v[m + '-span'] = {
            rowspan: 1,
            colspan: 1
          }
        }
      } else {
        mList[rowVal] = {
          num: 1,
          index: index,
          newIndex: index + 1
        }
        v[m + '-span'] = {
          rowspan: 1,
          colspan: 1
        }
      }
      return v
    })
  })
  if (type === 'single' && data.length != 0) {
    const newData = data.map((i, index) => {
      const temp = i
      const firstSpan = i[merge[0] + '-span']
      const mergeField = {}

      merge.forEach(n => {
        mergeField[n + '-span'] = firstSpan
      })
      return { ...temp, ...mergeField }
    })
    return newData
  } else {
    return data
  }
}

// 数字转中文数字
export function numberToChinese(num) {
  var units = ['', '十', '百', '千', '万', '十万', '百万', '千万', '亿']
  var digits = ['', '一', '二', '三', '四', '五', '六', '七', '八', '九']

  var result = String(num).replace(/./g, function (digit, index, array) {
    return digits[Number(digit)] + units[array.length - index - 1]
  })

  return result
}
