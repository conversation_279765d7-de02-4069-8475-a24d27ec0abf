const sm3 = require('sm-crypto').sm3
const sm2 = require('sm-crypto').sm2
/**
 * sm3加密
 * @param {明文} text
 * @param {盐值} saltValue
 * @param {盐值位置} saltPosition
 * @param {摘要次数} digestTimes
 */
export function sm3Encrypt(plaintext, saltValue, saltPosition, digestTimes) {
  saltValue = saltValue ? saltValue : ''
  saltPosition = saltPosition ? saltPosition : 0
  digestTimes = digestTimes ? digestTimes : 1
  let text = plaintext
  if (saltValue) {
    //输入盐值和盐值位置
    let tempArray = stringToByte(plaintext)
    let sm3SaltArray = stringToByte(saltValue)
    if (saltPosition) {
      //盐值位置不为0(盐值位置是指字节数组转换后的插入的位置)
      let position = plaintext.length < saltPosition ? plaintext.length : saltPosition
      if (saltPosition < plaintext.length) {
        let chunkArray1 = tempArray.slice(0, position)
        let chunkArray2 = tempArray.slice(position)
        let contactArray = [...chunkArray1, ...sm3SaltArray, ...chunkArray2]
        text = contactArray
      } else {
        let contactArray = [...tempArray, ...sm3SaltArray]
        text = contactArray
      }
    } else {
      let contactArray = [...sm3SaltArray, ...tempArray]
      text = contactArray
    }
  }

  return sm3(text, digestTimes)
}
/**
 * 字符串转字节序列
 * @param {*} str
 */
function stringToByte(str) {
  var bytes = new Array()
  for (var i = 0; i < str.length; i++) {
    var c = str.charCodeAt(i)
    var s = parseInt(c).toString(2)
    if (c >= parseInt('000080', 16) && c <= parseInt('0007FF', 16)) {
      var af = ''
      for (var j = 0; j < 11 - s.length; j++) {
        af += '0'
      }
      af += s
      var n1 = parseInt('110' + af.substring(0, 5), 2)
      var n2 = parseInt('110' + af.substring(5), 2)
      if (n1 > 127) n1 -= 256
      if (n2 > 127) n2 -= 256
      bytes.push(n1)
      bytes.push(n2)
    } else if (c >= parseInt('000800', 16) && c <= parseInt('00FFFF', 16)) {
      var af = ''
      for (var j = 0; j < 16 - s.length; j++) {
        af += '0'
      }
      af += s
      var n1 = parseInt('1110' + af.substring(0, 4), 2)
      var n2 = parseInt('10' + af.substring(4, 10), 2)
      var n3 = parseInt('10' + af.substring(10), 2)
      if (n1 > 127) n1 -= 256
      if (n2 > 127) n2 -= 256
      if (n3 > 127) n3 -= 256
      bytes.push(n1)
      bytes.push(n2)
      bytes.push(n3)
    } else if (c >= parseInt('010000', 16) && c <= parseInt('10FFFF', 16)) {
      var af = ''
      for (var j = 0; j < 21 - s.length; j++) {
        af += '0'
      }
      af += s
      var n1 = parseInt('11110' + af.substring(0, 3), 2)
      var n2 = parseInt('10' + af.substring(3, 9), 2)
      var n3 = parseInt('10' + af.substring(9, 15), 2)
      var n4 = parseInt('10' + af.substring(15), 2)
      if (n1 > 127) n1 -= 256
      if (n2 > 127) n2 -= 256
      if (n3 > 127) n3 -= 256
      if (n4 > 127) n4 -= 256
      bytes.push(n1)
      bytes.push(n2)
      bytes.push(n3)
      bytes.push(n4)
    } else {
      bytes.push(c & 0xff)
    }
  }
  return bytes
}

/**
 * 生成sm4随机key
 */
export function generateSM4RandomKey() {
  const key = '1ab21d8355cfa17f8e61194831e81a8f22bec8c728fefb747ed035eb5082aa2b'
  var result = ''
  for (var i = 0; i < key.length / 2; i += 8) {
    let tempnum = bigxor(parseInt(key.slice(i, i + 8), 16), Math.round(Math.random() * 1000000000)).toString(16)
    result += tempnum.length == 8 ? tempnum : '0'.repeat(8 - tempnum.length) + tempnum
  }
  return result
}

function bigxor(a, b) {
  var abin = a.toString(2)
  var bbin = b.toString(2)
  var loggest = abin.length >= bbin.length ? abin.length : bbin.length
  abin = abin.length == loggest ? abin : '0'.repeat(loggest - abin.length) + abin
  bbin = bbin.length == loggest ? bbin : '0'.repeat(loggest - bbin.length) + bbin
  var result = ''
  for (var i = loggest - 1; i >= 0; i--) {
    result = abin[i] == bbin[i] ? '0' + result : '1' + result
  }
  return parseInt(result, 2)
}

/**
 * sm2加密
 */
export function sm2Encode(sm2Plaintext, sm2PublicKey) {
  let sm2EncodeText = ''
  let sm2PublicKeyNum = sm2PublicKey.indexOf('034200')
  if (sm2PublicKeyNum !== -1) {
    const sm2PublicKeySbu = sm2PublicKey.substring(sm2PublicKeyNum + 6, sm2PublicKey.length)
    console.log('sm2PublicKeySbu', sm2PublicKeySbu)
    sm2EncodeText = sm2.doEncrypt(sm2Plaintext, sm2PublicKeySbu)
  } else {
    sm2EncodeText = sm2.doEncrypt(sm2Plaintext, sm2PublicKey)
  }
  return '04' + sm2EncodeText
}

/**
 * 生成sm2公钥
 */
function createSm2PublicKey() {
  let data = sm2.generateKeyPairHex()
  this.sm2PublicKey = data.publicKey
  this.sm2SecretKey = data.privateKey
}
/**
 * 生成sm2私钥
 */
function createSm2SecretKey() {
  this.sm2SecretKey = sm2.generateKeyPairHex().privateKey
}
