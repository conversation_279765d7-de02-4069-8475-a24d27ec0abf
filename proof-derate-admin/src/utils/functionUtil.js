export default {
  /**
   * @desc 函数防抖
   * @param func 函数
   * @param wait 延迟执行毫秒数
   * @param immediate true 表立即执行，false 表非立即执行
   */
  debounce(func, wait = 200, immediate = false) {
    let timeout
    return function() {
      if (timeout) clearTimeout(timeout)

      if (immediate) {
        const callNow = !timeout
        timeout = setTimeout(() => {
          timeout = null
          func.apply(this, arguments)
        }, wait)
        if (callNow) func.apply(this, arguments)
      } else {
        timeout = setTimeout(() => {
          func.apply(this, arguments)
        }, wait)
      }
    }
  },
  /**
   * @desc 函数节流
   * @param func 函数
   * @param wait 延迟执行毫秒数
   */
  throttle(func, wait = 200) {
    let timeout
    return function() {
      if (!timeout) {
        timeout = setTimeout(() => {
          func.apply(this, arguments)
          timeout = null
        }, wait)
      }
    }
  }
}
