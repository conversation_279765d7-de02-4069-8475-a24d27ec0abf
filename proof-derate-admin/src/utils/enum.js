export default {
  // 事项标准类型
  standardTypeList: [],
  // 事项类型
  mattersTypeList: [],
  // 实施区划
  actualizeList: [],
  // 证明开具单位类型
  unitTypeList: [],
  // 所属行业部门
  departmentList: [],
  // 事项证明状态
  proofStatusList: [],
  // 证明标准类型
  proofStandardTypeList: [],
  // 证明标准类型
  materialTypeList: [],
  // 证明材料类型
  proveMaterialTypeList: [],
  // 对比结果
  comparisonResults : [
    {value:'SAME',label:'一致'},
    {value:'DIFF',label:'不一致'},
  ],
  // 电子证明开具类型枚举
  electronicProofIssueType : {
    ISSUE_UNLIMITED: 'ISSUE_UNLIMITED',
    NOT_ISSUE_ASSIST_ONLY: 'NOT_ISSUE_ASSIST_ONLY',
    NOT_ISSUE_ANY_REJECT: 'NOT_ISSUE_ANY_REJECT',
    NOT_ISSUE_LAST_REJECT: 'NOT_ISSUE_LAST_REJECT'
  },
  // 电子证明开具类型选项列表
   electronicProofIssueOptions : [
    { value: 'ISSUE_UNLIMITED', label: '开具（不受审核结果限制）' },
    { value: 'NOT_ISSUE_ASSIST_ONLY', label: '不开具（仅协查，不绑定电子证明）' },
    { value: 'NOT_ISSUE_ANY_REJECT', label: '不开具（任一部门审核不通过则不开具）' },
    { value: 'NOT_ISSUE_LAST_REJECT', label: '不开具（最后一级部门审核不通过则不开具）' }
  ]
}
