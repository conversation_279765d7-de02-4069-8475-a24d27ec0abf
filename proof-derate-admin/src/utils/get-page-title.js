import defaultSettings from '@/settings'
import store from '@/store'

let title = ''
// 获取最新的systemInfo
function getSystemInfo() {
  const systemInfo = JSON.parse(sessionStorage.getItem('systemInfo'))
  if (systemInfo === null) {
    store.dispatch('settings/changeSetting').then((res) => {
      title = res.title
    })
  } else {
    title = systemInfo.title || defaultSettings.title
  }
}
export default function getPageTitle(pageTitle) {
  getSystemInfo()
  if (pageTitle) {
    // return `${pageTitle} - ${title}`
    return `${title}`
  }
  return `${title}`
}
