import Cookies from 'js-cookie'

const TokenKey = process.env.VUE_APP_BASE_PROJECT_NAME || 'licc-func-server'
const MenuName = 'Menu-Name'
export { TokenKey }
export function getToken() {
  return Cookies.get(TokenKey)
}

export function setToken(token) {
  return Cookies.set(Token<PERSON>ey, token)
}

export function removeToken() {
  return Cookies.remove(TokenKey)
}

export function getMenuName() {
  return Cookies.get(MenuName)
}
export function removeMenuName() {
  return Cookies.get(MenuName)
}
