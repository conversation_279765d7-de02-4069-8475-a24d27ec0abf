export function setSkinColor(color) {
  const skinList = [
    { color: '#1f9e73', linkStr: 'greenTheme' },
    { color: '#4293f4', linkStr: 'lightBlueTheme' },
    { color: '#0747a6', linkStr: 'darkBlueTheme' },
    { color: '#1772e5', linkStr: 'defaultTheme' }
  ]
  const head = document.getElementsByTagName('head')[0]
  const link = document.createElement('link')
  const skin = skinList.filter(item => item.color === color)[0]
  let skinDom = document.getElementById('skin')
  // 如果存在之前已经存在的主题文件，进行删除操作避免重复插入外链css
  if (skinDom !== null) {
    // removeCss(`/static/theme/${skin.linkStr}/theme/index.css`)
  }
  link.href = process.env.NODE_ENV === 'development' ? `/static/theme/${skin.linkStr}/theme/index.css` : '/proof-derate-web' + `/static/theme/${skin.linkStr}/theme/index.css`
  link.rel = 'stylesheet'
  link.type = 'text/css'
  link.id = 'skin'
  head.appendChild(link)
  document.getElementsByTagName('body')[0].style.setProperty('--skinColor', hexToRgb(skin.color))
  document.getElementsByTagName('body')[0].style.setProperty('--borderColor', skin.color)
}

/**
 * 删除 link 文件
 * @param href
 */
function removeCss(href) {
  const links = document.getElementsByTagName('link')
  for (let i = 0; i < links.length; i++) {
    const _href = links[i].href
    if (links[i] && links[i].href && links[i].href.indexOf(href) != -1) {
      links[i].parentNode.removeChild(links[i])
    }
  }
}

/**
 * 将颜色值HEX格式转换为rgb的格式
 * @param {hex} hex 需要转换的rgb字符串
 * @return {string}  ;
 */
function hexToRgb(hex) {
  const str = hex.replace('#', '')
  if (str.length % 3) {
    // hex格式不正确！
    return false
  }
  // 获取截取的字符长度
  const count = str.length / 3
  // 根据字符串的长度判断是否需要 进行幂次方
  const power = 6 / str.length
  const r = parseInt('0x' + str.substring(0 * count, 1 * count)) ** power
  const g = parseInt('0x' + str.substring(1 * count, 2 * count)) ** power
  const b = parseInt('0x' + str.substring(2 * count)) ** power
  const a = '0.1'
  return `rgba(${r}, ${g}, ${b},${a})`
}
