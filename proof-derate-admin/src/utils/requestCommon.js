import axios from 'axios'
import { MessageBox, Message } from 'element-ui'
import store from '@/store'
import { getToken, setToken, getMenuName } from '@/utils/auth'
import router from '@/router'
import { getQueryObject } from '@/utils/index'
import {
  SYSTEM_INFO_API,
  SYSTEM_BG_IMG_API,
  CAPTCHA_API,
  LOGIN
} from '@/utils/constant'

const whiteApiList = [SYSTEM_INFO_API, SYSTEM_BG_IMG_API, CAPTCHA_API]
const whiteMenusList = [LOGIN]
const systemInfo = JSON.parse(sessionStorage.getItem('systemInfo'))
// create an axios instance
const service = axios.create({
  baseURL: process.env.VUE_APP_BASE_LICC_API, // url = base url + request url
  timeout: 30000 // request timeout
})

// request interceptor
service.interceptors.request.use(
  (config) => {
    const hasToken = getToken()
    const MenuName = getMenuName()
    // do something before request is sent
    if (hasToken && whiteApiList.indexOf(config.url) === -1) {
      // let each request carry token
      config.headers['Authorization'] = hasToken
    }
    if (MenuName && whiteMenusList.indexOf(config.url) === -1) {
      config.headers['Menu-Name'] = MenuName
    }
    return config
  },
  (error) => {
    // do something with request error
    console.log(error) // for debug
    return Promise.reject(error)
  }
)

// response interceptor
service.interceptors.response.use(
  /**
   * If you want to get http information such as headers or status
   * Please return  response => response
   */

  /**
   * Determine the request status by custom code
   * Here is just an example
   * You can also judge the status by HTTP Status Code
   */
  (response) => {
    /* console.log(response.headers) // for debug
        console.log(response.headers.authorization) // for debug */
    const new_authorization = response.headers.authorization

    if (new_authorization) {
      setToken(new_authorization)
      store.dispatch('user/resetToken').then(() => {})
      // this.$store.commit('user/SET_TOKEN', new_authorization)
    }
    const res = response.data
    // if the custom code is not 20000, it is judged as an error.
    if (Number(res.meta.code) !== 200) {
      Message({
        message: res.meta.message || 'Error',
        type: 'error',
        duration: 5 * 1000
      })
      // token过期跳转到登录页
      /*  新编码
       *父级：授权认证失败 | 2Z010101000
       *子级：
       *无操作权限  | 2Z010101006

       *账号已被禁用  | 2Z010101009
       *该账号数据已被篡改，禁止登录  | 2Z010101011
       *初始密码未修改  | 2Z010101008
       *密码已过期  | 2Z010101010
       *该账号已被强制退出   | 2Z010101012
       *该角色没有有效的权限 | 2Z010101013
       *账号没有访问权限 | 2Z010101013
       */
      const internal_code = res.meta.code
      // || internal_code === '2Z010101013'
      if (internal_code === '2Z010101000' || internal_code === '2Z010101009') {
        // console.log('systemInfo',systemInfo)
        store.dispatch('user/logback').then(() => {
          if (systemInfo !== null && systemInfo.logout_redirect_url) {
            window.open(systemInfo.logout_redirect_url, '_self')
          } else {
            location.reload(true)
          }
        })
      } else if (
        internal_code === '2Z010101011' ||
        internal_code === '2Z010101013'
      ) {
        // 该账号数据已被篡改，禁止登的情况下存在强刷过快 导致看不到错误提示，故加入延时器
        store.dispatch('user/logback').then(() => {
          const Interval = setInterval(() => {
            clearInterval(Interval)
            // location.reload(true)
            // console.log('systemInfo',systemInfo)
            if (systemInfo !== null && systemInfo.logout_redirect_url) {
              window.open(systemInfo.logout_redirect_url, '_self')
            } else {
              location.reload(true)
            }
          }, 1000)
        })
      } else if (
        internal_code === '2Z010101008' ||
        internal_code === '2Z010101010'
      ) {
        router.replace({
          name: 'WhitePasswd',
          query: { redirect: getQueryObject().redirect, status: 1 }
        })
        // this.$router.push({ name: 'WhitePasswd', params: { ststus: 1 }})
      } else if (internal_code === '2Z010101012') {
        // 强制退出
        MessageBox.confirm(
          "<div style='display:flex;align-items: center;font-size:16px;font-weight:bold'><i class='el-icon-warning' style='color:#e6a23c;font-size:24px;margin-right:16px'></i><div>您已被管理员强制下线，请重新登录！</div></div><div style='margin:4px 0 0 42px'>如有问题，请及时联系系统管理员</div>",
          '警告提示',
          {
            showClose: false,
            showCancelButton: false,
            distinguishCancelAndClose: true,
            dangerouslyUseHTMLString: true,
            closeOnClickModal: false
            // iconClass: 'withdraw-warning el-icon-warning'
          }
        ).then(() => {
          store.dispatch('user/logback').then(() => {
            // location.reload(true)
            // console.log('systemInfo',systemInfo)
            if (systemInfo !== null && systemInfo.logout_redirect_url) {
              window.open(systemInfo.logout_redirect_url, '_self')
            } else {
              location.reload(true)
            }
          })
        })
      }
      // 50008: Illegal token; 50012: Other clients logged in; 50014: Token expired;
      if (res.code === 50008 || res.code === 50012 || res.code === 50014) {
        // to re-login
        MessageBox.confirm(
          'You have been logged out, you can cancel to stay on this page, or log in again',
          'Confirm logout',
          {
            confirmButtonText: 'Re-Login',
            cancelButtonText: 'Cancel',
            type: 'warning'
          }
        ).then(() => {
          store.dispatch('user/resetToken').then(() => {
            location.reload()
          })
        })
      }
      return Promise.reject(new Error(res.meta.message || 'Error'))
    } else {
      // token过期跳转到登录页
      /* if (Number(res.meta.internal_code) === 2010101005 || Number(res.meta.internal_code) === 2010101006) {
                store.dispatch('user/logback').then(() => {
                  location.reload(true)
                })
              } */
      return res
    }
  },
  (error) => {
    console.log('err' + error) // for debug
    Message({
      message: error.message,
      type: 'error',
      duration: 5 * 1000
    })
    return Promise.reject(error)
  }
)

export default service
