import defaultConfig from '@/config/default-config'

// 外部配置文件路径（相对于项目根目录的上级目录）
// const EXTERNAL_CONFIG_PATH = '../../config/app-config.json'
const PROD_CONFIG_PATH =  `/${process.env.VUE_APP_BASE_PROJECT_NAME}/config/app-config.json`
console.log('PROD_CONFIG_PATH', PROD_CONFIG_PATH)
export async function loadAppConfig() {
  // 开发环境直接使用默认配置
  if (process.env.NODE_ENV === 'development') {
    return defaultConfig
  }

  try {
    // 构建完整配置URL
    // const configUrl = new URL(EXTERNAL_CONFIG_PATH, window.location.origin).href
    const configUrl = `${PROD_CONFIG_PATH}?t=${Date.now()}`
    // 禁用缓存请求
    const response = await fetch(configUrl, {
      cache: 'no-store',
      headers: {
        'Cache-Control': 'no-cache',
        Pragma: 'no-cache',
      },
    })

    if (!response.ok) {
      throw new Error(`配置加载失败: ${response.status} ${response.statusText}`)
    }

    const externalConfig = await response.json()

    // 深度合并配置
    return deepMerge(defaultConfig, externalConfig)
  } catch (error) {
    console.error('[Config] 加载外部配置失败，使用默认配置:', error)
    return defaultConfig
  }
}

/**
 * 深度合并对象
 */
function deepMerge(target, source) {
  const result = { ...target }

  Object.keys(source).forEach((key) => {
    // 如果是对象且target中存在相同key，则递归合并
    if (source[key] instanceof Object && key in target) {
      result[key] = deepMerge(target[key], source[key])
    } else {
      result[key] = source[key]
    }
  })

  return result
}
