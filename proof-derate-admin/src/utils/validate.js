/**
 * Created by PanJiaChen on 16/11/18.
 */

/**
 * @param {string} path
 * @returns {Boolean}
 */
export function isExternal(path) {
  return /^(https?:|mailto:|tel:)/.test(path)
}

/**
 * @param {string} str
 * @returns {Boolean}
 */
export function validUsername(str) {
  const valid_map = ['admin', 'editor']
  return valid_map.indexOf(str.trim()) >= 0
}

/**
 * 密码校验
 * @param {String | Number} value
 * @returns {Boolean}
 */
export function validPassword(value) {
  // console.log(value, /^(?![da-z]+$)(?![dA-Z]+$)(?![d!#$%^&*]+$)(?![a-zA-Z]+$)(?![a-z!#$%^&*]+$)(?![A-Z!#$%^&*]+$)[da-zA-z!#$%^&*]{8,16}$/.test(value))
  // return /^(?![a-zA-Z]+$)(?![A-Z0-9]+$)(?![A-Z\\W!"#$%&'()*+,-./:;<=>?@[]^_`{|}~]+$)(?![a-z0-9]+$)(?![a-z\\W!"#$%&'()*+,-./:;<=>?@[]^_`{|}~]+$)(?![0-9\\W!"#$%&'()*+,-./:;<=>?@[]^_`{|}~]+$)[a-zA-Z0-9\\W!"#$%&'()*+,-./:;<=>?@[]^_`{|}~]{8,16}$/.test(value)
  // 正则表达式各部分的解释：
  // \d+ 表示至少一个数字
  // [A-Z]+ 表示至少一个大写字母
  // [a-z]+ 表示至少一个小写字母
  // [!"#$%&'()*+,-./:;<=>?@[\]^_`{|}~]+ 表示至少一个特殊字符
  // 注意：[] 需要转义为 \[\]
  // {8,16} 表示密码长度在8到16位之间
  // (?=...) 是正向先行断言，确保整个字符串包含指定的模式
  var regex = /^(?=.*\d)(?=.*[A-Z])(?=.*[a-z])(?=.*[!"#$%&'()*+,-./:;<=>?@[\]^_`{|}~]).{8,16}$/

  // 使用正则表达式测试密码
  return regex.test(value)
}

/**
 * 验证特殊符号
 * @returns {Boolean}
 */
export function validPrefix(value) {
  const reg = /[(\ )(\~)(\!)(\@)(\#)(\$)(\%)(\^)(\&)(\*)(\()(\))(\-)(\+)(\=)(\[)(\])(\{)(\})(\|)(\\)(\;)(\:)(\')(\")(\,)(\.)(\/)(\<)(\>)(\?)(\)]+/
  return reg.test(value)
}
