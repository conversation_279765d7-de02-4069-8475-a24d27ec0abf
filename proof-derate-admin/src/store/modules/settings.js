import defaultSettings from '@/settings'
import { getSystemInfo } from '@/api/user'
import { setSkinColor } from '@/utils/setSkin'
import { setIcon } from '@/utils/setfavicon'
import { Loading } from 'element-ui'
import { getbgImg } from '@/api/user'
const { title, showSettings, fixedHeader, sidebarLogo } = defaultSettings

const state = {
  title: title,
  showSettings: showSettings,
  fixedHeader: fixedHeader,
  sidebarLogo: sidebarLogo,
  isNewSystem: false,
  skinColor: '#1772e5',
  skinClass: ''
}

const mutations = {
  CHANGE_SETTING: (state, { key, value }) => {
    // eslint-disable-next-line no-prototype-builtins
    if (state.hasOwnProperty(key)) {
      state[key] = value
    }
  },
  SET_ISNEWSYSTEM: (state, isNewSystem) => {
    state.isNewSystem = isNewSystem
  },
  SET_SKINCOLOR: (state, skinData) => {
    state.skinColor = skinData.color
  },
  SET_SKINCLASS: (state, skinData) => {
    state.skinClass = skinData.linkStr
  }
}

const actions = {
  changeSetting({ commit }) {
    return new Promise((resolve, reject) => {
      let systemInfo = ''
      // 第一次访问页面获取页面的sessionStorage，如果有则用上次缓存的数据进行赋值，设置主题
      if (typeof sessionStorage.getItem('systemInfo') === 'string') {
        systemInfo = JSON.parse(sessionStorage.getItem('systemInfo'))
        setIcon(systemInfo.web_icon)
        setSkinColorAndClass({ commit }, systemInfo)
      } else {
        // 如果没有则用默认的数据进行设置主题
        systemInfo = {
          color: '#1772e5'
        }
        setSkinColorAndClass({ commit }, systemInfo)
      }
      getSystemInfo()
        .then(response => {
          const { data } = response
          /* if (!data) {
          reject('Verification failed, please Login again.')
        }
        commit('SET_permission', true) */
          sessionStorage.clear()
          sessionStorage.setItem('systemInfo', JSON.stringify(data))
          commit('SET_ISNEWSYSTEM', true)
          // commit('CHANGE_SETTING', { title: data.name })
          setSkinColorAndClass({ commit }, data)
          setIcon(data.web_icon)
          // loadingInstance.close()
          resolve(data)
        })
        .catch(error => {
          reject(error)
          // loadingInstance.close()
        })
    })
    // commit('CHANGE_SETTING', data)
  },
  bgImgSetting({ commit }) {
    return new Promise((resolve, reject) => {
      getbgImg()
        .then(res => {
          if (res.meta.code === '200' && res.data !== null) {
            // this.background = res.data || ''
            let bgImg = {
              background: res.data
            }

            sessionStorage.setItem('bgImg', JSON.stringify(bgImg))
            resolve(bgImg)
          } else {
            reject(error)
          }
        })
        .catch(error => {
          reject(error)
        })
    })
  }
}
/**
 * 设置皮肤主题和对应的类名以及颜色
 * @param {*} skinData  从sessionStorage 获取的systemInfo
 * @returns
 */
function setSkinColorAndClass({ commit }, skinData) {
  commit('SET_SKINCOLOR', skinData)
  setSkinColor(skinData.color)
  const skinList = [
    { color: '#1f9e73', linkStr: 'greenTheme' },
    { color: '#4293f4', linkStr: 'lightBlueTheme' },
    { color: '#0747a6', linkStr: 'darkBlueTheme' },
    { color: '#1772e5', linkStr: 'defaultTheme' }
  ]
  const skin = skinList.filter(item => item.color === skinData.color)[0]
  commit('SET_SKINCLASS', skin)
}
export default {
  namespaced: true,
  state,
  mutations,
  actions
}
