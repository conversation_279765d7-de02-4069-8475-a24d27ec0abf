import { constantRoutes, asyncRoutes } from '@/router'
import Layout from '@/layout'
import LayoutMain from '@/layout/main'
import ParentView from '@/components/ParentView'
/**
 * 对比本地路由表补充路由信息
 * @param {Array} resource 接口返回的菜单路由
 * @param {Object} route 前端自定义的路由
 */
// function hasPermission(resource, route) {
//   // if (route.meta && route.meta.completePath) {
//   //   return resource.find(x => x.menuKey === route.meta.completePath);
//   // } else {
//   //   return route;
//   // }
//   if (route.path === '*') {
//     return route
//   } else {
//     return resource.find(x => {
//       if (x.path === route.path) {
//         x.name = route.name
//         x.meta.breadcrumbBtnComponentId = route.meta.breadcrumbBtnComponentId
//         if (route.alwaysShow)x.alwaysShow = true
//         return x
//       }
//     })
//   }
// }

// export function filterlocalityAsyncRoutes(routes, resource) {
//   const res = []
//   console.log(routes)
//   routes.forEach(route => {
//     const tmp = { ...route }
//     const find = hasPermission(resource, route)
//     if (tmp.hidden) {
//       if (tmp.children && find.children) {
//         tmp.children = filterlocalityAsyncRoutes(tmp.children, find.children)
//       }
//       res.push(tmp)
//     } else if (find) {
//       if (tmp.children && find.children) {
//         tmp.children = filterlocalityAsyncRoutes(tmp.children, find.children)
//       }
//       res.push(find)
//     }
//   })
//   console.log(res)
//   return res
// }
function hasPermission(resource, route) {
  // if (route.meta && route.meta.completePath) {
  //   return resource.find(x => x.menuKey === route.meta.completePath);
  // } else {
  //   return route;
  // }
  if (route.path === '*') {
    return route
  } else {
    return resource.find((x, index) => {
      // console.log(x, 'X')
      if (x.path === route.path) {
        route.meta.title = x.meta.title
        route.meta.icon = x.meta.icon
        // route.meta.permission = x.button
        route.meta.permission = state.buttonList
        route.meta.index = index
        x.index = index
        route.hidden = x.hidden
        route.meta.hidden = x.hidden
        route.meta.externalUrl = x.externalUrl
        route.externalUrl = x.externalUrl
        route.meta.linkExternal = x.linkExternal
        route.meta.openWay = x.openWay
        return x
      }
      // 把跳转外链的router也取出来
      /*  if (x.link_external) {
        x.index = index
        return x
      } */
      // console.log(route.path, x.button)
      // route.meta.permission = x.button
      route.meta.index = 100
      route.meta.permission = state.buttonList
      if (route.hidden) {
        return x
      }
    })
  }
}
// 获取接口返回的所有权限编码
export function getAllPermission(resource) {
  resource.forEach((i) => {
    if (i.children !== null) {
      getAllPermission(i.children)
    } else {
      if (i.button) {
        state.buttonList = state.buttonList.concat(i.button)
      }
    }
  })
}
export function filterlocalityAsyncRoutes(routes, resource) {
  // console.log('resource',resource)
  const res = []
  routes.forEach((route) => {
    const tmp = { ...route }

    const find = hasPermission(resource, route)
    if (find) {
      // 修改菜单名称
      /*  if (tmp.meta) {
        tmp.meta.title = find.menu_name || find.meta.title
      } */
      if (tmp.children != null && tmp.children && find.children) {
        tmp.children = filterlocalityAsyncRoutes(tmp.children, find.children)
      } else {
        if (route.path !== '*') {
          delete tmp['children']
          delete tmp['redirect']
        }
      }
      !route.hidden && !res.length ? res.unshift(tmp) : res.push(tmp)
    }
  })
  return res
}

const state = {
  routes: [],
  addRoutes: [],
  currentRoutes: {},

  // 初始加载状态
  init: false,
  // 菜单数据
  menuList: [],
  // 按钮数据
  buttonList: []
}

const mutations = {
  SET_ROUTES: (state, routes) => {
    state.addRoutes = routes
    state.routes = constantRoutes.concat(routes)
  },
  SET_CURRENT_ROUTES: (state, routes) => {
    state.currentRoutes = routes
  },
  SET_SYSTEM_MENU: (state, data) => {
    state.init = true
    state.menuList = data.userMenuList
    state.buttonList = data.userButtonList
  }
}

const actions = {
  generatelocalitRoutes({ commit }, resource) {
    return new Promise((resolve) => {
      let accessedRoutes = []
      if (
        resource === null ||
        resource === undefined ||
        resource === {} ||
        resource === []
      ) {
        accessedRoutes = []
      } else {
        getAllPermission(resource)
        accessedRoutes = filterlocalityAsyncRoutes(asyncRoutes, resource)
        // accessedRoutes = resource
      }
      sortRoute(accessedRoutes)

      commit('SET_ROUTES', accessedRoutes)
      resolve(accessedRoutes)
    })
  },
  GenerateRoutes({ commit }, asyncRouter) {
    commit('SET_ROUTES', asyncRouter)
  }
}

function findFatherRouter(route, childrenRouter) {
  return route.find((item) => {
    if (!childrenRouter.meta.activeMenu) return
    const activeMenu = childrenRouter.meta.activeMenu.split('/')
    const fatherLastPath = activeMenu[activeMenu.length - 1]
    const path = item.path.split('/')
    const lastPath = path[path.length - 1]
    // const aaa = JSON.parse(JSON.stringify(item))
    return lastPath === fatherLastPath
  })
}
//
function findRouter(route, resource) {
  return resource.find((x, i) => {
    // console.log('findRouter-X', route, x)
    if (route.children) route.alwaysShow = true
    if (x.path === route.path) {
      route.name = x.name
      route.meta.breadcrumbBtnComponentId = x.meta.breadcrumbBtnComponentId
      /* 将本地的隐藏路由匹配到线上的路由上面*/
      if (x.children && x.children.length > 1 && route.component !== 'layout') {
        const array = x.children.filter((childrenRouter) => {
          const activeMenuRouter = findFatherRouter(
            route.children,
            childrenRouter
          )
          childrenRouter.meta.permission = activeMenuRouter
            ? activeMenuRouter.button
            : null
          return childrenRouter.hidden
        })

        route.children.push(...array)
      }
      return x
    }
  })
}

// 2024.7.10 - 匹配隐藏的路由到线上的路由表中
function matchingRoute(route, resource, lastLevelRoutes) {
  return resource.find((x, i) => {
    // 将本地hidden的路由根据activeMenu值匹配到线上的现有的目录下、设置新的activeMenu、赋值permission等
    if (route.children) {
      route.alwaysShow = true
      const arr = route.children.map((child) => child.path)
      lastLevelRoutes.forEach((e) => {
        if (arr.indexOf(e.path) !== -1) {
          lastLevelRoutes.forEach((item) => {
            if (item.meta?.fatherPath) {
              const activeMenuLastLevel = item.meta.fatherPath
              const activeMenu = route.children.find(
                (y) => y.path === activeMenuLastLevel
              )
              // 根据本地路由fatherPath的值，将隐藏的路由匹配到线上的路由去
              if (e.path === activeMenuLastLevel && item.hidden) {
                item.meta.activeMenu = activeMenu.meta.fullPath
                item.meta.permission = activeMenu.meta.permission
                item.meta.title = activeMenu.meta.title
                route.children.push(item)
                console.log(item.meta.title, route, item)
              }
            }
          })
        }
      })
    }
    // 将本地的name赋值给线上的路由上
    lastLevelRoutes.forEach((li) => {
      // 判断是否是线上的path
      if (li.path === route.path) {
        // console.log(li.path === route.path, li.path, route.path)
        // route.isInternalLink = true
        route.name = li.name
        route.meta.breadcrumbBtnComponentId = li.meta.breadcrumbBtnComponentId
      }
    })
    return x
  })
}
// 找到与targetPath匹配的路由项，并向其直接上级的children数组中添加一个新的路由项。
function addRouteToParentChildren(routers, targetPath, item) {
  // 定义一个递归函数来遍历路由配置
  function traverseRoutes(routes, parent = null) {
    for (const route of routes) {
      // 检查当前路由或其子路由是否包含目标路径
      if (
        route.path === targetPath ||
        traverseRoutes(route.children || [], route)
      ) {
        // 如果找到匹配的路径，向它的父路由的children中添加新路由
        if (parent && parent.children) {
          // 这里添加新的路由项，示例：添加一个名为"NewRoute"的路由
          /* parent.children.push({
            path: 'newRoute', // 新路由的路径
            component: () => import('@/views/some-new-view'), // 新路由的组件
            name: 'NewRoute', // 新路由的名称
            meta: { title: '新路由标题' } // 新路由的元信息
          }) */
          parent.children.push(item)
          // 假设添加一次就足够，返回true以停止进一步搜索
          return true
        }
        // 如果没有父路由（即顶级路由），或者不需要进一步操作，返回false
        return false
      }
    }
    // 如果没有找到匹配的路径，返回false
    return false
  }

  // 调用递归函数开始搜索
  traverseRoutes(routers)
}

// 最新使用此方法过滤菜单
export const filterAsyncRouter = (
  routers,
  lastRouter = false,
  type = false,
  resource,
  lastLevelRoutes
) => {
  return routers.filter((router) => {
    // const find = findRouter(router, resource)
    const find = matchingRoute(router, resource, lastLevelRoutes)
    // console.log('find', find)
    if (type && router.children) {
      router.children = filterChildren(router.children)
    }
    if (router.component) {
      if (router.component === 'layout') {
        // Layout组件特殊处理
        router.component = Layout
      } else if (router.component === 'layoutMain') {
        router.component = LayoutMain
      } else if (router.component === 'ParentView') {
        router.component = ParentView
      } else {
        let component = router.component
        // 只将非本项目的路由的component换成统一的index.vue
        if (typeof router.component === 'string') {
          const lastItemComponent = router.component.split('/').pop()
          if (
            lastItemComponent === 'index' ||
            lastItemComponent === 'index.vue'
          ) {
            if (router.isOutherPath) {
              component = 'common/index'
            }
          }
        }
        // 判断本地隐藏的路由则无需处理路由地址
        if (typeof component === 'string') {
          router.component = loadView(component)
        }
      }
    }
    // if (router.button) router.meta.permission = router.button
    if (router.children === null) delete router['children']

    router.redirect = 'noRedirect'
    if (find) {
      if (
        router.children != null &&
        router.children &&
        router.children.length
      ) {
        router.children = filterAsyncRouter(
          router.children,
          router,
          type,
          find.children,
          lastLevelRoutes
        )
      } else {
        delete router['children']
        delete router['redirect']
      }
    }
    /* if (find === undefined) { // 将不对的菜单过滤掉
      return false
    } else {
      return true
    } */
    return true
  })
}
// 对传入的路由根据路由进行排序（由小到大）
function sortRoute(route) {
  route.forEach((e) => {
    if (e.children) {
      e.children.sort((a, b) => {
        if (a.meta.index < b.meta.index) {
          return -1
        }
        if (a.meta.index > b.meta.index) {
          return 1
        }
        return 0
      })
      sortRoute(e.children)
    }
  })
}
function filterChildren(childrenMap, lastRouter = false) {
  var children = []
  childrenMap.forEach((el, index) => {
    if (el.children && el.children.length) {
      if (el.component === 'ParentView') {
        el.children.forEach((c) => {
          c.path = el.path + '/' + c.path
          if (c.children && c.children.length) {
            children = children.concat(filterChildren(c.children, c))
            return
          }
          children.push(c)
        })
        return
      }
    }
    if (lastRouter) {
      el.path = lastRouter.path + '/' + el.path
    }
    children = children.concat(el)
  })
  return children
}
export const loadView = (view) => {
  // return (resolve) => require([`@/${view}`], resolve)
  return (resolve) => require([`@/views/${view}`], resolve)
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
