import {
  login,
  logout,
  getInfo,
  getPermission,
  getCurrentUserInfo
} from '@/api/user'
import { getToken, setToken, removeToken } from '@/utils/auth'
import { setSkinColor } from '@/utils/setSkin'
import { resetRouter, asyncRoutes } from '@/router'
import { collectLastChildrenRoutes } from '@/utils' // get token from cookie

import { getMenuList, getSystemSettings } from '@/api/commonPack/platManege'
import permission from './permission'


const getDefaultState = () => {
  return {
    token: getToken(),
    name: '',
    avatar: '',
    account: '',
    // skinColor: '#1772e5' || localStorage.getItem('systemInfo').color,
    // skinClass: '',
    organization: '' || localStorage.getItem('organization'),
    permission: false,
    lastLevelRoutes: collectLastChildrenRoutes(asyncRoutes)
  }
}

// 将api路由的层级标出来放到meta中给隐藏的路由高亮菜单并切割path
function processRoutes(routes, parentPath = '/') {
  routes.forEach((route) => {
    // route.isInternalLink = false // 统一默认为false
    if (route.button) route.meta.permission = route.button // 将权限移到meta下的permission
    let _path = ''
    const isLiccMenu = state.lastLevelRoutes.find((r) => r.path === route.path)
    // console.log(route.meta, route.menuType, isLiccMenu)
    if (isLiccMenu) {
      _path = route.path
      route.isOutherPath = false
      route.meta.projectName = process.env.VUE_APP_BASE_PROJECT_NAME
    } else {
      const arr = route.path.split('/')
      route.isOutherPath = process.env.VUE_APP_BASE_PROJECT_NAME !== arr[0]
      route.meta.projectName = arr[0] // 取项目名称放到meta下
      _path = arr.slice(1).join('/')
    }

    route.path = _path
    // 累积当前路由的完整路径链条
    const fullPath = `${parentPath}${_path}`
    // 如果当前路由是叶子节点（没有children），则更新meta
    if (!route.children) {
      // console.error(fullPath.startsWith('//'))
      if (fullPath.startsWith('//')) {
        route.meta.fullPath = fullPath.slice(1)
        return
      }
      route.meta.fullPath = fullPath
    } else {
      // 递归处理子路由
      processRoutes(route.children, fullPath + '/')
    }
  })
}
const extractPermission = (menuItems) => {
  let keys = []
  const traverse = (items) => {
    items.forEach( ( item ) => {
      if (item.permission_code) keys.push(item.permission_code)
      if (item.button?.length) {
        item.button.forEach((btn) => btn.key && keys.push(btn.key))
      }
      if (item.children?.length) traverse(item.children)
    })
  }
  traverse(menuItems)
  return keys
}

const state = getDefaultState()

const mutations = {
  RESET_STATE: (state) => {
    Object.assign(state, getDefaultState())
  },
  SET_TOKEN: (state, token) => {
    state.token = token
  },
  SET_NAME: (state, name) => {
    state.name = name
  },

  SET_AVATAR: (state, avatar) => {
    state.avatar = avatar
  },
  SET_ACCOUNT: (state, account) => {
    state.account = account
  },
  SET_ORGANIZATION: (state, organization) => {
    state.organization = JSON.stringify(organization)
    localStorage.setItem('organization', state.organization)
  },
  SET_permission: (state, permission) => {
    state.permission = permission
  }
}
const actions = {
  // user login
  login({ commit }, userInfo) {
    return new Promise((resolve, reject) => {
      login(userInfo)
        .then((response) => {
          const { data } = response

          commit('SET_TOKEN', data.authorization)
          // commit('SET_SKINID', data.id)
          setToken(data.authorization)

          resolve(data)
        })
        .catch((error) => {
          reject(error)
        })
    })
  },
  getSystemSettings({ commit }) {
    return new Promise((resolve, reject) => {
      getSystemSettings().then((response) => {
        const { data } = response
        commit('SET_SKINID', data)
      })
    })
  },
  // get user info
  getPermission({ commit }) {
    return new Promise((resolve, reject) => {
      getPermission()
        .then((response) => {
          const { data } = response
          if (!data) {
            reject('Verification failed, please Login again.')
          }
          commit('SET_permission', true)
          resolve(data)
        })
        .catch((error) => {
          reject(error)
        })
    })
  },
  getCurrentUserInfo({ commit }) {
    return new Promise((resolve, reject) => {
      getCurrentUserInfo()
        .then((response) => {
          const { data } = response
          const organization = data.organization || {}
          commit( 'SET_ORGANIZATION', organization )
          const _extractPermission = extractPermission(data.menu_per_tree)
          const accountInfo = {
            account: data.user.account,
            name: data.user.user_name,
            department: organization ? organization.name : '',
            permission: _extractPermission,
          }
          sessionStorage.setItem('accountInfo', JSON.stringify(accountInfo))
          // commit('SET_ACCOUNT', data.current_user)
          commit('SET_permission', true)
          // 将本地所有的三级路由取出来
          // getMenuList()
          // getSystemSettings().then(response => {
          //   const { data } = response
          //   commit('SET_SKINCOLOR', data)
          //   setSkinColor(data.color)
          //   const skinList = [
          //     { color: '#1f9e73', linkStr: 'greenTheme' },
          //     { color: '#0747a6', linkStr: 'darkBlueTheme' },
          //     { color: '#1772e5', linkStr: 'defaultTheme' }
          //   ]
          //   const skin = skinList.filter(item => item.color === data.color)[0]
          //   commit('SET_SKINCLASS', skin)
          // })
          processRoutes(data.menu_per_tree)
          resolve(data.menu_per_tree)
        })
        .catch((error) => {
          reject(error)
        })
    })
  },
  // get user info
  getInfo({ commit, state }) {
    commit('SET_NAME', '123')
    commit('SET_AVATAR', '123')
    /* return new Promise((resolve, reject) => {
              getInfo(state.token).then(response => {
                const { data } = response

                if (!data) {
                  return reject('Verification failed, please Login again.')
                }

                const { name, avatar } = data

                commit('SET_NAME', name)
                commit('SET_AVATAR', avatar)
                resolve(data)
              }).catch(error => {
                reject(error)
              })
            }) */
  },

  // user logout
  logout({ commit, state }) {
    return new Promise((resolve, reject) => {
      logout(state.token)
        .then(() => {
          removeToken() // must remove  token  first
          resetRouter()
          commit('RESET_STATE')
          resolve()
        })
        .catch((error) => {
          reject(error)
        })
    })
  },

  logback({ commit, state }) {
    return new Promise((resolve, reject) => {
      removeToken() // must remove  token  first
      resetRouter()
      commit('RESET_STATE')
      resolve()
    })
  },

  // remove token
  resetToken({ commit, newToken }) {
    return new Promise((resolve) => {
      commit('SET_TOKEN', newToken)

      /*  removeToken() // must remove  token  first
      commit('RESET_STATE')
      resolve() */
    })
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
