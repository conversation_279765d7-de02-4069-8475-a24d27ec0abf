const state = {
  standardRule: {
    detail: false
  },
  platManage: {
    // 平台管理
    isDeparmentAdd: false, // 部门信息管理-新增部门
    isDeparmentTreeAdd: false, // 部门信息管理树状图-新增部门
    isAccessSystem: false, // 接口管理-接入应用系统
    isMenuAdd: false, // 菜单管理-新增
    accoundIdEdit: '', // 菜单管理-编辑菜单
    interfaceManagerAdd: false, // 接口管理 - 新增
    interfaceManagerUpland: false, //  接口管理 - 导入
    interfaceManagerExport: false, // 接口管理 - 导出
    isAccessSystemDisable: false, // 接入应用系统 - 详情
    isDisable: false, // 接入应用系统 - 详情 - 是否禁用
    isaddConfigManage: false, // 配置管理-字典 - 新增
    isAddEditConfigManage: false, // 配置管理 -字典- 新增
    isaddAdministrativeDivision: false, // 配置管理 -行政区域管理- 新增
    fileImportAdministrativeDivision: false, // 配置管理 -行政区域管理- 导入
    showMenuListButton: true, // 菜单管理-显示按钮
    isAccountResetPassword: true, // 账号管理- 重置密码
    isAccountDissable: true, // 账号管理- 删除账号
    accountStatus: '', // 账号管理- 账号状态
    isRoleDisable: true, // 角色管理 - 禁用按钮
    roleStatus: '', // 角色管理- 角色状态
    isConfigManageDetailEdit: true, // 配置管理- 字典管理 - 详情页编辑
    isConfigManageDetailDel: true, // 配置管理- 字典管理 - 详情页删除
    isAccessSystemRestPassword: true, // 配置管理- 字典管理 - 详情页删除
    isInterfaceManagerDetailDisable: true, // 接口管理- 接口管理 - 详情页禁用
    interfaceManagerDetailSatus: '', // 接口管理- 接口管理 - 详情页状态
    isInterfaceManagerDetailDel: '', // 接口管理- 接口管理 - 详情页删除
    reportDirectory: true, // 上报管理-上报管理清单-添加上报目录
    setpriority: true, // 上报管理-上报管理清单-批量设置优先级
    defaultPriority: true, // 上报管理-上报管理清单-批量设置优先级
    accessApplicationSystemImport: false, // 接口管理-接入应用系统-导入
    isDepartmentImport: false // 平台管理-部门管理-部门信息管理-导入
  }
}

const mutations = {
  SET_STANDARDRULE: (state, value) => {
    Object.keys(value).forEach(key => {
      state.standardRule[key] = value[key]
    })
  },
  SET_PLATMANAGE: (state, value) => {
    Object.keys(value).forEach(key => {
      state.platManage[key] = value[key]
    })
  }
}

export default {
  namespaced: true,
  state,
  mutations
}
