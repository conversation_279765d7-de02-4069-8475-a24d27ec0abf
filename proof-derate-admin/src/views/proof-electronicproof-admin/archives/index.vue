<!--
  电子证明-证明档案-20240812
-->
<template>
  <div class="content-wrapper investigationArchive padding-10">
    <CardTitle :title-name="titleName" />
    <el-card class="box-card" shadow="never">
      <el-form ref="form" :model="queryForm" label-width="120px" class="el-check-form" :rules="rules">
        <el-row :gutter="24" justify="center" type="flex">
          <el-col :span="10">
            <el-form-item label="办件单号">
              <el-input v-model="queryForm.serial_number" clearable placeholder="请输入办件单号" />
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="业务标识号">
              <el-input v-model="queryForm.business_serial_number" clearable placeholder="请输入业务标识号" />
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-button type="primary" @click="getArchivesListBySearch()">查询</el-button>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="10">
            <el-form-item label="证明名称">
              <el-input v-model="queryForm.license_name" clearable placeholder="请输入证明名称" />
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="证明状态">
              <el-checkbox-group v-model="proofResult">
                <el-checkbox label="not_sign">待开具</el-checkbox>
                <el-checkbox label="signed">已完成</el-checkbox>
                <!-- <el-checkbox label="SUCCESS">符合</el-checkbox>
                <el-checkbox label="FAIL">不符合</el-checkbox>-->
              </el-checkbox-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div style="color: #888; padding: 20px 10px 0" class="dashed-line">
        共
        <span class="text-red">{{ tableData.total }}</span>条符合查询条件
        <span v-if="tableData.content.length != 0">，以下是第1至第{{ tableData.content.length }}项</span>
      </div>
      <custom-table ref="table" :is-card-type="false" :table-data="tableData" :table-header="tableHeader" :stripe="false" :table-tools="tableTools" style="margin-top: 10px" @query="query" @refresh="query(1)">
        <template #handle_affairs_name="{ row }">
          <div>{{ row.handle_affairs_type !== 'NATURAL_PERSON' ? row.biz_org_name : row.handle_affairs_name }}</div>
        </template>
        <template #operation="{ row }">
          <div>
            <el-button type="text" @click="detail(row)">
              <span v-if="row.proof_result === 'not_sign'" v-permission="'license_derate:biz:archives:sign'">开具</span>
              <span v-else v-permission="'license_derate:biz:archives:view'">查看</span>
            </el-button>
          </div>
        </template>

      </custom-table>
    </el-card>
    <!-- </section> -->
  </div>
</template>

<script>
import CustomTable from '@/components/Element/Table';
// import { getList } from '@/api/assistInvestigate'
import { getArchivesList } from '@/api/electronic';
// import papeTitle from '@/components/papeTitle'
import CardTitle from '@/components/CardTitle';
export default {
  name: 'ProofInvestigationHandel',
  components: {
    CustomTable,
    // papeTitle,
    CardTitle
  },
  data() {
    return {
      queryForm: {
        serial_number: '', // 办件单号
        business_serial_number: '', // 业务标识号
        license_name: '',
        proof_result: [], //  not_sign :待开具 signed :已完成
        // page_direction: 'DESC',
        page_number: 1,
        page_size: 10
      },
      proofResult: ['not_sign', 'signed'],
      rules: {},
      checkList: [],
      numberOfElements: '',
      tableData: {
        border: false,
        content: [], // 表格数据
        loading: false, // 控制表格加载效果
        total: 0, // 分页查询时的总数，去掉该属性，不显示分页条
        currentPage: 1, // 当前页码
        pageSize: 10,
        isShowIndex: true,
        pageDirection: 'desc',
        isShowSelection: false // 是否显示多选框，默认false
      },
      tableHeader: [
        // { label: '序号', prop: 'index', minWidth: '80px', slot: 'index' },
        { label: '办件单号', prop: 'serial_number', align: 'left' }, // 配置slot属性，可支持使用插槽
        { label: '业务标识号', prop: 'business_serial_number', align: 'left' }, // 配置slot属性，可支持使用插槽
        { label: '事项名称', prop: 'item_name', align: 'left' },
        { label: '证明名称', prop: 'license_name', align: 'left' },
        { label: '办事人/办事单位', prop: 'handle_affairs_name', align: 'left', fixed: 'right', slot: 'handle_affairs_name' },
        { label: '发起部门', prop: 'from_assist_org_name', align: 'left', fixed: 'right' },
        {
          label: '证明状态',
          prop: 'proof_result',

          fixed: 'right',
          align: 'left',
          formatter: (row, col, val) => {
            return val === 'not_sign' ? '待开具' : '已完成'
          }
        },
        { label: '发起时间', prop: 'assist_time', align: 'left', fixed: 'right' },
        {
          label: '操作',
          prop: 'operation',

          fixed: 'right',
          align: 'left',
          slot: 'operation'
        }
      ],
      tableTools: [],
      titleName: '证明档案'
    }
  },

  mounted() {
    this.query()
  },

  methods: {
    detail(row) {
      console.log(row)
      this.$router.push({
        name: 'ElectronicArchivesDetail',
        query: {
          type: 'archives',
          // data: JSON.stringify(row),
          id: row.id,
          serial_number: row.serial_number
        }
      })
    },
    getArchivesList() {
      // const userdata = JSON.parse(this.$store.state.user.organization)
      this.queryForm.page_number = this.tableData.currentPage
      this.queryForm.page_size = this.tableData.pageSize
      this.tableData.loading = true
      // this.queryForm.toAssistCreditCode = userdata.credit_code
      this.queryForm.proof_result = this.proofResult
      getArchivesList(this.queryForm).then((res) => {
        // console.log(res)
        this.tableData.loading = false
        if (res.meta.code === '200' && res.data != null) {
          this.tableData.content = res.data.content
          this.tableData.total = Number(res.data.total_elements)
          this.numberOfElements = res.data.numberOfElements
        }
      }).catch(() => {
        this.tableData.loading = false
      })
    },
    getArchivesListBySearch() {
      this.tableData.currentPage = 1
      this.tableData.pageSize = 10
      this.getArchivesList()
    },
    query() {
      this.getArchivesList()
    }
  }
}
</script>
<style lang="scss">
@import '@/styles/element-ui.scss';
</style>
<style scoped>
.investigationArchive /deep/ .el-table .cell,
.el-table--border .el-table__cell:first-child .cell {
  /* text-align: center; */
}
.timeLimit {
  display: flex;
  align-items: flex-start;
  justify-content: center;
}
.timeLimit span {
  margin-right: 5px;
}
.warningImg {
  height: 25px;
  width: 25px;
}
.warning {
  color: crimson;
}
</style>
