<template>
  <div v-loading="pageLoading" class="content-wrapper investigationDetail padding-10">
    <CardTitle :title-name="detailTitle" :ifback="true" @back="back()" />
    <!-- <section class="content"> -->
    <el-form ref="form" :model="queryForm" label-width="180px" class="el-check-form" :rules="rules">
      <el-card class="box-card" shadow="never" :body-style="{ padding: '30px 20px' }">
        <span class="margin-left-10 info-wrap">
          <img :src="arrow" alt>
          <span class="info-title">部门申请信息</span>
        </span>
        <!-- justify="center" type="flex" -->
        <el-descriptions class="descriptions" title :column="2" border>
          <el-descriptions-item :label-style="{ width: '140px' }">
            <template slot="label">办件单号</template>
            {{ queryForm.serial_number }}
          </el-descriptions-item>
          <el-descriptions-item :label-style="{ width: '140px' }">
            <template slot="label">业务标识号</template>
            {{ queryForm.business_serial_number }}
          </el-descriptions-item>
          <el-descriptions-item :label-style="{ width: '140px' }">
            <template slot="label">办事人类型</template>
            {{ handleAffairsType[queryForm.handle_affairs_type] }}
          </el-descriptions-item>
          <el-descriptions-item :label-style="{ width: '140px' }">
            <template slot="label">事项名称</template>
            {{ queryForm.item_name }}
          </el-descriptions-item>
          <el-descriptions-item :label-style="{ width: '140px' }">
            <template slot="label">事项编码</template>
            {{ queryForm.item_code }}
          </el-descriptions-item>
          <el-descriptions-item :label-style="{ width: '140px' }">
            <template slot="label">材料名称</template>
            {{ queryForm.material_name }}
          </el-descriptions-item>
          <el-descriptions-item v-if="!islegalPerson" :label-style="{ width: '140px' }">
            <template slot="label">办事人</template>
            {{ queryForm.handle_affairs_name }}
          </el-descriptions-item>
          <el-descriptions-item v-else :label-style="{ width: '140px' }">
            <template slot="label">办事单位</template>
            {{ queryForm.biz_org_name }}
          </el-descriptions-item>
          <el-descriptions-item :label-style="{ width: '140px' }">
            <template v-if="!islegalPerson" slot="label">证件类型</template>
            <template v-else slot="label">证件类型</template>
            <span v-if="!islegalPerson">{{ queryForm.handle_affairs_id_type ? cardOptions.find((i) => i.value === queryForm.handle_affairs_id_type).label : queryForm.handle_affairs_id_type }}</span>
            <span v-else>{{ queryForm.legal_person_identity_type ? legalPersonIdentityTypeList.find((i) => i.value === queryForm.legal_person_identity_type).label : queryForm.legal_person_identity_type }}</span>
          </el-descriptions-item>
          <el-descriptions-item :label-style="{ width: '140px' }">
            <template slot="label">证件号码</template>
            <span v-if="!islegalPerson">{{ queryForm.handle_affairs_id_number }}</span>
            <span v-else>{{ queryForm.biz_org_credit_code }}</span>
          </el-descriptions-item>

          <el-descriptions-item :label-style="{ width: '140px' }">
            <template slot="label">发起时间</template>
            {{ queryForm.assist_time }}
          </el-descriptions-item>
          <el-descriptions-item :label-style="{ width: '140px' }">
            <template slot="label">发起人</template>
            {{ queryForm.from_assist_user_name }}
          </el-descriptions-item>
          <el-descriptions-item :label-style="{ width: '140px' }">
            <template slot="label">发起部门</template>
            {{ queryForm.from_assist_org_name }}
          </el-descriptions-item>
          <el-descriptions-item :label-style="{ width: '140px' }">
            <template slot="label">需求描述</template>
            {{ queryForm.from_demand }}
          </el-descriptions-item>

        </el-descriptions>
      </el-card>
      <el-card class="box-card" shadow="never">
        <span class="margin-left-10 info-wrap">
          <img :src="arrow" alt>
          <span class="info-title">证明开具结果</span>
        </span>
        <el-descriptions class="descriptions" title :column="2" border>
          <el-descriptions-item :label-style="{ width: '140px' }">
            <template slot="label">证明处理人</template>
            {{ queryForm.to_user_name }}
          </el-descriptions-item>
          <el-descriptions-item :label-style="{ width: '140px' }">
            <template slot="label">证明开具部门</template>
            {{ queryForm.to_assist_org_name }}
          </el-descriptions-item>
          <el-descriptions-item :label-style="{ width: '140px' }">
            <template slot="label">证明开具时间</template>
            {{ queryForm.audit_time }}
          </el-descriptions-item>
          <el-descriptions-item :label-style="{ width: '140px' }">
            <template slot="label">证明状态</template>
            {{ proofSesult[queryForm.proof_result] }}
          </el-descriptions-item>
        </el-descriptions>
      </el-card>
      <div class="btn-box">
        <el-button v-if="queryForm.proof_result ==='not_sign'" v-permission="'license_derate:biz:archives:sign'" type="primary" @click="toIssueService">开具电子证明</el-button>
        <el-button v-else v-permission="'license_derate:biz:archives:download'" type="primary" @click="downElectronicFile">下载电子证明</el-button>
      </div>
    </el-form>
  </div>
</template>

<script>
import { downProofArchives, getArchivesDetail,getArchivesDetailNonEncrypted } from '@/api/electronic'

import { download, upload } from '@/api/assistAttachment'
// import { handle } from '@/api/assistInvestigate'
import { applyView, archivesSave, archivesView, handleSave, handleView } from '@/api/assistInvestigate'
import { getCurrentAccount, getEnterpriseIdentityType } from '@/api/common/dict.js'
import CardTitle from '@/components/CardTitle'
import { dataURLtoDownload, getFileType, getIsWhitelist } from '@/utils/index.js'
import { validPrefix } from '@/utils/validate'

export default {
  components: {
    CardTitle
  },
  data() {
    return {
      handleAffairsType: {
        'NATURAL_PERSON': '自然人',
        'LEGAL_PERSON': '法人'
      },
      proofSesult: {
        'not_sign': '待开具',
        'signed': '已完成'
      },

      queryForm: {
        business_serial_number: '',
        assist_serial_number: '',
        handle_affairs_name: '',
        clerkDep: '',
        from_assist_org_name: '',
        assist_time: '',
        material_name: '',
        proof_catalog_name: '',
        to_assist_org_name: '',
        biz_org_credit_code: '',
        handle_affairs_identity_number: '',
        from_assist_user_name: '',
        to_user_name: '',
        from_demand: '',
        // handle_affairs_identity_number: '',
        item_name: '',
        item_code: '',
        handle_affairs_id_type: '',
        from_assist_contain: '',
        legal_person_identity_type: ''
      },
      addForm: {
        audit_result: 'SUCCESS',
        audit_suggestion: '',
        audit_time: '',
        fileName: '',
        raw: '',
        audit_id: '',
        to_user_id: '',
        to_user_name: '',
        to_assist_org_name: '',
        to_assist_credit_code: ''
      },
      assist_data_list: [], // 协查内容
      dialogVisible: false,
      islegalPerson: false,
      pageLoading: false,
      rules: {
        audit_suggestion: [{ required: true, message: '请输入协查意见', trigger: 'change' }]
      },
      isshowResult: false, // 是否显示协查结果
      isHandle: false, // 是否协查申请
      deptOptions: [
        {
          value: '1',
          label: '市公安局'
        }
      ],
      fileList: [],
      // IDENTITY :身份证 OFFICERS :军官证 PASSPORT :护照 EEP_HK_MACAO :港澳通行证 OTHER_IDENTITY_LICENSE :其他
      cardOptions: [
        {
          value: 'IDENTITY',
          label: '身份证'
        },
        {
          value: 'OFFICERS',
          label: '军官证'
        },
        {
          value: 'PASSPORT',
          label: '护照'
        },
        {
          value: 'EEP_HK_MACAO',
          label: '港澳通行证'
        },
        {
          value: 'OTHER_IDENTITY_LICENSE',
          label: '其他'
        }
      ],
      legalPersonIdentityTypeList: [
        {value: 'CREDIT_CODE', label: '统一社会信用代码'},
        {value: 'ORG_CODE_LICENSE', label: '组织机构代码证'},
        {value: 'BUSINESS_LICENSE', label: '营业执照'},
        {value: 'BUSINESS_UNIT_CERTIFICATE', label: '事业单位登记证书'},
        {value: 'COMMUNITY_REGISTRATION_CERTIFICATE', label: '社团登记证书'},
        {value: 'CIVILIAN_RUN_NOT_BUSINESS_REGISTRATION_CERTIFICATE', label: '民办非企业单位登记证书'},
        {value: 'WORKERS_AND_LEGAL_PERSON_QUALIFICATION', label: '工会法人资格证书'},
        {value: 'TAXATION_REGISTRATION_CERTIFICATE', label: '税务登记证'},
        {value: 'OTHER_ORG_IDENTITY_LICENSE', label: '其他有效机构身份证件'}
      ], // 法人证件类型
      fileBase64: {
        name: '',
        base64: ''
      },
      detailTitle: '证明档案',
      whitelist: ['doc', 'docx', 'pdf', 'jpg', 'png'],
      doubleData: [],
      singleData: [],
      account_info: [],
      arrow: require('@/assets/proof-exemption-admin-images/arrow.png'),
      searchImg: require('@/assets/proof-exemption-admin-images/u133.png'),
      addFile: require('@/assets/proof-exemption-admin-images/addFile.png')
    }
  },

  mounted() {
    // this.initData()
    // this.getDatabyid()
    this.getArchivesDetail()
    // this.getEnterpriseIdentityType()
    // this.getCurrentAccount()
  },
  beforeRouteEnter(to, from, next) {
    // 'handle'为证明协查处理 'archives' 为证明协查档案 剩余是申请协查
    if (to.query.type === 'handle') {
      to.meta.nav_key = 'ElectronicArchives'
    } else if (to.query.type === 'archives') {
      to.meta.nav_key = 'ElectronicArchives'
    } else {
      to.meta.nav_key = 'ElectronicArchives'
    }
    next((vm) => {})
  },
  methods: {
    // 获取详情
    getArchivesDetail() {
      getArchivesDetail({ serial_number: this.$route.query.serial_number }).then((res) => {
        // this.initData(res.data)
        // const userdata = JSON.parse(this.$store.state.user.organization)
        const accountInfo = JSON.parse(sessionStorage.getItem('accountInfo'))
        const { data } = res
        this.islegalPerson = data.handle_affairs_type !== 'NATURAL_PERSON'
        this.queryForm = data
        if (this.queryForm.proof_result === 'not_sign') {
          this.queryForm.audit_time = '无'
          this.queryForm.to_user_name = accountInfo.name
        }
      })
    },
    // 开具电子证明事件
    toIssueService () {
      getArchivesDetailNonEncrypted({ serial_number: this.$route.query.serial_number }).then( res => {
        const { data } = res
        const projectContext = {
          item_name:data.item_name, //事项名称=>发证事项名称
          item_code:data.item_code, //事项编码=>事项编码
          biz_num:data.business_serial_number, //业务流水号=>业务流水号
          CYRMC:data.handle_affairs_name, // 办事人=>持有人名称
          CYRSFZZLX:this.islegalPerson?data.legal_person_identity_type:data.handle_affairs_id_type, //证件类型=>持有人身份证件类型
          CYRSFZJHM:data.handle_affairs_id_number, //证件号码=>持有人身份证件号码
        }
        localStorage.setItem('projectContext',JSON.stringify(projectContext))
        const origin = window.location.origin
        const params = JSON.stringify({ id: this.queryForm.implement_code })
        window.open(`${origin}/license-derate-biz/white/check?tokenName=proof-derate-web&path=licenseIssueMaking/:id&params=${params}&type=inOutherLink`, '_blank')
      })

    },
    // 下载电子证明
    downElectronicFile() {
      downProofArchives( { 'serial_number': this.queryForm.serial_number } ).then( res => {
        dataURLtoDownload(res.data.file_data_base64, res.data.file_name)
      })
      // exportsDown(downProofArchives(), { 'serial_number': this.queryForm.serial_number })
    },

    getDatabyid() {
      if (this.$route.query.type === 'apply') {
        applyView(this.$route.query.id).then((res) => {
          if (res.meta.code === '200' && res.data != null) {
            // console.log(res.data)
            this.initData(res.data)
          }
        })
      } else if (this.$route.query.type === 'archives') {
        archivesView(this.$route.query.id).then((res) => {
          if (res.meta.code === '200' && res.data != null) {
            // console.log(res.data)
            this.initData(res.data)
          }
        })
      } else if (this.$route.query.type === 'handle') {
        // const data = JSON.parse(this.$route.query.data)
        console.log('this.$route.query.type', this.$route.query.type)
        handleView(this.$route.query.id).then((res) => {
          if (res.meta.code === '200' && res.data != null) {
            this.initData(res.data)
          }
        })
      }
    },
    getEnterpriseIdentityType() {
      getEnterpriseIdentityType().then((res) => {
        if (res.meta.code === '200' && res.data != null) {
          this.legalPersonIdentityTypeList = res.data
        } else {
          this.legalPersonIdentityTypeList = []
        }
      })
    },
    getCurrentAccount() {
      getCurrentAccount().then((res) => {
        if (res.meta.code === '200') {
          if (this.queryForm.proof_result === 'not_sign') {
            this.queryForm.to_user_name = res.data.account.userName
          }
        }
      })
    },
    initData(data) {
      // const data = JSON.parse(this.$route.query.data)
      // const userdata = this.$store.state.user.userdata
      const userdata = JSON.parse(this.$store.state.user.organization)
      const accountInfo = JSON.parse(sessionStorage.getItem('accountInfo'))
      console.log('accountInfo', accountInfo, 'userdata', userdata)
      // WAIT :待审核 SUCCESS :审核通过 FAIL :审核失败
      // 处理中的都为处理操作，显示提交按钮，有提交操作、选择附件、处理结果操作
      if (this.$route.query.type === 'handle') {
        this.isHandle = true
        this.isshowResult = true
        this.addForm.audit_result = 'SUCCESS'
        console.log('handle')
      } // 申请中查看操作都没有提交操作、选择附件、处理结果操作
      else if (this.$route.query.type === 'apply') {
        console.log('apply', data.audit_result)
        this.isHandle = false
        this.download(data.id)
        this.addForm.audit_result = data.audit_result
        if (data.audit_result === 'WAIT') {
          this.isshowResult = false
        } else if (data.audit_result === 'SUCCESS') {
          this.isshowResult = true
          // this.addForm.audit_result = '审核通过'
        } else if (data.audit_result === 'FAIL') {
          this.isshowResult = true
          // this.addForm.audit_result = '审核失败'
        }
      } else if (this.$route.query.type === 'archives') {
        // 档案中的待协查为处理操作
        if (data.audit_result === 'WAIT') {
          this.isHandle = true
          this.isshowResult = true
          //  this.addForm.audit_result = '审核通过'
        } // 档案中的已协查为查看操作
        else if (data.audit_result === 'SUCCESS' || data.audit_result === 'FAIL') {
          this.download(data.id)
          this.isHandle = false
          this.isshowResult = true
          this.addForm.audit_result = data.audit_result
          // this.addForm.audit_result = data.audit_result === 'SUCCESS' ? '符合' : '不符合'
        }
      }
      console.log('this.isHandle', this.isHandle, 'this.isshowResult', this.isshowResult)
      this.islegalPerson = data.handle_affairs_type != 'NATURAL_PERSON'
      this.detailTitle = data.material_name + '【' + data.handle_affairs_name + '】'
      this.queryForm = {
        business_serial_number: data.business_serial_number,
        assist_serial_number: data.assist_serial_number,
        handle_affairs_name: data.handle_affairs_name,
        from_assist_org_name: data.from_assist_org_name,
        assist_time: data.creation_time,
        material_name: data.material_name,
        proof_catalog_name: data.proof_catalog_name,
        to_assist_org_name: data.to_assist_org_name,
        handle_affairs_identity_number: data.handle_affairs_identity_number,
        from_assist_user_name: data.from_assist_user_name,
        from_assist_contain: data.from_assist_contain,
        // to_user_name: data.from_assist_user_name,
        from_demand: data.from_demand,
        item_name: data.item_name,
        handle_affairs_id_type: data.handle_affairs_id_type,
        legal_person_identity_type: data.legal_person_identity_type
      }
      this.assist_data_list = data.assist_data_list

      // this.assist_data_list[0].item_list.forEach(e => {
      //   e.cols = '2'
      // })
      // this.assist_data_list[0].item_list.push({ key: 'a1', value: 'a1', cols: '4' })
      // this.assist_data_list[0].item_list.push({ key: 'a2', value: 'a2', cols: '3' })
      // this.assist_data_list[0].item_list.push({ key: 'a3', value: 'a3', cols: '5' })
      // this.assist_data_list[0].item_list.push({ key: 'a4', value: 'a4', cols: '1' })
      if (this.assist_data_list != null) {
        if (this.assist_data_list.length !== 0) {
          this.doubleData = this.getDoubleData(this.assist_data_list[0].item_list).doubleData
          this.singleData = this.getDoubleData(this.assist_data_list[0].item_list).singleData
          if (this.doubleData.length != 0 && this.doubleData % 2 != 0) {
            this.doubleData.push({ key: null, value: null, cols: '2' })
          }
        }
      }

      // console.log('this.doubleData', this.doubleData)
      this.addForm.audit_suggestion = data.audit_suggestion
      this.addForm.audit_time = data.audit_time
      this.addForm.audit_id = data.id
      this.addForm.to_user_name = accountInfo.name
      this.addForm.to_assist_org_name = data.to_assist_org_name
      this.addForm.to_assist_credit_code = userdata.credit_code
      this.addForm.to_user_id = userdata.id
      console.log('this.addForm', this.addForm)
    },
    getDoubleData(data) {
      const doubleData = []
      const singleData = []
      data.forEach((e) => {
        if (e.cols === '2') {
          doubleData.push(e)
        } else {
          singleData.push(e)
        }
      })
      return {
        doubleData,
        singleData
      }
    },
    submitForm(formName) {
      this.$refs[formName].validate((valid, object) => {
        if (valid) {
          // alert('submit!')
          if (this.addForm.raw !== '') {
            Promise.all([this.handle()]).then((res) => {
              if (res[0].meta.code === '200' && res[0].data != null) {
                this.$message({
                  message: '处理成功',
                  type: 'success'
                })
                this.back()
              } else {
                this.$message({
                  message: res[0].meta.message + ',' + res[1].meta.message,
                  type: 'error'
                })
              }
            })
          } else {
            Promise.all([this.handle()]).then((res) => {
              if (res[0].meta.code === '200' && res[0].data != null) {
                this.$message({
                  message: '处理成功',
                  type: 'success'
                })
                this.back()
              } else {
                this.$message({
                  message: res[0].meta.message,
                  type: 'error'
                })
              }
            })
          }
        } else {
          const str = []
          for (const key in object) {
            object[key].map((item) => {
              str.push(item.message)
            })
            let dom = this.$refs[Object.keys(object)[0]]
            if (Object.prototype.toString.call(dom) !== '[object Object]') {
              dom = dom[0]
              break // 结束语句并跳出语句，进行下个语句执行
            }
            // console.log('dom',dom)
            // 定位代码
            dom.$el.scrollIntoView({
              block: 'center',
              behavior: 'smooth'
            })
          }
          // console.log('error submit!!')
          // return false
        }
      })
    },
    handleChange(file, list) {
      // const isLt2M = file.size / 1024 / 1024 < 1
      // if (!isLt2M) {
      //   this.$message.error('上传文件大小不能超过 1MB!')
      // } else {
      //   this.addForm.fileName = file.raw.name
      //   this.addForm.raw = file.raw
      //   this.fileList = list
      // }

      const fileName = getFileType(file.name).fileName
      const isLt10m = file.size / (1024 * 1024) < 1
      if (!isLt10m) {
        this.$message.error('文件大小不能超过1m！')
      } else {
        if (fileName.indexOf('http') != -1 || validPrefix(fileName)) {
          this.$message.error('文件名开头包含特殊符号！')
        } else {
          if (!getIsWhitelist(file.name, this.whitelist)) {
            this.$message.error(`请重新选择以${this.whitelist.join(',')}为后缀名的文件！`)
          } else {
            this.addForm.fileName = file.raw.name
            this.addForm.raw = file.raw
            this.fileList = list
          }
        }
      }
    },
    back() {
      if (this.$route.query.type === 'handle') {
        this.$router.push({
          name: 'ElectronicArchives'
        })
      } else if (this.$route.query.type === 'archives') {
        this.$router.push({
          name: 'ElectronicArchives'
        })
      } else {
        this.$router.push({
          name: 'ElectronicArchives'
        })
      }
    },
    cancle() {
      this.dialogVisible = false
      this.addForm.fileName = ''
      this.fileList = []
      this.addForm.raw = ''
    },
    getFile() {
      if (this.addForm.fileName === '') {
        this.$message({
          message: '请选择文件',
          type: 'warning'
        })
      } else {
        this.upload()
          .then((res) => {
            if (res.data != null && res.meta.code === '200') {
              this.$message({
                message: '文件上传成功',
                type: 'success'
              })
              // this.addForm.fileName = this.addForm.fileName
            } else {
              this.addForm.fileName = ''
              this.addForm.raw = ''
              this.$message({
                message: res.meta.message,
                type: 'warning'
              })
            }
          })
          .catch(() => {
            this.addForm.fileName = ''
            this.addForm.raw = ''
          })
        this.dialogVisible = false
      }
    },
    // 下载当前上传的文件
    downFile() {
      const blob = new Blob([this.addForm.raw])
      const downLink = document.createElement('a')
      downLink.download = this.addForm.raw.name
      downLink.href = URL.createObjectURL(blob)
      // 触发点击
      document.body.appendChild(downLink)
      downLink.click()
      // 然后移除
      document.body.removeChild(downLink)
    },
    // 上传附件
    upload() {
      const query = {
        relation: this.addForm.audit_id
      }
      const fd = new FormData()
      fd.append('file', this.addForm.raw)
      return new Promise((resolve, reject) => {
        upload(query, fd).then((res) => {
          resolve(res)
        })
      })
    },
    // 处理申请
    handle() {
      return new Promise((resolve, reject) => {
        const data = {
          audit_id: this.addForm.audit_id,
          to_user_id: this.addForm.to_user_id,
          to_user_name: this.addForm.to_user_name,
          audit_result: this.addForm.audit_result,
          audit_suggestion: this.addForm.audit_suggestion,
          to_user_content: this.account_info.mobile_phone
        }
        if (this.$route.query.type === 'handle') {
          handleSave(data).then((res) => {
            resolve(res)
          })
        } else if (this.$route.query.type === 'archives') {
          archivesSave(data).then((res) => {
            resolve(res)
          })
        }
      })
    },
    // 获取附件base64编码附件
    download(id) {
      const query = {
        relation: id
      }
      this.pageLoading = true
      download(query)
        .then((res) => {
          this.pageLoading = false
          if (res.meta.code === '200' && res.data !== null) {
            this.fileBase64 = {
              name: res.data.file_name,
              base64: res.data.file_data
            }
          }
        })
        .catch((err) => {
          this.pageLoading = false
        })
    },
    // 下载base64编码附件
    downFileBase64() {
      dataURLtoDownload(this.fileBase64.base64, this.fileBase64.name)
    },
    // 跳转制证签发
    toisuss() {
      // this.$router.push({ path: '/license-derate-biz/proofInvestigationAdmin/investigationServices/licenseIssueList' })
      // https://*************:6041/license-derate-biz/proofInvestigationAdmin/investigationServices/licenseIssueList
      const origin = window.location.origin
      console.log('origin', origin)
      window.open(`${origin}/license-derate-biz/white/check?tokenName=proof-derate-web&path=licenseIssueList&type=inOutherLink`, '_self')
    }
  }
}
</script>

<style scoped>
.btn-box{
  display: flex;
  justify-content: center;
  background: #fff;
  padding: 20px 0 50px;
}

.investigationDetail /deep/ .el-table .cell,
.el-table--border .el-table__cell:first-child .cell {
  text-align: center;
}

.content-header-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.content-header-title i {
  cursor: pointer;
}

.content-header-title h1 {
  font-size: 24px;
}

.select {
  width: 100%;
}

.investigationDetail /deep/.el-upload__input {
  display: none;
}

.investigationDetail /deep/.el-card {
  border-bottom: 0px;
  border-top: 0px;
}

.uploadtext {
  /* margin-left: 10px; */
  margin-left: 10px;
  line-height: 20px;
  margin-top: 10px;
  color: #999999;
}

.tip {
  margin-top: 5px;
  display: flex;
  align-items: center;
}

.tip i {
  color: #e6a23c;
  font-size: 20px;
  margin-right: 10px;
}

.filewrap {
  display: flex;
}

.filewrap .add-button {
  cursor: pointer;
}

.readme {
  /* margin-top: 80px;
  margin-left: 80px; */
}

.margin-10 {
  margin-top: 10px;
}

.info-wrap {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}
.isuss {
  cursor: pointer;
  color: #1f9e73;
}
.info-wrap img {
  width: 35px;
  height: 35px;
  margin-right: 10px;
}

.info-title {
  font-size: 20px;
  color: #333333;
}

.wrap-examine {
  /* background: #f0faf6; */
}

.wrap-examine img {
  width: 35px;
}

.wrap-examine /deep/ .el-card__body {
  /* background: #f0faf6; */
}
</style>
