<template>
  <div />
</template>
<script>
export default {
  data() {
    return {
      routes: this.$store.state.permission.routes,
      currentRouter: []
    }
  },
  created() {
    this.filterRouter(this.routes)
    console.log(this.currentRouter, '第一个有权限的name')
    this.$router.replace({ name: this.currentRouter[0].name })
    // this.$router.replace('licenseIssueLis')
    // this.$router.replace({ name: 'LicenseIssuelist' })
  },
  methods: {
    hasHidden(route) {
      return route.find(r => !r.hidden && !r.link_external)
    },
    isChildren() {
      return
    },
    filterFirstRouter(routes) {
      // let find = this.hasHidden(routes)
      this.isChildren(routes)
      console.log(find, parent)
      if (find) {
        if (find.children != null && find.children) {
          // find = this.filterFirstRouter(find.children)
        }
      }
      return find
    },
    filterRouter(routes) {
      return routes.filter(route => {
        if (!route.hidden && route.children) {
          this.filterRouter(route.children)
        } else {
          if (!route.hidden && !route.link_external && route.name) {
            this.currentRouter.push(route)
          }
        }
      })
    }
  }
}
</script>
