<template>
  <div id="interfaceManager-detail">
    <el-card class="access-system-application-boxcard">
      <div class="access-system-application-edit-table">
        <div class="access-system-application-edit-table-bar">
          <div class="access-system-application-edit-table-bar-item">
            <img src="~@/assets/images/file.png" alt />
            <span>接口配置</span>
          </div>
          <span></span>
        </div>
        <el-divider></el-divider>
        <el-descriptions class="margin-top" :column="2" :size="size" border>
          <el-descriptions-item>
            <template slot="label">接口地址</template>
            {{apiManageData.api_path}}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">接口名称</template>
            {{apiManageData.api_name}}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">状态</template>
            <span v-if="apiManageData.status==='NORMAL'">正常</span>
            <span v-else>禁用</span>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">最后更新时间</template>
            {{apiManageData.last_modification_time}}
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <div class="access-system-application-edit-table">
        <div class="access-system-application-edit-table-bar">
          <div class="access-system-application-edit-table-bar-item">
            <img src="~@/assets/images/file.png" alt />
            <span>接入应用系统</span>
          </div>
          <span>
            <!-- <img src="~@/assets/images/add2x.png" alt @click="addData" /> -->
          </span>
        </div>
        <el-divider></el-divider>
        <el-table :data="apiManageData.relation" border style="width: 100%">
          <el-table-column type="index" width="50" label="序号"></el-table-column>
          <el-table-column prop="name" label="业务系统全称"></el-table-column>
          <el-table-column prop="orgName" label="系统管理方"></el-table-column>
          <el-table-column prop="status" label="状态">
            <template slot-scope="scope">
              <span v-if="scope.row.status==='NORMAL'">正常</span>
              <span v-if="scope.row.status==='DISABLE'">禁用</span>
            </template>
            <!-- <template slot-scope="scope"> -->
            <!-- <span>{{levelOptions.filter(scope.row.level)}}</span> -->
            <!-- <el-radio v-model="scope.row.ischosed" label @change="radioChange(scope.row)"></el-radio> -->
            <!-- </template> -->

            <!-- levelOptions.filter() -->
          </el-table-column>
        </el-table>
      </div>
    </el-card>
  </div>
</template>

<script>
import { getApiManageDetail, disableApiManage, recoveryApiManage, delectApiManage } from '@/api/commonPack/platManege'

export default {
  data() {
    return {
      size: '',
      apiManageData: {
        relation: []
      },
      levelOptions: [
        { value: 'PROVINCE', label: '省级' },
        { value: 'CITY', label: '地市级' },
        { value: 'AREA', label: '区县级' }
      ]
    }
  },

  mounted() {
    this.getApiManageDetail()
  },
  computed: {
    isInterfaceManagerDetailDisable() {
      return this.$store.state.breadcrumbBtn.platManage.isInterfaceManagerDetailDisable
    },
    isInterfaceManagerDetailDel() {
      return this.$store.state.breadcrumbBtn.platManage.isInterfaceManagerDetailDel
    }
  },
  watch: {
    isInterfaceManagerDetailDisable(value) {
      // 监听到有变化就重新获取数据
      // console.log('isInterfaceManagerDetailDisable')
      this.handleChangeStatus(this.apiManageData)
    },
    isInterfaceManagerDetailDel(value) {
      this.handleDelect(this.apiManageData)
    }
  },
  methods: {
    getApiManageDetail() {
      getApiManageDetail(this.$route.query.id)
        .then(res => {
          if (res.meta.code === '200' && res.data != null) {
            this.apiManageData = res.data
            console.log('this.apiManageData', this.apiManageData)
            this.$store.commit('breadcrumbBtn/SET_PLATMANAGE', { interfaceManagerDetailSatus: this.apiManageData.status })
            if (this.apiManageData.relation.length != 0) {
              this.apiManageData.relation.forEach(e => {
                e.levelname = this.levelOptions.filter(i => {
                  return i.value == e.level
                })[0].label
              })
            }
          }
        })
        .catch(err => {})
    },
    handleChangeStatus(row) {
      const alert = {}
      if (row.status === 'NORMAL') {
        alert.content = '是否确认禁用此接口？'
        alert.status = 'DISABLE'
      } else {
        alert.content = '是否确认恢复此接口？'
        alert.status = 'NORMAL'
      }
      this.$alert(`<span>${alert.content}</span><br/><span>接口名称：${row.api_path}</span>`, '警告提示', {
        dangerouslyUseHTMLString: true,
        type: 'warning',
        showCancelButton: true,
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      })
        .then(() => {
          console.log(row.status)
          if (row.status === 'NORMAL') {
            this.disableApiManage(row.id)
          } else {
            this.recoveryApiManage(row.id)
          }
        })
        .catch(() => {
          /* this.$message({
          type: 'info',
          message: '已取消删除'
        }) */
        })
    },
    disableApiManage(id) {
      disableApiManage(id).then(res => {
        if (res.meta.code === '200') {
          this.$message({
            message: '修改成功',
            type: 'success'
          })
          this.getApiManageDetail()
        }
      })
    },
    recoveryApiManage(id) {
      recoveryApiManage(id).then(res => {
        if (res.meta.code === '200') {
          this.$message({
            message: '修改成功',
            type: 'success'
          })
          this.getApiManageDetail()
        }
      })
    },
    handleDelect(row) {
      const alert = {}
      alert.content = '是否删除此接口？'
      this.$alert(`<span>${alert.content}</span>`, '警告提示', {
        dangerouslyUseHTMLString: true,
        type: 'warning',
        showCancelButton: true,
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      })
        .then(() => {
          this.delectApiManage(row.id)
        })
        .catch(() => {
          /* this.$message({
          type: 'info',
          message: '已取消删除'
        }) */
        })
    },
    delectApiManage(id) {
      delectApiManage(id).then(res => {
        if (res.meta.code === '200') {
          this.$message({
            message: '修改成功',
            type: 'success'
          })
          this.getApiManageDetail()
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
#interfaceManager-detail {
  padding: 10px;
}
.access-system-application-edit-table {
  margin-bottom: 20px;
  &-bar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    //   margin-top: 51px;
    &-item {
      display: flex;
      align-items: flex-end;
    }
    img {
      width: 22px;
      height: 22px;
      margin-right: 5px;
    }
    span {
      color: #333333;
    }
  }
}

.access-system-application-edit-table ::v-deep .el-divider--horizontal {
  margin: 9px 0 21px;
}
</style>