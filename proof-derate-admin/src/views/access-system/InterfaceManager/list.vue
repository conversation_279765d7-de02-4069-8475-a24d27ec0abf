<template>
  <div id="access-system-application">
    <el-card class="access-system-application-boxcard">
      <!-- <div slot="header" class="clearfix">
        <el-button plain @click="queck()">快速注册</el-button>
        <el-button type="primary" plain @click="catalogueRegister()">目录注册</el-button>
      </div>-->
      <el-form :model="searchForm" class="demo-form-inline" label-width="120px" @submit.native.prevent>
        <el-row :gutter="24">
          <el-col :span="9">
            <el-form-item label="接口地址">
              <el-input v-model="searchForm.api_path" clearable placeholder="请输入接口地址" />
            </el-form-item>
          </el-col>
          <el-col :span="9">
            <el-form-item label="接口名称">
              <el-input v-model="searchForm.api_name" clearable placeholder="请输入接口名称" />
            </el-form-item>
          </el-col>
          <el-col :span="6" class="submitbtn">
            <el-button type="primary" plain native-type="submit" @click="onSubmit">查询</el-button>
            <el-button plain native-type="submit" @click="reset">重置</el-button>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="9">
            <el-form-item label="接口分类" prop="group_name">
              <el-select v-model="searchForm.group_name" filterable placeholder="请选择接口分类" clearable style="width: 100%;" @clear="groupNameClear">
                <el-option v-for="item in groupNameOptions" :key="item" :label="item" :value="item" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="9">
            <el-form-item label="接口状态">
              <!-- :indeterminate="isIndeterminate" -->
              <el-radio-group v-model="searchForm.status">
                <el-radio v-for="(item,idx) in statusOptions" :key="idx" :label="item.value">{{ item.label }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <custom-table
        ref="table"
        :is-card-type="false"
        :table-data="tableData"
        :table-header="tableHeader"
        @query="query"
        @refresh="query(1)"
      >
        <template #api_path="{ row }">
          <el-button type="text" @click="getDetail(row,'app:api_manage:view')">{{ row.api_path }}</el-button>
          <!-- <span v-else>{{ row.api_path }}</span> -->
        </template>
        <template #status="{ row }">
          <span v-if="row.status==='NORMAL'">正常</span>
          <span v-if="row.status==='DISABLE'">禁用</span>
        </template>
        <template #handle="{ row }">
          <el-button type="text" @click="handleEdit(row)">编辑</el-button>
          <el-button v-if="row.status==='DISABLE'" v-permission="'app:api_manage:recovery'" type="text" @click="handleChangeStatus(row)">恢复</el-button>
          <el-button v-else v-permission="'app:api_manage:disable'" type="text" class="table-delete" @click="handleChangeStatus(row)">禁用</el-button>
          <el-button v-permission="'app:api_manage:del'" type="text" class="table-delete" @click="delect(row)">删除</el-button>
        </template>
      </custom-table>
    </el-card>
    <el-dialog title="编辑" class="edit-group-form" width="40%" :visible.sync="dialogEditGroupFormVisible">
      <el-form :model="editGroupForm">
        <el-form-item label="接口地址" label-width="80px">
          <el-input v-model="editGroupForm.apiPath" disabled autocomplete="off" style="width: 80%;" />
        </el-form-item>
        <el-form-item label="接口名称" label-width="80px">
          <el-input v-model="editGroupForm.api_name" autocomplete="off" style="width: 80%;" />
        </el-form-item>
        <el-form-item label="接口分类" label-width="80px">
          <el-select v-model.trim="editGroupForm.newGroup" filterable allow-create default-first-option placeholder="请选择或输入接口分类" style="width: 80%;">
            <el-option v-for="item in groupNameOptions" :key="item" :label="item" :value="item" />
          </el-select>
        </el-form-item>
        <el-button type="text" class="manager-btn" @click="handleManagerGroup">管理分类</el-button>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitEditGroup">确 定</el-button>
        <el-button @click="dialogEditGroupFormVisible = false">取 消</el-button>
      </div>
    </el-dialog>
    <el-dialog title="新建" :visible.sync="dialogVisible" width="90%" @close="close">
      <div>
        <el-transfer v-model="transferValue" filterable :data="transferData" :titles="titles">
          <span slot-scope="{ option }">
            <el-tooltip class="item" effect="dark" :content="option.label" placement="top">
              <span>{{ option.label }}</span>
            </el-tooltip>
          </span>
        </el-transfer>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="cancle">取 消</el-button>
        <el-button type="primary" @click="sumbit">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog title="选择接口" :visible.sync="dialogVisible1" width="40%" @close="importClose">
      <div class="wrap">
        <el-table :data="selectValueList" border style="width: 100%" height="520px">
          <el-table-column prop="date" label="选择" width="180">
            <template slot-scope="scope">
              <el-checkbox v-model="scope.row.ischosed" />
              <!-- <el-radio v-model="scope.row.ischosed" label @change="radioChange(scope.row)"></el-radio> -->
            </template>
          </el-table-column>
          <el-table-column prop="api_path" label="接口地址" />
          <el-table-column prop="status" label="状态">
            <template slot-scope="scope">
              <span v-if="scope.row.status==='DISABLE'">禁用</span>
              <span v-if="scope.row.status==='NORMAL'">正常</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="importCancle">取 消</el-button>
        <el-button type="primary" @click="importSumbit">确 定</el-button>
      </span>
    </el-dialog>
    <!-- <fileDialog
      ref="fileDialog"
      :whitelist="whitelist"
      :file-size-limit="fileSizeLimit"
      :multiple="multiple"
      @dialogClose="dialogClose"
      @getFilelist="getFilelist"
    /> -->
    <fileDialog ref="fileDialog" :accept="accept" :limit="limit" :file-size-limit="fileSizeLimit" :is-show-temple="isShowTemple" :import-result-data="importResultData" @getFilelist="getFilelist" @downTemple="downTemple" />

    <managerGroup ref="managerGroup" @update="updateGroup" />
    <exportBox ref="export" />
  </div>
</template>

<script>
import { getapiManagePage, downloadApiManageTemplate, getUncheckedList, getCheckedList, createApiManage, disableApiManage, recoveryApiManage, delectApiManage, importApiManage, getApiGroupOptions, editApi, exportApiManage } from '@/api/commonPack/platManege'
import CustomTable from '@/components/Element/Table'
import fileDialog from '@/components/fileDialog/index2.vue'
import managerGroup from './components/manager-group.vue'
import exportBox from './components/export.vue'

import { exportsDown, isPermission, convertToCamelCase, convertToSnakeCase } from '@/utils/index.js'
export default {
  components: {
    CustomTable,
    fileDialog,
    managerGroup,
    exportBox
  },
  data() {
    return {
      transferData: [],
      transferValue: [],
      selectValueList: [],
      titles: ['未添加', '已添加'],
      searchForm: {
        api_path: '',
        api_name:'',
        page_num: 1,
        page_size: 10,
        status: null
      },
      statusOptions: [
        { value: null, label: '全部' },
        { value: 'NORMAL', label: '正常' },
        { value: 'DISABLE', label: '禁用' }
      ],
      dialogVisible: false,
      dialogVisible1: false,
      tableData: {
        content: [], // 表格数据
        loading: false, // 控制表格加载效果
        total: 10, // 分页查询时的总数，去掉该属性，不显示分页条
        currentPage: 1, // 当前页码
        pageSize: 10,
        pageDirection: 'desc',
        isShowIndex: true,
        border: false
        // isShowSelection: true // 是否显示多选框，默认false
      },
      tableHeader: [
        {
          label: '接口地址',
          prop: 'api_path',
          minWidth: '300px',
          align: 'left',
          slot: 'api_path'
        },
        {
          label: '接口名称',
          prop: 'api_name',
          minWidth: '150px',
          align: 'left'
        },
        {
          label: '接口分类',
          prop: 'group',
          minWidth: '150px',
          align: 'left'
        },
        {
          label: '状态',
          prop: 'status',
          minWidth: '150px',
          align: 'left',
          slot: 'status'
        },
        {
          label: '最后更新时间',
          prop: 'last_modification_time',
          minWidth: '200px',
          align: 'left'
        },
        {
          label: '操作',
          prop: '',
          slot: 'handle',
          minWidth: '160px',
          align: 'left'
        }
      ],
      /* whitelist: ['xlsx', 'xls'],
      fileSizeLimit: 2,
      multiple: false, */
      accept: '.xlsx,.xls',
      fileSizeLimit: 2,
      limit: 1,
      isShowTemple: true,
      importResultData: {},
      groupNameOptions: [],
      dialogEditGroupFormVisible: false, // 编辑弹窗
      editGroupForm: {
        id: '',
        api_name:'',
        apiPath: '',
        oldGroup: '',
        newGroup: ''
      }
    }
  },
  computed: {
    interfaceManagerAdd() {
      return this.$store.state.breadcrumbBtn.platManage.interfaceManagerAdd
    },
    interfaceManagerExport() {
      return this.$store.state.breadcrumbBtn.platManage.interfaceManagerExport
    },
    interfaceManagerUpland() {
      return this.$store.state.breadcrumbBtn.platManage.interfaceManagerUpland
    }
  },
  watch: {
    interfaceManagerAdd(value) {
      // 监听到有变化就重新获取数据
      if (value) {
        this.dialogVisible = true
      }
    },
    interfaceManagerExport(value) {
      console.log(value)
      // 监听到有变化就重新获取数据
      if (value) {
        // this.dialogVisible1 = true
        this.exportsApiList()
      }
    },
    interfaceManagerUpland(value) {
      // 监听到有变化就重新获取数据
      if (value) {
        this.$refs.fileDialog.dialogVisible = true
        this.$store.commit('breadcrumbBtn/SET_PLATMANAGE', { interfaceManagerUpland: false })
      }
    }
  },
  mounted() {
    this.getApiGroupOptions()
    this.getapiManagePage()
    // this.getUncheckedList()
    // this.getCheckedList()
  },
  methods: {
    isPermission,
    // 下载模板
    downTemple() {
      // exportsDown(downloadApiManageTemplate())
      exportsDown(downloadApiManageTemplate(), {}, '', 'licc')
    },
    // 导出
    exportsApiList() {
      exportsDown(exportApiManage(), {}, '', 'licc')
      this.$store.commit('breadcrumbBtn/SET_PLATMANAGE', { interfaceManagerExport: false })
    },
    // 获取分类options
    getApiGroupOptions() {
      getApiGroupOptions().then(res => {
        this.groupNameOptions = res.data
        console.log(this.groupNameOptions, 22)
      })
    },
    // 列表-编辑
    handleEdit(row) {
      const item = {
        apiPath: row.api_path,
        id: row.id,
        oldGroup: row.group,
        newGroup: row.group,
        api_name: row.api_name
      }
      this.editGroupForm = item
      this.dialogEditGroupFormVisible = true
    },
    // 管理分类组件-新增分类emit事件
    updateGroup(arr) {
      this.getApiGroupOptions()
      this.getapiManagePage()
      // this.groupNameOptions = arr.map((item) => item.group)
    },
    // 编辑分类确认
    submitEditGroup() {
      const params = {
        old_group: this.editGroupForm.oldGroup,
        new_group: this.editGroupForm.newGroup,
        api_name : this.editGroupForm.api_name,
        id:this.editGroupForm.id
      }
      console.log(params)
      editApi(params).then(res => {
        if (Number(res.meta.code) === 200) {
          this.dialogEditGroupFormVisible = false
          this.getApiGroupOptions()
          this.getapiManagePage()
        }
      })
    },
    // 打开管理分类弹窗
    handleManagerGroup() {
      this.$refs.managerGroup.dialogVisible = true
    },
    dialogClose() {
      this.$store.commit('breadcrumbBtn/SET_PLATMANAGE', { interfaceManagerUpland: false })
    },
    getFilelist(list) {
      console.log(list, this.$refs.fileDialog.fileList)
      this.importApiManage()
    },
    importApiManage() {
      if (this.$refs.fileDialog.fileList.length !== 0) {
        // importApiManage().then(res => {})
        const params = {
          config: this.$refs.fileDialog.config
        }
        const file = this.$refs.fileDialog.fileList[0].raw
        const fd = new FormData()
        fd.append('file', file)
        importApiManage(fd, params).then(res => {
          console.log(res)
          const { common } = convertToCamelCase(res.data)
          this.$refs.fileDialog.dialogVisible = false
          this.$refs.fileDialog.fileList = []
          this.importResultData = common
          this.$refs.fileDialog.dialogVisibleStatus = true

          const type = res.meta.code === '200' ? 'success' : 'error'
          // const data = res.data ? res.data : res.meta.message
          // const message = data.split('/').join('<br/>')
          this.$message({
            type: type,
            dangerouslyUseHTMLString: true,
            message: '导入成功！'
          })
          if (type === 'success') {
            console.log('getapiManagePage')
            this.getapiManagePage()
            // this.getUncheckedList()
            // this.getCheckedList()
          }
        }).catch(() => {
          this.$refs.fileDialog.dialogVisible = false
          this.$refs.fileDialog.fileList = []
        })
      }
    },
    onSubmit() {
      // this.searchForm.page_num = 1
      // this.searchForm.page_size = 10
      this.searchForm.page_num = 1
      this.searchForm.page_size = 10
      this.tableData.currentPage = this.searchForm.page_num
      this.tableData.pageSize = this.searchForm.page_size
      this.getapiManagePage()
    },
    reset() {
      this.searchForm = {
        api_path: '',
        page_num: 1,
        page_size: 10,
        status: null
      }
    },
    // 清空接口分类时去除对应参数
    groupNameClear(){
      delete this.searchForm.group_name
    },
    query() {
      this.searchForm.page_num = this.tableData.currentPage
      this.searchForm.page_size = this.tableData.pageSize
      this.getapiManagePage()
    },
    getapiManagePage() {
      getapiManagePage(this.searchForm).then(res => {
        if (res.data != null && res.meta.code === '200') {
          if (res.data.content != null) {
            const edit = res.data.content.find(x => x.id === this.editGroupForm.id) || []
            this.editGroupForm = {
              apiPath: edit.api_path,
              id: edit.id,
              oldGroup: edit.group,
              newGroup: edit.group
            }
            this.tableData.content = res.data.content
            this.tableData.total = Number(res.data.total_elements)
          } else {
            this.tableData.content = []
            this.tableData.total = 0
          }

          // this.tableData.currentPage = res.data.totalPages
        }
      })
    },
    getDetail(row, key) {
      if (isPermission(this.$route.meta.permission, key)) {
        this.$router.push({ name: 'InterfaceManagerDetail', query: { id: row.id }})
      }
    },
    delect(row) {
      this.handleDelect(row)
    },
    sumbit() {
      this.dialogVisible = false
      this.$store.commit('breadcrumbBtn/SET_PLATMANAGE', { interfaceManagerAdd: false })
      this.createApiManage()
    },
    importSumbit() {
      this.dialogVisible1 = false
      this.$store.commit('breadcrumbBtn/SET_PLATMANAGE', { interfaceManagerImport: false })
      // this.exportApiManage()
    },
    close() {
      this.$store.commit('breadcrumbBtn/SET_PLATMANAGE', { interfaceManagerAdd: false })
    },
    importClose() {
      this.$store.commit('breadcrumbBtn/SET_PLATMANAGE', { interfaceManagerImport: false })
    },
    cancle() {
      this.dialogVisible = false
      this.$store.commit('breadcrumbBtn/SET_PLATMANAGE', { interfaceManagerAdd: false })
    },
    importCancle() {
      this.dialogVisible1 = false
      this.$store.commit('breadcrumbBtn/SET_PLATMANAGE', { interfaceManagerImport: false })
    },
    getUncheckedList() {
      getUncheckedList().then(res => {
        if (res.data != null && res.meta.code === '200') {
          this.transferData = res.data.map(i => {
            return { key: i, label: i }
          })
          this.transferData = this.transferData.filter((item) => !item.label.includes('webapi'))
          console.log('this.transferData', this.transferData)
        }
      })
    },
    getCheckedList() {
      getCheckedList().then(res => {
        if (res.data != null && res.meta.code === '200') {
          this.transferValue = res.data.map(i => {
            return i.api_path
          })
          this.selectValueList = res.data.map(i => {
            i.ischosed = false
            return i
          })
          // console.log('this.transferValue ', this.transferValue)
        }
      })
    },
    createApiManage() {
      const data = {
        api_path: this.transferValue
      }
      createApiManage(data).then(res => {
        // console.log(res)
        if (res.meta.code === '200') {
          this.$message({
            message: '新增成功',
            type: 'success'
          })
          this.getapiManagePage()
        }
      })
    },
    handleChangeStatus(row) {
      const alert = {}
      if (row.status === 'NORMAL') {
        alert.content = '是否确认禁用此接口？'
        alert.status = 'DISABLE'
      } else {
        alert.content = '是否确认恢复此接口？'
        alert.status = 'NORMAL'
      }
      this.$alert(`<span>${alert.content}</span><br/><span>接口名称：${row.api_path}</span>`, '警告提示', {
        dangerouslyUseHTMLString: true,
        type: 'warning',
        showCancelButton: true,
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      })
        .then(() => {
          console.log(row.status)
          if (row.status === 'NORMAL') {
            this.disableApiManage(row.id)
          } else {
            this.recoveryApiManage(row.id)
          }
        })
        .catch(() => {
          /* this.$message({
          type: 'info',
          message: '已取消删除'
        }) */
        })
    },
    handleDelect(row) {
      const alert = {}
      alert.content = '是否删除此接口？'
      this.$alert(`<span>${alert.content}</span>`, '警告提示', {
        dangerouslyUseHTMLString: true,
        type: 'warning',
        showCancelButton: true,
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      })
        .then(() => {
          this.delectApiManage(row.id)
        })
        .catch(() => {
          /* this.$message({
          type: 'info',
          message: '已取消删除'
        }) */
        })
    },
    delectApiManage(id) {
      delectApiManage(id).then(res => {
        if (res.meta.code === '200') {
          this.$message({
            message: '修改成功',
            type: 'success'
          })
          this.getapiManagePage()
          this.getUncheckedList()
          this.getCheckedList()
        }
      })
    },
    disableApiManage(id) {
      disableApiManage(id).then(res => {
        if (res.meta.code === '200') {
          this.$message({
            message: '修改成功',
            type: 'success'
          })
          this.getapiManagePage()
          this.getCheckedList()
        }
      })
    },
    recoveryApiManage(id) {
      recoveryApiManage(id).then(res => {
        if (res.meta.code === '200') {
          this.$message({
            message: '修改成功',
            type: 'success'
          })
          this.getapiManagePage()
          this.getCheckedList()
        }
      })
    },
    exportApiManage() {
      const exportData = {
        data: []
      }
      this.selectValueList.forEach(e => {
        if (e.ischosed === true) {
          exportData.data.push(e)
        }
      })
      // console.log(exportData)
      if (exportData.data.length != 0) {
        exportsDown('/auth/webapi/v1/common/api_manage/export', exportData, '接口管理导出文件.xlsx')
      } else {
        this.$message({
          message: '请选择接口进行导出',
          type: 'error'
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
#access-system-application {
  padding: 10px;
}
.checkbox-group {
  display: inline-block;
  margin-left: 30px;
}
#access-system-application::v-deep .el-transfer-panel {
  width: 310px;
}
.table-delete {
  color: #ff2b2b;
}
.edit-group-form{
  .el-form{
    position: relative;
  }
  .manager-btn{
    position: absolute;
    bottom: 0;
    right: 25px;
  }
  .dialog-footer{
    display: flex;
    justify-content: center;
  }
}
</style>
