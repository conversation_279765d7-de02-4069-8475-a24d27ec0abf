<!--

 -->
<template>
  <div class="container">
    <CardTitle title-name="新建接口管理" />
    <el-card>
      <div class="content">
        <div class="box">
          <div class="steps"><span class="num">1</span><span class="name">选择接口</span></div>
          <div class="apipath-box">
            <el-transfer v-model="transferValue" filterable :data="transferData" :titles="titles">
              <span slot-scope="{ option }">
                <el-tooltip class="item" effect="dark" :content="option.label" placement="top">
                  <span>{{ option.label }}</span>
                </el-tooltip>
              </span>
            </el-transfer>
          </div>
          <div class="steps second"><span class="num">2</span><span class="name">选择接口分类</span></div>
          <el-form ref="form" :model="form" label-width="80px">
            <el-form-item label="接口分类" prop="groupName">
              <el-select v-model="form.groupName" filterable allow-create default-first-option placeholder="请选择接口分类" clearable style="width: 50%;">
                <el-option v-for="item in groupNameOptions" :key="item" :label="item" :value="item" />
              </el-select>
              <el-button type="text" class="manager-btn" @click="handleManagerGroup">管理分类</el-button>
            </el-form-item>
          </el-form>
          <div class="footer-btn">
            <el-button type="primary" @click="createApiManage">确 认</el-button>
          </div>
        </div>
      </div>
    </el-card>
    <managerGroup ref="managerGroup" @update="updateGroup" />

  </div>
</template>

<script>
import CardTitle from '@/components/CardTitle'
import { getapiManagePage, getUncheckedList, getCheckedList, createApiManage, disableApiManage, recoveryApiManage, delectApiManage, importApiManage, getApiGroupOptions } from '@/api/commonPack/platManege'
import managerGroup from './components/manager-group.vue'

export default {
  name: '',
  components: { CardTitle, managerGroup },

  data() {
    return {
      transferData: [],
      transferValue: [],
      titles: ['未添加', '已添加'],
      groupNameOptions: [],
      form: {
        groupName: ''
      }

    }
  },

  computed: {},

  mounted() {
    this.getUncheckedList()
    this.getApiGroupOptions()
  },

  methods: {
    // 获取分类options
    getApiGroupOptions() {
      getApiGroupOptions().then(res => {
        console.log(res, 1234)
        this.groupNameOptions = res.data
      })
    },
    // 获取接口数据
    getUncheckedList() {
      getUncheckedList().then(res => {
        if (res.data != null && res.meta.code === '200') {
          this.transferData = res.data.map(i => {
            return { key: i, label: i }
          })
          this.transferData = this.transferData.filter((item) => !item.label.includes('webapi'))
        }
      })
    },
    // 打开管理分类弹窗
    handleManagerGroup() {
      this.$refs.managerGroup.dialogVisible = true
    },
    updateGroup() {
      this.getApiGroupOptions()
    },
    // 确认
    createApiManage() {
      const that = this
      if (that.transferValue.length === 0) {
        that.$message({
          message: '请选择接口',
          type: 'error'
        })
        return
      }
      const data = {
        api_path: that.transferValue,
        group: that.form.groupName || null
      }
      createApiManage(data).then(res => {
        // console.log(res)
        if (res.meta.code === '200') {
          that.$message({
            message: '新增成功',
            type: 'success',
            onClose() {
              that.$router.go(-1)
            }
          })
        }
      })
    }
  }
}

</script>
<style lang='scss' scoped>
.container {
  padding: 10px;
}
.content{
  // padding: 20px;
  .steps{
    font-size: 16px;
    .num{
      display: inline-block;
      width: 20px;
      height: 20px;
      text-align: center;
      line-height: 20px;
      color: #fff;
      background: rgb(66, 147, 244);
      border-radius: 100%;
    }
    .name{
     color: rgb(66, 147, 244);
     padding-left: 6px;
    }
    &.second{
      margin:20px 0 10px;
    }
  }
  .apipath-box{
    margin-top: 10px;
  }
  .apipath-box::v-deep .el-transfer-panel {
    width: 40%;
  }
  .apipath-box::v-deep .el-transfer-panel__body{
    height: 400px;
  }
  .footer-btn{
    margin: 20px auto;
    display: flex;
    justify-content: center;
  }
  .manager-btn{
    margin-left: 6px;
  }
}
</style>
