<!-- 管理分类弹窗 -->
<template>
  <div>
    <el-dialog title="导出" class="edit-group-form" width="80%" :visible.sync="dialogVisible">
      <custom-table
        ref="table"
        :is-card-type="false"
        :table-data="tableData"
        :table-header="tableHeader"
        @select="select"
        @select-all="select"
      >
        <template #status="{ row }">
          <span v-if="row.status==='NORMAL'">正常</span>
          <span v-if="row.status==='DISABLE'">禁用</span>
        </template>
      </custom-table>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submit">确 定</el-button>
        <el-button @click="dialogVisible = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import CustomTable from '@/components/Element/Table'
import { getApiGroupOptions, editApiGroup } from '@/api/commonPack/platManege'
import { convertToCamelCase, convertToSnakeCase } from '@/utils/index.js'

export default {
  name: 'ManagerGroup',
  components: {
    CustomTable
  },

  data() {
    return {
      dialogVisible: false,
      tableData: {
        content: [{ apiPath: '222' }, { apiPath: '', isNew: true }], // 表格数据
        loading: false, // 控制表格加载效果
        currentPage: 1, // 当前页码
        pageSize: 10,
        isShowIndex: false,
        maxHeight: '200px',
        multiple: true,
        isShowSelection: true // 是否显示多选框，默认false
      },
      tableHeader: [
        {
          label: '接口地址',
          prop: 'apiPath',
          minWidth: '100px',
          align: 'left'
        },
        {
          label: '接口分类',
          prop: 'group',
          minWidth: '50px',
          align: 'left'
        },
        {
          label: '状态',
          prop: 'status',
          minWidth: '150px',
          align: 'left',
          slot: 'status'
        }
      ]
    }
  },

  computed: {},

  mounted() {
    // this.getApiGroupOptions()
  },

  methods: {
    select(selection) {
      console.log(selection)
    },
    submit() {}
  }
}

</script>
<style lang='scss' scoped>
.add-btn{
  margin-left: 20px;
}
.table-delete {
  color: #ff2b2b;
}
</style>
