<!-- 管理分类弹窗 -->
<template>
  <div>
    <el-dialog title="管理分类" class="edit-group-form" width="60%" :visible.sync="dialogVisible" @close="closeDialog">
      <!-- <el-button type="primary" plain class="add-btn" :disabled="addDisabled" @click="addGroup">添加分类</el-button> -->
      <custom-table
        ref="table"
        :is-card-type="false"
        :table-data="tableData"
        :table-header="tableHeader"
      >
        <template #group="{ row, $index }">
          <el-input v-if="row.isNew || row.isEdit" v-model.trim="row.group" maxlength="10" show-word-limit placeholder="请输入分类名称" @blur="pathInputBlur($index)" />
          <span v-else>{{ row.group }}</span>
        </template>

        <template #handle="{row,$index}">
          <el-button v-if="row.isNew || row.isEdit" type="text" @click="handleAdd(row,$index)">确定</el-button>
          <span v-else>
            <el-button type="text" @click="handleEdit($index)">编辑</el-button>
            <el-button type="text" class="table-delete" @click="delect(row,$index)">删除</el-button>
          </span>
        </template>
      </custom-table>
      <!-- <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submit">确 定</el-button>
        <el-button @click="dialogVisible = false">取 消</el-button>
      </div> -->
    </el-dialog>
  </div>
</template>

<script>
import CustomTable from '@/components/Element/Table'
import { getApiGroupOptions, editApiGroup, delApiGroup } from '@/api/commonPack/platManege'
import { convertToCamelCase, convertToSnakeCase } from '@/utils/index.js'

export default {
  name: 'ManagerGroup',
  components: {
    CustomTable
  },

  data() {
    return {
      addDisabled: false,
      dialogVisible: false,
      tableData: {
        content: [], // 表格数据
        loading: false, // 控制表格加载效果
        currentPage: 1, // 当前页码
        pageSize: 10,
        isShowIndex: true,
        border: true
      },
      tableHeader: [
        {
          label: '分类名称',
          prop: 'group',
          minWidth: '400px',
          align: 'left',
          slot: 'group'
        },
        {
          label: '操作',
          prop: '',
          slot: 'handle',
          minWidth: '100px',
          align: 'left'
        }
      ]
    }
  },

  computed: {},
  watch: {
    dialogVisible: {
      handler(val) {
        if (!val) return
        this.getApiGroupOptions()
      },
      deep: true,
      immediate: true
    }
  },

  created() {
    this.getApiGroupOptions()
  },

  methods: {
    // 获取分类options
    getApiGroupOptions() {
      getApiGroupOptions().then(res => {
        console.log(11)
        const data = convertToCamelCase(res.data)
        this.tableData.content = data.map((key, value) => {
          return { group: key, oldGroup: key }
        })
      })
    },
    addGroup() {
      this.addDisabled = true
      this.tableData.content.push({ group: '', oldGroup: '', isNew: true })
    },
    pathInputBlur(index) {
      // if (this.tableData.content[index].group) this.tableData.content[index].isNew = false
    },
    // 新增/编辑分类
    handleAdd(row, idx) {
      let flag = false
      this.tableData.content.forEach(element => {
        console.log(row.group === element.oldGroup, row.group, element.oldGroup)
        if (row.group === element.oldGroup) {
          // this.$message.error('已存在相同的分类名称，请另取')
          flag = true
          return
        }
      })
      if (flag) return
      if (row.isEdit) {
        const params = {
          old_group: row.oldGroup,
          new_group: row.group
        }
        editApiGroup(params).then(res => {
          if (Number(res.meta.code) === 200) {
            this.$message.success('修改成功')
          }
        })
        this.$set(this.tableData.content[idx], 'isEdit', false)
      } else if (row.isNew) {
        this.addDisabled = false
        this.$set(this.tableData.content[idx], 'isNew', false)
      }
    },
    handleEdit(index) {
      this.$set(this.tableData.content[index], 'isEdit', true)
      // this.tableData.content[index].isNew = true
    },
    delect(row, index) {
      const params = {
        old_group: row.oldGroup,
        new_group: ''
      }
      delApiGroup(params).then(res => {
        if (Number(res.meta.code) === 200) {
          this.$message.success('删除成功')
        }
      })
      this.tableData.content.splice(index, 1)
    },
    closeDialog() {
      this.$emit('update', this.tableData.content)
    },
    submit() {
      let flag = true
      this.tableData.content.forEach(ele => {
        if (!ele.group) {
          flag = true
        }
      })
      // const params = convertToSnakeCase
    }
  }
}

</script>
<style lang='scss' scoped>
.add-btn{
  margin-left: 20px;
}
.table-delete {
  color: #ff2b2b;
}
</style>
