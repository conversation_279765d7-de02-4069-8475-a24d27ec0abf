<template>
  <div class="ip-input">
    <el-input
      v-for="(part, index) in ipParts"
      :key="index"
      :ref="`input${index}`"
      v-model="ipParts[index]"
      :maxlength="3"
      class="ip-part"
      @input="handleInput(index)"
      @keydown.native="handleKeyDown(index, $event)"
      @blur="handleBlur(index)"
    />
    <!-- <div v-if="error" class="error-message">{{ error }}</div> -->
  </div>
</template>

<script>
export default {
  props: {
    // 父组件传递的当前 IP 地址
    value: {
      type: String,
      default: ''
    },
    // 父组件传递的所有 IP 地址列表
    allIps: {
      type: Array,
      default: () => []
    },
    // 当前 IP 地址的唯一标识（用于排除自身）
    currentIpIndex: {
      type: Number,
      default: -1
    }
  },
  data() {
    return {
      ipParts: ['', '', '', ''], // IP 地址的四个部分
      error: '', // 错误信息
      lastInputIndex: -1 // 记录最后一次输入的输入框索引
    }
  },
  watch: {
    // 监听父组件传递的 IP 地址变化
    value: {
      immediate: true,
      handler(newVal) {
        if (newVal) {
          const parts = newVal.split('.')
          if (parts.length === 4) {
            this.ipParts = parts
          }
        }
      }
    }
  },
  methods: {
    handleInput(index) {
      let value = this.ipParts[index]

      // 1. 限制输入为数字或星号（*）
      value = value.replace(/[^0-9*]/g, '')

      // 2. 如果不是最后一个输入框（index !== 3），则禁止输入星号
      if (index !== 3 && value.includes('*')) {
        value = value.replace(/\*/g, '') // 移除星号
      }

      // 3. 如果是最后一个输入框（index === 3），且输入了星号，则强制为单独的星号
      if (index === 3 && value.includes('*') && value !== '*') {
        value = '*' // 强制设置为单独的星号
      }

      // 2. 不允许第一个数字为 0（如 "01" -> "1"）
      if (value.length > 1 && value.startsWith('0')) {
        value = value.replace(/^0+/, '') // 去除开头的 0
        if (value === '') value = '0' // 如果全被清空，保留 0
      }

      // 3. 更新值
      this.ipParts[index] = value
      this.lastInputIndex = index

      // 4. 自动跳到下一个输入框
      if (value.length === 3 && index < 3) {
        this.$refs[`input${index + 1}`][0].focus()
      }

      // 5. 通知父组件 IP 地址变化
      const newIp = this.ipParts.join('.')
      this.$emit('input', newIp)

      // this.validateIp(newIp)
    },
    handleKeyDown(index, event) {
      // 处理键盘事件
      if (event.key === 'Backspace' && this.ipParts[index] === '' && index > 0) {
        this.$refs[`input${index - 1}`][0].focus()
      }
    },
    handleBlur(index) {
      // 如果当前输入框的值为空，则回填星号
      /* if (this.lastInputIndex !== -1 && this.ipParts[this.lastInputIndex] === '') {
        this.ipParts[this.lastInputIndex] = '*'
        this.$emit('input', this.ipParts.join('.')) // 通知父组件更新 IP 地址
      } */
      this.validateIp()
    },
    validateIp(newIp) {
      // 排除自身后校验是否有重复
      const currentIp = newIp || this.ipParts.join('.')
      const otherIps = this.allIps.filter((ip, index) => index !== this.currentIpIndex)
      console.log('validateIp:', currentIp, otherIps)
      if (otherIps.includes(currentIp)) {
        // this.error = 'IP 地址已存在，不能重复'
        this.$message.warning('IP 地址已存在，不能重复!')
        if (this.lastInputIndex !== -1) {
          setTimeout(() => {
            this.ipParts[this.lastInputIndex] = '' // 清空当前输入框的值
            this.$emit('input', this.ipParts.join('.')) // 通知父组件更新 IP 地址
          }, 1000)
        }
      } else {
        // this.error = ''
      }
    }
  }
}
</script>

<style scoped>
.ip-input {
  display: flex;
  align-items: center;
}

.ip-part {
  width: 60px;
  margin-right: 10px;
}

.error-message {
  color: red;
  margin-top: 10px;
}
</style>
