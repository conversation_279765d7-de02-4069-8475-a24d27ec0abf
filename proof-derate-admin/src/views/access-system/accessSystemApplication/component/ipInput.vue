<template>
  <div class="ip-input-group">
    <input type="text" :class="`ip-segment` + IpIndex" maxlength="3" inputmode="numeric">
    <span class="dot">.</span>
    <input type="text" :class="`ip-segment` + IpIndex" maxlength="3" inputmode="numeric">
    <span class="dot">.</span>
    <input type="text" :class="`ip-segment` + IpIndex" maxlength="3" inputmode="numeric">
    <span class="dot">.</span>
    <input :id="`last-segment` + IpIndex" type="text" :class="`ip-segment` + IpIndex" maxlength="3" inputmode="numeric">
  </div>
</template>
<script>
export default {
  props: {
    IpIndex: {
      type: Number,
      default: () => {
        return 0
      }
    },
    ipList: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      ip1: '',
      ip2: '',
      ip3: '',
      ip4: '',
      ipStr: ''
    }
  },
  watch: {
    ip1(value) {
      console.log('ip1', value)
    },
    ip2(value) {
      console.log('ip2', value)
    },
    ip3(value) {
      console.log('ip3', value)
    },
    ip4(value) {
      console.log('ip4', value)
    }
  },
  mounted() {
    this.loadIpInput()
    // this.setVal()
  },
  methods: {
    // 执行ip地址输入框对应逻辑
    loadIpInput() {
      // 自动跳转逻辑
      console.log(2233332222)
      document.querySelectorAll(`.ip-segment` + this.IpIndex).forEach((input, index, list) => {
        input.addEventListener('input', (e) => {
          const value = e.target.value
          // 过滤非数字字符
          if (index < 3) {
            e.target.value = value.replace(/[^\d]/g, '')
          }
          // else{
          //   e.target.value = value.replace(/[^\d|\*]/g, '')
          // }
          // 满3位跳转
          if (e.target.value.length >= 3 && index < 3) {
            list[index + 1].focus()
          }
        })

        // 键盘控制
        input.addEventListener('keydown', (e) => {
          if (e.key === 'ArrowLeft' && index > 0) {
            list[index - 1].focus()
          } else if (e.key === 'ArrowRight' || e.key === '.' || e.key === ':') {
            if (index < 3) list[index + 1].focus()
          } else if (e.key === 'Backspace' && e.target.value === '') {
            if (index > 0) list[index - 1].focus()
          }
        })

        // document.querySelectorAll(`.ip-segment` + this.IpIndex).forEach((input, index) => {
        console.log(222222, index)
        input.addEventListener('blur', (e) => {
          /* this.$emit('getIp')
          this.getVal()
          const hasMatchingIP = this.ipList.filter(item => item.ip === this.ipStr.slice(0, -1)).length
          if (hasMatchingIP > 1) {
            this.$message({
              message: '已有重复的ip地址，请重新输入',
              type: 'warning'
            })
          }
          console.log(`hasMatchingIP:`, hasMatchingIP) */
          if (index < 3) {
            const value = parseInt(e.target.value) || 0
            if (value < 0 || value > 255) {
              e.target.value = '' // 清空非法值
              e.target.classList.add('invalid')
            } else {
              e.target.value = value.toString() // 自动补0（可选）
              e.target.classList.remove('invalid')
            }
          }
        })
        // })

        document.getElementById(`last-segment` + this.IpIndex).addEventListener('blur', (e) => {
          const value = e.target.value
          if (value !== '*' && /^\d+$/.test(value)) {
            e.target.value = value // 显示为*
            if (value < 0 || value > 255) {
              e.target.value = '0'
            }
          } else {
            e.target.value = '*'
          }
        })
      })
    },
    // 获取当前填充的值
    getVal() {
      this.ipStr = ''
      document.querySelectorAll(`.ip-segment` + this.IpIndex).forEach((input, index) => {
        this.ipStr += input.value + '.'
      })
      // console.log('this.ipStr', this.ipStr, this.ipStr.slice(0, -1))
      // 判断是否重复
      return this.ipStr.slice(0, -1)
    },
    // 传入字符串填充输入框
    setVal(val) {
      const dataStr = val.split('.')
      console.log('dataStr', dataStr, this.ipList)
      document.querySelectorAll(`.ip-segment` + this.IpIndex).forEach((input, index) => {
        input.value = dataStr[index]
      })
    }
  }
}
</script>

<style>
.ip-input-group {
  display: flex;
  align-items: center;
  gap: 4px;
}
.ip-input-group input {
  width: 60px;
  padding: 8px;
  text-align: center;
  border: 1px solid #ccc;
  border-radius: 4px;
}
.dot {
  color: #666;
}
</style>
