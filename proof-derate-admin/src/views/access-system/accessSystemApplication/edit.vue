<template>
  <div id="access-system-application-edit">
    <el-card class="access-system-application-edit-boxcard">
      <div class="access-system-application-edit-title">
        <!-- <img src="~@/assets/images/file.png" alt /> -->
        <span>{{ tpyeText }}</span>
      </div>
      <el-form ref="from" :model="sendFrom" :rules="rules" class="sendFrom" label-width="120px" @submit.native.prevent>
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="业务系统全称" prop="name">
              <el-input v-model="sendFrom.name" maxlength="30" clearable placeholder="请输入业务系统全称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="系统管理方" prop="org_id">
              <el-select v-model="sendFrom.org_id" filterable placeholder="请选择" class="width-100">
                <el-option v-for="item in sysOptions" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <!-- <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="系统级别" prop="level">
              <el-select v-model="sendFrom.level" placeholder="请选择" class="width-100">
                <el-option v-for="item in levelOptions" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="客户端IP" prop="client_ip">
              <el-input v-model="sendFrom.client_ip" clearable placeholder="请输入客户端IP" />
            </el-form-item>
          </el-col>
        </el-row>-->
        <el-row v-if="$route.query.type !== 'edit'" :gutter="24">
          <el-col :span="10">
            <el-form-item label="app_key" prop="isCopyAppKey">
              <el-input v-model="sendFrom.app_key" :disabled="true" clearable placeholder="请输入" />
            </el-form-item>
          </el-col>
          <el-col :span="2">
            <el-button type="primary" @click="copyAppKey($event)">一键复制</el-button>
          </el-col>
          <el-col :span="10">
            <el-form-item label="密码" prop="isCopyPassword">
              <el-input v-model="passwordPlus" :disabled="true" clearable placeholder="请输入" />
            </el-form-item>
          </el-col>
          <el-col :span="2">
            <el-button type="primary" @click="copyPassword($event)">一键复制</el-button>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="有效期">
              <el-date-picker
                v-model="sendFrom.time"
                class="width-100"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="yyyy-MM-dd HH:mm"
                format="yyyy-MM-dd HH:mm"
              />
            </el-form-item>
          </el-col>
          <!-- <el-col :span="12">
            <el-form-item label="URL">
              <el-input v-model="sendFrom.url" clearable placeholder="请输入客户端IP" />
            </el-form-item>
          </el-col>-->
          <!-- <el-col :span="12">
            <el-form-item label="客户端IP" prop="client_ip">
              <el-input v-model="sendFrom.client_ip" clearable placeholder="请输入客户端IP" />
            </el-form-item>
          </el-col> -->
        </el-row>
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="备注">
              <el-input v-model="sendFrom.remarks" maxlength="200" clearable placeholder="请输入备注" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <div class="access-system-application-edit-table">
        <div class="access-system-application-edit-table-bar">
          <div class="access-system-application-edit-table-bar-item">
            <img src="~@/assets/images/file.png" alt>
            <span>IP地址配置</span><span class="describe">说明：若配置IP地址，则只允许以下IP地址访问系统，若不配置IP地址，则允许所有IP地址访问系统。</span>
          </div>
          <span>
            <!-- <img src="~@/assets/images/add2x.png" alt @click="addData" /> -->
            <el-button type="primary" @click="importIP()">导入IP</el-button>
            <el-button type="primary" @click="addIP()">添加IP</el-button>
          </span>
        </div>
        <el-divider />
        <el-table :data="sendFrom.client_ips" border style="width: 100%">
          <el-table-column label="序号" align="center" type="index" width="50" />
          <el-table-column label="客户端IP地址">
            <template slot-scope="scope">
              <!-- <el-form :ref="'ipNameForm'+[scope.$index]" :rules="ipRules" :model="scope.row" label-width="0px">
                <el-form-item prop="ip">
                  <el-input v-model="scope.row.ip" maxlength="200" clearable placeholder="请输入备注" />
                </el-form-item>
              </el-form> -->
              <!-- <div class="ip-input-group">
                <input type="text" class="ip-segment" maxlength="3" inputmode="numeric" />
                <span class="dot">.</span>
                <input type="text" class="ip-segment" maxlength="3" inputmode="numeric" />
                <span class="dot">.</span>
                <input type="text" class="ip-segment" maxlength="3" inputmode="numeric" />
                <span class="dot">.</span>
                <input type="text" class="ip-segment" maxlength="3" inputmode="numeric" id="last-segment" />
              </div> -->
              <!-- <ipInput :ref="'ipInput'+scope.$index" :ip-index="scope.$index" :ip-list="sendFrom.client_ips" /> -->
              <!-- <ip-input v-model="scope.row.ip" /> -->
              <ip-input
                v-model="scope.row.ip"
                :all-ips="allIps"
                :current-ip-index="scope.$index"
                @input="handleIpChange(scope.$index, $event)"
              />
            </template>
          </el-table-column>
          <el-table-column label="备注">
            <template slot-scope="scope">
              <el-input v-model="scope.row.remarks" maxlength="200" clearable placeholder="请输入备注" />
            </template>
          </el-table-column>
          <el-table-column prop="address" label="操作">
            <template slot-scope="scope">
              <el-button type="text" class="table-delete" @click="delectIpData(scope.$index)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div class="access-system-application-edit-table" style="margin-top: 10px">
        <div class="access-system-application-edit-table-bar">
          <div class="access-system-application-edit-table-bar-item">
            <img src="~@/assets/images/file.png" alt>
            <span>接口配置</span>
          </div>
          <span>
            <!-- <img src="~@/assets/images/add2x.png" alt @click="addData" /> -->
            <el-button type="primary" @click="showConfiguration()">批量配置范围</el-button>
            <el-button type="primary" @click="showInterface()">添加接口</el-button>
          </span>
        </div>
        <el-divider />
        <el-table :data="sendFrom.relation" border style="width: 100%">
          <el-table-column label="序号" align="center" type="index" width="50" />
          <!-- <el-table-column prop="api_id" label="接口地址">
            <template slot-scope="scope">
              <el-form :ref="'apiNameForm'+[scope.$index]" :rules="relationRules" :model="scope.row" label-width="0px">
                <el-form-item prop="api_name">
                  <el-input v-model="scope.row.api_name" clearable placeholder="请输入接口地址" @focus="dataChoseDialog(scope.$index)" />
                </el-form-item>
              </el-form>
            </template>
</el-table-column>
<el-table-column prop="name" label="数据范围">
  <template slot-scope="scope">
              <el-form :ref="'scopeForm'+[scope.$index]" :model="scope.row" label-width="0px">
                <el-form-item prop="scope" :rules="[{ required: true, message: '请选择数据范围',trigger: 'change'}]">
                  <el-select v-model="scope.row.scope" placeholder="请选择" class="width-100">
                    <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
                  </el-select>
                </el-form-item>
              </el-form>
            </template>
</el-table-column>-->
          <el-table-column prop="api_path" label="接口地址" />
          <!-- <el-table-column prop="scope" label="数据范围">
            <template slot-scope="scope">{{ options.find(i => i.value === scope.row.scope).label }}</template>
          </el-table-column> -->
          <el-table-column prop="group_name" label="接口分类" />
          <el-table-column prop="group_name" label="数据范围">
            <template v-if="$route.query.type === 'edit' || $route.query.type === 'add'" slot-scope="scope">
              <el-select v-model="scope.row.scope" filterable placeholder="请选择" class="width-100">
                <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </template>
            <template v-else slot-scope="scope">{{ options.find((i) => i.value === scope.row.scope).label }}</template>
          </el-table-column>
          <el-table-column prop="address" label="操作">
            <template slot-scope="scope">
              <el-button type="text" class="table-delete" @click="delectData(scope.$index)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>

    <!-- <el-dialog title="选择接口" :visible.sync="dialogVisible" width="30%">
      <div class="wrap">
        <el-table :data="selectValueList" border style="width: 100%" height="520px">
          <el-table-column prop="date" label="选择" width="180">
            <template slot-scope="scope">
              <el-checkbox v-model="scope.row.ischosed" @change="radioChange(scope.$index)" />
            </template>
          </el-table-column>
          <el-table-column prop="api_path" label="接口地址" />
        </el-table>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="getSelectValue">确 定</el-button>
      </span>
    </el-dialog>-->
    <el-dialog title="选择接口" :visible.sync="dialogVisible" width="90%">
      <tree-transfer
        ref="treeTransfer"
        :title="title"
        placeholder="请输入接口地址进行搜索"
        :from_data="fromData"
        :to_data="toData"
        :default-props="{ label: 'api_path' }"
        :mode="mode"
        height="400px"
        filter
        filterable
        open-all
        @add-btn="add"
        @remove-btn="remove"
      />
      <div slot="footer" class="dialog-footer" style="text-align: center">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="getInterface">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog title="批量配置数据范围" :visible.sync="configurationDialogVisible" width="30%" custom-class="configuration">
      <el-form ref="from" :model="rangeFrom" :rules="rules" class label-width="80px" @submit.native.prevent>
        <el-row :gutter="24">
          <el-col :span="24">
            <el-form-item label="数据范围" prop="configuration">
              <el-select v-model="rangeFrom.configuration" placeholder="请选择" class="width-100">
                <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer" style="text-align: center">
        <el-button type="primary" @click="getRange">确 定</el-button>
        <el-button @click="configurationDialogVisible = false">取 消</el-button>
      </div>
    </el-dialog>
    <el-dialog title="批量导入" :visible.sync="batchImportDialogVisible" width="40%">
      <div class="batchImportwrap">
        <div class="batchImportwrap-word">
          填写说明：从左往右输入的字段依次为：IP地址 备注。字段之间可用空格、逗号或制表符进行分隔。<br>(至少填写IP地址)<br>
          例如:<br>************,备注1<br>
          ************,备注2<br>
          ************,备注3<br>
        </div>
        <el-form ref="from" :model="ipFrom" :rules="rules" class label-width="120px" @submit.native.prevent>
          <el-row :gutter="24">
            <el-col :span="24">
              <el-form-item label="IP地址导入文本" prop="configuration">
                <el-input v-model="ipFrom.ipAddress" type="textarea" :rows="4" clearable placeholder="请输入IP地址" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div class="batchImportwrap-btn">
          <el-button type="primary" @click="getIPAddress">确 定</el-button>
          <el-button @click="batchImportDialogVisible = false">取 消</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getAutoKey,
  getAutoPassword,
  createAccessSys,
  getOrganizationList,
  getAccessSysData,
  editAccessSysData,
  getCheckedList,
  getDataScopeApp
} from '@/api/commonPack/platManege'
import { plusXing } from '@/utils/index.js'
import clip from '@/utils/clipboard'
import { sm2Encode } from '@/utils/sm-encrypt-utils'
import treeTransfer from 'el-tree-transfer'
// import ipInput from './component/ipInput.vue'
import IpInput from './component/IpInput2.vue'
export default {
  components: {
    treeTransfer,
    // ipInput,
    IpInput
  },
  data() {
    var validateRepeat = (rule, value, callback) => {
      let validateItem = []
      validateItem = this.sendFrom.relation.filter((i) => {
        return i.api_name == value
      })
      if (validateItem.length > 1) {
        callback(new Error('该接口已被选中，请选择其他接口地址'))
      } else {
        callback()
      }
    }
    // 允许末位带*的IPv4校验（示例：192.168.1.*）
    const validateIPv4Wildcard = (rule, value, callback) => {
      const textStr = /^((25[0-5]|2[0-4]\d|1\d{2}|[1-9]?\d)\.){3}(25[0-5]|2[0-4]\d|1\d{2}|[1-9]?\d|\*)$/
      if (textStr.test(value)) {
        callback()
      } else {
        callback(new Error('ip地址有误'))
      }
    }
    return {
      mode: 'transfer', // transfer addressList
      tpyeText: '新建应用系统',
      passwordPlus: '',
      sendFrom: {
        name: '',
        org_id: '',
        // level: '',
        client_ip: '',
        time: '',
        app_key: '',
        password: '',
        effector_begin: '',
        effector_end: '',
        // url: '',
        remarks: '',
        relation: [],
        isCopyAppKey: '',
        isCopyPassword: '',
        client_ips: []
      },
      rangeFrom: {
        configuration: 'ALL'
      },
      rules: {
        name: [{ required: true, message: '请输入业务系统全称', trigger: 'change' }],
        org_id: [{ required: true, message: '请输入系统管理方', trigger: 'change' }],
        // level: [{ required: true, message: '请输入系统级别', trigger: 'change' }],
        // client_ip: [{ required: true, message: '请输入客户端IP', trigger: 'change' }],
        isCopyAppKey: [{ required: true, message: '请复制appkey', trigger: 'blur' }],
        isCopyPassword: [{ required: true, message: '请复制密码', trigger: 'blur' }]
      },
      levelOptions: [
        { value: 'PROVINCE', label: '省级' },
        { value: 'CITY', label: '地市级' },
        { value: 'AREA', label: '区县级' }
      ],
      options: [],
      sysOptions: [],
      choseTableIndex: 0,
      dialogVisible: false,
      configurationDialogVisible: false, // 批量配置数据范围弹窗
      batchImportDialogVisible: false, // ip导入框
      configuration: '',
      selectValueList: [],

      relationRules: {
        api_name: [
          { required: true, message: '请输入接口地址' },
          { validator: validateRepeat, trigger: 'change' }
        ]
      },
      ipRules: {
        ip: [
          { required: true, message: '请输入接口地址' },
          { validator: validateIPv4Wildcard, trigger: 'change' }
        ]
      },
      isFirstSubmit: true, // 是否为第一次提交
      fromData: [
        {
          id: '1',
          pid: 0,
          api_path: '一级 1',
          children: [
            {
              id: '1-1',
              pid: '1',
              api_path: '二级 1-1',
              disabled: true,
              children: []
            },
            {
              id: '1-2',
              pid: '1',
              api_path: '二级 1-2',
              children: [
                {
                  id: '1-2-1',
                  pid: '1-2',
                  children: [],
                  api_path: '二级 1-2-1'
                },
                {
                  id: '1-2-2',
                  pid: '1-2',
                  children: [],
                  api_path: '二级 1-2-2'
                }
              ]
            }
          ]
        }
      ],
      toData: [
        // {
        //   api_path: "通用接口",
        //   group: "通用接口",
        //   id: "1",
        //   pid: 0,
        //   children: [
        //     {
        //       api_path: '/auth/api/v1/common/auth/get_token3',
        //       group: "通用接口",
        //       id: 'ff8080819034fda5019035076d8d0004',
        //       ischosed: false,
        //       last_modification_time: '2024-06-20 17:44:02',
        //       pid: '1',
        //       status: 'NORMAL',
        //     }
        //   ]
        // }
      ],
      title: ['未添加', '已添加'],
      ipFrom: {
        ipAddress: ''
      },
      allIps: []
    }
  }, // 注册
  computed: {
    isDeparmentAdd() {
      return this.$store.state.breadcrumbBtn.platManage.isAccessSystem
    }
  },
  watch: {
    isDeparmentAdd(value) {
      // 监听到有变化就重新获取数据
      if (value) {
        this.sumbit()
      }
    }
  },
  mounted() {
    this.getDataScope()
    this.getAutoKey()
    this.getAutoPassword()
    this.getOrganizationList()
    this.initText()
    this.$store.commit('breadcrumbBtn/SET_PLATMANAGE', { isAccessSystem: false })
  },

  methods: {
    handleIpChange(index, newIp) {
      // 更新 IP 地址
      this.$set(this.allIps, index, newIp)
      // 校验是否有重复
      const otherIps = this.allIps.filter((ip, i) => i !== index)
      /* if (otherIps.includes(newIp)) {
        this.$set(this.errors, index, 'IP 地址已存在，不能重复')
      } else {
        this.$set(this.errors, index, '')
      } */
    },
    // 获取数据范围枚举
    getDataScope() {
      getDataScopeApp().then((res) => {
        this.options = res.data
      })
    },
    // 切换模式 现有树形穿梭框模式transfer 和通讯录模式addressList
    changeMode() {
      if (this.mode == 'transfer') {
        this.mode = 'addressList'
      } else {
        this.mode = 'transfer'
      }
    },
    // 监听穿梭框组件添加
    add(fromData, toData, obj) {
      // 树形穿梭框模式transfer时，返回参数为左侧树移动后数据、右侧树移动后数据、移动的{keys,nodes,halfKeys,halfNodes}对象
      // 通讯录模式addressList时，返回参数为右侧收件人列表、右侧抄送人列表、右侧密送人列表
      console.log('fromData:', fromData)
      console.log('toData:', toData)
      console.log('obj:', obj)
    },
    // 监听穿梭框组件移除
    remove(fromData, toData, obj) {
      // 树形穿梭框模式transfer时，返回参数为左侧树移动后数据、右侧树移动后数据、移动的{keys,nodes,halfKeys,halfNodes}对象
      // 通讯录模式addressList时，返回参数为右侧收件人列表、右侧抄送人列表、右侧密送人列表
      console.log('fromData:', fromData)
      console.log('toData:', toData)
      console.log('obj:', obj)
    },
    showConfiguration() {
      this.configurationDialogVisible = true
    },
    showInterface() {
      this.dialogVisible = true
    },

    importIP() {
      this.batchImportDialogVisible = true
      this.ipFrom.ipAddress = ''
    },

    addIP() {
      // this.$refs.ipInput.getVal()
      this.sendFrom.client_ips.push({ ip: '', remarks: '' })
    },
    getIPAddress() {
      /* this.sendFrom.client_ips.forEach((e, index) => {
        e.ip = this.$refs['ipInput' + index].getVal()
      }) */
      const ipAddressList = this.splitTextByNewline(this.ipFrom.ipAddress)
      const ipList = []
      ipAddressList.forEach(e => {
        const item = this.validateCommaBeforeIPv4(e)
        if (!item.isValid) {
          this.$message({
            message: '不符合校验规则，请重新输入',
            type: 'warning'
          })
        } else {
          ipList.push(item.ip)
        }
      })
      // 检测导入的ip存在相同的ip
      const duplicates = [...new Set(ipList.map(([ip]) => ip).filter((ip, i, arr) => arr.indexOf(ip) !== i))]
      if (duplicates.length > 0) {
        this.$message({
          message: `存在重复的IP: ${duplicates.join(', ')}`,
          type: 'error',
          duration: 5000 // 显示时长（毫秒）
        })
        return
      }
      // 提取 client_ips 的所有 ip（用于快速查找）
      const clientIpSet = new Set(this.sendFrom.client_ips.map(item => item.ip))
      // 遍历 list，收集匹配的 IP（使用 Set 自动去重）
      const matchedIps = new Set()
      ipList.forEach(item => {
        const ip = item[0]
        if (clientIpSet.has(ip)) {
          matchedIps.add(ip)
        }
      })

      // 转为数组
      const result = Array.from(matchedIps)
      if (result.length > 0) {
        this.$message({
          message: `存在重复的IP: ${result.join(', ')}`,
          type: 'error',
          duration: 5000 // 显示时长（毫秒）
        })
      } else {
        this.batchImportDialogVisible = false
        ipList.forEach(e => {
          const item = {
            ip: e[0],
            remarks: e[1]
          }
          this.sendFrom.client_ips.push(item)
        })
        /* this.$message({
          message: '没有匹配的IP',
          type: 'warning',
          duration: 3000
        }); */
      }
      /* if (ipList.length === ipAddressList.length) {
        this.batchImportDialogVisible = false
        ipList.forEach(e => {
          const item = {
            ip: e[0],
            remarks: e[1]
          }
          this.sendFrom.client_ips.push(item)
        })
        setTimeout(() => {
          this.sendFrom.client_ips.forEach((e, index) => {
            this.$refs['ipInput' + index].setVal(e.ip)
          })
        }, 500)
      } */
    },
    delectIpData(index) {
      this.sendFrom.client_ips.splice(index, 1)
    },
    getInterface() {
      this.dialogVisible = false
      this.sendFrom.relation = []
      if (this.toData.length !== 0) {
        this.toData.forEach((e) => {
          if (e.children) {
            e.children.forEach((e1) => {
              this.sendFrom.relation.push({ api_id: e1.id, scope: this.rangeFrom.configuration, api_path: e1.api_path, group_name: e1.group })
            })
          }
        })
      } else {
        this.sendFrom.relation = []
      }
    },
    initText() {
      if (this.$route.query.type == 'edit') {
        this.tpyeText = '编辑应用系统'
        this.getCheckedList().then(() => {
          this.getAccessSysData()
        })
      } else {
        this.getCheckedList()
        this.tpyeText = '新增应用系统'
      }
    },
    copyPassword(event) {
      clip(this.sendFrom.password, event)
      this.sendFrom.isCopyPassword = 'isCopy'
    },
    copyAppKey(event) {
      clip(this.sendFrom.app_key, event)
      this.sendFrom.isCopyAppKey = 'isCopy'
    },
    addData() {
      if (this.sendFrom.relation.length < this.selectValueList.length) {
        this.sendFrom.relation.push({ api_id: '', scope: 'ALL', api_name: '' })
      } else {
        this.$message({
          message: '已达到创建上限',
          type: 'error'
        })
      }
    },
    delectData(index) {
      this.sendFrom.relation.splice(index, 1)
    },
    /* getIp() {
      console.log(this.sendFrom.client_ips, 999)
      this.sendFrom.client_ips.forEach((e, index) => {
        e.ip = this.$refs['ipInput' + index].getVal()
      })
    }, */
    sumbit() {
      this.$store.commit('breadcrumbBtn/SET_PLATMANAGE', { isAccessSystem: false })
      // if (this.isCopyAppKey && this.isCopyPassword) {
      /* this.sendFrom.client_ips.forEach((e, index) => {
        e.ip = this.$refs['ipInput' + index].getVal()
      }) */
      const hasInvalidIP = this.sendFrom.client_ips.some(item => !/^(\d+|\*)(\.(\d+|\*)){3}$/.test(item.ip))
      if (hasInvalidIP) {
        this.$message.warning('IP地址不符合校验规则，请重新输入!')
        return
      }
      this.$refs['from'].validate((valid) => {
        if (valid) {
          // if (this.dataValidate()) {

          // }

          // if (this.ipValidate()) {
          if (this.$route.query.type === 'edit') {
            this.editAccessSysData()
          } else {
            this.createAccessSys()
          }
        }
        // }
      })
      // } else {
      //   this.$message({
      //     message: '',
      //     type: 'error'
      //   })
      // }
    },
    createAccessSys() {
      // this.sendFrom.password = this.Rsa.encrypt(this.sendFrom.password)
      // this.sendFrom.password = sm2Encode(this.sendFrom.password, '04cc481446172ef4fc9cca98e522a4b7628736c6e6d1c1eac4c86e694425df8873c2a35f3670177f20c4dc0e022a46fb4d366379a8a83963739f92dbff05db8012')
      if (this.isFirstSubmit) {
        this.sendFrom.password = sm2Encode(this.sendFrom.password,this.$appConfig.VUE_APP_ENCRYPT_KEY || process.env.VUE_APP_ENCRYPT_KEY)
      }
      this.isFirstSubmit = false
      if (this.sendFrom.time !== null) {
        this.sendFrom.effector_begin = this.sendFrom.time[0] || ''
        this.sendFrom.effector_end = this.sendFrom.time[1] || ''
      } else {
        this.sendFrom.effector_begin = ''
        this.sendFrom.effector_end = ''
      }
      const copyForm = JSON.parse(JSON.stringify(this.sendFrom))
      delete copyForm.isCopyAppKey
      delete copyForm.isCopyPassword

      createAccessSys(copyForm).then((res) => {
        if (res.meta.code === '200') {
          this.$message({
            message: '创建成功',
            type: 'success'
          })
          this.$router.push({ name: 'accessSystemApplication' })
        }
      })
    },
    editAccessSysData() {
      // this.sendFrom.password = this.Rsa.encrypt(this.sendFrom.password)
      if (this.sendFrom.time !== null) {
        this.sendFrom.effector_begin = this.sendFrom.time[0] || ''
        this.sendFrom.effector_end = this.sendFrom.time[1] || ''
      } else {
        this.sendFrom.effector_begin = ''
        this.sendFrom.effector_end = ''
      }
      editAccessSysData(this.sendFrom).then((res) => {
        if (res.meta.code === '200') {
          this.$message({
            message: '编辑成功',
            type: 'success'
          })
          this.$router.push({ name: 'accessSystemApplication' })
        }
      })
    },
    dataChoseDialog(index) {
      this.choseTableIndex = index
      this.dialogVisible = true
    },
    getAutoKey() {
      getAutoKey().then((res) => {
        if (res.meta.code === '200' && res.data != null) {
          this.sendFrom.app_key = res.data
        }
      })
    },
    getAutoPassword() {
      getAutoPassword().then((res) => {
        if (res.meta.code === '200' && res.data != null) {
          this.sendFrom.password = res.data
          this.passwordPlus = plusXing(res.data, 0, 0, '*')
        }
      })
    },
    getOrganizationList() {
      getOrganizationList({}).then((res) => {
        if (res.meta.code === '200' && res.data != null) {
          this.sysOptions = res.data.map((i) => {
            return { value: i.id, label: i.name }
          })
        }
      })
    },
    getAccessSysData() {
      getAccessSysData(this.$route.query.id).then((res) => {
        if (res.data != null && res.meta.code === '200') {
          Object.assign(this.sendFrom, res.data)
          this.sendFrom.relation = this.sendFrom.relation === null ? [] : this.sendFrom.relation
          if (this.sendFrom.effector_begin !== '' && this.sendFrom.effector_end !== '') {
            this.sendFrom.time = [this.sendFrom.effector_begin, this.sendFrom.effector_end]
          }
          // console.log('this.selectValueList', this.selectValueList)
          /* this.sendFrom.relation.forEach(e0 => {
            const item = this.selectValueList.filter(i => {
              return i.id == e0.apiId
            })
            e0.api_name = item[0].api_path
            e0.api_id = e0.apiId
          }) */
          // 获取详情数据，设置已选中数组
          const treeData = []
          const fromGroupIndex = []
          const apiIdList = []
          this.sendFrom.relation.forEach((e, index) => {
            apiIdList.push(e.api_id)
            const data = {
              id: index + 1 + '',
              pid: index,
              group: e.group_name,
              api_path: e.group_name || '',
              children: []
            }
            if (fromGroupIndex.indexOf(e.group_name) === -1) {
              fromGroupIndex.push(e.group_name)
              treeData.push(data)
            }
          })
          this.sendFrom.relation.forEach((e) => {
            treeData.forEach((e1, index) => {
              e1.id = index + 1 + ''
              e1.pid = 0
              if (e.group_name === e1.group) {
                e.pid = e1.id
                e.group = e1.group
                e.id = e.api_id
                e1.children.push(e)
              }
            })
          })
          this.allIps = this.sendFrom.client_ips.map(item => item.ip)
          /* setTimeout(() => {
            this.sendFrom.client_ips.forEach((e, index) => {
              this.$refs['ipInput' + index].setVal(e.ip)
            })
          }, 500) */

          // 删除原数组中被选中的值
          this.removeMatchedIds(this.fromData, apiIdList)
          /* this.fromData.forEach((e) => {
            e.children.forEach((e1, index) => {
              // console.log('e1.id',e1.id)
              if (apiIdList.indexOf(e1.id) != -1) {
                console.log('e1', e1)
                e.children.splice(index, 1)
              }
            })
          }) */

          this.toData = treeData
        }
      })
    },
    removeMatchedIds(arr, apiIdList) {
      for (let i = arr.length - 1; i >= 0; i--) {
        const item = arr[i]
        const id = item.id

        // 如果当前对象的 id 在 apiIdList 中，直接删除
        if (apiIdList.includes(id)) {
          arr.splice(i, 1)
          continue // 跳过后续检查，因为当前对象已被删除
        }

        // 递归处理 children
        if (item.children && item.children.length > 0) {
          this.removeMatchedIds(item.children, apiIdList)

          // 可选：如果 children 为空，删除该对象（按需求调整）
          if (item.children.length === 0) {
            arr.splice(i, 1)
          }
        }
      }
    },
    getCheckedList() {
      return getCheckedList().then((res) => {
        if (res.data != null && res.meta.code === '200') {
          this.selectValueList = res.data.map((i) => {
            i.ischosed = false
            return i
          })
          // this.fromData = this.selectValueList
          this.fromData = []
          const treeData = []
          const fromGroupIndex = []
          res.data.forEach((e, index) => {
            const data = {
              id: index + 1 + '',
              pid: index,
              group: e.group,
              api_path: e.group || '',
              children: []
            }
            if (fromGroupIndex.indexOf(e.group) === -1) {
              fromGroupIndex.push(e.group)
              treeData.push(data)
            }
          })
          res.data.forEach((e) => {
            treeData.forEach((e1, index) => {
              e1.id = index + 1 + ''
              e1.pid = 0
              if (e.group === e1.group) {
                e.pid = e1.id
                e1.children.push(e)
              }
            })
          })

          // treeData[1].id = '1'
          // treeData[1].pid = 0
          // treeData[1].children.forEach((e, index) => {
          //   // e.id = index
          //   e.pid = '1'
          // })
          this.fromData = JSON.parse(JSON.stringify(treeData))
        }
      })
    },
    radioChange(data) {
      this.selectValueList.forEach((e, index) => {
        if (index != data) {
          e.ischosed = false
        }
      })
      this.setSelectValue()
    },
    setSelectValue() {
      this.sendFrom.relation[this.choseTableIndex].api_id = ''
      const selectIdList = this.sendFrom.relation.map((i) => {
        return i.api_id
      })
      //   this.sendFrom.relation.forEach(e => {})
      this.selectValueList.forEach((e) => {
        if (e.ischosed) {
          //   if (selectIdList.indexOf(e.id) == -1) {
          this.sendFrom.relation[this.choseTableIndex].api_id = e.id
          this.sendFrom.relation[this.choseTableIndex].api_name = e.api_path
          //   } else {
          //     this.$message({
          //       message: '请选择其他数据',
          //       type: 'error'
          //     })
          //   }
        }
        // e.ischosed = false
      })
    },
    //
    getRange() {
      this.configurationDialogVisible = false
      if (this.sendFrom.relation.length !== 0) {
        this.sendFrom.relation.forEach((e) => {
          e.scope = this.rangeFrom.configuration
        })
      }
    },
    getSelectValue() {
      this.sendFrom.relation[this.choseTableIndex].api_id = ''
      const selectIdList = this.sendFrom.relation.map((i) => {
        return i.api_id
      })
      //   this.sendFrom.relation.forEach(e => {})
      this.selectValueList.forEach((e) => {
        if (e.ischosed) {
          //   if (selectIdList.indexOf(e.id) == -1) {
          this.sendFrom.relation[this.choseTableIndex].api_id = e.id
          this.sendFrom.relation[this.choseTableIndex].api_name = e.api_path
          //   } else {
          //     this.$message({
          //       message: '请选择其他数据',
          //       type: 'error'
          //     })
          //   }
        }
        // e.ischosed = false
      })
      this.dialogVisible = false
    },
    dataValidate() {
      const apiNameList = []
      const scopeList = []
      this.sendFrom.relation.forEach((e, index) => {
        this.$refs['apiNameForm' + index].validate((valid, object) => {
          if (valid) {
            apiNameList.push(valid)
          }
        })
        this.$refs['scopeForm' + index].validate((valid, object) => {
          if (valid) {
            scopeList.push(valid)
          }
        })
      })
      if (apiNameList.length == this.sendFrom.relation.length && scopeList.length == this.sendFrom.relation.length) {
        return true
      } else {
        return false
      }
    },
    // IP地址配置输入框校验
    ipValidate() {
      const ipNameList = []
      const scopeList = []
      this.sendFrom.client_ips.forEach((e, index) => {
        this.$refs['ipNameForm' + index].validate((valid, object) => {
          if (valid) {
            ipNameList.push(valid)
          }
        })
      })
      if (apiNameList.length == this.sendFrom.client_ips.length) {
        return true
      } else {
        return false
      }
    },
    // 校验完整ip格式
    validateFullIP() {
      const segments = Array.from(document.querySelectorAll('.ip-segment')).map((input) => input.value.padStart(3, '0'))
      const fullIP = segments.join('.')
      const pattern = /^((25[0-5]|2[0-4]\d|1\d{2}|[1-9]?\d)\.){3}(25[0-5]|2[0-4]\d|1\d{2}|[1-9]?\d|\*)$/
      return pattern.test(fullIP)
    },

    splitTextByNewline(text) {
      return text.split(/\r?\n/).filter(item => item.trim() !== '') // ‌:ml-citation{ref="1,2" data="citationList"}
    },
    // 校验导入框中的ip地址
    validateCommaBeforeIPv4(str) {
      const ip = this.getBeforeComma(str)
      const IPv4_REGEX = /^((25[0-5]|2[0-4]\d|1\d{2}|[1-9]?\d)\.){3}(25[0-5]|2[0-4]\d|1\d{2}|[1-9]?\d|\*)$/
      return {
        ip,
        isValid: IPv4_REGEX.test(ip[0]),
        error: ip ? null : '空字符串无法校验'
      }
    },
    getBeforeComma(str) {
      return str.split(',')
    }
  }
}
</script>

<style lang="scss" scoped>
#access-system-application-edit {
  padding: 10px;

  .access-system-application-edit-title {
    font-size: 20px;
    font-weight: 600;
  }

  .sendFrom {
    margin-top: 30px;
  }

  .width-100 {
    width: 100%;
  }

  .access-system-application-edit-table {
    &-bar {
      display: flex;
      align-items: center;
      justify-content: space-between;

      //   margin-top: 51px;
      &-item {
        display: flex;
        align-items: flex-end;
      }

      img {
        width: 22px;
        height: 22px;
        margin-right: 5px;
      }

      span {
        color: #333333;
      }
    }
  }
}

.access-system-application-edit-table ::v-deep .el-divider--horizontal {
  margin: 9px 0 21px;
}

.table-delete {
  color: #ff2b2b;
}

.access-system-application-edit-table ::v-deep .el-form .is-error {
  margin-top: 15px;
  margin-bottom: 15px;
}

.access-system-application-edit-table ::v-deep .el-form-item {
  margin-top: 0px;
  margin-bottom: 0px;
}

.configuration .el-dialog__body {
  padding-top: 0;
}

#access-system-application-edit ::v-deep .wl-transfer .transfer-left,
.wl-transfer .transfer-right {
  // height: 94%;
}

#access-system-application-edit ::v-deep .wl-transfer .transfer-main {
  height: calc(100% - 70px);
}
.batchImportwrap-word {
  //height: 100px;
  background-color: rgba(252, 248, 227, 1);
  padding: 10px;
  margin-bottom: 10px;
}
.batchImportwrap-btn {
  text-align: center;
}
.describe {
  color: #c0c4cc !important;
  font-size: 12px;
  margin-left: 10px;
}
.ip-input-group {
  display: flex;
  align-items: center;
  gap: 4px;
}
.ip-segment {
  width: 60px;
  padding: 8px;
  text-align: center;
  border: 1px solid #ccc;
  border-radius: 4px;
}
.dot {
  color: #666;
}
</style>
