<template>
  <div id="accessSystemApplication-detail">
    <el-card class="access-system-application-boxcard">
      <div class="access-system-application-edit-table">
        <div class="access-system-application-edit-table-bar">
          <div class="access-system-application-edit-table-bar-item">
            <img src="~@/assets/images/file.png" alt>
            <span>{{ accessSysData.name }}</span>
          </div>
          <span />
        </div>
        <el-divider />
        <el-descriptions class="margin-top" :column="3" :size="size" border>
          <el-descriptions-item>
            <template slot="label">AppKey</template>
            {{ accessSysData.app_key }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">系统管理方</template>
            {{ accessSysData.org_name }}
          </el-descriptions-item>
          <!-- <el-descriptions-item>
            <template slot="label">系统级别</template>
            {{level}}
          </el-descriptions-item> -->
          <el-descriptions-item>
            <template slot="label">客户端IP</template>
            {{ accessSysData.client_ip }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">有效期</template>
            <span v-if="accessSysData.effector_begin!=null">{{ accessSysData.effector_begin }}至{{ accessSysData.effector_end }}</span>
          </el-descriptions-item>
          <!-- <el-descriptions-item>
            <template slot="label">URL</template>
            {{accessSysData.url}}
          </el-descriptions-item> -->
          <el-descriptions-item>
            <template slot="label">最后更新时间</template>
            {{ accessSysData.last_modification_time }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">状态</template>
            <span v-if="accessSysData.status==='NORMAL'">正常</span>
            <span v-else>禁用</span>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">备注</template>
            {{ accessSysData.remarks }}
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <div class="access-system-application-edit-table">
        <div class="access-system-application-edit-table-bar">
          <div class="access-system-application-edit-table-bar-item">
            <img src="~@/assets/images/file.png" alt>
            <span>IP地址配置</span><span class="describe">说明：若配置IP地址，则只允许以下IP地址访问系统，若不配置IP地址，则允许所以IP地址访问系统。</span>
          </div>
          <span>
            <!-- <img src="~@/assets/images/add2x.png" alt @click="addData" /> -->
          </span>
        </div>
        <el-divider />
        <el-table :data="accessSysData.client_ips" border style="width: 100%">
          <el-table-column type="index" width="50" label="序号" />
          <el-table-column prop="ip" label="客户端IP地址" />
          <el-table-column prop="remarks" label="备注" />
        </el-table>
      </div>

      <div class="access-system-application-edit-table">
        <div class="access-system-application-edit-table-bar">
          <div class="access-system-application-edit-table-bar-item">
            <img src="~@/assets/images/file.png" alt>
            <span>接口配置</span>
          </div>
          <span>
            <!-- <img src="~@/assets/images/add2x.png" alt @click="addData" /> -->
          </span>
        </div>
        <el-divider />
        <el-table :data="accessSysData.relation" border style="width: 100%">
          <el-table-column type="index" width="50" label="序号" />
          <el-table-column prop="api_path" label="接口地址" />
          <el-table-column prop="scope" label="数据范围" :formatter="scopeFilter" />
        </el-table>
      </div>
    </el-card>
    <el-dialog class="reset-password" title="成功提示" top="25%" :visible.sync="repwDialogVisible" width="30%">
      <div class="boby">
        <div class="status el-icon-success" />
        <div class="content">
          <p>密码重置成功</p>
          <p>
            初始化密码：{{ passwordPlus }}
            <el-button
              :type="isCopypassword?'info':'primary'"
              :disabled="isCopypassword"
              plain
              size="mini"
              @click="copyPassword($event)"
            >{{ isCopypassword?'已复制':'一键复制' }}</el-button>
          </p>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="repwDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmReview">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getAccessSysData, disableAccessSys, recoveryAccessSys, resetAccessSysPassword } from '@/api/commonPack/platManege'
import { plusXing } from '@/utils/index.js'
import clip from '@/utils/clipboard'
export default {
  data() {
    return {
      size: '',
      accessSysData: {
        relation: [],
        client_ips:[]
      },
      levelOptions: [
        { value: 'PROVINCE', label: '省级' },
        { value: 'CITY', label: '地市级' },
        { value: 'AREA', label: '区县级' }
      ],
      options: [
        { value: 'ALL', label: '全部' },
        { value: 'CURRENT', label: '本级' },
        { value: 'CURRENT_SUB', label: '本级以及下级' },
        { value: 'CURRENT_LAST', label: '本级以及子孙级' },
        { value: 'ORG', label: '本部门' },
        { value: 'ORG_SUB', label: '本部门以及下级部门' },
        { value: 'ORG_LAST', label: '本部门以及子孙部门' },
        { value: 'ONLY_SUB_ORG', label: '仅下级部门' },
        { value: 'ONLY_LAST_ORG', label: '仅子孙部门' },
        { value: 'CURRENT_SYSTEM', label: '本系统' }
      ],
      level: '',
      passwordPlus: '',
      repwDialogVisible: false,
      isCopypassword: false,
      resetCopyPassword: ''
    }
  },
  computed: {
    isAccessSystemDisable() {
      return this.$store.state.breadcrumbBtn.platManage.isAccessSystemDisable
    },
    isAccessSystemRestPassword() {
      return this.$store.state.breadcrumbBtn.platManage.isAccessSystemRestPassword
    }
  },
  watch: {
    isAccessSystemDisable(value) {
      // 监听到有变化就重新获取数据
      if (value) {
        this.handleChangeStatus()
      }
    },
    isAccessSystemRestPassword() {
      console.log('isAccessSystemRestPassword', this.accessSysData)
      this.handleChangePassword(this.accessSysData)
    }
  },

  mounted() {
    this.getAccessSysData()
  },
  methods: {
    getAccessSysData() {
      getAccessSysData(this.$route.query.id)
        .then(res => {
          if (res.meta.code === '200' && res.data != null) {
            this.accessSysData = res.data
            /* this.level = this.levelOptions.filter(i => {
              return i.value === this.accessSysData.level
            } )[0].label */
            if (this.accessSysData.status === 'NORMAL') {
              this.$store.commit('breadcrumbBtn/SET_PLATMANAGE', { isDisable: true })
            } else {
              this.$store.commit('breadcrumbBtn/SET_PLATMANAGE', { isDisable: false })
            }
            console.log('this.accessSysData', this.accessSysData)
          }
        })
        .catch(() => {})
    },
    handleChangeStatus() {
      const alert = {}
      if (this.accessSysData.status === 'NORMAL') {
        alert.content = '是否确认禁用此系统？'
        alert.status = 'DISABLE'
      } else {
        alert.content = '是否确认恢复此系统？'
        alert.status = 'NORMAL'
      }
      this.$alert(`<span>${alert.content}</span><br/><span>系统名称：${this.accessSysData.name}</span>`, '警告提示', {
        dangerouslyUseHTMLString: true,
        type: 'warning',
        showCancelButton: true,
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      })
        .then(() => {
          this.$store.commit('breadcrumbBtn/SET_PLATMANAGE', { isAccessSystemDisable: false })
          if (this.accessSysData.status === 'NORMAL') {
            this.disableAccessSys(this.accessSysData.id)
          } else {
            this.recoveryAccessSys(this.accessSysData.id)
          }
        })
        .catch(() => {
          this.$store.commit('breadcrumbBtn/SET_PLATMANAGE', { isAccessSystemDisable: false })
          /* this.$message({
          type: 'info',
          message: '已取消删除'
        }) */
        })
    },
    disableAccessSys(id) {
      disableAccessSys(id).then(res => {
        if (res.meta.code === '200') {
          this.$message({
            message: '修改成功',
            type: 'success'
          })
          this.getAccessSysData()
        }
      })
    },
    recoveryAccessSys(id) {
      recoveryAccessSys(id).then(res => {
        if (res.meta.code === '200') {
          this.$message({
            message: '修改成功',
            type: 'success'
          })
          this.getAccessSysData()
        }
      })
    },
    scopeFilter(val1, val2, val3) {
      let data = ''
      console.log(val1, val2, val3)
      if (val3 != null) {
        data = this.options.filter(i => {
          return i.value === val3
        })[0].label
        return data
      } else {
        return val3
      }
    },
    handleChangePassword(row) {
      const alert = {}
      alert.content = '是否重置密码？'
      this.$alert(`<span>${alert.content}</span>`, '警告提示', {
        dangerouslyUseHTMLString: true,
        type: 'warning',
        showCancelButton: true,
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      })
        .then(() => {
          this.isCopypassword = false
          this.resetAccessSysPassword(row.id)
        })
        .catch(() => {
          /* this.$message({
          type: 'info',
          message: '已取消删除'
        }) */
        })
    },
    resetAccessSysPassword(id) {
      resetAccessSysPassword(id).then(res => {
        if (res.data != null && res.meta.code === '200') {
          this.resetCopyPassword = res.data
          this.$message({
            message: '重置成功',
            type: 'success'
          })
          this.passwordPlus = plusXing(res.data, 0, 0, '*')
          this.repwDialogVisible = true
          // this.getAccessSyspage()
        }
      })
    },
    confirmReview() {
      this.repwDialogVisible = false
      console.log(this.isCopypassword)
      if (!this.isCopypassword) {
        this.$alert(`<span>请复制初始化密码</span><br/><span>点击“一键复制”按钮，复制初始化密码</span>`, '警告提示', {
          dangerouslyUseHTMLString: true,
          type: 'warning',
          confirmButtonText: '确定'
        }).then(() => {
          this.repwDialogVisible = true
        })
      }
    },
    copyPassword(event) {
      clip(this.resetCopyPassword, event)
      this.isCopypassword = true
    }
  }
}
</script>

<style lang="scss" scoped>
#accessSystemApplication-detail {
  padding: 10px;
}
.access-system-application-edit-table {
  margin-bottom: 20px;
  &-bar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    //   margin-top: 51px;
    &-item {
      display: flex;
      align-items: flex-end;
    }
    img {
      width: 22px;
      height: 22px;
      margin-right: 5px;
    }
    span {
      color: #333333;
    }
  }
}

.access-system-application-edit-table ::v-deep .el-divider--horizontal {
  margin: 9px 0 21px;
}
.describe {
  color: #c0c4cc !important;
  font-size: 12px;
  margin-left: 10px;
}
.reset-password {
  .boby {
    display: flex;
    align-items: center;
    .status {
      color: #67c23a;
      // transform: translateY(-50%);
      font-size: 24px !important;
    }
    .content {
      padding-left: 12px;
      padding-right: 12px;
    }
  }
  .el-dialog__body {
    padding: 0px 20px;
  }
}
</style>
