<template>
  <div id="access-system-application">
    <el-card class="access-system-application-boxcard">
      <!-- <div slot="header" class="clearfix">
        <el-button plain @click="queck()">快速注册</el-button>
        <el-button type="primary" plain @click="catalogueRegister()">目录注册</el-button>
      </div>-->
      <el-form :model="searchForm" class="demo-form-inline" label-width="120px" @submit.native.prevent>
        <el-row :gutter="24">
          <el-col :span="9">
            <el-form-item label="业务系统全称">
              <el-input v-model="searchForm.name" clearable placeholder="请输入业务系统全称" />
            </el-form-item>
          </el-col>
          <el-col :span="9">
            <el-form-item label="系统管理方">
              <el-input v-model="searchForm.org_name" clearable placeholder="请输入系统管理方" />
            </el-form-item>
          </el-col>
          <el-col :span="5" class="submitbtn">
            <el-button type="primary" plain native-type="submit" @click="onSubmit">查询</el-button>
            <el-button plain native-type="submit" @click="reset">重置</el-button>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="9">
            <el-form-item label="AppKey">
              <el-input v-model="searchForm.app_key" clearable placeholder="请输入AppKeys" />
            </el-form-item>
          </el-col>
          <el-col :span="9">
            <el-form-item label="系统服务状态">
              <!-- :indeterminate="isIndeterminate" -->
              <el-radio-group v-model="searchForm.status">
                <el-radio v-for="(item, idx) in statusOptions" :key="idx" :label="item.value">{{ item.label }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <custom-table
        ref="table"
        :is-card-type="false"
        :table-data="tableData"
        :table-header="tableHeader"
        @query="query"
        @refresh="query(1)"
      >
        <template #name="{ row }">
          <el-button
            v-if="isPermission(row.permission_codes, 'app:access_sys:view')"
            type="text"
            @click="getDetail(row, 'app:access_sys:view')"
          >{{ row.name }}</el-button>
          <span v-else>{{ row.name }}</span>
        </template>
        <template #status="{ row }">
          <span v-if="row.status === 'NORMAL'">启用</span>
          <span v-if="row.status === 'DISABLE'">禁用</span>
        </template>

        <template #handle="{ row }">
          <el-button
            v-permission="'app:access_sys:reset_password'"
            :disabled="!isPermission(row.permission_codes, 'app:access_sys:reset_password')"
            type="text"
            @click="resetPassword(row)"
          >重置密码</el-button>
          <el-button
            v-permission="'app:access_sys:edit'"
            :disabled="!isPermission(row.permission_codes, 'app:access_sys:edit')"
            type="text"
            @click="handleEdit(row)"
          >编辑</el-button>
          <el-button
            v-if="row.status === 'DISABLE'"
            v-permission="'app:access_sys:recovery'"
            :disabled="!isPermission(row.permission_codes, 'app:access_sys:recovery')"
            type="text"
            @click="handleChangeStatus(row)"
          >恢复</el-button>
          <el-button
            v-else
            v-permission="'app:access_sys:disable'"
            :disabled="!isPermission(row.permission_codes, 'app:access_sys:disable')"
            type="text"
            class="table-delete"
            @click="handleChangeStatus(row)"
          >禁用</el-button>
        </template>
      </custom-table>
    </el-card>
    <el-dialog class="reset-password" title="成功提示" top="25%" :visible.sync="repwDialogVisible" width="30%">
      <div class="boby">
        <div class="status el-icon-success" />
        <div class="content">
          <p>密码重置成功</p>
          <p>
            初始化密码：{{ passwordPlus }}
            <el-button
              :type="isCopypassword ? 'info' : 'primary'"
              :disabled="isCopypassword"
              plain
              size="mini"
              @click="copyPassword($event)"
            >{{ isCopypassword ? '已复制' : '一键复制' }}</el-button>
          </p>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="repwDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmReview">确定</el-button>
      </span>
    </el-dialog>

    <!-- <fileDialog ref="fileDialog" :whitelist="whitelist" :file-size-limit="fileSizeLimit"
      :file-dialog-title="fileDialogTitle" :multiple="multiple" :is-show-temple="true" :is-check-file-name="true"
      application-type="divisionCode" @dialogClose="dialogClose" @getFilelist="getFileList" @downTemple="exportTempleDownload" /> -->

    <!-- <el-dialog title="提示" :visible.sync="importErrorDialog" width="50%" :show-close="false" top="300px">
      <p>
        <i class="el-icon-warning-outline" style="color: chocolate;font-size: 18px;"></i>
        文件导入失败，{{importFailList.length}}条数据校验失败，失败原因如下：
      </p>
      <div class="el-dialog-div">
        <div class="error-content" v-for="(item, index) in importFailList" :key="index">
          {{item}}
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="importErrorDialog = false">确 定</el-button>
        <el-button @click="importErrorDialog = false">取 消</el-button>
      </span>
    </el-dialog> -->
    <import-error-dialog ref="importErrorDialog" :import-fail-list="importFailList" />
    <fileDialog
      ref="fileDialog"
      :accept="accept"
      :limit="limit"
      :file-size-limit="fileSizeLimit"
      :is-show-temple="isShowTemple"
      :import-result-data="importResultData"
      @getFilelist="getFilelistf"
      @downTemple="exportTempleDownload"
    />
  </div>
</template>

<script>
// import fileDialog from '@/components/fileDialog'
import fileDialog from '@/components/fileDialog/index2.vue'
import { getAccessSyspage, resetAccessSysPassword, disableAccessSys, recoveryAccessSys, accessApplicationSystemImport, accessSysDownTemplate } from '@/api/commonPack/platManege'
import CustomTable from '@/components/Element/Table'
import { plusXing, convertToCamelCase } from '@/utils/index.js'
import clip from '@/utils/clipboard'
import { isPermission } from '@/utils/index.js'
import { exportsDown } from '@/utils'
export default {
  components: {
    CustomTable,
    fileDialog,
    ImportErrorDialog: () => import('@/components/ImportErrorDialog')
  },
  data() {
    return {
      searchForm: {
        name: '',
        org_name: '',
        page_num: '1',
        page_size: '10',
        status: null,
        app_key: ''
      },
      statusOptions: [
        { value: null, label: '全部' },
        { value: 'NORMAL', label: '启用' },
        { value: 'DISABLE', label: '禁用' }
      ],
      checkAll: true,
      isIndeterminate: false,
      checkAll1: true,
      isIndeterminate1: false,
      repwDialogVisible: false,
      isCopypassword: false,
      resetCopyPassword: '',
      passwordPlus: '',
      tableData: {
        content: [], // 表格数据
        loading: false, // 控制表格加载效果
        total: 10, // 分页查询时的总数，去掉该属性，不显示分页条
        currentPage: 1, // 当前页码
        pageSize: 10,
        pageDirection: 'desc',
        isShowIndex: true,
        border: false
      },
      tableHeader: [

        {
          label: '业务系统全称',
          prop: 'name',
          minWidth: '200px',
          align: 'left',
          slot: 'name'
        },
        {
          label: 'AppKey',
          prop: 'app_key',
          minWidth: '140px',
          align: 'left'
        },
        {
          label: '系统管理方',
          prop: 'org_name',
          minWidth: '160px',
          align: 'left'
        },
        {
          label: '系统服务状态',
          prop: 'status',
          minWidth: '180px',
          align: 'left',
          slot: 'status'
        },
        {
          label: '最后更新时间',
          prop: 'last_modification_time',
          minWidth: '160px',
          align: 'left'
        },
        {
          label: '操作',
          prop: '',
          slot: 'handle',
          minWidth: '160px',
          align: 'left'
        }
      ],
      levelOptions: [
        { value: 'PROVINCE', label: '省级' },
        { value: 'CITY', label: '地市级' },
        { value: 'AREA', label: '区县级' }
      ],
      options: [
        { value: 'ALL', label: '全部' },
        { value: 'CURRENT', label: '本级' },
        { value: 'CURRENT_SUB', label: '本级以及下级' },
        { value: 'CURRENT_LAST', label: '本级以及子孙级' },
        { value: 'ORG', label: '本部门' },
        { value: 'ORG_SUB', label: '本部门以及下级部门' },
        { value: 'ORG_LAST', label: '本部门以及子孙部门' },
        { value: 'ONLY_SUB_ORG', label: '仅下级部门' },
        { value: 'ONLY_LAST_ORG', label: '仅子孙部门' },
        { value: 'CURRENT_SYSTEM', label: '本系统' }
      ],
      /* whitelist: ['xlsx', 'xls'],//白名单
      fileSizeLimit: 1,
      multiple: false, */
      fileDialogTitle: '导入文件',
      accept: '.xlsx,.xls',
      fileSizeLimit: 2,
      limit: 1,
      isShowTemple: true,
      importResultData: {},
      importErrorDialog: false, // 导入失败弹窗提示
      importFailList: []// 导入失败数据
    }
  },
  computed: {
    accessApplicationSystemImport() {
      return this.$store.state.breadcrumbBtn.platManage.accessApplicationSystemImport
    },
    accessApplicationSystemExport() {
      return this.$store.state.breadcrumbBtn.platManage.accessApplicationSystemExport
    }

  },
  watch: {
    accessApplicationSystemImport(value) { // 导入
      // 监听到有变化就重新获取数据
      if (value) {
        this.$refs.fileDialog.dialogVisible = true
        this.$store.commit('breadcrumbBtn/SET_PLATMANAGE', { accessApplicationSystemImport: false })
      }
    }
  },
  mounted() {
    this.getAccessSyspage()
  },

  methods: {
    isPermission,
    // 下载模板
    downTemple() {
      exportsDown(accessSysDownTemplate())
    },
    abc() {
      console.log(12332323232323)
    },
    getFilelistf(list) {
      if (this.$refs.fileDialog.fileList.length !== 0) {
        // importApiManage().then(res => {})
        const params = {
          config: this.$refs.fileDialog.config
        }
        const file = list[0].raw
        const fd = new FormData()
        fd.append('file', file)
        accessApplicationSystemImport(fd, params).then(res => {
          const { common } = convertToCamelCase(res.data)
          this.$refs.fileDialog.dialogVisible = false
          this.$refs.fileDialog.fileList = []
          this.importResultData = common
          this.$refs.fileDialog.dialogVisibleStatus = true

          // const message = data.split('/').join('<br/>')
          /* if (res.meta.code !== '200') { // 接口请求失败
            this.$message({
              type: 'error',
              dangerouslyUseHTMLString: true,
              message: res.meta.message
            })
          } else { // 接口请求成功
            if (!data) { // 完全请求成功
              this.$message({
                type: 'success',
                message: '全部信息导入成功'
              })
            } else {
              this.importFailList = res.data
              this.$refs.importErrorDialog.dialogVisible = true
            }
            this.$refs.fileDialog.loading = false
            this.$refs.fileDialog.dialogVisible = false
            this.getA ccessSyspage()
          }*/
        }).catch(() => {
          this.$refs.fileDialog.dialogVisible = false
          this.$refs.fileDialog.fileList = []
        })
      }
    },
    onSubmit() {
      // this.searchForm.page_num = '1'
      // this.searchForm.page_size = '10'
      this.searchForm.page_num = 1
      this.searchForm.page_size = 10
      this.tableData.currentPage = this.searchForm.page_num
      this.tableData.pageSize = this.searchForm.page_size
      this.getAccessSyspage()
    },
    reset() {
      this.searchForm = {
        name: '',
        org_name: '',
        page_num: '1',
        page_size: '10',
        status: null
      }
    },
    query() {
      this.searchForm.page_num = this.tableData.currentPage
      this.searchForm.page_size = this.tableData.pageSize
      this.getAccessSyspage()
    },
    getAccessSyspage() {
      getAccessSyspage(this.searchForm).then(res => {
        if (res.data != null && res.meta.code === '200') {
          if (res.data.content != null) {
            this.tableData.content = res.data.content
            this.tableData.total = Number(res.data.total_elements)
          } else {
            this.tableData.content = []
            this.tableData.total = 0
          }

          // this.tableData.currentPage = res.data.totalPages
        }
      })
    },
    getDetail(row, key) {
      if (isPermission(this.$route.meta.permission, key)) {
        this.$router.push({ name: 'accessSystemApplicationDetail', query: { id: row.id }})
      }
    },
    handleEdit(row) {
      this.$router.push({ name: 'accessSystemApplicationEdit', query: { type: 'edit', id: row.id }})
    },
    resetPassword(row) {
      this.handleChangePassword(row)
    },
    resetAccessSysPassword(id) {
      resetAccessSysPassword(id).then(res => {
        if (res.data != null && res.meta.code === '200') {
          this.resetCopyPassword = res.data
          this.passwordPlus = plusXing(res.data, 0, 0, '*')
          this.repwDialogVisible = true
          // this.$message({
          //   message: '重置成功',
          //   type: 'success'
          // })
          // this.getAccessSyspage()
        }
      })
    },
    handleChangePassword(row) {
      const alert = {}
      alert.content = '是否重置密码？'
      this.$alert(`<span>${alert.content}</span>`, '警告提示', {
        dangerouslyUseHTMLString: true,
        type: 'warning',
        showCancelButton: true,
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      })
        .then(() => {
          this.isCopypassword = false
          this.resetAccessSysPassword(row.id)
        })
        .catch(() => {
          /* this.$message({
          type: 'info',
          message: '已取消删除'
        }) */
        })
    },
    handleChangeStatus(row) {
      const alert = {}
      if (row.status === 'NORMAL') {
        alert.content = '是否确认禁用此业务系统？'
        alert.status = 'DISABLE'
      } else {
        alert.content = '是否确认恢复此业务系统？'
        alert.status = 'NORMAL'
      }
      this.$alert(`<span>${alert.content}</span><br/><span>业务系统名称：${row.name}</span>`, '警告提示', {
        dangerouslyUseHTMLString: true,
        type: 'warning',
        showCancelButton: true,
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      })
        .then(() => {
          if (row.status === 'NORMAL') {
            this.disableAccessSys(row.id)
          } else {
            this.recoveryAccessSys(row.id)
          }
        })
        .catch(() => {
          /* this.$message({
          type: 'info',
          message: '已取消删除'
        }) */
        })
    },
    disableAccessSys(id) {
      disableAccessSys(id).then(res => {
        if (res.meta.code === '200') {
          this.$message({
            message: '修改成功',
            type: 'success'
          })
          this.getAccessSyspage()
        }
      })
    },
    recoveryAccessSys(id) {
      recoveryAccessSys(id).then(res => {
        if (res.meta.code === '200') {
          this.$message({
            message: '修改成功',
            type: 'success'
          })
          this.getAccessSyspage()
        }
      })
    },
    confirmReview() {
      this.repwDialogVisible = false
      console.log(this.isCopypassword)
      if (!this.isCopypassword) {
        this.$alert(`<span>请复制初始化密码</span><br/><span>点击“一键复制”按钮，复制初始化密码</span>`, '警告提示', {
          dangerouslyUseHTMLString: true,
          type: 'warning',
          confirmButtonText: '确定'
        }).then(() => {
          this.repwDialogVisible = true
        })
      }
    },
    copyPassword(event) {
      clip(this.resetCopyPassword, event)
      this.isCopypassword = true
    },

    /**
     * 弹窗关闭
     */
    dialogClose() {
      this.$store.commit('breadcrumbBtn/SET_PLATMANAGE', {
        accessApplicationSystemImport: false
      })
      this.$refs.fileDialog.fileList = []
    },
    /**
     * 获取文件
     */

    getFileList(list) {
      if (this.$refs.fileDialog.fileList.length !== 0) {
        // importApiManage().then(res => {})
        const params = {
          config: this.$refs.fileDialog.config
        }
        const file = list[0].raw
        const fd = new FormData()
        fd.append('file', file)
        accessApplicationSystemImport(fd, params).then(res => {
          const { common } = convertToCamelCase(res.data)
          this.$refs.fileDialog.dialogVisible = false
          this.$refs.fileDialog.fileList = []
          this.importResultData = common
          this.$refs.fileDialog.dialogVisibleStatus = true

          // const message = data.split('/').join('<br/>')
          /* if (res.meta.code !== '200') { // 接口请求失败
            this.$message({
              type: 'error',
              dangerouslyUseHTMLString: true,
              message: res.meta.message
            })
          } else { // 接口请求成功
            if (!data) { // 完全请求成功
              this.$message({
                type: 'success',
                message: '全部信息导入成功'
              })
            } else {
              this.importFailList = res.data
              this.$refs.importErrorDialog.dialogVisible = true
            }
            this.$refs.fileDialog.loading = false
            this.$refs.fileDialog.dialogVisible = false
            this.getA ccessSyspage()
          }*/
        }).catch(() => {
          this.$refs.fileDialog.dialogVisible = false
          this.$refs.fileDialog.fileList = []
        })
      }
    },
    /**
     * 下载导入模板
     */
    exportTempleDownload() {
      exportsDown('/auth/webapi/v1/common/access_sys/download_template', {}, '接入应用系统导入模板.xlsx')
    }
  }
}
</script>

<style lang="scss" scoped>
#access-system-application {
  padding: 10px;
}

.checkbox-group {
  display: inline-block;
  margin-left: 30px;
}

.table-delete {
  color: #ff2b2b;
}

.copy-passsword {
  color: red;
}

.reset-password {
  .boby {
    display: flex;
    align-items: center;

    .status {
      color: #67c23a;
      // transform: translateY(-50%);
      font-size: 24px !important;
    }

    .content {
      padding-left: 12px;
      padding-right: 12px;
    }
  }

  .el-dialog__body {
    padding: 0px 20px;
  }
}

::v-deep .el-dialog__footer {
  text-align: center;
}

</style>
