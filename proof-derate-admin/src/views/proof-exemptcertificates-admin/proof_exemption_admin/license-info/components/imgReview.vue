<template>
  <section class="preview-box">
    <!-- <div class="pagination">第{{ currentNum }}页，共{{ imgDataList.length }}页</div> -->
    <div class="down">
      <!-- <el-button size="small" type="info" @click="down">下载</el-button> -->
    </div>
    <div class="img-box">
      <!-- <el-carousel indicator-position="outside" :autoplay="false" arrow="always" @change="carouselChange">
        <el-carousel-item v-for="(item,idx) in imgDataList" :key="idx">
          <img :src="'data:image/jpg;base64,'+item.file_data" alt="" srcset="" class="carousel-img" no-repeat>
        </el-carousel-item>
      
      </el-carousel>-->
      <!-- <iframe :src="pdfSrc"></iframe> -->
      <pdf ref="pdf" :src="pdfSrc" :page="key+1" class="pdf-preview" v-for="(i,key) in pageCount" :key="key"></pdf>
    </div>
  </section>
</template>
<script>
// import { dataURLtoDownload } from '@/utils'
import pdf from 'vue-pdf'
export default {
  components: {
    pdf
  },
  props: {
    data: {
      type: Array,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      imgDataList: [],
      currentNum: 1,
      pdfSrc: '',
      currentPage: 1, // pdf文件页码
      pageCount: 0 // pdf文件总页数
    }
  },
  watch: {
    data: {
      handler(val) {
        this.initData(val)
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    getNumPages() {
      let loadingTask = pdf.createLoadingTask(this.pdfSrc)
      loadingTask.promise
        .then(pdf => {
          this.pageCount = pdf._pdfInfo.numPages

          // console.log('this.pageCount', this.pageCount)
        })
        .catch(err => {
          console.error('pdf 加载失败', err)
        })
    },
    initData(val) {
      this.imgDataList = val
      // console.log('this.imgDataList', this.imgDataList)
      this.pdfSrc = 'data:application/pdf;base64,' + this.imgDataList[0].file_data
      if (val[0].file_data !== undefined) {
        this.getNumPages()
      }
    },
    carouselChange(index) {
      this.currentNum = index + 1
    },
    down() {
      if (this.imgDataList[this.currentNum - 1].fileData === undefined) return
      // dataURLtoDownload(this.imgDataList[this.currentNum - 1].fileData, this.imgDataList[this.currentNum - 1].fileData)
    }
  }
}
</script>
<style lang="scss" scoped>
.down {
  // background: #888;
  position: absolute;
  top: 30px;
  right: 50px;
}
.img-box {
  height: 620px;
  text-align: center;
  overflow: auto;
  // height: 100%;
  iframe {
    height: 500px;
    width: 50%;
  }
}
.pdf-preview ::v-deep canvas {
  //提高指定样式规则的应用优先权（优先级）
  //   width: 800px !important;
  // height: 800px !important;
  width: 60% !important;
  object-fit: contain;
}
</style>
