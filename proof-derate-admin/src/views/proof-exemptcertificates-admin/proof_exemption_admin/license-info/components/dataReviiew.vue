<template>
  <div class="data-review-container">
    <div class="datacontent">
      <div v-for="(item,idx) in list" :key="idx" class="row">
        <div v-for="(el,index) in item" :key="index" class="twoclounm clounm">
          <span class="name">{{ el.keyName }}</span>
          <span class="value" v-if="typeof(el.keyData)==='object'">
            <img :src="'data:image/jpg;base64,'+el.keyData.file_data" alt />
          </span>
          <span class="value" v-else>{{ el.keyData }}</span>
        </div>
      </div>
      <!-- <div class="row">
        <div class="clounm">
          <span class="name">证照名称</span>
          <span class="value">结婚证结婚证结婚证结婚证结婚证结婚证结婚证结婚证结婚证结婚证结婚证结婚证结婚证结婚证结婚证结婚证结婚证结婚证结婚证结婚证结婚证结婚证结婚证结婚证结婚证结婚证结婚证结婚证结婚证结婚证</span>
        </div>
      </div>-->
    </div>
  </div>
</template>

<script>
export default {
  props: {
    data: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      list: []
    }
  },
  watch: {
    data: {
      handler(val) {
        this.initData(val)
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    initData(val) {
      const array = []
      for (let i = 0; i < val.length - 1; i += 2) {
        const index = Math.floor(i / 2)
        const obj = [val[i], val[i + 1]]
        array[index] = obj
      }
      let index = 0
      array.forEach(i => {
        i.forEach(val => {
          index = index + 1
          // let obj = JSON.parse(val.keyData)
          // if (typeof obj == 'object' && obj) {
          //   console.log('obj',obj)
          // }
          if (val.keyValue === 'RX' && typeof val.keyData === 'string') {
            let data = JSON.parse(val.keyData)
            val.keyData = data
          }
        })
      })

      if (index < val.length) {
        array[array.length] = [val[val.length - 1], []]
      }
      this.list = array
      // this.list = [[{keyName:'证照名称', keyData:'中华人民共和国结婚证'}],[{keyName:'证照名称', keyData:'中华人民共和国结婚证'}]]
      // console.log('this.list', this.list)
    }
  }
}
</script>
<style lang="scss" scoped>
.data-review-container {
  display: flex;
  justify-content: center;
  .datacontent {
    border: 1px solid #ccc;
    font-size: 14px;
    color: #606266;
    width: 50%;
  }
  .row {
    display: flex;
    text-align: left;
    border-bottom: 1px solid #ccc;
    margin: 0;
    &:last-child {
      border-bottom: none;
    }
    .clounm {
      display: flex;
      // align-items: center;
      line-height: 1.5;
      .name {
        width: 100px;
        background: #ececec;
        display: inline-block;
        width: 130px;
        text-align: justify;
        text-justify: distribute-all-lines; // 这行必加，兼容ie浏览器
        text-align-last: justify;
        padding: 5px 10px;
      }
      .value {
        flex: 1;
        padding: 5px 0px 5px 10px;
        img {
          height: 100px;
        }
      }
    }
    .twoclounm {
      width: 50%;
    }
  }
  /* .table{
    width: 100%;
    table-layout: fixed;
    display: table;
    tbody{
      width: 100%;
      display: table-row-group;
      vertical-align: middle;
      border-color: inherit;
    }
    tr{
      display: table-row;
    }
  } */
}
</style>
