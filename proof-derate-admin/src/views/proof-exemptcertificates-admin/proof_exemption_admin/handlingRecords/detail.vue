<template>
  <div class="content-wrapper padding-10">
    <!-- <section class="content-header"> -->
    <!-- <p class="header-title">
        <i class="el-icon-arrow-left" @click="back()" />
        业务流水号：{{ objectData.biz_num }}
    </p>-->
    <CardTitle :title-name="'业务流水号：'+ objectData.biz_num " :ifback="true" @back="back()"></CardTitle>
    <el-card class="box-card card2">
      <p class="title">
        <span class="margin-left-10 info-wrap">
          <img :src="arrow" alt />
          <span class="info-title">用证信息</span>
        </span>
      </p>
      <el-descriptions class="descriptions" title :column="2" :size="size" border>
        <el-descriptions-item label-class-name="left-label" :label-style="{width:'140px'}" content-class-name="right-content">
          <template slot="label">调用结果</template>
          {{ objectData.call_result==='SUCCESS'?'成功':'失败，' }}
          <span v-if="objectData.call_result!=='SUCCESS'">
            <!-- <span>{</span> -->
            {{ objectData.message }}
            <!-- <span>}</span> -->
          </span>
        </el-descriptions-item>
        <el-descriptions-item label-class-name="left-label" :label-style="{width:'140px'}" content-class-name="right-content">
          <template slot="label">用证时间</template>
          {{ objectData.use_time }}
        </el-descriptions-item>
        <el-descriptions-item label-class-name="left-label" :label-style="{width:'140px'}" content-class-name="right-content">
          <template slot="label">用证事项</template>
          {{ objectData.item_name }}
        </el-descriptions-item>
        <el-descriptions-item label-class-name="left-label" :label-style="{width:'140px'}" content-class-name="right-content">
          <template slot="label">用证事项编码</template>
          {{ objectData.item_code }}
        </el-descriptions-item>
        <el-descriptions-item label-class-name="left-label" :label-style="{width:'140px'}" content-class-name="right-content">
          <template slot="label">证照名称</template>
          {{ objectData.license_name }}
        </el-descriptions-item>
        <el-descriptions-item label-class-name="left-label" :label-style="{width:'140px'}" content-class-name="right-content">
          <template slot="label">证照号码</template>
          {{ objectData.license_code }}
        </el-descriptions-item>
        <el-descriptions-item label-class-name="left-label" :label-style="{width:'140px'}" content-class-name="right-content">
          <template slot="label">持有人名称</template>
          {{ objectData.holder_name }}
        </el-descriptions-item>
        <el-descriptions-item label-class-name="left-label" :label-style="{width:'140px'}" content-class-name="right-content">
          <template slot="label">持有人身份证号码</template>
          {{ objectData.holder_id_code }}
        </el-descriptions-item>
      </el-descriptions>
      <p class="title">
        <span class="margin-left-10 info-wrap">
          <img :src="arrow" alt />
          <span class="info-title">操作人员信息</span>
        </span>
      </p>
      <!-- <el-descriptions-item>
            <template slot="label">用证事项</template>
            {{objectData.item_name}}
      </el-descriptions-item>-->
      <el-descriptions class="descriptions" title :column="2" border>
        <el-descriptions-item label-class-name="left-label" :label-style="{width:'140px'}" content-class-name="right-content">
          <template slot="label">操作人名称</template>
          {{ objectData.operation_user_name }}
        </el-descriptions-item>
        <el-descriptions-item label-class-name="left-label" :label-style="{width:'140px'}" content-class-name="right-content">
          <template slot="label">操作人账号</template>
          {{ objectData.operation_user_account }}
        </el-descriptions-item>
        <el-descriptions-item label-class-name="left-label" :label-style="{width:'140px'}" content-class-name="right-content">
          <template slot="label">操作人身份证号码</template>
          {{ objectData.operation_user_id_code }}
        </el-descriptions-item>
        <el-descriptions-item label-class-name="left-label" :label-style="{width:'140px'}" content-class-name="right-content">
          <template slot="label">操作人身份证件类型</template>
          {{ objectData.operation_user_id_type }}
        </el-descriptions-item>
        <el-descriptions-item label-class-name="left-label" :label-style="{width:'140px'}" content-class-name="right-content">
          <template slot="label">行政区划</template>
          {{ objectData.division }}
        </el-descriptions-item>
        <el-descriptions-item label-class-name="left-label" :label-style="{width:'140px'}" content-class-name="right-content">
          <template slot="label">行政区划代码</template>
          {{ objectData.division_code }}
        </el-descriptions-item>
        <el-descriptions-item label-class-name="left-label" :label-style="{width:'140px'}" content-class-name="right-content">
          <template slot="label">所属部门名称</template>
          {{ objectData.org_name }}
        </el-descriptions-item>
        <el-descriptions-item label-class-name="left-label" :label-style="{width:'140px'}" content-class-name="right-content">
          <template slot="label">客户端地址</template>
          {{ objectData.client_address }}
        </el-descriptions-item>
      </el-descriptions>
    </el-card>
    <!-- </section> -->
  </div>
</template>

<script>
import { getUseViewLogDetail } from '@/api/exemptCertificates'
import CardTitle from '@/components/CardTitle'
export default {
  components: {
    CardTitle
  },
  data() {
    return {
      size: '',
      objectData: {
        biz_num: '',
        call_result: '',
        use_time: '',
        item_name: '',
        item_code: '',
        license_name: '',
        license_code: '',
        holder_name: '',
        holder_id_code: '',
        operation_user_name: '',
        operation_user_account: '',
        item_name: '',
        operation_user_id_code: '',
        operation_user_id_type: '',
        division: '',
        division_code: '',
        org_name: '',
        client_address: '',
        message: ''
      },
      arrow: require('@/assets/proof-exemptcertificates-admin-images/arrow.png')
    }
  },

  mounted() {
    // console.log(this.$route.query.id)
    const param = {
      id: this.$route.query.id
    }
    if (this.$route.query.id) {
      this.getUseViewLogDetail(param)
    }
  },

  methods: {
    getUseViewLogDetail(param) {
      getUseViewLogDetail(param).then(res => {
        // console.log('res', res.data)
        if (res.meta.code === '200' && res.data != null) {
          this.objectData = Object.assign(this.objectData, res.data)
          console.log('this.objectData', this.objectData)
        }
      })
    },
    back() {
      this.$router.push({ name: 'handlingRecords' })
    }
  }
}
</script>

<style lang="scss" scoped>
.title {
  font-size: 20px;
  color: #125dcd;
  font-weight: 550;
  margin: 10px 0;
}
.header-title {
  font-size: 24px;
  font-weight: 600;
  i {
    cursor: pointer;
  }
}
</style>
<style>
.left-label {
  width: 180px;
}
.right-content {
  width: 300px;
}
.info-wrap {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}
.info-wrap img {
  width: 35px;
  height: 35px;
  margin-right: 10px;
}
.info-title {
  font-size: 20px;
  color: #333333;
}
.descriptions {
  margin-bottom: 20px;
}
</style>
