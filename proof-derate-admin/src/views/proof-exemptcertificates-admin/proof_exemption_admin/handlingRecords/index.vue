<template>
  <div class="content-wrapper padding-10">
    <!-- <section class="content-header">
      <h1>用证记录</h1>
    </section>-->
    <!-- <papeTitle :title-name="titleName" :is-has-back="false" /> -->
    <CardTitle :title-name="titleName">
      <template></template>
    </CardTitle>
    <!-- <section class="content"> -->
    <el-form ref="form" :model="checkform" label-width="200px" class="el-check-form" :rules="rules">
      <el-card class="box-card card1">
        <el-row>
          <el-col :xs="24" :sm="12" :md="12" :lg="24">
            <el-row align="middle">
              <el-col :xs="24" :sm="12" :md="12" :lg="10">
                <el-form-item label="用证事项">
                  <el-input v-model="checkform.item_name" clearable placeholder="请输入用证事项" />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="12" :lg="10">
                <el-form-item label="用证事项编码">
                  <el-input v-model="checkform.item_code	" clearable placeholder="请输入用证事项编码" />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="12" :lg="4">
                <div class="btn-wrap">
                  <el-button type="primary" @click="query(1)">查询</el-button>
                </div>
              </el-col>
            </el-row>
            <el-row align="middle">
              <el-col :xs="24" :sm="12" :md="12" :lg="10">
                <el-form-item label="证照名称">
                  <el-input v-model="checkform.license_name" clearable placeholder="请输入证照名称" />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="12" :lg="10">
                <el-form-item label="持有人名称">
                  <el-input v-model="checkform.holder_name" clearable placeholder="请输入持有人名称" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row align="middle">
              <el-col :xs="24" :sm="12" :md="12" :lg="10">
                <el-form-item label="调用结果">
                  <el-radio-group v-model="checkform.call_result">
                    <el-radio label="SUCCESS,FAILURE">全部</el-radio>
                    <el-radio label="SUCCESS">成功</el-radio>
                    <el-radio label="FAILURE">失败</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="12" :lg="10">
                <el-form-item label="用证时间">
                  <el-date-picker
                    v-model="value1"
                    type="datetimerange"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    style="width:100%"
                    value-format="yyyy-MM-dd HH:mm:ss "
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-col>
        </el-row>
        <div style="color: #888; padding:20px 10px 0" class="dashed-line">
          共
          <span class="text-red">{{ tableData.total }}</span> 条符合查询条件
        </div>
        <custom-table
          ref="table"
          :is-card-type="false"
          :table-data="tableData"
          :table-header="tableHeader"
          :stripe="false"
          :table-tools="tableTools"
          @query="query"
          @refresh="query(1)"
        >
          <template #operation="{ row }">
            <div>
              <el-button v-permission="'exempt:use:log:view'" type="text" @click="detail(row)">查看</el-button>
            </div>
          </template>
          <template #process_result="{ row }">
            <div>
              <p
                style="text-align: left;margin-left:10px;"
                :class="row.process_result==='FAILURE'?'redtext':''"
              >{{ row.process_result==='SUCCESS'?'通过':row.process_result==='TEMPORARY_STORAGE'?'暂存':row.process_result==='FAILURE'?'不通过':row.process_result }}</p>
              <div v-if="row.process_result==='TEMPORARY_STORAGE'||row.process_result==='SUCCESS'">
                <el-progress :percentage="row.biz_progress" color="#19A76D" />
              </div>
              <div v-if="row.process_result==='FAILURE'">
                <el-progress :percentage="100" color="#FF7266" />
              </div>
            </div>
          </template>
        </custom-table>
      </el-card>
    </el-form>
    <!-- </section> -->
  </div>
</template>

<script>
import CustomTable from '@/components/Element/Table'
import { getManagerPage } from '@/api/exemptCertificates'
import { getUseViewLogPage } from '@/api/exemptCertificates'
import papeTitle from '@/components/papeTitle'
import CardTitle from '@/components/CardTitle'
export default {
  name: 'Freearchives',
  components: {
    CustomTable,
    papeTitle,
    CardTitle
  },
  data() {
    return {
      checkform: {
        item_name: '',
        item_code: '',
        dentity_number_or_org_code: '',
        holder_name: '',
        call_result: 'SUCCESS,FAILURE',
        use_start_date: '',
        use_end_date: '',
        page_number: 1,
        page_size: 10
      },
      value1: '',
      checkList: ['SUCCESS', 'FAILURE'],
      rules: {
        code: [{ required: true, message: '请输入授权码', trigger: 'blur' }],
        item_name: [{ required: true, message: '请输入事件名称', trigger: 'blur' }]
      },
      tableData: {
        border: false,
        content: [], // 表格数据
        loading: false, // 控制表格加载效果
        total: 0, // 分页查询时的总数，去掉该属性，不显示分页条
        currentPage: 1, // 当前页码
        pageSize: 10,
        isShowIndex: true,
        pageDirection: 'desc',
        isShowSelection: false // 是否显示多选框，默认false
      },
      tableHeader: [
        { label: '业务办理号', prop: 'biz_num', align: 'left', minWidth: '160px' }, // 配置slot属性，可支持使用插槽
        {
          label: '用证事项',
          prop: 'item_name',
          align: 'left',
          minWidth: '200px'
        },
        { label: '用证事项编码', prop: 'item_code', align: 'left', minWidth: '200px' },
        // isHeaderslot 与 prop 不要同名
        {
          label: '证照名称',
          prop: 'license_name',
          width: '200px',
          align: 'left',
          fixed: 'right'
        },
        {
          label: '持有人名称',
          prop: 'holder_name',
          width: '220px',
          align: 'left',
          fixed: 'right'
        },
        {
          label: '调用结果',
          prop: 'call_result',
          width: '80px',
          align: 'left',
          fixed: 'right',
          formatter: (row, col, val) => {
            return row.call_result === 'FAILURE' ? '失败' : '成功'
          }
        },
        {
          label: '用证时间',
          prop: 'use_time',
          width: '180px',
          align: 'left',
          fixed: 'right'
        },
        {
          label: '操作',
          prop: 'operation',
          width: '120px',
          fixed: 'right',
          align: 'left',
          slot: 'operation'
        }
        //  { label: '归档', prop: 'process_result',slot: 'process_result', width: '120px', fixed: 'right'}
      ],
      tableTools: [],
      titleName: '用证记录'
    }
  },

  mounted() {
    this.query()
  },

  methods: {
    query() {
      //   this.getManagerPage()
      this.getUseViewLogPage()
    },
    detail(row) {
      this.$router.push({
        name: 'handlingRecordsDetail',
        query: {
          id: row.id
        }
      })
    },
    getUseViewLogPage() {
      this.checkform.page_number = this.tableData.currentPage
      this.checkform.page_size = this.tableData.pageSize
      // console.log('this.value1', this.value1)
      if (this.value1 != null) {
        this.checkform.use_start_date = this.value1[0]
        this.checkform.use_end_date = this.value1[1]
      } else {
        this.checkform.use_start_date = ''
        this.checkform.use_end_date = ''
      }

      getUseViewLogPage(this.checkform).then(res => {
        if (res.meta.code === '200' && res.data != null) {
          this.tableData.content = res.data.content
          this.tableData.total = Number(res.data.total_elements)
        }
      })
    },
    getManagerPage() {
      this.checkform.page_number = this.tableData.currentPage
      this.checkform.page_size = this.tableData.pageSize
      this.checkform.process_result = this.checkList.join(',')
      this.tableData.loading = true
      getManagerPage(this.checkform)
        .then(res => {
          this.tableData.loading = false
          if (res.meta.code === '200' && res.data != null) {
            this.tableData.content = res.data.content
            this.tableData.total = Number(res.data.total_elements)
          }
        })
        .catch(() => {
          this.tableData.loading = false
        })
    }
  }
}
</script>

<style scoped>
.card2 {
  margin-top: 20px;
}
.btn-wrap {
  text-align: center;
}
.box-card /deep/ .el-progress__text {
  color: #aaaaaa;
}
.redtext {
  color: #ff7266;
}
</style>
