import { createEnum } from '@/utils/index'

// 字体
export const FONT_FAMILY = createEnum({
  '微软雅黑': [0, '微软雅黑'],
  '仿宋': [1, '仿宋'],
  '黑体': [2, '黑体'],
  '楷体': [3, '楷体'],
  '宋体': [4, '宋体']
})

// 文档大小
export const PAPER_SIZE = createEnum({
  A3: [0, 'A3'],
  A4: [1, 'A4'],
  A5: [2, 'A5'],
  A6: [3, 'A6'],
  B3: [4, 'B3'],
  B4: [5, 'B4'],
  B5: [6, 'B5'],
  C4: [7, 'C4'],
  C5: [8, 'C5'],
  C6: [9, 'C6'],
  '自定义': [10, '自定义']
})

// 文档方向
export const PAPER_DIRECTION = createEnum({
  '横向': [0, '横向'],
  '竖向': [1, '竖向']
})

// 换行方式
export const WRAP_WAY = createEnum({
  '不换行': [0, '不换行'],
  '自动换行': [1, '自动换行'],
  '限定字数换行': [2, '限定字数换行'],
  '关键字换行': [3, '关键字换行']
})
