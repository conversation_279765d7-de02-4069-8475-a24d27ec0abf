<!--  -->
<template>
  <div class="detail-wrap">
    <el-card>
      <div class="page-title">
        <div class="left">
          <!-- <img src="@/assets/proof-exemptcertificates-admin-images/page-title-icon.png" alt srcset> -->
        </div>
        <div class="page-name">材料目录与水印的绑定关系</div>
      </div>
      <el-descriptions class="descriptions" :column="2" :label-style="LS">
        <el-descriptions-item label="材料目录名称">{{ waterData.catalog_name }}</el-descriptions-item>
        <el-descriptions-item label="操作账号">{{ waterData.user_name }}</el-descriptions-item>
        <el-descriptions-item label="材料目录编码">{{ waterData.catalog_code }}</el-descriptions-item>
        <el-descriptions-item label="绑定时间">{{ waterData.last_modification_time }}</el-descriptions-item>
        <el-descriptions-item label="材料目录状态">{{ waterData.catalog_status.desc }}</el-descriptions-item>
      </el-descriptions>
      <el-descriptions class="descriptions1" :column="2" :label-style="LS">
        <el-descriptions-item label="绑定水印样式名称">{{ waterData.watermark_name }}</el-descriptions-item>
      </el-descriptions>
      <div v-if="waterData.watermark_id" class="watermark-detail">
        <div class="watermark-label-title">水印样式预览</div>
        <detail-view type="info" source="dokumenList" :watermark-id="waterData.watermark_id" />
      </div>
    </el-card>
  </div>
</template>

<script>
import DetailView from './components/detail-view.vue'
import { getFillElement } from '@/api/materials-grade'
export default {
  name: 'DokumentDetail',
  components: {
    DetailView
  },
  data() {
    return {
      waterData: {
        user_name: '',
        catalog_code: '',
        last_modification_time: '',
        catalog_status: {
          desc: '',
          value: ''
        },
        watermark_name: ''
      },
      LS: {
        width: '140px',
        'text-align': 'right',
        display: 'inline-block',
        'margin-right': '60px'
      }
    }
  },

  computed: {},

  mounted() {
    this.getFillElement(this.$route.query.id)
  },

  methods: {
    getFillElement(id) {
      getFillElement(id)
        .then(res => {
          if (res.meta.code === '200' && res.data !== null) {
            this.waterData = res.data
          }
        })
        .catch(() => {})
    }
  }
}
</script>
<style lang='scss' scoped>
.detail-wrap {
  padding: 10px;
  .watermark-detail {
    margin-left: 190px;
  }
}
.page-title {
  display: flex;
  align-items: center;
  border-bottom: 1px solid #cccccc;
  padding: 2px 0 9px 0;
  .left {
    display: flex;
    align-items: center;
    img {
      width: 22px;
      height: 22px;
    }
  }
}
.descriptions {
  margin-top: 20px;
}
.watermark-label-title{
  font-size: 14px;
  color: #aaa;
  padding-left: 10px;
  margin-top: 10px;
}
</style>
