<template>
  <div class="content-wrapper dataSharingManagement padding-10">
    <!-- <section class="content-header title-algin-midle">
      <h1>水印应用管理</h1>
      <span align="right">
        <el-button type="primary" @click="editWatermarks">编辑默认水印</el-button>
        <el-button type="primary" @click="manageWatermarks">管理水印应用</el-button>
      </span>
    </section>-->
    <CardTitle :title-name="titleName">
      <template>
        <el-button type="primary" @click="editWatermarks">编辑默认水印</el-button>
        <el-button type="primary" @click="manageWatermarks">管理水印应用</el-button>
      </template>
    </CardTitle>
    <!-- <papeTitle :title-name="titleName" :is-has-back="false">
      <span align="right">
        <el-button type="primary" @click="editWatermarks">编辑默认水印</el-button>
        <el-button type="primary" v-permission="'exempt:water:manage:add'" @click="manageWatermarks">管理水印应用</el-button>
      </span>
    </papeTitle>-->
    <!-- <section class="content"> -->
    <el-card class="department-box">
      <el-form :model="searchForm" label-width="120px" @submit.native.prevent>
        <el-row :gutter="24">
          <el-col :span="10">
            <el-form-item label="证明目录">
              <el-input v-model="searchForm.proof_catalog_name" clearable placeholder="请输入证明目录" />
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="证明目录编码">
              <el-input v-model="searchForm.proof_catalog_code" clearable placeholder="请输入证明目录编码" />
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-button style="width: 120px;" type="primary" native-type="submit" @click="onSubmit">查询</el-button>
          </el-col>
        </el-row>
      </el-form>
      <div style="color: #888; padding:20px 10px 0" class="dashed-line">
        共
        <span class="text-red">{{ tableData.total }}</span> 条符合查询条件
      </div>
      <custom-table
        ref="table"
        :is-card-type="false"
        :table-data="tableData"
        :table-header="tableHeader"
        @query="query"
        @refresh="query(1)"
      >
        <template #name="{ row }">
          <div>{{ row.name }}</div>
        </template>
        <template #operation="{ row }">
          <div>
            <!-- <el-button type="text" @click="handleEdit(row)">编辑</el-button> -->
            <el-button type="text" v-permission="'exempt:water:manage:delete'" class="table-delete" @click="handleDelete(row)">删除</el-button>
          </div>
        </template>
      </custom-table>

      <el-dialog :visible.sync="deleteDialogVisible" width="30%" title="删除水印样式">
        <div style="padding-bottom:20px">是否确定删除水印样式？删除后该操作不可恢复，请谨慎操作。</div>
        <div class="dialog-footer">
          <el-button @click="deleteCancel">取 消</el-button>
          <el-button type="primary" @click="deleteSubmit">确 定</el-button>
        </div>
      </el-dialog>
      <el-dialog :visible.sync="manageWatermarksDialogVisible" width="40%" title="管理水印应用">
        <div style="padding-bottom:20px">
          <el-transfer
            ref="transfer"
            v-model="value"
            :filterable="true"
            :titles="['待选', '已选']"
            filter-placeholder="请输入证明目录名称"
            :data="generateData"
            class="transfer"
            @change="selectionChange"
          />
        </div>
        <div class="manageWatermarks-dialog-footer">
          <el-button @click="manageWatermarksDialogVisible=false">取 消</el-button>
          <el-button type="primary" @click="manageWatermarksSubmit">确 定</el-button>
        </div>
      </el-dialog>
    </el-card>
    <!-- </section> -->
  </div>
</template>

<script>
import { getWatermarkPage, deleteAssociationCatalog, getCatalogList, bindAssociationCatalog, associationCatalogPage, hasCatalogList } from '@/api/materials-grade'
import CustomTable from '@/components/Element/Table'
import { debounce } from '@/utils/index'
import papeTitle from '@/components/papeTitle'
import CardTitle from '@/components/CardTitle'
export default {
  components: {
    CustomTable,
    papeTitle,
    CardTitle
  },

  data() {
    return {
      searchForm: {
        proof_catalog_name: '',
        proof_catalog_code: '',
        page_direction: 'ASC',
        page_number: 1,
        page_size: 10
      },
      tableData: {
        content: [], // 表格数据
        loading: false, // 控制表格加载效果
        total: 10, // 分页查询时的总数，去掉该属性，不显示分页条
        currentPage: 1, // 当前页码
        pageSize: 10,
        pageDirection: 'desc',
        isShowIndex: true,
        border: false
      },
      tableHeader: [
        {
          label: '证明目录',
          prop: 'name',
          slot: 'name',
          minWidth: '200px',
          align: 'left'
        },
        {
          label: '证明目录编码',
          prop: 'code',
          minWidth: '240px',
          align: 'left'
        },
        {
          label: '操作时间',
          prop: 'last_modification_time',
          minWidth: '240px',
          align: 'left'
        },
        {
          label: '操作',
          prop: 'operation',
          slot: 'operation',
          // minWidth: '100px',
          align: 'left'
        }
      ],
      deleteDialogVisible: false,
      manageWatermarksDialogVisible: false,
      permissionList: ['auth:online_user:kick_out'],
      deleteId: '',
      generateData: [],
      value: [],
      valueCode: [],
      watermarkList: [],
      titleName: '水印应用管理'
    }
  },

  mounted() {
    //   this.tableHeader = getOperationPermissionList(this.$route.meta.permission, this.permissionList, this.tableHeader)
    this.getWatermarkList()
    this.getCatalogList()
    this.associationCatalogPage()
  },

  methods: {
    searchHandler: debounce(
      function () {
        this.searchForm.current = 1
        this.getWatermarkList()
      },
      200,
      true
    ),
    /**
     * 获取在线用户列表
     */
    getWatermarkList() {
      const data = {
        page_num: 1,
        page_size: 10
      }
      getWatermarkPage(data)
        .then(res => {
          if (res.data != null && res.meta.code === '200') {
            if (res.data.content != null) {
              // this.tableData.content = res.data.content
              // this.tableData.total = Number(res.data.total_elements)
              this.watermarkList = res.data.content
              console.log('this.watermarkList', this.watermarkList)
            } else {
              // this.tableData.content = []
              // this.tableData.total = 0
            }
          }
        })
        .catch(() => {})
    },
    onSubmit() {
      this.associationCatalogPage()
      if (this.searchForm.page_number === 1) this.tableData.currentPage = 1
    },
    query() {
      this.searchForm.page_number = this.tableData.currentPage
      this.searchForm.page_size = this.tableData.pageSize
      this.associationCatalogPage()
    },

    /**
     * 跳转详情
     */
    goDetail(row) {
      console.log('执行', row)
      this.$router.push({
        name: 'WatermarkManagementDetail',
        query: {
          id: row.id
        }
      })
    },

    /**
     * 编辑
     */
    handleEdit(row) {
      this.$router.push({
        name: 'WatermarkManagementEdit',
        query: {
          isEdit: true,
          id: row.id
        }
      })
    },

    /**
     * 删除
     */
    handleDelete(row) {
      /* if (isPermission(this.$route.meta.permission, key)) {
        this.$router.push({ name: 'RoleDetailWatch', query: { type: 'watch', id: row.id }})
      } */
      this.deleteDialogVisible = true
      this.deleteId = row.code
    },
    /**
     * 删除-取消
     */
    deleteCancel() {
      this.deleteDialogVisible = false
    },

    /**
     *
     */
    deleteSubmit() {
      const params = { proof_catalog_code_list: this.deleteId }
      deleteAssociationCatalog(params)
        .then(res => {
          if (Number(res.meta.code) === 200) {
            this.$message({
              message: '删除成功',
              type: 'success'
            })
            this.deleteDialogVisible = false
            this.associationCatalogPage()
          }
        })
        .catch(() => {})
    },
    /**
     * 新增水印
     */
    manageWatermarks() {
      this.manageWatermarksDialogVisible = true
      hasCatalogList().then(res => {
        console.log(res)
        if (res.data != null) {
          this.value = res.data.map(i => {
            if (i.id) return i.id
          })
          this.valueCode = res.data.map(i => {
            if (i.code) return i.code
          })
          // this.tableData.total = Number(res.data.totalElements)
          // this.watermarkList = res.data.content
          // console.log('this.watermarkList', this.watermarkList)
        } else {
          // this.tableData.content = []
          // this.tableData.total = 0
        }
      })
    },

    editWatermarks() {
      this.$router.push({
        name: 'dokumenWatermarkEdit',
        query: {
          isEdit: true,
          id: this.watermarkList[0].id
        }
      })
    },
    selectionChange(val) {
      console.log(val)
      this.valueCode = []
      this.generateData.forEach(e => {
        if (val.indexOf(e.key) !== -1) {
          this.valueCode.push(e.code)
        }
      })
      console.log('this.valueCode', this.valueCode)
    },
    getCatalogList() {
      getCatalogList().then(res => {
        if (res.meta.code === '200' && res.data !== null) {
          this.generateData = res.data.map(e => {
            return { label: e.name, key: e.id, code: e.code }
          })
        }
      })
    },
    bindAssociationCatalog(data) {
      bindAssociationCatalog(data).then(res => {
        if (res.meta.code === '200') {
          // console.log(res)
          this.value = []
          this.$message({
            message: '绑定成功',
            type: 'success'
          })
          this.associationCatalogPage()
        }
      })
    },
    manageWatermarksSubmit() {
      // this.watermarkList = []
      if (this.watermarkList.length !== 0) {
        const data = {
          proof_catalog_code_list: this.valueCode.join(','),
          waterMarkId: this.watermarkList[0].id
        }
        this.bindAssociationCatalog(data)
      } else {
        this.$message({
          message: '请点击编辑默认水印按钮，新建默认水印',
          type: 'warning'
        })
      }
      this.manageWatermarksDialogVisible = false
    },
    associationCatalogPage() {
      associationCatalogPage(this.searchForm).then(res => {
        console.log(res)
        if (res.data.content != null) {
          this.tableData.content = res.data.content
          this.tableData.total = Number(res.data.total_elements)
          // this.watermarkList = res.data.content
          console.log('this.value', this.value)
        } else {
          this.tableData.content = []
          this.tableData.total = 0
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.department-container {
  padding: 10px;
}
.table-delete {
  color: #ff2b2b;
}
.department-box {
  &-title {
    height: 36px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 0px 0 30px 20px;

    &-h3 {
      margin: 0;
      padding: 0;
    }

    &-btn {
      display: flex;
      align-items: center;

      .btn {
        width: 36px;
        height: 36px;
        background: #f7f7f7;
        border: 1px solid #d9d9d9;
        border-radius: 4px;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;

        &:first-child {
          margin-right: 12px;
        }

        &.active {
          background: #e0f0ff;
          border: 1px solid #99ceff;
        }
      }
    }
  }

  .success {
    color: #67c23a;
  }
}

::v-deep .el-message-box__wrapper {
  height: 180px;
}

.button_group {
  display: flex;
  justify-content: flex-end;
}
.manageWatermarks-dialog-footer {
  text-align: center;
}
.transfer ::v-deep .el-transfer-panel {
  width: 266px;
}
.dialog-footer {
  text-align: center;
}
</style>

