<template>
  <div class="content-wrapper">
    <div class="out_wrap">
      <el-card>
        <div class="table_wrap">
          <div class="top-tips">
            <div class="left-wrap">
              <span>该列表作用于水印样式编辑页的水印内容信息项。</span>
            </div>
            <el-button type="primary" class="save" @click="add">添加数据项</el-button>
          </div>
          <el-table :data="tableData" border style="width: 100%">
            <el-table-column label="序号" type="index" width="140" align="center" />
            <el-table-column label="水印内容信息项" align="center">
              <template slot-scope="scope">
                <el-form :ref="'contentInformation' + [scope.$index]" :model="scope.row" :rules="rules" label-width="0px">
                  <el-form-item prop="name">
                    <el-input v-model="scope.row.name" maxlength="50" />
                  </el-form-item>
                </el-form>
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center" width="220">
              <template slot-scope="scope">
                <span class="delete-wrap" @click="deleteData(scope.$index,scope.row.id)">删除</span>
              </template>
            </el-table-column>
          </el-table>
          <section class="button-btn">
            <el-button type="primary" class="save" @click="save">保存</el-button>
            <el-button @click="cancel">取消</el-button>
          </section>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script>
import { getWatermarkItemList, getWatermarkItemDelete, getWatermarkItemCreate } from '@/api/materials-grade'

export default {
  data() {
    var validateRepeat = (rule, value, callback) => {
      let validateItem = []
      validateItem = this.tableData.filter(i => {
        return i.name === value
      })
      if (validateItem.length > 1) {
        callback(new Error('列表存在重复值，不允许重复！'))
      } else {
        callback()
      }
    }
    return {
      tableData: [],
      tableLength: 0,
      rules: {
        name: [
          { required: true, message: '不可为空！', trigger: 'blur' },
          { max: 50, message: '不允许超过50个字！', trigger: 'blur' },
          { validator: validateRepeat, trigger: 'change' }
        ]
      }
    }
  },
  mounted() {
    this.getWatermarkItem()
  },
  methods: {
    getWatermarkItem() {
      getWatermarkItemList()
        .then(res => {
          this.tableData = res.data
          this.tableLength = res.data.length
        })
        .catch(() => {})
    },
    /**
     * 新增
     */
    add() {
      this.tableData.push({
        name: ''
      })
    },
    /**
     * 保存
     */
    save() {
      const list = []
      this.tableData.forEach((e, index) => {
        this.$refs['contentInformation' + index].validate((valid, object) => {
          if (valid) {
            list.push(valid)
          }
        })
      })
      if (list.length === this.tableData.length) {
        getWatermarkItemCreate(this.tableData)
          .then(res => {
            if (Number(res.meta.code) === 200) {
              this.$message({
                message: '保存成功',
                type: 'success'
              })
              this.$router.go(-1)
            }
          })
          .catch(() => {})
        return true
      } else {
        return false
      }
    },

    /**
     * 取消
     */
    cancel() {
      this.$router.go(-1)
    },

    /**
     * 删除数据
     */
    deleteData(index, id) {
      this.tableData.splice(index, 1)

      /* if (this.tableData.length > this.tableLength) {
        const params = [{ id }]
        getWatermarkItemDelete(params).then(res => {
          this.tableData.splice(index, 1)
          this.tableLength = 1
        }).catch(() => {})
      } else {

      } */
    }
  }
}
</script>

<style lang="scss" scoped>
.out_wrap {
  padding: 10px;
}

.table_wrap {
  margin: 20px;
}

.top-tips {
  margin: 0 10px 20px 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: rgb(153, 153, 153);
  font-size: 14px;
}

::v-deep .el-form .is-error {
  margin-top: 15px;
  margin-bottom: 15px;
}

::v-deep .el-form-item {
  margin-top: 0px;
  margin-bottom: 0px;
}

::v-deep .el-card__body {
  padding: 0 10px 10px 10px;
}

.button-btn {
  margin-top: 30px;
  display: flex;
  justify-content: center;

  .save {
    margin-right: 10px;
  }
}

.delete-wrap {
  color: #ff2b2b;
  cursor: pointer;
}
</style>
