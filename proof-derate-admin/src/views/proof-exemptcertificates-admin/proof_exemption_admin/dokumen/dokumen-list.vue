<template>
  <div class="materialApplication-management">
    <el-card class="box-card">
      <el-form ref="form" :model="form" label-width="130px" @submit.native.prevent>
        <el-row :gutter="24">
          <el-col :span="9">
            <el-form-item label="材料目录名称" prop="catalog_name">
              <el-input v-model="form.catalog_name" maxlength="50" placeholder="请输入材料目录名称" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="9">
            <el-form-item label="材料目录编码" prop="catalog_code">
              <el-input v-model="form.catalog_code" maxlength="50" placeholder="请输入材料目录编码" clearable />
            </el-form-item>
          </el-col>

          <el-col :span="4" style="text-align: right">
            <el-button type="primary" plain native-type="submit" @click="onSubmit">查询</el-button>
            <el-button native-type="submit" @click="resetForm">重置</el-button>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="9">
            <el-form-item label="绑定水印样式名称" prop="watermark_name">
              <el-input v-model="form.watermark_name" clearable maxlength="50" placeholder="请输入绑定水印样式名称" />
            </el-form-item>
          </el-col>
          <el-col :span="9">
            <el-form-item label="材料目录状态" prop="catalog_status_list">
              <el-radio-group v-model="form.catalog_status_list">
                <el-radio v-for="(item,idx) in statusOptions" :key="idx" :label="item.value">{{ item.label }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <custom-table
        ref="table"
        :is-card-type="false"
        :table-data="tableData"
        :table-header="tableHeader"
        :table-tools="tableTools"
        :show-input="false"
        @refresh="query(1)"
        @query="query"
      >
        <template #catalog_name="{ row }">
          <div>
            <el-button type="text" @click="getDetail(row,'application:fill_element:get')">{{ row.catalog_name }}</el-button>
          </div>
        </template>
        <template #default_catalog="{ row }">
          <el-button v-permission="'application:fill_element:update_in_list'" type="text" @click="handleReplace(row)">更换</el-button>
          <!-- <el-button type="text" type="text" @click="handleReplace(row)">更换</el-button> -->
          <!-- <span v-else style="color:#606266">——</span> v-permission="'auth:account:disable'" -->
        </template>
      </custom-table>
    </el-card>
  </div>
</template>

<script>
import CustomTable from '@/components/Element/Table/index.vue'
import { export_json_to_excel } from '@/vendor/Export2Excel'
// import { fillElement } from '@/api/application'
import { isPermission } from '@/utils/index.js'

export default {
  components: { CustomTable },
  data() {
    return {
      form: {
        catalog_code: '',
        catalog_name: '',
        catalog_status_list: '1,2',
        watermark_name: '',
        page_num: 1,
        page_size: '10'
      },
      statusOptions: [
        { value: '1,2', label: '全部' },
        { value: 1, label: '已发布' },
        { value: 2, label: '已停用' }
      ],

      tableTools: [],
      tableData: {
        content: [], // 表格数据
        loading: false, // 控制表格加载效果
        total: 0, // 分页查询时的总数，去掉该属性，不显示分页条
        currentPage: 1, // 当前页码
        pageSize: 10, // 每页显示的数量
        isShowSelection: false, // 是否显示多选框，默认false
        border: false,
        pageDirection: 'desc',
        isShowIndex: true
      },
      // 表头配置
      tableHeader: [
        {
          label: '材料目录名称',
          prop: 'catalog_name',
          slot: 'catalog_name',
          minWidth: '100px',
          align: 'left'
        },
        {
          label: '材料目录编码',
          prop: 'catalog_code',
          minWidth: '100px',
          align: 'left'
        },
        {
          label: '材料目录状态',
          prop: 'catalog_status',
          minWidth: '100px',
          align: 'left',
          formatter: (row, col, val) => {
            return val.desc
          }
        },
        {
          label: '绑定水印样式名称',
          prop: 'watermark_name',
          minWidth: '100px',
          align: 'left'
        },
        {
          label: '操作',
          prop: 'default_catalog',
          slot: 'default_catalog',
          minWidth: '90px',
          align: 'left'
        }
      ]
    }
  },

  created() {
    this.query()
  },

  methods: {
    onSubmit() {
      console.log(this.form)
      this.query()
    },
    /**
     * 重置
     */
    resetForm() {
      this.$refs['form'].resetFields()
    },
    // currentPage: 当前页码
    query() {
      this.tableData.loading = true
      this.form.page_num = this.tableData.currentPage
      this.form.page_size = this.tableData.pageSize
      const params = JSON.parse(JSON.stringify(this.form))
      params.catalog_status_list = params.catalog_status_list.length > 1 ? params.catalog_status_list.split(',') : [params.catalog_status_list]
      fillElement(params)
        .then(res => {
          this.tableData.loading = false
          if (res.data.content && res.data.content.length) {
            this.tableData.content = res.data.content
            this.tableData.total = Number(res.data.total_elements)
          } else {
            this.tableData.content = []
            this.tableData.total = 0
          }
        })
        .catch(() => {
          this.tableData.loading = false
        })
    },
    getDetail(row, key) {
      if (isPermission(this.$route.meta.permission, key)) {
        this.$router.push({ name: 'DokumenDetail', query: { id: row.id }})
      }
    },
    handleReplace(row) {
      this.$router.push({ name: 'DokumenEdit', query: { isSingle: '0', id: row.id }})
    }
  }
}
</script>
<style lang="scss" scoped>
.materialApplication-management {
  padding: 10px;
}
.el-select {
  width: 100%;
}
.dialog-title {
  line-height: 24px;
  font-size: 18px;
  color: #303133;
  font-weight: 700;
}
.dialog-footer {
  text-align: center;
}
.query_button {
  background-color: #e0f0ff;
  border-color: #99ceff;
  color: #2697ff;
}
.edit {
  color: #2697ff;
  cursor: pointer;
}
</style>
