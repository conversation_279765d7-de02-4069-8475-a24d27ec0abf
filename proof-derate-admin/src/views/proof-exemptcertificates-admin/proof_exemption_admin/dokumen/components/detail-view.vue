<template>
  <div class="detail_wrap">
    <el-card>
      <div v-show="type === 'operation'" class="header">
        <div class="icon">
          <img src="@/assets/commonPack_images/account-detail.png" alt="" srcset="">
        </div>
        <span class="tit">水印样式详情</span>
        <div style="position: absolute; right: 20px;padding-bottom: 6px;">
          <el-button type="primary" @click="editWatermark()">编辑水印样式</el-button>
          <el-button type="danger" @click="deleteWatermark()">删除水印样式</el-button>
        </div>
      </div>
      <div :class="['content_wrap',{'mt0':source === 'dokumenList'}]">
        <div class="left_wrap">
          <pdf ref="pdf" :src="watermarkPreview" class="pdf-preview" />
          <!-- <img :src="'data:application/pdf;base64,'+watermarkPreview" alt=""> -->
        </div>
        <div>
          <ul>
            <li><span class="title">样式名称</span><span class="desc">{{ watermarkDetail.name }}</span></li>
            <li><span class="title">应用说明</span><span class="desc">{{ watermarkDetail.description }}</span></li>
            <li><span class="title">默认水印内容</span><span class="desc">{{ watermarkDetail.content }}</span></li>
            <li><span class="title">换行方式</span><span class="desc">{{ watermarkDetail.wrap_way_ch }}</span></li>
            <li><span class="title">文字字体</span><span class="desc">{{ watermarkDetail.font_family_ch }}</span></li>
            <li><span class="title">文字颜色</span><span class="desc">{{ watermarkDetail.font_color }}</span><span
              class="color_wrap"
              :style="{background:watermarkDetail.font_color}"
            /></li>
            <li><span class="title">文字大小</span><span class="desc">{{ watermarkDetail.font_size }} px</span></li>
            <li><span class="title">旋转角度</span><span class="desc">{{ watermarkDetail.rotate }}°</span></li>
            <li><span class="title">透明度</span><span class="desc">{{ watermarkDetail.pellucidity }}%</span></li>
            <li><span class="title">水印坐标</span><span class="desc">( X ：{{ watermarkDetail.axis_x }} px，Y ：{{ watermarkDetail.axis_y }} px )</span></li>
            <li><span class="title">水印重复</span><span class="desc">( {{ watermarkDetail.repeat_row }}行，{{ watermarkDetail.repeat_column }}列 )</span><span style="margin-left: 10px;"><el-checkbox v-model="watermarkDetail.tiled" disabled>是否平铺</el-checkbox></span></span></li>
            <li><span class="title">X间距离</span><span class="desc">{{ watermarkDetail.spacing_x }} px</span></li>
            <li><span class="title">Y间距离</span><span class="desc">{{ watermarkDetail.spacing_y }} px</span></li>
            <li><span class="title">水印交错</span><span class="desc">{{ watermarkDetail.stagger }}%</span></li>
          </ul>
        </div>
      </div>

      <el-dialog :visible.sync="deleteDialogVisible" width="30%" title="删除水印样式" @close="handleClose">
        <div style="padding-bottom:20px">是否确定删除水印样式？删除后该操作不可恢复，请谨慎操作。</div>
        <div class="dialog-footer">
          <el-button @click="deleteCancel">取 消</el-button>
          <el-button type="primary" @click="deleteSubmit">确 定</el-button>
        </div>
      </el-dialog>
    </el-card>
  </div>
</template>

<script>
import pdf from 'vue-pdf'
import { getWatermarkDetail, getWatermarkPreview, getWatermarkDelete } from '@/api/materials-grade'
import { FONT_FAMILY, PAPER_SIZE, PAPER_DIRECTION, WRAP_WAY } from '../enum'

export default {
  components: {
    pdf
  },
  props: {
    type: { type: String, default: 'operation' }, // 'operation'展示编辑删除功能，'info'只展示水印内容
    source: { type: String, default: 'watermark' }, // 'watermark'来源水印，'dokumenList'加注件
    watermarkId: { type: String, default: '' } // 水印id
  },
  data() {
    return {
      watermarkDetail: {},
      watermarkPreview: '',
      deleteDialogVisible: false,
      id: ''
    }
  },
  computed: {
  },
  watch: {
    $route() {
      // this.getBreadcrumb()
      console.log('通用水印详情', this.type, this.source, this.watermarkId)
    }
  },
  mounted() {
    console.log('通用水印详情1', this.type, this.source, this.watermarkId)

    if (this.source === 'watermark') {
      this.id = this.$route.query.id
    } else if (this.source === 'dokumenList') {
      this.id = this.watermarkId
    }
    this.getWatermarkDetail()
  },
  methods: {
    getWatermarkDetail() {
      getWatermarkDetail(this.id).then((res) => {
        this.watermarkDetail = res.data

        this.watermarkDetail.wrap_way_ch = WRAP_WAY.getDescFromValue(this.watermarkDetail.wrap_way)
        this.watermarkDetail.font_family_ch = FONT_FAMILY.getDescFromValue(this.watermarkDetail.font_family)
        console.log(this.watermarkDetail)
      }).catch(() => { })
      getWatermarkPreview(this.id).then(res => {
        this.watermarkPreview = 'data:application/pdf;base64,' + res.data.base64_data
      }).catch(() => {})
    },
    deleteCancel() {
      this.deleteDialogVisible = false
    },

    deleteSubmit() {
      const params = [{ id: this.id }]
      getWatermarkDelete(params).then(res => {
        if (Number(res.meta.code) === 200) {
          this.$message({
            message: '删除成功',
            type: 'success'
          })
          this.deleteDialogVisible = false
          this.$router.go(-1)
        }
      }).catch(() => {})
    },

    handleClose() {
      this.$store.commit('breadcrumbBtn/SET_APPLICATION_MANAGEMENT', { showWatermarkDeleteDialog: false })
    },

    editWatermark() {
      this.$router.push({ name: 'WatermarkManagementEdit', query: { isEdit: true, id: this.id }})
    },

    deleteWatermark() {
      this.deleteDialogVisible = true
    }
  }

}
</script>

<style lang="scss" scoped>
.detail_wrap {
    padding: 10px;

    .dialog-footer {
        text-align: center;
    }

    .header {
        height: 50px;
        line-height: 50px;
        display: flex;
        align-items: center;
        border-bottom: 1px solid #cccccc;

        .icon {
            width: 22px;
            height: 22px;
            display: flex;
            align-items: center;

            img {
                width: 100%;
                height: 100%;
            }
        }
    }

    .content_wrap {
        margin-top: 40px;
        display: flex;
    }
    .mt0{
      margin-top: 0 !important;
    }

    .left_wrap {
        width: 500px;
        height: auto;
        border: 1px solid #e5e5e5;
        border-radius: 4px;
    }
    .pdf-preview ::v-deep canvas {
      //提高指定样式规则的应用优先权（优先级）
      //   width: 800px !important;
      height: auto !important;
      object-fit: contain;
    }

    ul,
    li {
        list-style: none;
    }

    ul {
        margin: 0;
    }

    li {
        margin-top: 22px;
        font-size: 14px;
        line-height: 18px;
        color: #888888;
        display: flex;
        align-items: center;
    }

    li:first-child {
        margin-top: 2px;
    }

    .title {
        width: 150px;
        text-align: right;
    }

    .desc {
        margin-left: 53px;
        color: #333333;
        max-width: 464px;
    }

    .color_wrap {
        width: 16px;
        height: 16px;
        margin-left: 10px;
    }

}
</style>
