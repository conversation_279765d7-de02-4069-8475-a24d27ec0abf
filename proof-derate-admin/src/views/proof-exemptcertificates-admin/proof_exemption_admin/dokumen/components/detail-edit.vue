<template>
  <div class="edit_wrap">
    <el-card>
      <div class="header">
        <div class="icon">
          <img :src="imgUrl" alt="" srcset="">
        </div>
        <span class="tit">{{ isEdit ? '编辑水印样式' : '新增水印样式' }}</span>
      </div>
      <div class="detail-cont">
        <el-form ref="form" :model="form" :rules="rules" :label-position="labelPosition" label-width="120px">
          <el-row :gutter="24">
            <el-col :span="24">
              <el-form-item label="样式名称" prop="style_name">
                <el-input v-model="form.style_name" maxlength="30" placeholder="请输入样式名称" />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="应用说明" prop="application_desc">
                <el-input v-model="form.application_desc" type="textarea" :rows="4" placeholder="请输入应用说明" />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <div style="display: flex;">
                <div>
                  <el-col :span="24">
                    <el-form-item label="默认水印内容" prop="default_content">
                      <el-col :span="16" style="padding-left: 0;">
                        <div class="tips_text">支持动态取值，请点击右侧多选框进入，或直接输入@{key}。</div>
                        <el-input
                          v-model="form.default_content"
                          type="textarea"
                          :rows="7"
                          placeholder="请输入水印内容"
                          maxlength="200"
                          show-word-limit
                        />
                      </el-col>
                      <el-col :span="8">
                        <div class="tips_text">动态取值内容</div>
                        <div class="dynamic_wrap">
                          <div
                            v-for="(item, index) in watermarkItemList"
                            :key="item.id"
                            :value="item.id"
                            :label="item.name"
                            class="content_item"
                            :tabindex="index + 1"
                            @click="handleContentItemClick(index)"
                          >{{ item.name }}</div>
                        </div>
                      </el-col>
                    </el-form-item>
                  </el-col>

                  <el-col :span="24">
                    <el-form-item label="换行方式">
                      <div style="display: flex;">
                        <el-select
                          v-model="form.wrap_style"
                          placeholder="请选择"
                          @change="handleWrapChange"
                        >
                          <el-option
                            v-for="item in wrapStyleOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                          />
                        </el-select>
                        <div v-if="form.wrap_style == 1" class="auto_wrap">
                          第一条水印在文档原点根据角度进行旋转后，如果文本超出
                          文档宽度，则超出宽度的部分自动换行。</div>
                        <div v-if="form.wrap_style == 2" class="auto_wrap">
                          <el-form-item prop="wrap_number">
                            <span>当水印文本超过</span>
                            <!-- <el-input
                              v-model="form.wrap_number"
                              prop="wrap_number"
                              style="width: 60px;margin: 0 10px;"
                            /> -->
                            <el-input-number v-model="form.wrap_number" style="width: 100px;margin: 0 10px;" :min="1" controls-position="right" />

                            <span>字时自动换行</span>
                          </el-form-item>
                        </div>
                        <div v-if="form.wrap_style == 3" class="auto_wrap">
                          <el-form-item prop="wrap_key_word">
                            <span>当水印文本识别</span>
                            <el-input
                              v-model="form.wrap_key_word"
                              style="width: 240px;margin: 0 10px;"
                              placeholder="请输入文本，多个用^隔开"
                              type="nm"
                            />
                            <span>字时自动换行</span>
                          </el-form-item>
                        </div>
                      </div>
                    </el-form-item>
                  </el-col>

                  <el-col :span="15">
                    <el-form-item label="文字字体" prop="font_style">
                      <el-select
                        v-model="form.font_style"
                        placeholder="请选择"
                        @change="handleFontStyleChange"
                      >
                        <el-option
                          v-for="item in fontStyleOptions"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                        />
                      </el-select>
                    </el-form-item>
                  </el-col>

                  <el-col :span="15">
                    <el-form-item label="文字颜色" prop="font_color">
                      <!-- <el-color-picker v-model="form.font_color" :predefine="fontColorOptions"
                                    class="theme-picker" popper-class="theme-picker-dropdown" /> -->
                      <div class="color_wrap">
                        <div
                          v-for="(item, index) in fontColorOptions"
                          :key="index"
                          :style="item == color ? { background: item, 'box-shadow': `0 0 3px 2px ${item}` } : { background: item }"
                          class="color_block"
                          @click.prevent="handleColorSelected(item)"
                        />
                        <el-input
                          v-model="form.font_color"
                          style="width: 140px; margin-left: 24px;"
                          minlength="7"
                          maxlength="7"
                          @input="changeValue"
                        >
                          <div
                            slot="suffix"
                            class="color_block"
                            :style="{ background: form.font_color, 'margin': '7px 10px 0 0' }"
                          />
                        </el-input>
                      </div>
                    </el-form-item>
                  </el-col>
                  <el-col :span="15">
                    <el-form-item label="文字大小" prop="font_size">
                      <el-input-number
                        v-model="form.font_size"
                        style="width: 103px;"
                        controls-position="right"
                        :min="6"
                        :max="72"
                      />
                    </el-form-item>
                  </el-col>

                  <el-col :span="15">
                    <el-form-item prop="tilt_angle">
                      <span slot="label">
                        <span class="span-box">
                          <span>旋转角度</span>
                          <el-tooltip
                            style="display:inline"
                            effect="light"
                            content="水印以左下角为原点进行逆时针旋转。"
                          >
                            <i style="padding-left: 10px;"><img
                              width="16"
                              style="vertical-align: middle;"
                              src="@/assets/commonPack_images/icon_question.png"
                              alt=""
                            ></i>
                          </el-tooltip>
                        </span>
                      </span>
                      <div style="display: flex;">
                        <el-slider
                          v-model="form.tilt_angle"
                          style="width: 258px;"
                          :format-tooltip="formatTiltAngleTooltip"
                          :min="0"
                          :max="360"
                        />
                        <el-select
                          v-model.number="form.tilt_angle"
                          style="margin-left: 20px; width: 150px;"
                          filterable
                          allow-create
                          clearable
                          placeholder="请选择或输入"
                          @change="handleAngelSelect"
                        >
                          <el-option
                            v-for="item in tiltAngleOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                          />
                        </el-select>
                        <!-- <div style="position: relative; margin-left: 20px;">
                                        <el-select style="width: 156px;" filterable v-model="form.tilt_angle"
                                            placeholder="请选择或输入" @blur="handleAngelSelect">
                                            <el-option v-for="item in tiltAngleOptions" :key="item.value"
                                                :label="item.label" :value="item.value">
                                            </el-option>
                                        </el-select>
                                        <el-input ref="tiltAngleRef" v-model.number="form.tilt_angle" placeholder="请选择或输入"
                                            @input="handleAngelInput" style="width:120px;position: absolute; left: 0;" />
                                    </div> -->
                      </div>
                    </el-form-item>
                  </el-col>

                  <el-col :span="15">
                    <el-form-item prop="transparency" label="透明度">
                      <div style="display: flex;">
                        <el-slider
                          v-model="form.transparency"
                          style="width: 258px;"
                          :format-tooltip="formatTransparencyTooltip"
                          :min="0"
                          :max="100"
                        />
                        <el-select
                          v-model.number="form.transparency"
                          style="margin-left: 20px; width: 150px;"
                          filterable
                          allow-create
                          clearable
                          :filter-method="TransparencyFilter"
                          placeholder="请选择或输入"
                          @change="handleTransparencySelect"
                        >
                          <el-option
                            v-for="item in transparencyOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                          />
                        </el-select>
                      </div>
                    </el-form-item>
                  </el-col>
                  <el-col :span="15">
                    <el-form-item style="margin-bottom: 0;">
                      <span class="tips_text">以左下角为文档坐标原点和水印原点。</span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="15">
                    <el-form-item required>
                      <span slot="label">
                        <span class="span-box">
                          <span>水印坐标</span>
                          <el-tooltip style="display:inline" effect="light" content="第一条水印的坐标位置。">
                            <i style="padding-left: 10px;"><img
                              width="16"
                              style="vertical-align: middle;"
                              src="@/assets/commonPack_images/icon_question.png"
                              alt=""
                            ></i>
                          </el-tooltip>
                        </span>
                      </span>
                      <div class="coordinate_wrap">
                        <el-form-item prop="x">
                          <span class="tips_text" style="margin-left: 6px;"> X：</span>
                          <el-input-number
                            v-model.number="form.x"
                            style="width: 103px;"
                            controls-position="right"
                            :min="-5000"
                            :max="5000"
                          />
                        </el-form-item>
                        <el-form-item prop="y" class="y_coordinate">
                          <span class="tips_text" style="margin-left: 42px;">Y：</span>
                          <el-input-number
                            v-model.number="form.y"
                            style="width: 103px;"
                            controls-position="right"
                            :min="-5000"
                            :max="5000"
                          />
                        </el-form-item>
                        <span class="tips_text" style="margin-left: 14px;">单位：像素</span>
                      </div>
                    </el-form-item>
                  </el-col>

                  <el-col :span="15">
                    <el-form-item required>
                      <span slot="label">
                        <span class="span-box">
                          <span>水印重复</span>
                          <el-tooltip style="display:inline" effect="light" content="以正方向重复排列水印。">
                            <i style="padding-left: 10px;"><img
                              width="16"
                              style="vertical-align: middle;"
                              src="@/assets/commonPack_images/icon_question.png"
                              alt=""
                            ></i>
                          </el-tooltip>
                        </span>
                      </span>
                      <div class="coordinate_wrap">

                        <el-form-item prop="row" class="">
                          <span class="tips_text"> 行：</span>
                          <el-input-number
                            v-model.number="form.row"
                            :disabled="checked"
                            style="width: 103px;"
                            controls-position="right"
                            :min="0"
                            :max="20"
                          />
                        </el-form-item>
                        <el-form-item prop="col" class="">
                          <span class="tips_text" style="margin-left: 40px;">列：</span>
                          <el-input-number
                            v-model.number="form.col"
                            :disabled="checked"
                            style="width: 103px;"
                            controls-position="right"
                            :min="0"
                            :max="20"
                          />
                        </el-form-item>
                        <el-checkbox
                          v-model="checked"
                          style="margin-left: 14px;"
                          @change="handleTileSelect"
                        >是否平铺</el-checkbox>
                      </div>
                    </el-form-item>
                  </el-col>

                  <el-col :span="15">
                    <el-form-item label="X间距" prop="xSpacing">
                      <el-input-number
                        v-model="form.xSpacing"
                        :disabled="checked ? false : (form.col > 1 ? false : true)"
                        style="width: 103px;"
                        controls-position="right"
                        :min="0"
                        :max="1000"
                      />
                      <span class="tips_text" style="margin-left: 8px;">像素</span>
                    </el-form-item>
                  </el-col>

                  <el-col :span="15">
                    <el-form-item label="Y间距" prop="ySpacing">
                      <el-input-number
                        v-model="form.ySpacing"
                        :disabled="checked ? false : (form.row > 1 ? false : true)"
                        style="width: 103px;"
                        controls-position="right"
                        :min="0"
                        :max="1000"
                      />
                      <span class="tips_text" style="margin-left: 8px;">像素</span>
                    </el-form-item>
                  </el-col>

                  <el-col :span="15">
                    <el-form-item prop="watermark_alternation">
                      <span slot="label">
                        <span class="span-box">
                          <span>水印交错</span>
                          <el-tooltip
                            style="display:inline"
                            effect="light"
                            content="百分比表示奇偶行间水印交错程度，0为左对齐，100%为水印完全错开。"
                          >
                            <i style="padding-left: 10px;"><img
                              width="16"
                              style="vertical-align: middle;"
                              src="@/assets/commonPack_images/icon_question.png"
                              alt=""
                            ></i>
                          </el-tooltip>
                        </span>
                      </span>
                      <div style="display: flex;">
                        <el-slider
                          v-model="form.watermark_alternation"
                          style="width: 258px;"
                          :format-tooltip="formatTooltip"
                        />
                        <el-input-number
                          v-model.number="form.watermark_alternation"
                          style="width: 103px;margin-left: 16px;"
                          controls-position="right"
                          :min="0"
                          :max="100"
                        />
                        <!-- <el-input
                          v-model.number="form.watermark_alternation"
                          style="width: 103px;margin-left: 16px;"
                          placeholder=""
                          min="0"
                          max="100"
                          type="number"
                        /> -->
                      </div>
                    </el-form-item>
                  </el-col>
                </div>

                <el-col :span="9">
                  <div style="margin-top: 5px;">
                    <div class="preview_title_wrap" @click="showSettings">
                      <div>预览：</div>
                      <div class="title_right">
                        <div style="display: flex; align-items: center; margin-right: 8px;">
                          <img
                            class="icon_image"
                            src="@/assets/commonPack_images/polygon_icon.png"
                            alt=""
                          >
                        </div>
                        <div>设定文档尺寸</div>
                      </div>
                    </div>
                    <!-- <div class="preview_wrap" :style="{height:`${previewHeight}px`}"> -->
                    <div class="preview_wrap">
                      <pdf ref="pdf" :src="watermarkPreview" class="pdf-preview" />

                    </div>
                  </div>
                </el-col>
              </div>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="16" :offset="8">
              <el-form-item>
                <el-button type="primary" @click="onPreview('form')">预览</el-button>
                <el-button type="primary" @click="onSubmit('form')">保存</el-button>
                <el-button @click="handleCancel">取消</el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div class="dialog_wrap">
          <el-dialog :visible.sync="editDialogVisible" width="30%" title="设定文件尺寸" @close="handleClose">
            <el-form
              ref="editForm"
              :model="settingsForm"
              :rules="settingsRules"
              label-width="80px"
              @submit.native.prevent
            >

              <el-form-item label="文件尺寸" prop="file_size">
                <el-select
                  v-model="settingsForm.file_size"
                  style="width: 456px;"
                  placeholder="请选择"
                  @change="handleFileSizeSelect"
                >
                  <el-option
                    v-for="item in fileSizeOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>

              <template v-if="settingsForm.file_size === 10">
                <el-form-item label="宽度" prop="file_width">
                  <el-input-number
                    v-model="settingsForm.file_width"
                    style="width: 230px; margin-right: 10px;"
                    controls-position="right"
                    :min="0"
                  />
                  <span class="tips_text">px</span>
                </el-form-item>

                <el-form-item label="高度" prop="file_height">
                  <el-input-number
                    v-model="settingsForm.file_height"
                    style="width: 230px; margin-right: 10px;"
                    controls-position="right"
                    :min="0"
                  />
                  <span class="tips_text">px</span>
                </el-form-item>

              </template>

              <el-form-item v-if="settingsForm.file_size !== 10" label="方向" prop="file_direction">
                <el-select
                  v-model="settingsForm.file_direction"
                  style="width: 456px;"
                  placeholder="请选择"
                  @change="handleDirectionSelect"
                >
                  <el-option
                    v-for="item in fileDirectionOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>

              <el-form-item style="padding-top: 0;">
                <div class="text_red">注意：此文件尺寸仅用于辅助水印样式配置，不影响归档文件尺寸。</div>
              </el-form-item>

            </el-form>
            <div class="dialog-footer">
              <el-button @click="editCancel('editForm')">取 消</el-button>
              <el-button type="primary" @click="edit('editForm')">确 定</el-button>
            </div>
          </el-dialog>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import pdf from 'vue-pdf'
import { getWatermarkItemList, getWatermarkCreate, getWatermarkDetail, getWatermarkEdit, getWatermarkPreview, getWatermarkNewPreview } from '@/api/materials-grade'
import { FONT_FAMILY, PAPER_SIZE, PAPER_DIRECTION, WRAP_WAY } from '../enum'

export default {
  components: {
    pdf
  },
  data() {
    var checkName = (rule, value, callback) => {
      console.log(value, (/[\u4e00-\u9fa5_a-zA-Z0-9]+$/g).test(value))
      if (!value) {
        return callback(new Error('请输入样式名称'))
      } else if (!(/[\u4e00-\u9fa5_a-zA-Z0-9]+$/g).test(value)) {
        return callback(new Error('请输入格式正确的样式名称'))
      } else {
        callback()
      }
    }
    var checkTiltAngle = (rule, value, callback) => {
      setTimeout(() => {
        if (!Number.isInteger(value)) {
          callback(new Error('请输入数字值'))
        } else {
          if (value < 0 || value > 360) {
            callback(new Error('必须大于0或者小于360'))
          } else {
            callback()
          }
        }
      }, 1000)
    }
    var checkRowCol = (rule, value, callback) => {
      if (!Number.isInteger(value)) {
        callback(new Error('请输入数字值'))
      } else {
        if (value < 0 || value > 20) {
          callback(new Error('必须大于0或者小于360'))
        } else {
          callback()
        }
      }
    }
    var checkColor = (rule, value, callback) => {
      setTimeout(() => {
        if (!value.startsWith('#')) {
          callback(new Error('必须以#号开头'))
        } else if (value.length !== 7) {
          callback(new Error('必须是7位长度'))
        } else if (!/^#?([a-fA-F0-9]{6})$/.test(value)) {
          callback(new Error('请输入正确的16进制颜色值，如#F2F2F2'))
        } else {
          callback()
        }
      }, 1000)
    }
    return {

      labelPosition: 'right',
      form: {
        style_name: '', // 样式名称
        application_desc: '', // 应用说明
        default_content: '', // 默认水印内容
        wrap_style: 2, // 换行方式
        wrap_number: '', // 换行字数
        wrap_key_word: '', // 换行关键字
        font_style: 0, // 文字字体
        font_color: '#555555', // 文字颜色
        font_size: '25', // 文字大小
        tilt_angle: 30, // 倾斜角度
        transparency: 80, // 透明度
        x: 0, // y坐标
        y: 0, // x坐标
        row: 1, // 行
        col: 1, // 列
        xSpacing: 100, // x间距
        ySpacing: 100, // y间距
        watermark_alternation: 0 // 水印交错
      },
      dynamic_prop: 'wrap_style', // 动态换行的动态验证prop
      color: '#F2F2F2', // 文字颜色
      checked: false, // 是否勾选平铺
      xDisable: false,
      yDisable: false,
      rules: {
        style_name: [
          { required: true, validator: checkName, trigger: 'blur' }
        ],
        application_desc: [
          { required: true, message: '请输入应用说明', trigger: 'blur' }
        ],
        wrap_style: [{
          required: true, message: '请选择换行方式', trigger: 'blur'
        }],
        tilt_angle: [
          { required: true, message: '请选择旋转角度', trigger: 'blur' },
          { validator: checkTiltAngle, trigger: 'blur' }
        ],
        font_color: [
          { required: true, message: '请输入文字颜色', trigger: 'blur' },
          { validator: checkColor, trigger: 'blur' }
        ],
        font_style: [
          { required: true, message: '请选择文字字体', trigger: 'blur' }
        ],
        font_size: [
          { required: true, message: '请选择文字大小', trigger: 'blur' }
        ],
        transparency: [
          { required: true, message: '请选择透明度', trigger: 'blur' }
        ],
        x: [
          { required: true, message: '请输入x坐标', trigger: 'blur' }
        ],
        y: [
          { required: true, message: '请输入y坐标', trigger: 'blur' }
        ],
        row: [{ required: true, message: '请输入行数', trigger: 'blur' }],
        col: [{ required: true, message: '请输入列数', trigger: 'blur' }],
        watermark_alternation: [
          { required: true, message: '请选择水印交错', trigger: 'blur' }
        ]
      },

      allRules: {
        style_name: [
          { required: true, validator: checkName, trigger: 'blur' }
        ],
        application_desc: [
          { required: true, message: '请输入应用说明', trigger: 'blur' }
        ],
        wrap_style: [{
          required: true, message: '请选择换行方式', trigger: 'blur'
        }],
        tilt_angle: [
          { required: true, message: '请选择旋转角度', trigger: 'blur' },
          { validator: checkTiltAngle, trigger: 'blur' }
        ],
        font_color: [
          { required: true, message: '请输入文字颜色', trigger: 'blur' },
          { validator: checkColor, trigger: 'blur' }
        ],
        font_style: [
          { required: true, message: '请选择文字字体', trigger: 'blur' }
        ],
        font_size: [
          { required: true, message: '请选择文字大小', trigger: 'blur' }
        ],
        transparency: [
          { required: true, message: '请选择透明度', trigger: 'blur' }
        ],
        x: [
          { required: true, message: '请输入x坐标', trigger: 'blur' }
        ],
        y: [
          { required: true, message: '请输入y坐标', trigger: 'blur' }
        ],
        xSpacing: [{
          required: true, message: '请输入x间距', trigger: 'blur'
        }],
        ySpacing: [{
          required: true, message: '请输入y间距', trigger: 'blur'
        }],
        watermark_alternation: [
          { required: true, message: '请选择水印交错', trigger: 'blur' }
        ]
      },

      // 文字换行选项
      wrapStyleOptions: WRAP_WAY.list,
      // 文字字体选项
      fontStyleOptions: FONT_FAMILY.list,

      // 文字颜色
      fontColorOptions: [
        '#F2F2F2', '#CCCCCC', '#989898', '#555555', '#333333', '#000000',
        '#1772E5', '#0747A6', '#1F9E73', '#FF6633'
      ],

      tiltAngleOptions: [
        { value: 0, label: '0°' },
        { value: 30, label: '30°' },
        { value: 45, label: '45°' },
        { value: 315, label: '315°' },
        { value: 330, label: '330°' }
      ],

      transparencyOptions: [
        { value: 10, label: '10%' },
        { value: 20, label: '20%' },
        { value: 30, label: '30%' },
        { value: 40, label: '40%' },
        { value: 50, label: '50%' },
        { value: 60, label: '60%' },
        { value: 70, label: '70%' },
        { value: 80, label: '80%' },
        { value: 90, label: '90%' },
        { value: 100, label: '100%' }
      ],

      editDialogVisible: false,
      settingsForm: {
        file_size: 0, // 文件尺寸
        file_width: '', // 文件宽度
        file_height: '554',
        file_direction: 1 // 文件方向
      },

      fileSizeOptions: PAPER_SIZE.list, // 文档大小

      fileDirectionOptions: PAPER_DIRECTION.list, // 文档方向

      settingsRules: {
        file_size: [
          { required: true, message: '请选择文件尺寸', trigger: 'change' }
        ],

        file_direction: [
          { required: true, message: '请选择方向', trigger: 'change' }
        ],

        file_width: [],

        file_height: []
      },
      watermarkItemList: [], // 配置项
      watermarkPreview: '' // 水印预览
    }
  },
  computed: {
    isEdit() {
      return this.$route.query.isEdit === true
    },
    imgUrl() {
      return this.isEdit ? require('@/assets/commonPack_images/account-edit.png') : require('@/assets/commonPack_images/account-add.png')
    }
  },
  watch: {
    checked: function(newVal) {
      const _this = this
      _this.$refs['form'].clearValidate()
      if (newVal) {
        _this.$nextTick(function() {
          _this.rules = Object.assign({}, _this.allRules)
        })
      } else {
        _this.$nextTick(function() {
          _this.rules.xSpacing = []
          _this.rules.ySpacing = []
        })
      }
    },

    'form.wrap_style': function(newVal) {
      const _this = this
      _this.$refs['form'].clearValidate()
      if (newVal === 2) {
        _this.dynamic_prop = 'wrap_number'
        _this.$nextTick(function() {
          _this.rules = Object.assign({}, { 'wrap_number': [{ required: true, message: '请输入换行字数', trigger: 'blur' }] }, _this.rules)
        })
      } else if (newVal === 3) {
        _this.$nextTick(function() {
          _this.rules = Object.assign({ 'wrap_key_word': [{ required: true, message: '请输入换行关键字', trigger: 'blur' }] }, _this.rules)
        })
      }
    }
  },
  mounted() {
    if (this.$route.query.isEdit && this.$route.query.id) {
      this.getWatermarkDetail(this.$route.query.id)
    }
    this.getWatermarkItem()
  },
  methods: {
    // 获取配置项
    getWatermarkItem() {
      getWatermarkItemList().then((res) => {
        this.watermarkItemList = res.data
      }).catch(() => {})
    },
    // 编辑状态获取详情
    getWatermarkDetail(id) {
      getWatermarkDetail(id).then((res) => {
        const watermarkEdit = {}; const { data } = res
        watermarkEdit.id = data.id
        watermarkEdit.style_name = data.name
        watermarkEdit.application_desc = data.description
        watermarkEdit.default_content = data.content
        watermarkEdit.wrap_style = Number(data.wrap_way)
        watermarkEdit.wrap_number = Number(data.wrap_way) === 2 ? data.wrap_value : ''
        watermarkEdit.wrap_key_word = Number(data.wrap_way) === 3 ? data.wrap_value : ''
        watermarkEdit.font_style = Number(data.font_family)
        watermarkEdit.font_color = data.font_color
        watermarkEdit.font_size = data.font_size
        watermarkEdit.tilt_angle = data.rotate
        watermarkEdit.transparency = data.pellucidity
        watermarkEdit.x = data.axis_x
        watermarkEdit.y = data.axis_y
        watermarkEdit.row = data.repeat_row
        watermarkEdit.col = data.repeat_column
        this.checked = data.tiled
        watermarkEdit.xSpacing = data.spacing_x
        watermarkEdit.ySpacing = data.spacing_y
        watermarkEdit.watermark_alternation = data.stagger
        this.settingsForm.file_size = Number(data.paper_size)
        this.settingsForm.file_direction = Number(data.paper_direction)
        this.settingsForm.file_width = data.paper_urx
        this.settingsForm.file_height = data.paper_ury
        this.form = watermarkEdit
        console.log(this.form, watermarkEdit, this.checked)
        getWatermarkPreview(data.id).then(result => {
          this.watermarkPreview = 'data:application/pdf;base64,' + result.data.base64_data
        }).catch(() => {})
      }).catch(() => { })
    },
    // 编辑>水印

    handleContentItemClick(index) {
      const selectValue = this.watermarkItemList[index].name
      let _value = this.form.default_content + '@{' + selectValue + '}'
      _value = _value.length > 200 ? _value.substring(0, 200) : _value
      // console.log(_value.length, _value)
      this.form.default_content = this.form.default_content.length < 200 ? _value : this.form.default_content
    },

    handleWrapChange() {

    },

    handleColorSelected(item) {
      this.color = item
      this.form.font_color = item
    },
    changeValue(value) {
      console.log('value', value)

      // this.form.font_color = /^#?[a-fA-F0-9]{6}$/.test(value) ? value : ""
    },

    handleFontStyleChange() {

    },

    handleAngelSelect(value) {
      if (!value) {
        this.form.tilt_angle = 0
      } else {
        const valueString = /^[0-9]*$/.test(parseInt(value)) ? String(parseInt(value)).replace('.', '') : ''
        this.form.tilt_angle = valueString ? Number(valueString) : 0
      }
    },

    handleAngelInput(value) {

    },
    TransparencyFilter(value) {
      if (!value) {
        this.form.transparency = 0
      } else {
        const valueString = /^[0-9]*$/.test(parseInt(value)) ? String(parseInt(value)).replace('.', '') : ''
        this.form.transparency = valueString ? Number(valueString) : 0
      }
    },
    handleTransparencySelect(value) {
      if (!value) {
        this.form.transparency = 0
      } else {
        const valueString = /^[0-9]*$/.test(parseInt(value)) ? String(parseInt(value)).replace('.', '') : ''
        this.form.transparency = valueString ? Number(valueString) : 0
      }
    },

    /**
         * 方向选择
         */
    handleDirectionSelect(value) {

    },

    /**
         * 水印重复是否平铺勾选事件
         * @param {*} value
         */
    handleTileSelect(value) {

    },

    formatTiltAngleTooltip(val) {
      return val + '°'
    },

    formatTooltip(val) {
      return val + '%'
    },

    formatTransparencyTooltip(val) {
      return val + '%'
    },
    // 生成参数
    setParams() {
      console.log(this.settingsForm)
      const params = {}; const _form = this.form
      params.name = _form.style_name
      params.description = _form.application_desc
      params.content = _form.default_content || '仅供预览使用的水印文字'
      params.wrap_way = _form.wrap_style
      params.wrap_value = _form.wrap_style === 2 ? _form.wrap_number : _form.wrap_style === 3 ? _form.wrap_key_word : ''
      params.font_family = _form.font_style
      params.font_color = _form.font_color
      params.font_size = _form.font_size
      params.rotate = _form.tilt_angle
      params.pellucidity = _form.transparency
      params.axis_x = _form.x
      params.axis_y = _form.y
      params.repeat_row = _form.row
      params.repeat_column = _form.col
      params.tiled = this.checked
      params.spacing_x = _form.xSpacing
      params.spacing_y = _form.ySpacing
      params.stagger = _form.watermark_alternation
      params.paper_size = this.settingsForm.file_size
      params.paper_direction = this.settingsForm.file_direction
      params.paper_urx = this.settingsForm.file_width
      params.paper_ury = this.settingsForm.file_height
      params.id = _form.id || ''
      return params
    },
    /**
     * 表单提交
     * @param {*} formName
     */
    onSubmit(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          const params = this.setParams()
          if (this.$route.query.isEdit && this.$route.query.id) {
            getWatermarkEdit(params).then(result => {
              if (Number(result.meta.code) === 200) {
                this.$message({
                  message: '保存成功',
                  type: 'success'
                })
                this.$router.go(-1)
              }
            }).catch(() => { })
            return
          }
          getWatermarkCreate(params).then(result => {
            if (Number(result.meta.code) === 200) {
              this.$message({
                message: '保存成功',
                type: 'success'
              })
              this.$router.go(-1)
            }
          }).catch(() => { })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    // 预览
    onPreview(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          const params = this.setParams()
          getWatermarkNewPreview(params).then(res => {
            this.watermarkPreview = 'data:application/pdf;base64,' + res.data.base64_data
          }).catch(() => {})
        }
      })
    },
    handleCancel() {
      this.$router.go(-1)
    },

    showSettings() {
      this.editDialogVisible = true
    },

    edit(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.editDialogVisible = false
          // this.previewHeight = this.settingsForm.file_height
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },

    editCancel() {
      this.editDialogVisible = false
      this.settingsForm.file_size = 0
      this.settingsForm.file_direction = 1
    },

    /**
         * 文件大小下拉选择事件
         */
    handleFileSizeSelect(val) {
      if (val === 10) {
        this.$refs['editForm'].clearValidate()
        this.settingsRules.file_width.push({
          required: true, message: '请输入宽度', trigger: 'change'
        })
        this.settingsRules.file_height.push({
          required: true, message: '请输入高度', trigger: 'change'
        })

        this.settingsForm.file_direction = 0
      } else {
        this.$refs['editForm'].clearValidate()
        this.settingsRules.file_width = []
        this.settingsRules.file_height = []
        this.settingsRules.file_direction.push({
          required: true, message: '请选择方向', trigger: 'change'
        })
      }
    },

    handleClose() {
      console.log('执行')
      this.$refs['editForm'].clearValidate()
      // this.$refs["editForm"].resetFields();
    }

  }
}
</script>

<style lang="scss" scoped>
.edit_wrap {
    padding: 10px;

    .dialog-footer {
        text-align: center;
    }

    .header {
        height: 40px;
        line-height: 40px;
        display: flex;
        align-items: center;
        border-bottom: 1px solid #cccccc;

        .icon {
            width: 22px;
            height: 22px;
            display: flex;
            align-items: center;

            img {
                width: 100%;
                height: 100%;
            }
        }

        .tit {
            padding-left: 5px;
        }
    }

    .detail-cont {
        margin-top: 30px;
    }

    // ::v-deep .el-form-item__label{
    //     font-size: 16px;
    // }
    ::v-deep .el-form-item__content {
        padding-left: 80px;
        // font-size: 16px;
    }

    .tips_text {
        // font-size: 16px;
        color: #999999;
    }

    .dynamic_wrap {
        text-align: center;
        width: 195px;
        height: 159px;
        border: 1px solid #e5e5e5;
        border-radius: 4px;
        overflow-y: scroll;
        cursor: pointer;
    }

    .content_item {
        width: 100%;
        padding: 2px 12px;
        height: 30px;
        line-height: 30px;
        color: #333333;
    }

    .content_item:focus {
        background: #f1f1f1;
    }

    .preview_title_wrap {
        width: 500px;
        margin-bottom: 10px;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .title_right {
        display: flex;
        align-items: center;
        color: #1772E5;
        cursor: pointer;
    }

    .icon_image {
        width: 24px;
        height: 24px;
    }

    .preview_wrap {
        width: 500px;
        height: auto;
        border: 1px solid #e5e5e5;
        border-radius: 4px;
    }

    .auto_wrap {
        margin-left: 10px;
        // line-height: 20px;
        color: #999999;

        ::v-deep .el-form-item__content {
            padding-left: 0;
        }
        ::v-deep .el-form-item__error {
            margin-left: 110px;
        }
    }

    .theme-picker .el-color-picker__trigger {
        height: 26px !important;
        width: 26px !important;
        padding: 2px;
    }

    .theme-picker-dropdown .el-color-dropdown__link-btn {
        display: none;
    }

    .tiltAngle_input {
        width: 150px;
        position: absolute;
    }

    ::v-deep .el-form-item__error {
        margin-left: 80px;
    }

    .dialog_wrap {
        ::v-deep .el-form-item__content {
            padding-left: 0;
        }

        .text_red {
            color: #f56c6c;
        }

        ::v-deep .el-form-item__error {
            margin-left: 0;
        }
    }

    .coordinate_wrap {
        display: flex;

        ::v-deep .el-form-item__content {
            padding-left: 0;
        }

        ::v-deep .el-form-item__error {
            margin-left: 30px;
        }

        .y_coordinate {
            ::v-deep .el-form-item__error {
                margin-left: 68px;
            }
        }

    }

    .color_wrap {
        display: flex;
        align-items: center;
        height: 40px;
    }

    .color_block {
        width: 26px;
        height: 26px;
        cursor: pointer;
    }
}
</style>
