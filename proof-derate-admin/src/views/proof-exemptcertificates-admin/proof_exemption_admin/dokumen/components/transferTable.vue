<template>
  <div class="watermarkList-transfer">
    <!-- <el-transfer v-model="value" filterable :titles="['全部材料目录','已选材料目录']" :data="transferdata" /> -->
    <!-- <transfer v-model="value" filterable :titles="['全部材料目录','已选材料目录']" :data="transferdata" />  -->
    <div class="transfer">
      <div class="transfer-left">
        <div class="transfer-left-title">
          <div class="transfer-left-title-content">
            <span>全部材料目录</span>
            <span>{{transferlistLength}}/{{transferlist.length}}项</span>
          </div>
          <div class="transfer-left-title-inp">
            <el-input placeholder="请输入内容" suffix-icon="el-icon-search" v-model="input1" @input="querySearch"></el-input>
          </div>
          <div class="transfer-left-title-table">
            <!-- <el-scrollbar style="max-height:180px;"> -->
            <el-table
              class="el-table"
              ref="leftTable"
              :data="transferlist"
              border
              style="width: 100%"
              height="220px"
              @selection-change="selectionChange"
            >
              <el-table-column type="selection" width="55"></el-table-column>
              <el-table-column prop="catalog_name" width="180" label="材料目录名称"></el-table-column>
              <el-table-column prop="catalog_code" label="材料目录编码"></el-table-column>
              <el-table-column prop="watermark_name" label="绑定水印样式名称"></el-table-column>
            </el-table>
            <!-- </el-scrollbar> -->
          </div>
        </div>
      </div>
      <div class="transfer-midle">
        <!-- <div class="transfer-midle-leftbtn" :class="transferlistLength>0?'active':''" @click="getSelect()">
                <i class="el-icon-arrow-right"></i>
        </div>-->
        <el-button
          class="transfer-midle-leftbtn"
          :class="transferlistLength>0?'active':''"
          icon="el-icon-arrow-right"
          :disabled="!transferlistLength>0"
          @click="getSelect()"
        ></el-button>
        <!-- <div class="transfer-midle-rightbtn" :class="transferSelectListLength>0?'active':''" @click="sendSelect()">
                <i class="el-icon-arrow-left"></i>
        </div>-->
        <el-button
          class="transfer-midle-rightbtn"
          :class="transferSelectListLength>0?'active':''"
          :disabled="!transferSelectListLength>0"
          icon="el-icon-arrow-left"
          @click="sendSelect()"
        ></el-button>
      </div>
      <div class="transfer-right">
        <div class="transfer-left-title">
          <div class="transfer-left-title-content">
            <span>已选材料目录</span>
            <span>{{transferSelectListLength}}/{{transferSelectlist.length}}项</span>
          </div>
          <div class="transfer-left-title-inp">
            <el-input placeholder="请输入内容" suffix-icon="el-icon-search" v-model="input2" @input="querySearch1"></el-input>
          </div>
          <div class="transfer-left-title-table">
            <!-- <el-scrollbar style="max-height:180px;"> -->
            <el-table
              ref="rightTable"
              :data="transferSelectlist"
              border
              style="width: 100%"
              height="220px"
              @selection-change="chosedSelectionChange"
            >
              <el-table-column type="selection" width="55"></el-table-column>
              <el-table-column prop="catalog_name" width="180" label="材料目录名称"></el-table-column>
              <el-table-column prop="catalog_code" label="材料目录编码"></el-table-column>
              <el-table-column prop="watermark_name" label="绑定水印样式名称"></el-table-column>
            </el-table>
            <!-- </el-scrollbar> -->
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
const _ = require('lodash')
export default {
  props: {
    transferData: {
      type: Array,
      default() {
        return []
      }
    }
  },
  data() {
    return {
      index: 0,
      transferdata: [],
      transferlist: [],
      transferlistCopy: [],
      transferSelectlist: [],
      transferSelectlistCopy: [],
      watermarkOptions: [],
      transferlistLength: 0, // 全部材料目录选中时数量
      transferSelectListLength: 0, // 已选材料目录选中时数量
      input1: '',
      input2: ''
    }
  },
  watch: {
    transferData(newValue, oldValue) {
      if (this.index === 0) {
        this.transferlist = JSON.parse(JSON.stringify(newValue))
        this.transferlistCopy = JSON.parse(JSON.stringify(newValue))
        this.listData = JSON.parse(JSON.stringify(newValue))
        console.log('this.transferlistCopy', this.transferlistCopy)
        this.index++
      }
    }
  },
  mounted() {},

  methods: {
    querySearch(queryString, cb) {
      var restaurants = this.transferlistCopy
      var results = queryString ? restaurants.filter(this.createStateFilter(queryString)) : restaurants
      this.transferlist = results
      // cb(results)
    },
    querySearch1(queryString, cb) {
      var restaurants = this.transferSelectlistCopy
      var results = queryString ? restaurants.filter(this.createStateFilter(queryString)) : restaurants
      this.transferSelectlist = results
    },
    createStateFilter(queryString) {
      return state => {
        // return state.catalog_name.toLowerCase().indexOf(queryString.toLowerCase()) > -1
        return state.filterateData.toLowerCase().indexOf(queryString.toLowerCase()) > -1
      }
    },
    getSelect() {
      const selectionList = this.$refs.leftTable.selection
      // this.transferSelectlist = this.$refs.leftTable.selection
      const seletIndex = []
      selectionList.forEach(e0 => {
        this.transferlist.forEach((e1, index) => {
          if (e1.catalog_id === e0.catalog_id) {
            seletIndex.push(e1.catalog_id)
          }
        })
      })
      //   this.transferSelectlist = this.transferSelectlist.concat(this.transferlist.filter(item => seletIndex.indexOf(item.catalog_id) !== -1))
      //   this.transferSelectlistCopy = JSON.parse(JSON.stringify(this.transferSelectlist))
      this.transferSelectlist = this.transferSelectlist.concat(selectionList)
      this.transferSelectlistCopy = this.transferSelectlistCopy.concat(selectionList)
      this.transferlist = this.transferlist.filter(item => seletIndex.indexOf(item.catalog_id) === -1)
      this.transferlistCopy = this.transferlistCopy.filter(item => seletIndex.indexOf(item.catalog_id) === -1)
      // this.transferlistCopy.forEach((e, index) => {
      //   if (seletIndex.indexOf(e.catalog_id) !== -1) {
      //     console.log('111', index)
      //     this.transferlistCopy.splice(index, 1)
      //   }
      // })
      console.log('this.transferlistCopy', this.transferlistCopy)
      if (this.input2 !== '') {
        this.querySearch1(this.input2)
      }
      this.$emit('getTransferData', this.transferSelectlistCopy)
    },
    sendSelect() {
      const selectionList = this.$refs.rightTable.selection
      // this.transferSelectlist = this.$refs.leftTable.selection
      const seletId = []
      const seletIndex = []
      selectionList.forEach(e0 => {
        this.listData.forEach((e1, index) => {
          if (e1.catalog_id === e0.catalog_id) {
            seletId.push(e1.catalog_id)
            e0.index = index
          }
        })
      })

      // this.transferlist = this.transferSelectlist.filter(item => seletIndex.indexOf(item.catalog_id) !== -1).concat(this.transferlist)
      selectionList.forEach(e3 => {
        this.transferlist.splice(e3.index, 0, e3)
        this.transferlistCopy.splice(e3.index, 0, e3)
      })

      this.transferlistCopy = _.uniqBy(this.transferlistCopy, item => item.catalog_id)
      this.transferlist = _.uniqBy(this.transferlist, item => item.catalog_id)
      this.transferSelectlist = this.transferSelectlist.filter(item => seletId.indexOf(item.catalog_id) === -1)
      this.transferSelectlistCopy = this.transferSelectlistCopy.filter(item => seletId.indexOf(item.catalog_id) === -1)
      console.log('this.transferlistCopysendSelect', this.transferlistCopy, 'selectionList', selectionList)
      // this.transferlistCopy = JSON.parse(JSON.stringify(this.transferlist))
      // this.listData.forEach((e1,index)=>{
      //   seletIndex.indexOf
      // })
      if (this.input1 !== '') {
        this.querySearch(this.input1)
      }
      this.$emit('getTransferData', this.transferSelectlistCopy)
    },
    selectionChange(data) {
      this.transferlistLength = data.length
    },
    chosedSelectionChange(data) {
      this.transferSelectListLength = data.length
    }
  }
}
</script>

<style lang="scss" scoped>
.watermarkList-transfer {
  margin-top: 16px;
  margin-left: 26px;
}
.transfer {
  height: 350px;
  width: 1400px;
  display: flex;
  &-left,
  &-right {
    // flex: 1;
    width: 613px;
    border: 1px solid #e5e5e5;
    &-title {
      //   border-bottom: 1px solid #e5e5e5;
      color: #666666;
      &-content {
        height: 44px;
        line-height: 44px;
        padding-left: 10px;
        background: #f8f8f8;
        display: flex;
        justify-content: space-between;
        padding-right: 10px;
      }
      &-inp {
        padding: 20px 25px;
        // padding-left: 10px;
        padding-bottom: 10px;
      }
      &-table {
        padding: 0 25px;
        height: 220px;
      }
    }
  }
  &-midle {
    // width: 60px;
    display: flex;
    // flex: 0 0 120px;
    flex: 0 0 182px;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    &-leftbtn {
      width: 30px;
      height: 30px;
      border: 1px solid #c0c4cc;
      line-height: 30px;
      text-align: center;
      margin-bottom: 10px;
      cursor: pointer;
    }
    &-rightbtn {
      width: 30px;
      height: 30px;
      border: 1px solid #c0c4cc;
      line-height: 30px;
      text-align: center;
      cursor: pointer;
    }
    .active {
      background: #c1dbfa;
    }
  }
  &-right {
    // flex: 1;
  }
  .el-table .cell {
    line-height: 14px;
  }
}
::v-deep .el-table .el-table__cell {
  padding: 10px 0;
}
</style>