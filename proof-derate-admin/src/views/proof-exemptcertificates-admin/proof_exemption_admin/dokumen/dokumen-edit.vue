<!--  -->
<template>
  <div class="dokument-detail-wrap">
    <el-card>
      <div class="page-title">
        <div class="left">
          <!-- <img src="@/assets/proof-exemptcertificates-admin-images/page-title-icon.png" alt srcset> -->
        </div>
        <div v-if="isSingle==='0'" class="page-name">更换绑定水印</div>
        <div v-if="isSingle==='1'" class="page-name">批量绑定水印</div>
      </div>
      <el-descriptions v-if="isSingle==='0'" class="descriptions" :column="1" :label-style="LS">
        <el-descriptions-item label="材料目录名称">{{ waterData.catalog_name }}</el-descriptions-item>
        <el-descriptions-item label="材料目录编码">{{ waterData.catalog_code }}</el-descriptions-item>
        <el-descriptions-item label="材料目录状态">{{ waterData.catalog_status.desc }}</el-descriptions-item>
      </el-descriptions>
      <div v-if="isSingle==='1'" class="watermarkList">
        <p>材料目录：</p>
        <div class="watermarkList-transfer">
          <transferTable :transfer-data="transferlist" @getTransferData="getTransferData" />
          <!-- <el-transfer v-model="value" filterable :titles="['全部材料目录','已选材料目录']" :data="transferdata" /> -->
          <!-- <transfer v-model="value" filterable :titles="['全部材料目录','已选材料目录']" :data="transferdata" />  -->
        </div>
      </div>

      <el-form ref="watermarkName" :model="waterData" :rules="rules" label-width="155px">
        <!-- <el-form-item label="材料目录:" prop="name">

        </el-form-item>-->
        <el-form-item label="绑定水印样式名称:" prop="watermark_id">
          <!-- <el-select v-model="watermarkNameForm.name" placeholder="请选择绑定水印样式名称">
            <el-option label="区域一" value="shanghai" />
            <el-option label="区域二" value="beijing" />
          </el-select>-->
          <el-select v-model="waterData.watermark_id" placeholder="请选择">
            <el-option v-for="item in watermarkOptions" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
      </el-form>
      <div v-if="waterData.watermark_id" :key="waterData.watermark_id" class="watermark-detail">
        <div class="watermark-label-title">水印样式预览</div>
        <detail-view type="info" source="dokumenList" :watermark-id="waterData.watermark_id" />
      </div>
      <div class="sendbtn">
        <el-button type="primary" @click="save()">保存</el-button>
        <el-button @click="back()">取消</el-button>
      </div>
    </el-card>
  </div>
</template>

<script>
const _ = require('lodash')
import DetailView from './components/detail-view.vue'
import { getFillElement, updateFillElement, getWatermarkList, getAllList } from '@/api/materials-grade'
// import { fillElement } from '@/api/application'
import transferTable from './components/transferTable.vue'
export default {
  name: 'DokumentDetail',
  components: {
    DetailView,
    transferTable
  },
  data() {
    return {
      waterData: {
        user_name: '',
        catalog_code: '',
        last_modification_time: '',
        watermark_id: '',
        catalog_status: {
          desc: '',
          value: ''
        },
        watermark_name: ''
      },
      watermarkNameForm: {
        name: ''
      },
      form: {
        catalog_code: '',
        catalog_name: '',
        catalog_status: '',
        watermark_name: '',
        page_num: 1,
        page_size: '10'
      },
      value: [],
      transferdata: [],
      transferlist: [],
      transferlistCopy: [],
      transferSelectlist: [],
      transferSelectlistCopy: [],
      watermarkOptions: [],
      transferlistLength: 0, // 全部材料目录选中时数量
      transferSelectListLength: 0, // 已选材料目录选中时数量
      isSingle: '0', // 0 代表单个更换 ，1 代表批量更换
      pageNum: 0,
      rules: {
        watermark_id: [{ required: true, message: '请选择绑定水印样式名称', trigger: 'change' }]
      },
      LS: {
        width: '140px',
        'text-align': 'right',
        display: 'inline-block',
        'margin-right': '60px'
      },
      input1: '',
      input2: ''
    }
  },

  computed: {},

  mounted() {
    this.initType()
    this.getFillList()
    this.getWatermarkList()
  },

  methods: {
    querySearch(queryString, cb) {
      var restaurants = this.transferlistCopy
      var results = queryString ? restaurants.filter(this.createStateFilter(queryString)) : restaurants
      this.transferlist = results
      // cb(results)
    },
    querySearch1(queryString, cb) {
      var restaurants = this.transferSelectlistCopy
      var results = queryString ? restaurants.filter(this.createStateFilter(queryString)) : restaurants
      this.transferSelectlist = results
    },
    createStateFilter(queryString) {
      return state => {
        return state.catalog_name.toLowerCase().indexOf(queryString.toLowerCase()) > -1
      }
    },
    initType() {
      this.isSingle = this.$route.query.isSingle
      if (this.isSingle === '0') {
        this.getFillElement(this.$route.query.id)
      }
      // console.log('this.isSingle', this.isSingle)
    },
    getFillElement(id) {
      getFillElement(id)
        .then(res => {
          if (res.meta.code === '200' && res.data !== null) {
            this.waterData = res.data
          }
        })
        .catch(err => {})
    },
    getFillList() {
      this.getAllList()
    },

    getAllList() {
      getAllList().then(res => {
        if (res.meta.code === '200' && res.data !== null) {
          this.transferlist = this.transferlist.concat(res.data)
          this.transferlist.forEach(e => {
            e.filterateData = e.catalog_name + e.catalog_code + e.watermark_name
          })
          this.transferlistCopy = JSON.parse(JSON.stringify(this.transferlist))
          this.listData = res.data
          // console.log('res.data.total_pages', res.data.total_pages)
          // this.pageNum = res.data.total_pages
          // this.form.page_num = this.form.page_num + 1
          this.transferdata = this.transferlist.map(e => {
            const item = {
              key: e.catalog_id,
              label: e.catalog_name + `（${e.catalog_code}） + ` + e.watermark_name
            }
            return item
          })
        } else {
        }
      })
    },
    getTransferData(data) {
      console.log('data', data)
      this.value = data.map(i => {
        return i.catalog_id
      })
    },
    update() {
      let data = {}
      if (this.isSingle === '1') {
        data = {
          catalog_id_list: this.value,
          watermark_id: this.waterData.watermark_id
        }
        if (this.value.length != 0) {
          this.$refs['watermarkName'].validate(valid => {
            if (valid) {
              this.send(data)
            }
          })
        } else {
          this.$message({
            message: '请选择材料目录',
            type: 'warning'
          })
        }
      } else {
        data = {
          catalog_id_list: [this.waterData.catalog_id],
          watermark_id: this.waterData.watermark_id
        }
        this.send(data)
      }
    },
    send(data) {
      console.log('data', data)
      updateFillElement(data).then(res => {
        if (res.meta.code === '200') {
          this.$message({
            message: '保存成功',
            type: 'success'
          })
        } else {
          this.$message({
            message: res.meta.message,
            type: 'error'
          })
        }
        if (this.isSingle === '1') {
          this.back()
        } else {
          this.$router.push({ name: 'DokumenDetail', query: { isSingle: '0', id: this.$route.query.id }})
        }
      })
    },
    getWatermarkList() {
      getWatermarkList().then(res => {
        if (res.meta.code === '200' && res.data !== null) {
          this.watermarkOptions = res.data
          if (this.isSingle === '1') {
            this.waterData.watermark_id = this.watermarkOptions[0].id
          }
        }
      })
    },
    save() {
      this.update()
    },
    back() {
      this.$router.push({ name: 'dokumenList' })
    },
    getSelect() {
      const selectionList = this.$refs.leftTable.selection
      // this.transferSelectlist = this.$refs.leftTable.selection
      const seletIndex = []
      selectionList.forEach(e0 => {
        this.transferlist.forEach((e1, index) => {
          if (e1.catalog_id === e0.catalog_id) {
            seletIndex.push(e1.catalog_id)
          }
        })
      })
      this.transferSelectlist = this.transferSelectlist.concat(this.transferlist.filter(item => seletIndex.indexOf(item.catalog_id) !== -1))
      this.transferSelectlistCopy = JSON.parse(JSON.stringify(this.transferSelectlist))
      // this.transferSelectlist = this.transferSelectlist.concat(selectionList)
      // this.transferSelectlistCopy =this.transferSelectlistCopy.concat(selectionList)
      this.transferlist = this.transferlist.filter(item => seletIndex.indexOf(item.catalog_id) === -1)
      this.transferlistCopy = this.transferlistCopy.filter(item => seletIndex.indexOf(item.catalog_id) === -1)
      // this.transferlistCopy.forEach((e, index) => {
      //   if (seletIndex.indexOf(e.catalog_id) !== -1) {
      //     console.log('111', index)
      //     this.transferlistCopy.splice(index, 1)
      //   }
      // })
      console.log('this.transferlistCopy', this.transferlistCopy)
    },
    sendSelect() {
      const selectionList = this.$refs.rightTable.selection
      // this.transferSelectlist = this.$refs.leftTable.selection
      const seletId = []
      const seletIndex = []
      selectionList.forEach(e0 => {
        this.listData.forEach((e1, index) => {
          if (e1.catalog_id === e0.catalog_id) {
            seletId.push(e1.catalog_id)
            e0.index = index
          }
        })
      })

      // this.transferlist = this.transferSelectlist.filter(item => seletIndex.indexOf(item.catalog_id) !== -1).concat(this.transferlist)
      selectionList.forEach(e3 => {
        this.transferlist.splice(e3.index, 0, e3)
        this.transferlistCopy.splice(e3.index, 0, e3)
      })

      this.transferlistCopy = _.uniqBy(this.transferlistCopy, item => item.catalog_id)
      this.transferlist = _.uniqBy(this.transferlist, item => item.catalog_id)
      this.transferSelectlist = this.transferSelectlist.filter(item => seletId.indexOf(item.catalog_id) === -1)
      this.transferSelectlistCopy = this.transferSelectlistCopy.filter(item => seletId.indexOf(item.catalog_id) === -1)
      console.log('this.transferlistCopysendSelect', this.transferlistCopy, 'selectionList', selectionList)
      // this.transferlistCopy = JSON.parse(JSON.stringify(this.transferlist))
      // this.listData.forEach((e1,index)=>{
      //   seletIndex.indexOf
      // })
      if (this.input1 !== '') {
        this.querySearch(this.input1)
      }
    },
    selectionChange(data) {
      this.transferlistLength = data.length
    },
    chosedSelectionChange(data) {
      this.transferSelectListLength = data.length
    }
  }
}
</script>
<style lang='scss' scoped>
.dokument-detail-wrap {
  padding: 10px;
}
.page-title {
  display: flex;
  align-items: center;
  border-bottom: 1px solid #cccccc;
  padding: 2px 0 9px 0;
  .left {
    display: flex;
    align-items: center;
    img {
      width: 22px;
      height: 22px;
    }
  }
}
.descriptions {
  margin-top: 20px;
}
.watermark-detail {
  margin-left: 190px;
}
.watermarkList {
  // margin-left: 28px;
  margin-bottom: 30px;
  display: flex;
  p {
    width: 150px;
    margin-top: 31px;
    font-size: 14px;
    color: #888;
    font-weight: 700;
    text-align: right;
  }
  p:before {
    content: '*';
    color: #f56c6c;
    margin-right: 4px;
  }
  &-transfer {
    margin-top: 15px;
    margin-left: 25px;
  }
}
.watermarkList ::v-deep .el-input__prefix {
  right: 13px;
  left: unset;
}
.watermarkList ::v-deep .el-transfer-panel {
  width: 612px;
}
.watermarkList ::v-deep .el-button + .el-button {
  margin-left: 0;
}
.watermarkList ::v-deep .el-button {
  width: 35px;
  height: 33px;
  padding: 0;
  display: block;
}
.watermarkList ::v-deep .el-transfer-panel__filter .el-input__inner {
  border-radius: 4px;
}
.watermarkList ::v-deep .el-transfer__buttons {
  padding: 0 76px;
}
.watermarkList ::v-deep .el-button--primary.is-disabled {
  background-color: #f8f8f8;
  border-color: #f2f6fc;
}
.watermarkList ::v-deep button i {
  color: #888;
}
.watermarkList ::v-deep .el-button--primary {
  background-color: #e5f1ff;
  border-color: #f2f6fc;
}
.sendbtn {
  margin-top: 40px;
  text-align: center;
}
.sendbtn ::v-deep.el-button {
  width: 120px;
  height: 40px;
}
</style>
<style lang="scss">
.dokument-detail-wrap {
  .el-form-item__label {
    color: #888;
  }
  .el-select {
    margin-left: 45px !important;
  }
}
.watermark-label-title {
  font-size: 14px;
  color: #aaa;
  padding-left: 10px;
  margin-top: 10px;
}
// .transfer {
//   height: 300px;
//   width: 1200px;
//   display: flex;
//   &-left,
//   &-right {
//     flex: 1;
//     border: 1px solid #e5e5e5;
//     &-title {
//       border-bottom: 1px solid #e5e5e5;
//       color: #666666;
//       &-content {
//         height: 44px;
//         line-height: 44px;
//         padding-left: 10px;
//         background: #f8f8f8;
//         display: flex;
//         justify-content: space-between;
//         padding-right: 10px;
//       }
//       &-inp {
//         padding: 20px;
//         padding-left: 10px;
//         padding-bottom: 10px;
//       }
//       &-table {
//         padding: 0 10px;
//         height: 184px;
//       }
//     }
//   }
//   &-midle {
//     width: 60px;
//     display: flex;
//     flex: 0 0 120px;
//     flex-direction: column;
//     justify-content: center;
//     align-items: center;
//     &-leftbtn {
//       width: 30px;
//       height: 30px;
//       border: 1px solid #c0c4cc;
//       line-height: 30px;
//       text-align: center;
//       margin-bottom: 10px;
//       cursor: pointer;
//     }
//     &-rightbtn {
//       width: 30px;
//       height: 30px;
//       border: 1px solid #c0c4cc;
//       line-height: 30px;
//       text-align: center;
//       cursor: pointer;
//     }
//     .active {
//       background: #c1dbfa;
//     }
//   }
//   &-right {
//     flex: 1;
//   }
//   .el-table .cell {
//     line-height: 14px;
//   }
// }

// 滚动条样式
::-webkit-scrollbar {
  width: 16px;
  height: 16px;
}
::-webkit-scrollbar-track {
  background: rgb(239, 239, 239);
}
::-webkit-scrollbar-thumb {
  background: #bfbfbf;
  border-radius: 10px;
}

.el-table__body-wrapper::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}
</style>
