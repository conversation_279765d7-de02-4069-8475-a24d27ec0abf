<template>
  <div id="tipDialog">
    <el-dialog title="提示" :visible.sync="dialogVisible" width="30%" @close="cancle">
      <div class="tip-wrap">
        <img :src="wait" alt>
        <span>办事人授权中，请稍等...</span>
      </div>
      <span slot="footer" class="dialog-footer">
        <span>
          <img :src="tipLit" alt>若办事人没有可授权证照，
          <span class="nolicence" @click="tipClick">点击“无证照可授权”</span>
        </span>

        <el-button @click="cancle">取 消</el-button>
        <!-- <el-button type="primary" @click="dialogVisible = false">确 定</el-button> -->
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      wait: require('@/assets/proof-exemptcertificates-admin-images/u47.png'),
      tip: require('@/assets/proof-exemptcertificates-admin-images/u86.png'),
      tipLit: require('@/assets/proof-exemptcertificates-admin-images/u49.png')
    }
  },
  mounted() {},

  methods: {
    cancle() {
      this.$emit('tipDialogCancle')
    },
    tipClick() {
      this.$emit('tipClick')
    }
  }
}
</script>

<style lang="scss" scoped>
.tip-wrap {
  display: flex;
  flex-direction: column;
  align-items: center;
  img {
    width: 60px;
  }
  span {
    font-size: 18px;
    color: #666666;
    font-weight: 600;
    margin-top: 10px;
  }
}
.dialog-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  span {
    display: flex;
  }
  .nolicence {
    cursor: pointer;
    color: #01a463;
  }
  img {
    margin-right: 5px;
    width: 20px;
  }
}
</style>
