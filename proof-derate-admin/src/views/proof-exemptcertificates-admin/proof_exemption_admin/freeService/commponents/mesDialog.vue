<template>
  <div id="mesDialog">
    <el-dialog title="录入个人信息" :visible.sync="dialogVisible" width="40%" @close="cancle">
      <div class="mesDialog-wrap">
        <el-form ref="form1" :model="mesForm" label-width="80px" class="el-check-form" v-if="!isLegalPerson">
          <el-row :gutter="24" justify="center" type="flex">
            <el-col :span="12">
              <el-form-item label="办事人">
                <el-input v-model="mesForm.handle_affairs_name" clearable placeholder="请输入办事人" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="证件类型">
                <el-select v-model="mesForm.identity_type" placeholder="请选择">
                  <el-option v-for="item in identityTypeoptionslist" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="证件号码">
                <el-input v-model="mesForm.identity_number" clearable placeholder="请输入证件号码" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>

        <el-form ref="form1" :model="mesLegalPersonForm" label-width="150px" class="el-check-form" v-else>
          <el-row :gutter="24" justify="center" type="flex">
            <el-col :span="12">
              <el-form-item label="办事人">
                <el-input v-model="mesLegalPersonForm.handle_affairs_name" clearable placeholder="请输入办事人" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="证件类型">
                <el-select v-model="mesLegalPersonForm.identity_type" placeholder="请选择">
                  <el-option v-for="item in identityTypeoptionslist" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24" type="flex">
            <el-col :span="12">
              <el-form-item label="证件号码">
                <el-input v-model="mesLegalPersonForm.identity_number" clearable placeholder="请输入证件号码" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="企业或机构名称">
                <el-input v-model="mesLegalPersonForm.biz_org_name" clearable placeholder="请输入企业或机构名称" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24" type="flex">
            <el-col :span="12">
              <el-form-item label="企业或机构证件类型">
                <el-select v-model="mesLegalPersonForm.biz_org_identity_type" placeholder="请选择">
                  <el-option v-for="item in identityTypeoptionslist" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="企业或机构证件号码">
                <el-input v-model="mesLegalPersonForm.biz_org_identity_num" clearable placeholder="请输入企业或机构证件号码" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="sendData">确 定</el-button>
        <el-button @click="cancle">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getExemptIdentityType } from '@/api/common/dict'
export default {
  data() {
    return {
      mesForm: {
        handle_affairs_name: '',
        identity_type: '',
        identity_number: ''
      },
      mesLegalPersonForm: {
        handle_affairs_name: '',
        identity_type: '',
        identity_number: '',
        biz_org_name: '',
        biz_org_identity_type: '',
        biz_org_identity_num: ''
      },
      identityTypeoptionslist: []
    }
  },
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    isLegalPerson: {
      type: Boolean,
      default: false
    }
  },
  mounted() {
    this.getExemptIdentityType()
    console.log('isLegalPerson', this.isLegalPerson)
  },

  methods: {
    cancle() {
      this.$emit('mesDialogCancle')
    },
    getExemptIdentityType() {
      getExemptIdentityType().then(res => {
        if (res.meta.code === '200') {
          this.identityTypeoptionslist = res.data
        }
      })
    },
    sendData() {
      if (this.isLegalPerson) {
        this.$emit('sendData', this.mesLegalPersonForm)
      } else {
        this.$emit('sendData', this.mesForm)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-footer {
  text-align: center;
}
</style>