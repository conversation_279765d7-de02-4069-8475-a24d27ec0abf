<template>
  <div id="tipMeDialog">
    <el-dialog title="提示" :visible.sync="dialogVisible" width="30%" @close="cancle">
      <div class="tip-wrap">
        <img v-if="isSus" :src="sus" alt>
        <img v-else :src="tip" alt>
        <span v-if="isSus" class="center">{{ tipStr }}</span>
        <span v-else>{{ tipStr }}</span>
      </div>
      <span v-if="hasRes" slot="footer" class="dialog-footer">
        <el-button @click="cancle">取 消</el-button>
        <el-button type="primary" @click="getMes">确 定</el-button>
      </span>
      <span v-else slot="footer" class="dialog-footer">
        <el-button @click="cancle">关闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    tipStr: {
      type: String,
      default: '办事人无证照可授权，是否确定继续办理？'
    },
    hasRes: {
      type: Boolean,
      default: true
    },
    isSus: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      wait: require('@/assets/proof-exemptcertificates-admin-images/u47.png'),
      tip: require('@/assets/proof-exemptcertificates-admin-images/u86.png'),
      tipLit: require('@/assets/proof-exemptcertificates-admin-images/u49.png'),
      sus: require('@/assets/proof-exemptcertificates-admin-images/u150.png')
    }
  },
  mounted() {},

  methods: {
    cancle() {
      this.$emit('tipMeDialogCancle')
    },
    getMes() {
      this.$emit('getMes')
    }
  }
}
</script>

<style lang="scss" scoped>
.tip-wrap {
  display: flex;
  flex-direction: column;
  align-items: center;

  img {
    width: 60px;
  }
  span {
    font-size: 18px;
    color: #666666;
    font-weight: 600;
    margin-top: 10px;
    min-width: 180px;
  }
  .center {
    text-align: center;
  }
}
.dialog-footer {
  display: flex;
  align-items: center;
  justify-content: center;
  padding-bottom: 20px;
  span {
    display: flex;
  }
  .nolicence {
    cursor: pointer;
    color: #01a463;
  }
  img {
    margin-right: 5px;
    width: 20px;
  }
}
</style>
