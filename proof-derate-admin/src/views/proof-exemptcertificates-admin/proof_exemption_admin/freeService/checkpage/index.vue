<template>
  <div class="content-wrapper padding-10">
    <CardTitle :title-name="freeServiceData.item_name" :ifback="true" @back="back()">
      <template>
        <el-button v-permission="'exempt:use:service:temp'" type="warning" :loading="loading"
          @click="holdUp()">暂存</el-button>
        <el-button v-permission="'exempt:use:service:save'" type="primary" :loading="loading"
          @click="nextTab()">下一步</el-button>
      </template>
    </CardTitle>
    <!-- <section class="content"> -->
    <el-card class="box-card" :body-style="{ 'padding': '30px 20px' }">
      <span class="margin-left-10 info-wrap info-wrap-first">
        <div class="info-wrap-title">
          <img :src="arrow" alt />
          <span class="info-title">事项基本信息</span>
        </div>
      </span>
      <el-descriptions class="margin-top margin-bottom-10" title :column="2" border>
        <el-descriptions-item label-class-name="left-label" :label-style="{ width: '150px' }"
          content-class-name="right-content">
          <template slot="label">事项名称</template>
          {{ freeServiceData.item_name }}
        </el-descriptions-item>
        <el-descriptions-item label-class-name="left-label" :label-style="{ width: '150px' }"
          content-class-name="right-content">
          <template slot="label">事项编码</template>
          {{ freeServiceData.item_code }}
        </el-descriptions-item>
        <el-descriptions-item label-class-name="left-label" :label-style="{ width: '150px' }"
          content-class-name="right-content">
          <template slot="label">事项所属行政区划</template>
          {{ freeServiceData.item_division_name }}
        </el-descriptions-item>
        <el-descriptions-item label-class-name="left-label" :label-style="{ width: '150px' }"
          content-class-name="right-content">
          <template slot="label">事项实施机构</template>
          {{ freeServiceData.item_org_name }}
        </el-descriptions-item>
      </el-descriptions>
      <span class="margin-left-10 info-wrap">
        <div class="info-wrap-title">
          <img :src="arrow" alt />
          <span class="info-title">服务对象</span>
        </div>
        <el-button type="primary" icon="el-icon-edit" v-permission="'exempt:use:service:edit'"
          @click="editData">编辑信息</el-button>
      </span>
      <el-descriptions class="margin-top" title :column="2" border>
        <el-descriptions-item label-class-name="left-label" :label-style="{ width: '150px' }"
          content-class-name="right-content">
          <template slot="label">业务办理号</template>
          {{ freeServiceData.serial_number }}
        </el-descriptions-item>
        <el-descriptions-item v-if="!isLegalPerson" label-class-name="left-label" :label-style="{ width: '150px' }"
          content-class-name="right-content">
          <template slot="label">办事人</template>
          {{ freeServiceData.handle_affairs_name }}
        </el-descriptions-item>
        <el-descriptions-item v-if="isLegalPerson" label-class-name="left-label" :label-style="{ width: '150px' }"
          content-class-name="right-content">
          <template slot="label">企业或机构名称</template>
          {{ freeServiceData.biz_org_name }}
        </el-descriptions-item>
        <el-descriptions-item v-if="isLegalPerson" label-class-name="left-label" :label-style="{ width: '150px' }"
          content-class-name="right-content">
          <template slot="label">企业或机构证件类型</template>
          {{ biz_org_identity_type_name }}
        </el-descriptions-item>
        <el-descriptions-item v-if="isLegalPerson" label-class-name="left-label" :label-style="{ width: '150px' }"
          content-class-name="right-content">
          <template slot="label">企业或机构证件号码</template>
          {{ freeServiceData.biz_org_identity_num }}
        </el-descriptions-item>
        <el-descriptions-item v-if="!isLegalPerson" label-class-name="left-label" :label-style="{ width: '150px' }"
          content-class-name="right-content">
          <template slot="label">证件类型</template>
          {{ identity_type_name }}
        </el-descriptions-item>
        <el-descriptions-item v-if="!isLegalPerson" label-class-name="left-label" :label-style="{ width: '150px' }"
          content-class-name="right-content">
          <template slot="label">证件号码</template>
          {{ freeServiceData.identity_number }}
        </el-descriptions-item>
        <!-- <div v-if="isLegalPerson"> -->
        <el-descriptions-item v-if="isLegalPerson" label-class-name="left-label" content-class-name="right-content"
          :label-style="{ width: '150px' }">
          <template slot="label">经办人名称</template>
          {{ freeServiceData.handle_affairs_name }}
        </el-descriptions-item>
        <el-descriptions-item v-if="isLegalPerson" label-class-name="left-label" content-class-name="right-content"
          :label-style="{ width: '150px' }">
          <template slot="label">证件类型</template>
          {{ to_user_id_type_name }}
        </el-descriptions-item>
        <el-descriptions-item v-if="isLegalPerson" label-class-name="left-label" content-class-name="right-content"
          :label-style="{ width: '150px' }">
          <template slot="label">证件号码</template>
          {{ freeServiceData.identity_number }}
        </el-descriptions-item>
        <!-- </div> -->
      </el-descriptions>
    </el-card>
    <el-card class="box-card box-card2" :body-style="{ 'padding': '0px 20px' }">
      <span class="margin-left-10 info-wrap">
        <div class="info-wrap-title">
          <img :src="arrow" alt />
          <span class="info-title">
            事项办理材料
            <!-- <img :src="iconImg" class="icon" v-popover:popover /> -->
            <el-popover ref="popover" placement="right" title="办理条件" width="706" trigger="hover"
              popper-class="popoverwrap">
              <p style="line-height:22px">{{ freeServiceData.accept_condition }}</p>
            </el-popover>
          </span>
        </div>
        <!-- <el-button type="primary" v-permission="'exempt:use:service:archive'" icon="el-icon-download" @click="showfileDialog">一键归档</el-button> -->
      </span>
      <!-- <section class="content"> -->
      <!-- <el-card class="box-card"> -->
      <custom-table ref="table" :is-card-type="false" :table-data="tableData" :table-header="tableHeader"
        :stripe="false" :table-tools="tableTools" @refresh="query(1)" @query="query" @allcheck="allcheck">
        <template #material_name="{ row, $index }">
          <div v-if="row.replace_cancel_way === '转化为电子证照'">
            <span class="breakword">
              {{ row.material_name }}
              <i class="el-icon-postcard" />
            </span>
          </div>
          <div v-else>
            <span class="breakword">{{ row.material_name }}</span>
          </div>
        </template>
        <template #operate="{ row, $index }">
          <div v-if="row.replace_cancel_way === '转化为电子证照'">
            <!-- <el-button type="text">下载归档</el-button> &&row.archiving_id_list!=undefined -->
            <div v-if="row.auth_code != '' && row.auth_code != null && row.valid_license_error_msg == null">
              <el-button type="text" @click="importData(row, $index)"
                v-if="row.attachment_list && row.attachment_list.length < 5">上传材料</el-button>
              <el-button type="text" @click="toShowTip(row, $index, '转化为电子证照')">查看证照</el-button>
              <!-- <el-button v-if="row.archiving_id_list.length!=0" type="text" @click="downArchivingFile(row,$index)">下载归档</el-button> -->
            </div>
            <div v-else-if="row.auth_code === null && row.valid_license_error_msg == null">
              <el-button type="text" @click="importData(row, $index)"
                v-if="row.attachment_list && row.attachment_list.length < 5">上传材料</el-button>
              <span style="margin-left:10px;color:#ccc">用户未授权证照</span>
            </div>
            <div
              v-else-if="(row.auth_code === '' || row.auth_code == null) && row.valid_license_error_msg === undefined">
              <p>
                <!-- <span class="word" @click="downFile(row,$index)">
                  <i v-if="row.attachment_name!=''&&row.attachment_name!=null" class="el-icon-link" />
                  {{ row.attachment_name +'【'+row.license_count+'】' }}
                </span>-->
              </p>
              <el-button type="text" @click="importData(row, $index)"
                v-if="row.attachment_list && row.attachment_list.length < 5">上传材料</el-button>
            </div>
            <div v-else-if="row.valid_license_error_msg != ''">
              <p>
                <span class="word">
                  <i v-if="row.attachment_name != '' && row.attachment_name != null" class="el-icon-link" />
                  <span v-if="row.attachment_name != '' && row.attachment_name != null"
                    @click="downFile(row, $index)">{{
                      row.attachment_name }}</span>
                  <span v-else style="margin-left:10px;color:#ccc">授权失败，用户已拒绝授权</span>
                </span>
              </p>
              <el-button type="text" @click="importData(row, $index)"
                v-if="row.attachment_list && row.attachment_list.length < 5">上传材料</el-button>
            </div>
          </div>
          <div v-else-if="row.replace_cancel_way === '部门间协查'">
            <div v-if="row.config_assist_user == true">
              <el-button type="text" @click="importData(row, $index)"
                v-if="row.attachment_list && row.attachment_list.length < 5">上传材料</el-button>
              <el-button v-if="row.assist_serial_number === null" type="text" @click="application(row)">申请协查</el-button>
              <el-button v-else-if="row.audit_result != 'WAIT' && row.assist_serial_number != null" type="text"
                @click="toShowTip(row, $index, '部门间协查')">查看协查结果</el-button>
              <span v-else-if="row.audit_result === 'WAIT'" style="margin-left:10px;color:#ccc">协查处理中</span>
            </div>
            <div v-else-if="row.config_assist_user == false">
              <p v-if="row.attachment_name != '' && row.attachment_name != null">
                <span class="word" @click="downFile(row, $index)">
                  <i class="el-icon-link" />
                  {{ row.attachment_name }}
                </span>
              </p>
              <p v-else>
                <span style="margin-left:10px;color:#ccc">未配置协查人员处理</span>
              </p>
              <el-button type="text" @click="importData(row, $index)"
                v-if="row.attachment_list && row.attachment_list.length < 5">上传材料</el-button>
            </div>
          </div>
          <div v-else-if="row.replace_cancel_way === '数据共享'">
            <!-- <span>无</span> v-if="dataSharingQueryData.length!=0"-->
            <el-button type="text" @click="importData(row, $index)"
              v-if="row.attachment_list && row.attachment_list.length < 5">上传材料</el-button>
            <el-button type="text" @click="toShowTip(row, $index, '数据共享')">获取数据</el-button>
            <!-- <el-button
              v-if="row.data_shared_query_last_result!==''&&row.data_shared_query_last_result!==null"
              type="text"
              @click="downDataShar(row)"
            >下载归档</el-button>-->

            <!-- <span>系统未获取数据</span> -->
          </div>

          <div v-else-if="row.replace_cancel_way === '电子证明'">
            <!-- <span>无</span> -->
            <el-button type="text" @click="importData(row, $index)"
              v-if="row.attachment_list && row.attachment_list.length < 5">上传材料</el-button>
            <el-button v-if="row.apply_license != true" type="text"
              @click="getElectronicCertification(row, $index)">开具电子证明</el-button>
            <el-button v-if="row.apply_license == true && row.auth_code !== null" type="text"
              @click="toShowTip(row, $index, '电子证明')">查看电子证明</el-button>
            <span v-if="row.apply_license == true && row.auth_code == null"
              style="margin-left:10px;color:#ccc">电子证明待签发</span>
          </div>
          <div v-else-if="row.replace_cancel_way === '告知承诺'">
            <!-- </el-button> -->
            <!-- <br></br> -->
            <el-button type="text" @click="importData(row, $index)"
              v-if="row.attachment_list && row.attachment_list.length < 5">上传材料</el-button>
            <!-- <p> -->
            <!-- <span class="word"  style="cursor:pointer;" @click="downFile(row,$index)">
              <i v-if="row.commit_attachment_name!=''&&row.commit_attachment_name!=null" class="el-icon-link" />
              {{ row.commit_attachment_name }}
            </span>-->
            <el-button v-if="row.commit_attachment_name != '' && row.commit_attachment_name != null" type="text"
              @click="downFile(row, $index)">下载模板</el-button>

            <!-- </p> -->
          </div>
          <div v-else-if="row.replace_cancel_way === '直接取消'">
            <span>无</span>
          </div>
          <!-- v-if="row.replace_cancel_way==='其他情况'" -->
          <div v-else>
            <!-- <p v-if="row.attachment_name!=''&&row.attachment_name!=null">
              <span class="word" @click="downFile(row,$index)">
                <i class="el-icon-link" />
                {{ row.attachment_name }}
              </span>
            </p>-->
            <el-button type="text" @click="importData(row, $index)"
              v-if="row.attachment_list && row.attachment_list.length < 5">上传材料</el-button>
          </div>
        </template>
        <!-- <template #license_name="{ row }">
          <div v-if="row.license_name!=null&&row.auth_code!=null&&row.replace_cancel_way=='转化为电子证照'" @click="getView(row)">
            <span style="color: #409eff;cursor: pointer;">
              <i class="el-icon-postcard" />
              {{ row.license_name }}
            </span>
          </div>
          <div v-else>
            <span style="margin-left:10px;color:#ccc">{{ row.license_name }}</span>
          </div>
        </template>-->
        <!-- <template #attachment_name="{ row ,$index}">
          <div v-if="row.replace_cancel_way=='告知承诺'" @click="downFile(row,$index)">
            <span style="color: #409eff;cursor: pointer;">
              {{ row.commit_attachment_name }}
            </span>
          </div>
          <div v-else @click="downFile(row,$index)">
            <span style="color: #409eff;cursor: pointer;">{{ row.attachment_name }}</span>
          </div>
        </template>-->
        <!-- @click="downFile(item.raw,$index)" -->
        <template #attachment_list="{ row, $index }">
          <div v-for="(item, key) in row.attachment_list" :key="key"
            style="display: flex;justify-content: space-between;padding: 5px 29px;">
            <span style="color: #409eff;cursor:pointer;display: flex;align-items: center;"
              @click="downFileList(row, item)">
              <img style="height:18px;margin-right:5px;" :src="fileImg" alt />
              <span style="width: 184px;white-space: normal;">{{ item.file_name }}</span>
            </span>
            <span style="color: #9E9E9E;cursor: pointer;" @click="delectRow(row, key)">x</span>
          </div>
        </template>

        <template #verification_and_inspection="{ row }">
          <div style="display:flex;align-items: center;justify-content: center;">
            <!-- <el-checkbox v-model="row.verification_and_inspection" /> -->
            <div class="circle" v-if="row.verification_and_inspection"></div>
            <div class="circle" style="background:#E01212" v-if="!row.verification_and_inspection"></div>
            <span v-if="!row.verification_and_inspection">未核验</span>
            <span v-if="row.verification_and_inspection">已核验</span>
          </div>
        </template>
        <template #archivedFile="{ row }">
          <div>
            <el-checkbox v-model="row.archivedFile" />
            <!-- {{row.verification_and_inspection +1}} -->
          </div>
        </template>
        <!-- row.replace_cancel_way=='直接取消'|| -->
        <template #replace_cancel_way="{ row }">
          <span
            v-if="row.replace_cancel_way == '电子证明' || row.replace_cancel_way == '其他' || row.replace_cancel_way == '转化为电子证照' || row.replace_cancel_way == '自行调查' || row.replace_cancel_way == '部门间协查' || row.replace_cancel_way == '数据共享'"
            class="greenWord">{{ row.replace_cancel_way }}</span>
          <span v-else-if="row.replace_cancel_way == '告知承诺'" class="greenWord">{{ row.replace_cancel_way }}</span>
          <span v-else-if="row.replace_cancel_way == '需要提交'" class="redWord">{{ row.replace_cancel_way }}</span>
          <span v-else>{{ row.replace_cancel_way }}</span>
        </template>
        <!-- <template>
                  <div slot="archivedFile_c">
                    <span @click="checkClick()">
                      <span>归档</span>
                      <el-checkbox v-model="checkbox.checked"></el-checkbox>
                    </span>
                  </div>
        </template>-->
      </custom-table>
      <!-- </el-card> -->
      <!-- </section> -->
      <span class="margin-left-10 info-wrap info-wrap-first">
        <div class="info-wrap-title">
          <img :src="arrow" alt />
          <span class="info-title">办理信息</span>
        </div>
      </span>
      <el-descriptions class="margin-top margin-bottom-10" title :column="2" border>
        <el-descriptions-item label-class-name="left-label" :label-style="{ width: '150px' }"
          content-class-name="right-content">
          <template slot="label">操作人</template>
          {{ freeServiceData.operate_name }}
        </el-descriptions-item>
        <el-descriptions-item label-class-name="left-label" :label-style="{ width: '150px' }"
          content-class-name="right-content">
          <template slot="label">操作部门</template>
          {{ freeServiceData.operate_org_name }}
        </el-descriptions-item>
        <el-descriptions-item label-class-name="left-label" :label-style="{ width: '150px' }"
          content-class-name="right-content">
          <template slot="label">操作时间</template>
          {{ freeServiceData.operate_time }}
        </el-descriptions-item>
      </el-descriptions>
      <div class="foot-btn"></div>
    </el-card>
    <el-card v-if="isDetail == '1'" class="box-card box-card3">
      <div slot="header" class="cardtitle">
        <span>办理意见</span>
      </div>
      <el-form ref="form" :model="checkform" label-width="100px" class="el-check-form" :rules="rules">
        <el-row>
          <!-- <el-col :xs="24" :sm="12" :md="12" :lg="10"> -->
          <!-- <div class="minddle-form"> -->
          <el-row align="middle" justify="center" type="flex" :gutter="16">
            <el-col :xs="24" :sm="12" :md="12" :lg="8">
              <el-form-item label="经办人">
                <el-input v-model="checkform.eventName" clearable placeholder="请输入经办人" :disabled="true" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="12" :lg="8">
              <el-form-item label="经办部门">
                <el-input v-model="checkform.eventName" clearable placeholder="请输入经办部门" :disabled="true" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row align="middle" justify="center" type="flex" :gutter="16">
            <el-col :xs="24" :sm="12" :md="12" :lg="8">
              <el-form-item label="办理结果">
                <el-input v-model="checkform.eventName" clearable placeholder="请输入办理结果" :disabled="true" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="12" :lg="8">
              <el-form-item label="办理时间">
                <el-input v-model="checkform.eventName" clearable placeholder="请输入办理时间" :disabled="true" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row align="middle" justify="center" type="flex" :gutter="16">
            <el-col :xs="24" :sm="12" :md="12" :lg="16">
              <el-form-item label="意见内容">
                <el-input v-model="checkform.eventName" type="textarea" :rows="4" clearable placeholder="请输入意见内容"
                  :disabled="true" />
              </el-form-item>
            </el-col>
          </el-row>
          <!-- </div> -->
          <!-- </el-col> -->
        </el-row>
        <div class="foot-btn">
          <el-button size="small" @click="back()">返回</el-button>
        </div>
      </el-form>
    </el-card>
    <!-- </section> -->
    <el-dialog title="上传文件" :visible.sync="dialogVisible1" width="40%" :before-close="cancle">
      <div class>
        <p>请选择您要导入的数据</p>
        <el-row :gutter="24" justify="center" align="middle" type="flex">
          <el-col :span="20">
            <el-input v-model="fileForm.fileName" placeholder readonly />
          </el-col>
          <el-col :span="4">
            <el-upload :auto-upload="false" :show-file-list="false" class="upload-demo" action :on-change="handleChange"
              :file-list="fileList" :accept="acceptOther ? '.doc, .docx, .pdf' : '.doc, .docx, .pdf, .jpg, .png'">
              <!-- .doc, .docx, .pdf, .jpg, .png -->
              <el-button type="primary">浏览</el-button>
            </el-upload>
          </el-col>
        </el-row>
        <p class="tip">
          <i class="el-icon-info" />
          <span v-if="acceptOther">温馨提示：请选择以doc/docx/pdf为后缀名的文件且上传的文件不能超过1M!</span>
          <span v-else>温馨提示：请选择以doc/docx/pdf/jpg/png为后缀名的文件且上传的文件不能超过1M!</span>
          <!-- <el-button type="text" @click="getUserTemplate">下载导入模板</el-button> -->
        </p>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="cancle()">取 消</el-button>
        <el-button type="primary" :loading="fileLoading" @click="importFile()">导入</el-button>
      </span>
    </el-dialog>
    <!-- 申请部门协查弹窗-->
    <deptDialog :dialog-visible="dialogVisible2" :free-service-data="freeServiceData" :org-list="orgList"
      :select-row="selectionRow" @deptSumbit="deptSumbit" @deptCancle="deptCancle" />
    <!-- 申请个人协查弹窗-->
    <legalpersonDialog :dialog-visible="dialogVisible3" :free-service-data="freeServiceData" :org-list="orgList"
      :select-row="selectionRow" @deptSumbit="deptSumbit1" @deptCancle="deptCancle1" />
    <!-- 文件上传弹窗 -->
    <fileDialog ref="fileDialog" :dialog-visible="dialogVisible4" :table-data-content="tableData.content"
      @deptSumbit="deptSumbit2" @deptCancle="deptCancle2" />
    <!-- 获取数据共享结果展示弹窗 -->
    <datashareingDialog ref="datashareingDialog" :dialog-visible="dialogVisible5" :data-list="dataList"
      @dialogCancle="datashareingDialogCancle" />
    <!-- 开具电子证明弹窗 -->
    <electronicCertificationDialog :dialog-visible="dialogVisible6" :free-service-data="freeServiceData"
      :table-index="tableIndex" @dialogCancle="electronicCertificationDialogCancle"
      @sendiIssueLicenseApply="issueLicenseApply" />
    <!-- 编辑信息弹窗 -->
    <dataEditDialog :dialog-visible="dialogVisible7" :free-service-data="freeServiceData" :update-data="updateData"
      :identity-typeoptions="identityTypeoptions" @dialogCancle="dataEditDialogCancle"
      @dialogCancleAndUpdate="updateinit" />
    <!-- 数据共享查询框 -->
    <dataSharingQueryDialog :dialog-visible="dialogVisible8" :data-sharing-query-data="dataSharingQueryData"
      :limt-count="limtCount" @dataSharingQuerySumbit="dataSharingQuerySumbit"
      @dataSharingQueryCancle="dataSharingQueryCancle" />
    <!-- PDF文件超出提示弹窗 -->
    <tipDialog :dialog-visible="dialogVisible9" @next="tipNext" @cancle="tipCancle" />
    <!-- 查看证照弹窗 -->
    <licenceDialog :dialog-visible="licencedialogVisible" @licensedialogVisible="licensedialogVisible"
      @licensedialogSumbit="licensedialogSumbit"></licenceDialog>
  </div>
</template>

<script>
import CustomTable from '@/components/Element/Table'
import deptDialog from './components/deptDialog'
import legalpersonDialog from './components/legalpersonDialog'
import datashareingDialog from './components/dataShareingDialog'
import electronicCertificationDialog from './components/electronicCertificationDialog'
import tipDialog from './components/tipDialog'
import fileDialog from './components/fileDialog'
import dataEditDialog from './components/dataEditDialog'
import licenceDialog from './components/licenceDialog'
import { getExemptIdentityType } from '@/api/common/dict'
import { downloadFile } from '@/api/common/download'
import {
  holdUp,
  getView,
  getManagerDetail,
  uploadFile,
  download,
  attachmentArchiving,
  getDataSharedConfig,
  getDataSharedData,
  getLicenseViewUrl,
  getLicenseItemArchivist,
  getYssYstView,
  getViewPdf,
  getDataSharedDataArchivist,
  getInvestigationArchivist
} from '@/api/exemptCertificates'
import { dataURLtoDownload, getIsWhitelist, getFileType } from '@/utils/index.js'
import { getViewBySerialNumber, getAssistOrg, getAssist } from '@/api/assistInvestigate'
import { validPrefix } from '@/utils/validate'
import { issueLicenseApply } from '@/api/exemptCertificates'
import dataSharingQueryDialog from '@/views/proof-exemptcertificates-admin/proof_exemption_admin/freeService/checkpage/components/dataSharingQueryDialog'
import CardTitle from '@/components/CardTitle'
export default {
  name: 'CheckPage',
  components: {
    CustomTable,
    deptDialog,
    legalpersonDialog,
    fileDialog,
    datashareingDialog,
    electronicCertificationDialog,
    dataEditDialog,
    dataSharingQueryDialog,
    CardTitle,
    tipDialog,
    licenceDialog
  },
  data() {
    return {
      iconImg: require('@/assets/proof-exemptcertificates-admin-images/icon.png'),
      arrow: require('@/assets/proof-exemptcertificates-admin-images/arrow.png'),
      fileImg: require('@/assets/images/fileList.png'),
      deptForm: {
        eventName: '',
        dep: ''
      },
      freeServiceData: {
        serial_number: '',
        handle_affairs_name: '',
        identity_type: '',
        identity_number: '',
        biz_org_name: '',
        biz_org_identity_type: '',
        // biz_org_identity_type_name: '',
        biz_org_identity_num: '',
        to_user_account: '',
        to_user_name: '',
        to_user_org: '',
        to_user_org_code: '',
        item_name: ''
      },
      // rules: [],
      checkform: {
        code: '',
        eventName: ''
      },
      searchForm: {
        name: '',
        region: ''
      },
      fileForm: {
        fileName: '',
        file: ''
      },
      checkbox: {
        checked: true
      },
      loading: false,
      isDetail: '0',
      alltableCheck: true,
      dialogVisible: false,
      dialogVisible1: false,
      dialogVisible2: false,
      dialogVisible3: false,
      dialogVisible4: false,
      isHFW: false,
      dialogVisible5: false,
      dialogVisible6: false,
      dialogVisible7: false,
      dialogVisible8: false,
      dialogVisible9: false,
      licencedialogVisible: false,
      selectFileIndex: 0, // table中选择上传文件的数据序号
      fileLoading: false,
      acceptOther: true, // 是否是替代方式为其它，需要提交，自行调查
      idcard: require('@/assets/proof-exemptcertificates-admin-images/card.png'),
      idcardBack: require('@/assets/proof-exemptcertificates-admin-images/cardback.png'),
      fileList: [],
      exemptIdentityTypeList: [],
      orgList: [],
      selectionRow: {},
      tableData1: [],
      investigation_detail_list: [],
      dataSharingQueryData: [],
      limtCount: 0,
      // doc/docx/pdf/jpg/png
      whitelist: ['doc', 'docx', 'pdf', , 'jpg', 'png'],
      tableData: {
        content: [], // 表格数据
        loading: false, // 控制表格加载效果
        total: 0, // 分页查询时的总数，去掉该属性，不显示分页条
        currentPage: 1, // 当前页码
        pageSize: 10,
        pageDirection: 'desc',
        isShowSelection: false, // 是否显示多选框，默认false
        isShowIndex: true
      },
      tableHeader: [
        { label: '材料名称', prop: 'material_name', slot: 'material_name', minWidth: '160px', align: 'left' }, // 配置slot属性，可支持使用插槽
        { label: '证明目录', prop: 'proof_catalog_name', minWidth: '160px', align: 'left' },
        {
          label: '替代方式',
          prop: 'replace_cancel_way',
          slot: 'replace_cancel_way',
          minWidth: '200px',
          align: 'left'
        },

        // { label: '证照', prop: 'license_name', slot: 'license_name', minWidth: '180px' },
        // { label: '文件', prop: 'attachment_name', slot: 'attachment_name', minWidth: '180px' },
        { label: '文件', prop: 'attachment_list', slot: 'attachment_list', minWidth: '180px', align: 'left' },

        { label: '操作', slot: 'operate', width: '200px', fixed: 'right', align: 'left' },
        {
          label: '核检状态',
          prop: 'verification_and_inspection',
          slot: 'verification_and_inspection',
          minWidth: '80px',
          align: 'left'
        }
        // isHeaderslot 与 prop 不要同名
        // {
        //   label: '归档',
        //   prop: 'archivedFile',
        //   slot: 'archivedFile',
        //   width: '120px',
        //   fixed: 'right',
        //   isHeaderslot: 'archivedFile_c',
        //   headerchecked: true
        // }
        //  { label: '归档', prop: 'archivedFile',slot: 'archivedFile', width: '120px', fixed: 'right'}
      ],
      tableTools: [],
      rules: {
        code: [{ required: true, message: '请输入授权码', trigger: 'blur' }],
        eventName: [{ required: true, message: '请输入事件名称', trigger: 'blur' }]
      },
      isLegalPerson: false,
      promiseArray: [],
      fileData: [],
      userIdType: [
        { value: '10', label: '身份证' },
        { value: '11', label: '军官证' },
        { value: '12', label: '士兵证' },
        { value: '13', label: '警官证' },
        { value: '14', label: '港澳居民来往内地通行证' },
        { value: '15', label: '台湾居民来往大陆通行证' },
        { value: '40', label: '其他有效个人身份证件' }
      ],
      dataList: {
        errormes: ''
      },
      electronicCertificationData: {
        h_affairs_name: '',
        h_affairs_identity_type: '',
        h_affairs_identity_number: '',
        item_name: '',
        material_name: '',
        issue_org_name: '',
        description: ''
      },
      selectrow: '', // 列表当前选中行
      selectIndex: 0, // 当前选中列表中行的索引值
      selectType: '', // 证明类型
      tableIndex: 0,
      fileImgData: '',
      updateData: {}, // 编辑用户信息
      identityTypeoptions: [], // 证件类型
      proof_list_id: '',
      to_user_id_type_name: '', // 经办人证件类型中文
      identity_type_name: '', //  证件类型
      biz_org_identity_type_name: '', // 企业或机构证件类型
      isOpenDialog: false // 查看证照是否弹窗
    }
  },
  watch: {
    promiseArray: {
      handler(val) {
        if (this.fileData.length != 0 && val.length === this.fileData.length) {
          this.$message({
            message: '归档成功',
            type: 'success'
          })
        }
      },
      deep: true,
      immediate: true
    }
  },
  mounted() {
    this.ininData()
    console.log('this.$route.meta.permission', this.$route.meta.permission)
    // this.getDataSharedData()
  },
  beforeRouteLeave(to, from, next) {
    from.meta.keepAlive = false
    next()
  },
  methods: {
    eventChose() {
      this.dialogVisible = true
    },
    ininData() {
      this.tableData.content = []
      const serial_number = this.$route.query.serial_number

      this.getExemptIdentityType().then(() => {
        this.getManagerDetail(serial_number)
      })
      this.isDetail = this.$route.query.isDetail
    },
    // 获取证件类型
    getExemptIdentityType() {
      return getExemptIdentityType().then(res => {
        if (res.meta.code === '200' && res.data != null) {
          this.exemptIdentityTypeList = res.data
          this.identityTypeoptions = this.exemptIdentityTypeList
        } else {
          this.$message({
            message: res.meta.message,
            type: 'error'
          })
        }
      })
    },
    handleClose() { },
    searchFrom() { },
    // 申请协查
    application(row) {
      this.getAssistOrg(row.id)
      this.selectionRow = row
      if (this.isLegalPerson) {
        this.dialogVisible3 = true
      } else {
        this.dialogVisible2 = true
      }
    },
    // 查看协查结果
    getInvestigationDetail(row) {
      row.verification_and_inspection = true
      const url = this.$router.resolve({
        name: 'investigationDetail',
        query: {
          type: 'archives',
          id: row.assist_id
        }
      })
      const data = {
        serial_number: row.serial_number,
        proof_list_id: row.proof_list_id
      }
      getInvestigationArchivist(data).then(res => {
        if (res.meta.code === '200' && res.data) {
          // if (row.attachment_list.length < 1) {
            
          // } else {
          //   // this.$message({
          //   //   message: '单个材料文件不超过5份',
          //   //   type: 'warning'
          //   // })
          // }
          row.attachment_list.push({
              file_name: res.data.file_name,
              id: res.data.id,
              item_code: row.item_code,
              proof_catalog_code: row.proof_catalog_code,
              replace_cancel_way: row.replace_cancel_way,
              replace_way_id: row.id,
              is_upload_file: false
            })
          this.holdUp()
        } else {
          this.$message({
            type: 'error',
            message: res.meta.message
          })
        }
      })
      window.open(url.href, '_blank')
    },
    importFile() {
      // this.uploadFile(this.tableData.content[this.selectFileIndex].raw)
      console.log('this.fileForm.file', this.fileForm.file)
      if (this.fileForm.file) {
        this.dialogVisible1 = false
        if (this.tableData.content[this.selectFileIndex].attachment_list.length < 5) {
          this.uploadFile(this.fileForm.file).then(res => {
            if (res === '200') {
              this.tableData.content[this.selectFileIndex].attachment_name = this.fileForm.fileName
              this.tableData.content[this.selectFileIndex].raw = this.fileForm.file
              // this.tableData.content[this.selectFileIndex].attachment_list.push({ raw: this.fileForm.file, file_name: this.fileForm.fileName })
            }
          })
        } else {
          this.$message({
            message: '单个材料文件不超过5份',
            type: 'warning'
          })
        }
      } else {
        this.$message({
          message: '请选择文件',
          type: 'warning'
        })
      }
    },
    importData(row, index) {
      this.dialogVisible1 = true
      this.selectFileIndex = index
    },
    // 下载当前上传的模板文件
    downFile(row, index) {
      if (this.tableData.content[index].raw == undefined) {
        this.download(this.tableData.content[index])
      } else {
        const blob = new Blob([this.tableData.content[index].raw])
        const downLink = document.createElement('a')
        downLink.download = this.tableData.content[index].attachment_name
        downLink.href = URL.createObjectURL(blob)
        // 触发点击
        document.body.appendChild(downLink)
        downLink.click()
        // 然后移除
        document.body.removeChild(downLink)
      }
      // if (this.fileForm.file != '') {

      // }
    },
    // 下载
    downFileList(row, item) {
      console.log('item', item)
      if (item.raw == undefined) {
        this.downloadList(row, item)
      } else {
        const blob = new Blob([item.raw])
        const downLink = document.createElement('a')
        downLink.download = item.file_name
        downLink.href = URL.createObjectURL(blob)
        // 触发点击
        document.body.appendChild(downLink)
        downLink.click()
        // 然后移除
        document.body.removeChild(downLink)
      }
    },
    downloadList(row, item) {
      const fileForm = {
        exempt_down_file_replace_way_type: '',
        file_id: item.id,
        replace_way_id: row.id
      }
      if (row.replace_cancel_way === '无需清理') {
        fileForm.exempt_down_file_replace_way_type = 'DO_NOT_CLEAN'
      }
      if (row.replace_cancel_way === '其他') {
        fileForm.exempt_down_file_replace_way_type = 'OTHER'
      }
      if (row.replace_cancel_way === '告知承诺') {
        fileForm.exempt_down_file_replace_way_type = 'CLERK_COMMITMENT'
        fileForm.file_id = row.commit_attachment_id
      }
      if (row.replace_cancel_way === '直接取消') {
        fileForm.exempt_down_file_replace_way_type = 'DIRECTLY_CANCEL'
      }
      if (row.replace_cancel_way === '自行调查') {
        fileForm.exempt_down_file_replace_way_type = 'DEPT_SURVEY'
      }
      if (row.replace_cancel_way === '数据共享') {
        fileForm.exempt_down_file_replace_way_type = 'DATA_SHARING'
      }
      if (row.replace_cancel_way === '部门间协查') {
        fileForm.exempt_down_file_replace_way_type = 'INVESTIGATION'
      }

      if (row.replace_cancel_way === '转化为电子证照') {
        // 是否有归档文件
        if (row.archiving_id != '' && row.archiving_id != null) {
          fileForm.exempt_down_file_replace_way_type = 'LICENSE_ATTACHMENT_ARCHIVIN'
        } else {
          fileForm.exempt_down_file_replace_way_type = 'LICENSE'
        }
      }
      downloadFile({ id: item.id }).then(res => {
        if (row.replace_cancel_way != '告知承诺') {
          dataURLtoDownload(res.data.file_data_base64, res.data.file_name)
        } else {
          dataURLtoDownload(res.data.file_data_base64, res.data.file_name)
        }
      })
    },
    // 删除文件数据
    delectRow(row, index) {
      row.attachment_list.splice(index, 1)
      if (row.attachment_list.length === 0) {
        row.verification_and_inspection = false
        this.holdUp()
      }
    },
    // 下载归档文件
    downArchivingFile(row, index) {
      row.archiving_id_list.forEach(e => {
        const fileForm = {
          exempt_down_file_replace_way_type: 'LICENSE_ATTACHMENT_ARCHIVING',
          file_id: e.archiving_id,
          replace_way_id: row.id
        }
        download(fileForm).then(res => {
          if (res.data != null) {
            dataURLtoDownload(res.data, e.archiving_name)
          }
        })
      })
    },
    // 上传文件前判断
    handleChange(file, fileList) {
      const fileName = getFileType(file.name).fileName
      const isLt10m = file.size / (1024 * 1024) < 1
      if (!isLt10m) {
        this.$message.error('文件大小不能超过1m！')
      } else {
        if (fileName.indexOf('http') != -1 || validPrefix(fileName)) {
          this.$message.error('文件名开头包含特殊符号！')
        } else {
          if (!getIsWhitelist(file.name, this.whitelist)) {
            this.$message.error(`请重新选择以${this.whitelist.join(',')}为后缀名的文件！`)
          } else {
            this.fileForm.fileName = file.name
            this.fileForm.file = file.raw
          }
        }
      }
    },
    // 查看证照前选择个人证照或者企业证照跳转对应链接
    showLicenceDialog() {
      this.licencedialogVisible = true
    },
    licensedialogVisible() {
      this.licencedialogVisible = false
    },
    licensedialogSumbit(code) {
      let data = JSON.parse(JSON.stringify(this.selectrow))
      data.holder_type = code
      this.licencedialogVisible = false
      this.getView(data)
    },
    // 获取证照数据
    getView(row) {
      if (row.auth_code_source === 'HFW') {
        let parmas = {}
        if (this.isOpenDialog) {
          parmas = {
            auth_code: row.auth_code,
            replace_way_id: row.id,
            holder_type: row.holder_type
          }
        } else {
          parmas = {
            auth_code: row.auth_code,
            replace_way_id: row.id
          }
        }

        const serial_number = row.serial_number
        getView(parmas, serial_number)
          .then(res => {
            console.log(res)
            this.isOpenDialog = false
            if (res.data != null && res.meta.code === '200') {
              row.verification_and_inspection = true
              // console.log(row)
              window.open(res.data)

              // this.tableData.content[this.selectFileIndex].verification_and_inspection = true
            } else {
              this.$message({
                message: res.meta.message,
                type: 'warning'
              })
            }
          })
          .catch(() => {
            this.isOpenDialog = false
          })
      } else {
        // this.getYssYstView(row)
        let param = {}
        if (this.isOpenDialog) {
          param = {
            auth_token: row.auth_code,
            replace_way_id: row.id,
            serial_number: row.serial_number,
            auth_code_source: row.auth_code_source,
            holder_type: row.holder_type
          }
        } else {
          param = {
            auth_token: row.auth_code,
            replace_way_id: row.id,
            serial_number: row.serial_number,
            auth_code_source: row.auth_code_source
          }
        }

        row.verification_and_inspection = true
        this.getViewPdf(row)
        const outlink = this.$router.resolve({ name: 'outLink', query: { data: JSON.stringify(param) } })
        console.log('outlink.href', outlink.href)
        window.open(outlink.href, '_blank')
      }
    },
    // 查询粤商通
    getYssYstView(row) {
      const serial_number = row.serial_number
      const param = {
        auth_token: row.auth_code,
        replace_way_id: row.id,
        serial_number: row.serial_number,
        auth_code_source: row.auth_code_source
      }
      getYssYstView(serial_number, param).then(res => {
        if (res.data != null && res.meta.code === '200') {
          // console.log(res.data.data_item)
          this.fileImgData = res.data.file
          // console.log('this.fileImgData',this.fileImgData)
          dataURLtoDownload(this.fileImgData.file_data, this.fileImgData.file_name)
        } else {
          this.$message({
            message: res.meta.message,
            type: 'error'
          })
        }
      })
    },
    // getView(row) {
    //   const parmas = {
    //     auth_code: row.auth_code,
    //     replace_way_id: row.id
    //   }
    //   const serial_number = row.serial_number
    //   getView(parmas, serial_number).then(res => {
    //     // console.log(res)
    //     if (res.data != null && res.meta.code === '200') {
    //       window.open(res.data)
    //       row.verification_and_inspection = true
    //     } else {
    //       this.$message({
    //         message: res.meta.message,
    //         type: 'warning'
    //       })
    //     }
    //   })
    // },
    cancle() {
      this.fileForm.fileName = ''
      this.fileForm.file = ''
      this.dialogVisible1 = false
    },
    back() {
      this.$router.push({
        name: 'freeServiceitem1'
      })
    },
    getAssistOrg(id) {
      const parmas = {
        replace_way_id: id
      }
      getAssistOrg(parmas).then(res => {
        console.log('getAssistOrg', res)
        if (res.meta.code === '200' && res.data != null) {
          this.orgList = res.data
        } else {
          this.$message({
            message: res.meta.message,
            type: 'error'
          })
        }
      })
    },
    // 暂存接口
    holdUp() {
      console.log('this.$store.state.user', JSON.parse(this.$store.state.user.organization))
      console.log(sessionStorage.getItem('accountInfo'))
      const accountInfo = JSON.parse(sessionStorage.getItem('accountInfo'))
      const userdata = JSON.parse(this.$store.state.user.organization)
      this.freeServiceData.to_user_name = accountInfo.name
      this.freeServiceData.to_user_account = accountInfo.account
      this.freeServiceData.to_user_org = accountInfo.department
      this.freeServiceData.to_user_org_code = userdata.credit_code
      this.freeServiceData.process_result = 'TEMPORARY_STORAGE'
      this.loading = true
      // 安全整改去除多余参数
      this.clearExcessiveParameter(this.freeServiceData)
      console.log('this.freeServiceData', this.freeServiceData)
      return holdUp(this.freeServiceData)
        .then(res => {
          if (res.meta.code === '200' && res.data !== '') {
            this.$message({
              message: '暂存成功',
              type: 'success'
            })
            this.tableData.content = []
            this.ininData()
          } else {
            this.$message({
              message: res.meta.message,
              type: 'warning'
            })
          }
        })
        .catch(() => {
          this.loading = false
        })
    },
    // 清除多余参数，适应安全整改需要
    clearExcessiveParameter(data) {
      data.proof_list_bo_list.forEach((e, index) => {
        // 免证办无需清理表
        if (e.do_not_clean_detail_list != null && e.do_not_clean_detail_list.length != 0) {
          //  e.replace_cancel_way = '无需清理'
          e.do_not_clean_detail_list.forEach(e1 => {
            delete e1.proof_catalog_name
            delete e1.material_name
          })
        }
        // 免证办直接取消表
        if (e.directly_cancel_detail_list != null && e.directly_cancel_detail_list.length != 0) {
          // e.replace_cancel_way = '直接取消'
          e.directly_cancel_detail_list.forEach(e1 => {
            delete e1.proof_catalog_name
            delete e1.material_name
          })
        }
        // 免证办替代方式之其它表
        if (e.other_detail_list != null && e.other_detail_list.length != 0) {
          // e.replace_cancel_way = '其它表'
          e.other_detail_list.forEach(e1 => {
            delete e1.proof_catalog_name
            delete e1.material_name
          })
        }
        // 免证办材料电子证照清理方式
        if (e.license_detail_list != null && e.license_detail_list.length != 0) {
          //  e.replace_cancel_way = '转化为电子证照'
          e.license_detail_list.forEach(e1 => {
            delete e1.proof_catalog_name
            delete e1.material_name
            delete e1.archiving_id_list
            if (e1.exempt_license_detail_list != null) {
              e1.exempt_license_detail_list.forEach(e2 => {
                delete e1.proof_catalog_name
              })
            }
          })
        }

        // 免证办材料电子证明
        if (e.license_item_detail != null) {
          delete e.license_item_detail.proof_catalog_name
          delete e.license_item_detail.material_name
          delete e.license_item_detail.index
        }

        // 替代方式之人工部门协查表
        if (e.investigation_detail_list != null && e.investigation_detail_list.length != 0) {
          //  e.replace_cancel_way = '部门间协查'
          e.investigation_detail_list.forEach(e1 => {
            delete e1.proof_catalog_name
            delete e1.material_name
            delete e1.audit_result
            delete e1.tableIndex
          })
        }
        // 替代方式之部门自行调查表
        if (e.dept_survey_detail_list != null && e.dept_survey_detail_list.length != 0) {
          //  e.replace_cancel_way = '自行调查'
          e.dept_survey_detail_list.forEach(e1 => {
            delete e1.proof_catalog_name
            delete e1.material_name
          })
        }
        // 替代方式之数据共享-办理详情BO对象
        if (e.dataSharedDetailList != null && e.dataSharedDetailList.length != 0) {
          //  e.replace_cancel_way = '数据共享'
          e.dataSharedDetailList.forEach(e1 => {
            delete e1.proof_catalog_name
            delete e1.material_name
          })
        }
        // 替代方式之告知承诺表-办理详情BO对象
        if (e.commitment_detail_list != null && e.commitment_detail_list.length != 0) {
          //  e.replace_cancel_way = '告知承诺'
          e.commitment_detail_list.forEach(e1 => {
            delete e1.proof_catalog_name
            delete e1.material_name
          })
        }
      })
    },
    // 下载归档文件
    download(row) {
      const fileForm = {
        exempt_down_file_replace_way_type: '',
        file_id: row.attachment_id,
        replace_way_id: row.id
      }
      if (row.replace_cancel_way === '无需清理') {
        fileForm.exempt_down_file_replace_way_type = 'DO_NOT_CLEAN'
      }
      if (row.replace_cancel_way === '其他') {
        fileForm.exempt_down_file_replace_way_type = 'OTHER'
      }
      if (row.replace_cancel_way === '告知承诺') {
        fileForm.exempt_down_file_replace_way_type = 'CLERK_COMMITMENT'
        fileForm.file_id = row.commit_attachment_id
      }
      if (row.replace_cancel_way === '直接取消') {
        fileForm.exempt_down_file_replace_way_type = 'DIRECTLY_CANCEL'
      }
      if (row.replace_cancel_way === '自行调查') {
        fileForm.exempt_down_file_replace_way_type = 'DEPT_SURVEY'
      }
      if (row.replace_cancel_way === '部门间协查') {
        fileForm.exempt_down_file_replace_way_type = 'INVESTIGATION'
      }

      if (row.replace_cancel_way === '转化为电子证照') {
        // 是否有归档文件
        if (row.archiving_id != '' && row.archiving_id != null) {
          fileForm.exempt_down_file_replace_way_type = 'LICENSE_ATTACHMENT_ARCHIVIN'
        } else {
          fileForm.exempt_down_file_replace_way_type = 'LICENSE'
        }
      }
      download(fileForm).then(res => {
        if (row.replace_cancel_way != '告知承诺') {
          dataURLtoDownload(res.data, row.attachment_name)
        } else {
          dataURLtoDownload(res.data, row.commit_attachment_name)
        }
      })
    },
    downloadarchiving() { },
    // 获取免证办数据详情
    getManagerDetail(serial_number) {
      getManagerDetail(serial_number)
        .then(res => {
          if (res.meta.code === '200' && res.data != null) {
            this.loading = false
            this.freeServiceData = Object.assign(this.freeServiceData, res.data)
            console.log('this.freeServiceData', this.freeServiceData)
            // this.tableData.content = [
            //   {proof_catalog_name:'电子证明',
            //   material_name:'电子证明',
            //   replace_cancel_way:'电子证明'
            //   }
            // ]
            //  this.tableData.content = this.freeServiceData.proof_list_bo_list
            this.freeServiceData.proof_list_bo_list.forEach((e, index) => {
              // 免证办无需清理表
              if (e.do_not_clean_detail_list != null && e.do_not_clean_detail_list.length != 0) {
                //  e.replace_cancel_way = '无需清理'
                e.do_not_clean_detail_list.forEach(e1 => {
                  e1.proof_catalog_name = e.proof_catalog_name
                  e1.material_name = e.material_name
                  if (!e1.attachment_list) {
                    e1.attachment_list = []
                  }
                  this.tableData.content.push(e1)
                })
              }
              // 免证办直接取消表
              if (e.directly_cancel_detail_list != null && e.directly_cancel_detail_list.length != 0) {
                // e.replace_cancel_way = '直接取消'
                e.directly_cancel_detail_list.forEach(e1 => {
                  e1.proof_catalog_name = e.proof_catalog_name
                  e1.material_name = e.material_name
                  if (!e1.attachment_list) {
                    e1.attachment_list = []
                  }
                  this.tableData.content.push(e1)
                })
              }
              // 免证办替代方式之其它表
              if (e.other_detail_list != null && e.other_detail_list.length != 0) {
                // e.replace_cancel_way = '其它表'
                e.other_detail_list.forEach(e1 => {
                  e1.proof_catalog_name = e.proof_catalog_name
                  e1.material_name = e.material_name
                  if (!e1.attachment_list) {
                    e1.attachment_list = []
                  }
                  this.tableData.content.push(e1)
                })
              }
              // 免证办材料电子证照清理方式
              if (e.license_detail_list != null && e.license_detail_list.length != 0) {
                //  e.replace_cancel_way = '转化为电子证照'
                e.license_detail_list.forEach(e1 => {
                  e1.proof_catalog_name = e.proof_catalog_name
                  e1.material_name = e.material_name
                  e1.archiving_id_list = []
                  if (e1.exempt_license_detail_list != null) {
                    e1.exempt_license_detail_list.forEach(e2 => {
                      e1.proof_catalog_name = e.proof_catalog_name
                      if (e2.archiving_id != null) {
                        e1.archiving_id_list.push(e2)
                      }
                    })
                  }
                  if (!e1.attachment_list) {
                    e1.attachment_list = []
                  }
                  this.tableData.content.push(e1)
                })
              }

              // 免证办材料电子证明
              if (e.license_item_detail != null) {
                //  e.replace_cancel_way = '转化为电子证照'
                // e.license_item_detail.forEach(e1 => {
                //   e1.proof_catalog_name = e.proof_catalog_name
                //   e1.material_name = e.material_name
                //   e1.replace_cancel_way = e.replace_cancel_way

                // })
                e.license_item_detail.proof_catalog_name = e.proof_catalog_name
                e.license_item_detail.material_name = e.material_name
                e.license_item_detail.index = index
                // e.license_item_detail.attachment_list = []
                // e.license_item_detail.verification_and_inspection = e.license_item_detail.apply_license
                this.tableData.content.push(e.license_item_detail)
              }

              // 替代方式之人工部门协查表
              if (e.investigation_detail_list != null && e.investigation_detail_list.length != 0) {
                //  e.replace_cancel_way = '部门间协查'
                e.investigation_detail_list.forEach(e1 => {
                  e1.proof_catalog_name = e.proof_catalog_name
                  e1.material_name = e.material_name
                  e1.audit_result = ''
                  if (!e1.attachment_list) {
                    e1.attachment_list = []
                  }
                  this.tableData.content.push(e1)
                  e1.tableIndex = e.index

                  // e1.config_assist_user = false
                  this.investigation_detail_list.push(e1)
                  // if (e1.assist_serial_number != null && e1.assist_serial_number != undefined) {
                  //   this.getViewBySerialNumber(e1).then(() => {})
                  //   // e1.audit_result = 'WAIT'
                  // }
                })
              }
              // 替代方式之部门自行调查表
              if (e.dept_survey_detail_list != null && e.dept_survey_detail_list.length != 0) {
                //  e.replace_cancel_way = '自行调查'
                e.dept_survey_detail_list.forEach(e1 => {
                  e1.proof_catalog_name = e.proof_catalog_name
                  e1.material_name = e.material_name
                  if (!e1.attachment_list) {
                    e1.attachment_list = []
                  }
                  this.tableData.content.push(e1)
                })
              }
              // 替代方式之数据共享-办理详情BO对象
              if (e.dataSharedDetailList != null && e.dataSharedDetailList.length != 0) {
                //  e.replace_cancel_way = '数据共享'
                e.dataSharedDetailList.forEach(e1 => {
                  e1.proof_catalog_name = e.proof_catalog_name
                  e1.material_name = e.material_name
                  if (!e1.attachment_list) {
                    e1.attachment_list = []
                  }
                  // e1.data_shared_query_last_result = e.data_shared_query_last_result
                  this.tableData.content.push(e1)
                })
              }
              // 替代方式之告知承诺表-办理详情BO对象
              if (e.commitment_detail_list != null && e.commitment_detail_list.length != 0) {
                //  e.replace_cancel_way = '告知承诺'
                e.commitment_detail_list.forEach(e1 => {
                  e1.proof_catalog_name = e.proof_catalog_name
                  e1.material_name = e.material_name
                  if (!e1.attachment_list) {
                    e1.attachment_list = []
                  }
                  this.tableData.content.push(e1)
                })
              }
            })
            console.log('this.tableData.content', this.tableData.content)
            this.isLegalPerson = this.freeServiceData.exempt_certificates_type !== 'NATURAL_PERSON'
            this.$refs.fileDialog.setData(this.tableData.content)

            console.log('this.isLegalPerson', this.isLegalPerson)
            if (this.freeServiceData.identity_type != null) {
              this.to_user_id_type_name = this.exemptIdentityTypeList.filter(i => i.value === this.freeServiceData.identity_type)[0].label
              this.freeServiceData.to_user_id_type_name = this.to_user_id_type_name
            }

            console.log('this.freeServiceData.identity_type', this.freeServiceData.identity_type, 'this.exemptIdentityTypeList', this.exemptIdentityTypeList)
            this.identity_type_name = this.exemptIdentityTypeList.filter(i => i.value == this.freeServiceData.identity_type)[0].label
            // console.log('this.identity_type_name',this.identity_type_name)
            this.freeServiceData.identity_type_name = this.identity_type_name
            this.biz_org_identity_type_name = this.exemptIdentityTypeList.filter(i => i.value == this.freeServiceData.biz_org_identity_type)[0].label
            // console.log('this.freeServiceData.identity_type_name', this.identity_type_name, 'this.exemptIdentityTypeList', this.exemptIdentityTypeList)
            this.freeServiceData.biz_org_identity_type_name = this.biz_org_identity_type_name
          } else {
            this.$message({
              message: res.meta.message,
              type: 'error'
            })
          }
        })
        .catch(() => {
          this.loading = false
        })
        .then(() => {
          if (this.investigation_detail_list.length != 0) {
            this.investigation_detail_list.forEach(async e1 => {
              if (e1.assist_serial_number != null && e1.assist_serial_number != undefined) {
                // await this.getViewBySerialNumber(e1)
                const assist_serial_number = e1.assist_serial_number
                await this.getViewBySerialNumber(assist_serial_number, e1).then(res => { })
                // e1.audit_result = 'WAIT'
              }
            })
          }
        })
    },
    uploadFile(raw) {
      const data = new FormData()
      data.append('file', raw)
      return new Promise((resolve, reject) => {
        uploadFile(data)
          .then(res => {
            resolve(res.meta.code)
            if (res.data != null && res.meta.code === '200') {
              this.tableData.content[this.selectFileIndex].verification_and_inspection = true
              this.holdUp()
              this.tableData.content[this.selectFileIndex].attachment_id = res.data.sessionId
              this.tableData.content[this.selectFileIndex].attachment_name = this.tableData.content[this.selectFileIndex].attachment_name
              console.log('this.tableData.content[this.selectFileIndex]', this.tableData.content[this.selectFileIndex])
              this.tableData.content[this.selectFileIndex].attachment_list.push({
                raw: this.fileForm.file,
                file_name: this.fileForm.fileName,
                id: res.data.sessionId,
                item_code: this.tableData.content[this.selectFileIndex].item_code,
                proof_catalog_code: this.tableData.content[this.selectFileIndex].proof_catalog_code,
                replace_cancel_way: this.tableData.content[this.selectFileIndex].replace_cancel_way,
                replace_way_id: this.tableData.content[this.selectFileIndex].id,
                is_upload_file: true
              })
            } else {
              this.$message({
                message: res.meta.message,
                type: 'error'
              })
            }
            // attachment_id: null
            // attachment_name: null
          })
          .catch(err => {
            reject(err)
          })
      })
    },
    // 点击下一步 先暂存再进入下一步
    nextTab() {
      this.holdUp().then(() => {
        // 去除多余参数
        this.clearExcessiveParameter(this.freeServiceData)
        let isAllcheck = '1' // 0 未全部核验 1 全部核验
        this.tableData.content.forEach(e => {
          if (!e.verification_and_inspection) {
            isAllcheck = '0'
          }
        })
        this.$router.push({
          name: 'handlingOpinions',
          query: {
            isAllcheck: isAllcheck,
            isLegalPerson: this.radio,
            isDetail: this.isDetail,
            serial_number: this.freeServiceData.serial_number,
            data: JSON.stringify(this.freeServiceData)
          }
        })
        console.log('JSON.stringify(this.freeServiceData)', this.freeServiceData)
      })
    },
    query() { },
    goProofFile() { },
    cellClick(row, column, cell, event) {
      console.log(row, column, cell, event)
    },
    allcheck(flag) {
      if (flag === true) {
        this.tableData.content.forEach(e => {
          e.archivedFile = true
        })
      } else {
        this.tableData.content.forEach(e => {
          e.archivedFile = false
        })
      }
    },
    deptCancle() {
      this.dialogVisible2 = false
    },
    deptSumbit(data) {
      this.getAssist(data, this.freeServiceData.serial_number)
      this.dialogVisible2 = false
    },
    deptCancle1() {
      this.dialogVisible3 = false
    },
    deptSumbit1(data) {
      console.log(data, this.freeServiceData.serial_number)
      this.getAssist(data, this.freeServiceData.serial_number)
      this.dialogVisible3 = false
    },
    deptCancle2() {
      this.dialogVisible4 = false
    },
    datashareingDialogCancle() {
      this.dialogVisible5 = false
    },
    electronicCertificationDialogCancle() {
      this.dialogVisible6 = false
    },
    dataEditDialogCancle() {
      this.dialogVisible7 = false
    },
    updateinit() {
      this.ininData()
      this.dialogVisible7 = false
    },
    deptSumbit2(data) {
      this.fileData = data
      this.promiseArray = []
      if (data != undefined) {
        this.dialogVisible4 = false
        data.forEach(async e => {
          const data = {
            replace_way_id: e.exempt_license_id,
            license_code: e.license_code
          }
          // await new Promise((resolve, reject) => {
          await this.attachmentArchiving(data, e.serial_number, e).then(res => {
            this.promiseArray.push(res)
          })
          // promiseArray.push(this.attachmentArchiving(data, e.serial_number))
        })
      } else {
        this.$message({
          message: '请选择归档文件',
          type: 'warning'
        })
      }
    },
    attachmentArchiving(data, serial_number, item) {
      return new Promise((resolve, reject) => {
        attachmentArchiving(data, serial_number)
          .then(res => {
            if (res.meta.code === '200') {
              // this.$message({
              //   message: '归档成功',
              //   type: 'success'
              // })
            } else {
              this.$message({
                message: res.meta.message,
                type: 'error'
              })
            }
            resolve(true)
          })
          .catch(() => {
            this.$message({
              message: item.material_name + '归档失败',
              type: 'warning'
            })
          })
      })
    },
    getDataSharedData() {
      const parmas = {
        serial_number: this.$route.query.serial_number
      }
      getDataSharedData(parmas).then(res => {
        if (res.data != null && res.meta.code === '200') {
          this.dataList = res.data
          if (this.dataList.searchDataList.length === 0) {
            this.dataList.errormes = '暂无数据'
          }
        } else if (res.data === null && res.meta.code === '200') {
          this.dataList.errormes = '暂无数据'
        } else {
          this.dataList.errormes = '接口报错，服务异常'
        }
      })
    },
    getViewBySerialNumber(assist_serial_number, e1) {
      // this.freeServiceData.serial_number
      return getViewBySerialNumber(assist_serial_number).then(res => {
        if (res.meta.code === '200' && res.data != null) {
          // console.log(res)
          e1.audit_result = res.data.audit_result
          e1.assist_id = res.data.id
          // console.log('row', e1.audit_result)
          // this.tableData.content[e1.tableIndex].audit_result = e1.audit_result
          // console.log(this.tableData.content)
          // this.$set(this.tableData.content[e1.tableIndex], 'audit_result', e1.audit_result)
        } else {
          this.$message({
            message: res.meta.message,
            type: 'error'
          })
        }
      })
    },
    getAssist(params, serial_number) {
      getAssist(params, serial_number).then(res => {
        if (res.data != null && res.meta.code === '200') {
          this.$message({
            message: '申请协查成功',
            type: 'success'
          })
          // this.ininData()
          this.selectionRow.assist_serial_number = res.data
          this.getViewBySerialNumber(res.data, this.selectionRow)
        } else {
          this.$message({
            message: res.meta.message,
            type: 'error'
          })
        }
      })
    },

    issueLicenseApply(data) {
      issueLicenseApply(data).then(res => {
        // console.log(res)
        if (res.meta.code === '200') {
          this.$message({
            message: '开具成功',
            type: 'success'
          })
          this.dialogVisible6 = false
          this.tableData.content = []
          this.ininData()
          // this.selectrow.verification_and_inspection = true
        } else {
          this.$message({
            message: res.meta.message,
            type: 'warning'
          })
        }
      })
    },
    showfileDialog() {
      // console.log('freeServiceData',this.freeServiceData)
      let data = ''
      for (let i = 0; i < this.tableData.content.length; i++) {
        if (this.tableData.content[i].auth_code_source === 'HFW') {
          this.dialogVisible4 = true
          this.isHFW = true
          break
        } else if (this.tableData.content[i].auth_code_source === 'YST' || this.tableData.content[i].auth_code_source === 'YSS') {
          data = this.tableData.content[i]
        }
      }
      if (!this.isHFW) {
        this.getViewPdf(data)
        // this.getYssYstView(data)
      }
    },
    getViewPdf(row) {
      const serial_number = row.serial_number
      let param = {}
      if (this.isOpenDialog) {
        param = {
          auth_token: row.auth_code,
          replace_way_id: row.id,
          serial_number: row.serial_number,
          auth_code_source: row.auth_code_source,
          holder_type: row.holder_type
        }
      } else {
        param = {
          auth_token: row.auth_code,
          replace_way_id: row.id,
          serial_number: row.serial_number,
          auth_code_source: row.auth_code_source
        }
      }
      getViewPdf(serial_number, param)
        .then(res => {
          this.isOpenDialog = false
          if (res.data != null && res.meta.code === '200') {
            // console.log(res.data.data_item)
            this.fileImgData = res.data.file
            if (row.attachment_list.length < 5) {
              row.attachment_list.push({
                file_name: this.fileImgData.file_name,
                id: this.fileImgData.attachment_id,
                item_code: row.item_code,
                proof_catalog_code: row.proof_catalog_code,
                replace_cancel_way: row.replace_cancel_way,
                replace_way_id: row.id,
                is_upload_file: false
              })
              console.log('row', row)
              this.selectrow.attachment_list = row.attachment_list
              this.selectrow.verification_and_inspection = true
              this.holdUp()
            } else {
              // this.$message({
              //   message: '单个材料文件不超过5份',
              //   type: 'warning'
              // })
            }

            // console.log('this.fileImgData',this.fileImgData)
            // dataURLtoDownload(this.fileImgData.file_data, this.fileImgData.file_name)
          } else {
            this.$message({
              message: res.meta.message,
              type: 'error'
            })
          }
        })
        .catch(() => {
          this.isOpenDialog = false
        })
    },
    toShowTip(row, $index, type) {
      this.selectrow = row
      this.selectIndex = $index
      this.selectType = type
      if (row.attachment_list.length > 5 || row.attachment_list.length === 5) {
        this.dialogVisible9 = true
      } else {
        if (type === '数据共享') {
          this.getDataShar(row, $index)
        } else if (type === '部门间协查') {
          this.getInvestigationDetail(row)
        } else if (type === '转化为电子证照') {
          console.log(row)
          // holder_type 等于 个人和机构 和其他 或者为空和auth_code_source为JX_GOV_SERVICE_CODE 展示弹窗
          if (row.holder_type !== null && row.holder_type !== 'PERSON_AND_ORG' && row.holder_type !== 'OTHER') {
            // delete row.holder_type
            this.isOpenDialog = false
            this.getView(row)
          } else if ((row.holder_type === 'PERSON_AND_ORG' || row.holder_type === 'OTHER') && row.auth_code_source === 'JX_GOV_SERVICE_CODE') {
            this.isOpenDialog = true
            this.showLicenceDialog()
          } else if ((!row.hasOwnProperty('holder_type') || row.holder_type === null) && row.auth_code_source === 'JX_GOV_SERVICE_CODE') {
            this.isOpenDialog = true
            this.showLicenceDialog()
          } else {
            // delete row.holder_type
            this.isOpenDialog = false
            this.getView(row)
          }
        } else if (type === '电子证明') {
          this.getLicenseViewUrl(row)
        }
      }
    },
    tipNext() {
      this.dialogVisible9 = false
      if (this.selectType === '数据共享') {
        this.getDataShar(this.selectrow, this.selectIndex)
      } else if (this.selectType === '部门间协查') {
        this.getInvestigationDetail(this.selectrow, this.selectIndex)
      } else if (this.selectType === '转化为电子证照') {
        if (this.selectrow.holder_type !== null && this.selectrow.holder_type !== 'PERSON_AND_ORG' && this.selectrow.holder_type !== 'OTHER') {
          // delete this.selectrow.holder_type
          this.isOpenDialog = false
          this.getView(this.selectrow, this.selectIndex)
        } else if (this.selectrow.holder_type === 'PERSON_AND_ORG' || this.selectrow.holder_type === 'OTHER') {
          this.isOpenDialog = true
          this.showLicenceDialog()
        }
      } else if (this.selectType === '电子证明') {
        this.getLicenseViewUrl(this.selectrow, this.selectIndex)
      }
    },
    tipCancle() {
      this.dialogVisible9 = false
    },
    getDataShar(row, $index) {
      this.selectrow = row
      const data = {
        serial_number: row.serial_number,
        proof_list_id: row.proof_list_id
      }
      this.proof_list_id = row.proof_list_id
      console.log('getDataShar', row)
      this.limtCount = row.search_count_limit
      getDataSharedConfig(data).then(res => {
        console.log('getDataSharedConfig', res)
        if (res.meta.code === '200' && res.data !== null) {
          this.dialogVisible8 = true
          this.dataSharingQueryData = res.data
        } else {
          this.dataSharingQueryData = []
        }
      })
    },
    downDataShar(row) {
      console.log('downDataShar', row)
      const data = {
        serial_number: row.serial_number,
        proof_list_id: row.proof_list_id
      }
      getDataSharedDataArchivist(data).then(res => {
        console.log(res)
        // this.ininData()
        if (res.data !== null && res.meta.code === '200') {
          dataURLtoDownload(res.data.fileDataByBase64, res.data.file_name)
        } else {
          this.$message({
            message: res.meta.message,
            type: 'error'
          })
        }
      })
    },
    dataSharingQueryCancle() {
      this.dialogVisible8 = false
    },
    dataSharingQuerySumbit(num) {
      console.log('num', num)
      this.dialogVisible8 = false
      const data = {
        proof_list_id: this.proof_list_id,
        serial_number: this.$route.query.serial_number,
        search_condition_list: this.dataSharingQueryData
      }
      if (num === 0 || num < 0) {
        data.search_limit = false
      }

      this.selectrow.data_shared_query = JSON.stringify(this.dataSharingQueryData)

      getDataSharedData(data).then(res => {
        const data = {
          serial_number: this.$route.query.serial_number,
          proof_list_id: this.proof_list_id
        }

        // this.tableData.content = []
        // this.ininData()
        if (res.meta.code === '200' && res.data != null) {
          this.dialogVisible5 = true
          this.selectrow.verification_and_inspection = true
          this.dataList = res.data
          this.selectrow.search_count_limit--
          console.log('this.selectrow', this.selectrow)
          console.log('this.dataList.length',this.dataList.searchDataList.length ,res)
          if (this.dataList.searchDataList.length !== 0) {
            getDataSharedDataArchivist(data).then(res => {
              console.log('getDataSharedDataArchivist', res)
              if (res.meta.code === '200' && res.data) {
                if (this.selectrow.attachment_list.length < 5) {
                  this.selectrow.attachment_list.push({
                    file_name: res.data.file_name,
                    id: res.data.id,
                    item_code: this.selectrow.item_code,
                    proof_catalog_code: this.selectrow.proof_catalog_code,
                    replace_cancel_way: this.selectrow.replace_cancel_way,
                    replace_way_id: this.selectrow.id,
                    is_upload_file: false
                  })

                  this.holdUp()
                } else {
                  // this.$message({
                  //   message: '单个材料文件不超过5份',
                  //   type: 'warning'
                  // })
                  this.holdUp()
                }
              } else {
                this.$message({
                  type: 'error',
                  message: res.meta.message
                })
              }
            })
          }
        } else {
          this.selectrow.verification_and_inspection = false
          this.$message({
            message: res.meta.message,
            type: 'error'
          })
          this.dataList = []
        }
      })
    },
    getElectronicCertification(row, index) {
      this.dialogVisible6 = true
      console.log('index', index)
      this.tableIndex = row.index
      this.selectrow = row
    },
    getElectronicCertificationMes(row) {
      this.dialogVisible6 = true
      row.verification_and_inspection = true
    },
    getLicenseViewUrl(row) {
      getLicenseViewUrl(row.auth_code).then(res => {
        if (res.data != null && res.meta.code === '200') {
          row.verification_and_inspection = true
          let item = {
            replace_way_id: row.id,
            auth_code: row.auth_code
          }
          console.log('row', row)
          getLicenseItemArchivist(item).then(res => {
            if (res.data != null && res.meta.code === '200') {
              if (row.attachment_list.length < 5) {
                row.attachment_list.push({
                  file_name: res.data.file_name,
                  id: res.data.id,
                  item_code: row.item_code,
                  proof_catalog_code: row.proof_catalog_code,
                  replace_cancel_way: row.replace_cancel_way,
                  replace_way_id: row.id,
                  is_upload_file: false
                })
                this.holdUp()
              } else {
                // this.$message({
                //   message: '单个材料文件不超过5份',
                //   type: 'warning'
                // })
              }
            }
          })
          window.open(res.data)
        } else {
          this.$message({
            message: res.meta.message,
            type: 'error'
          })
        }
      })
    },
    // 编辑服务对象信息
    editData() {
      let data = ''
      for (let i = 0; i < this.tableData.content.length; i++) {
        if (this.tableData.content[i].auth_code_source === 'HFW') {
          data = this.tableData.content[i]
        } else if (this.tableData.content[i].auth_code_source === 'YST' || this.tableData.content[i].auth_code_source === 'YSS') {
          data = this.tableData.content[i]
        }
      }
      console.log('this.tableData.content', this.tableData.content)
      this.updateData = data
      this.dialogVisible7 = true
    }
  }
}
</script>

<style scoped>
.box-card2 /deep/.el-button+.el-button {
  /* margin-left: 0px; */
}

.cardtitle {
  color: #409eff;
}

.cardtitle img {
  width: 16px;
  margin-left: 5px;
  margin-bottom: 2px;
}

.mestitle {
  color: #666666;
}

.mescontent {
  color: #333333;
  font-weight: 550;
}

/* .minddle-form {
  width: 80%;
  display: inline-block;
} */
.el-check-form {
  /* text-align: center; */
}

.el-card {
  border: 0px;
  /* text-emphasis: none; */
}

.el-card.is-always-shadow {
  box-shadow: 0 0 0 0;
}

.box-card1 /deep/.el-card__body {
  padding-bottom: 0px;
}

.box-card2 /deep/.el-card__header {
  padding-top: 0px;
  text-align: left;
}

.box-card3 /deep/.el-card__header {
  text-align: left;
}

.onekeytest {
  height: 200px;
  line-height: 200px;
  text-align: center;
}

.checkbtn {
  display: inline-block;
  width: 140px;
  height: 34px;
  background: inherit;
  background-color: rgba(22, 155, 213, 1);
  border: none;
  border-radius: 5px;
  text-align: center;
  line-height: 34px;
  color: #fff;
}

.list-li {
  max-height: 50px;
  border-bottom: 1px dashed;
  margin: 10px 0;
}

.card-wrap {
  /* text-align: center; */
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
}

.card-wrap img {
  margin: 10px 0;
}

.card-dialog /deep/ .el-dialog__header {
  background: #4b87c5;
  text-align: left;
}

.card-dialog /deep/ .el-dialog__title {
  color: #fff;
  font-size: 14px;
}

.foot-btn {
  height: 100px;
  text-align: center;
  line-height: 100px;
}

.content-wrapper /deep/.el-upload__input {
  display: none;
}

.word {
  color: #409eff;
}

.card-dialog /deep/ .el-dialog__body {
  padding: 0;
}

.deptform {
  width: 100%;
}

.box-card2 /deep/ .el-popover {
  padding: 20px;
}

.padding-left-20 {
  padding-left: 20px;
}

.greenWord {
  color: #19be6b;
}

.yellowWord {
  color: #ff9900;
}

.redWord {
  color: #f56c6c;
}

.tip i {
  color: #e6a23c;
}

.info-wrap {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
  padding-right: 10px;
}

.info-wrap-first {
  padding-right: 0px;
}

.info-wrap img {
  width: 35px;
  height: 35px;
  margin-right: 10px;
}

.info-wrap-title {
  display: flex;
  align-items: center;
}

.info-wrap-title .icon {
  height: 20px;
}

.info-title {
  font-size: 20px;
  color: #333333;
}

.content-header-title {
  display: flex;
  justify-content: space-between;
  align-content: center;
}

.content-header-title i {
  cursor: pointer;
}

.breakword {
  white-space: break-spaces;
}

.circle {
  height: 5px;
  width: 5px;
  border-radius: 50%;
  background: #1f9e73;
  margin-right: 5px;
}

.margin-bottom-10 {
  margin-bottom: 10px;
}
</style>
<style>
.popoverwrap {
  padding: 20px 40px;
}

.popoverwrap .el-popover__title {
  font-weight: 600;
}
</style>
