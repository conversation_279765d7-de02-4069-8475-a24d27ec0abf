<template>
  <div id="deptDialog">
    <el-dialog
      title="警告提示"
      :visible.sync="dialogVisible"
      width="30%"
      class="card-dialog"
      :center="true"
      :show-close="true"
      :before-close="cancle"
      v-if="this.limtCount===0||this.limtCount<0"
    >
      <div class="card-wrap">
        <p class="card-wrap-title">
          <i class="el-icon-warning-outline"></i>
          <span>您的查询次数已达上限</span>
        </p>
        <p class="card-wrap-content">点击'查看数据'，可查看上一次数据共享结果</p>
      </div>
      <div slot="footer" class="dialog-footer-left">
        <el-button type="primary" @click="sumbit()">查询</el-button>
        <el-button @click="cancle()">取消</el-button>
      </div>
    </el-dialog>
    <el-dialog
      :title="countTitle"
      :visible.sync="dialogVisible"
      width="40%"
      class="card-dialog"
      :center="true"
      :show-close="true"
      :before-close="cancle"
      v-else
    >
      <div class="card-wrap">
        <el-form ref="form" label-width="160px" class="deptform">
          <el-card class="box-card">
            <el-row v-for="(i,key) in dataSharingQueryDataList" :key="key">
              <el-col :xs="24" :sm="12" :md="12" :lg="24">
                <div class="minddle-form">
                  <el-row type="flex" align="middle" justify="center">
                    <el-col :span="24">
                      <el-form-item :label="i.display_name+'：'">
                        <el-input v-model="i.search_value" clearable placeholder="请输入" v-if="i.data_type!=='DATETIME'" />
                        <el-date-picker
                          v-else
                          v-model="i.timeList"
                          type="daterange"
                          style="width:100%"
                          range-separator="至"
                          start-placeholder="开始日期"
                          end-placeholder="结束日期"
                          :disabled="true"
                        ></el-date-picker>
                      </el-form-item>
                    </el-col>
                  </el-row>
                </div>
              </el-col>
            </el-row>
          </el-card>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="sumbit()">查询</el-button>
        <el-button @click="cancle()">取消</el-button>
      </span>
    </el-dialog>
    <!-- 当查询次数为0时 -->
  </div>
</template>

<script>
import { parseTime } from '@/utils/index.js'
export default {
  data() {
    return {
      deptForm: {
        eventName: '',
        dep: ''
      },

      value1: '',
      dataSharingQueryDataList: [],
      dataSharingQueryDataListCppy: [],
      rules: {
        org_code: [{ required: true, message: '请选择协查部门', trigger: 'change' }],
        from_demand: [{ required: true, message: '请输入协查需求描述', trigger: 'change' }]
      },
      countTitle: `查询条件(剩余查询次数：`
      // freeServiceData: {
      //   from_demand: ''
      // }
      // dialogVisible: false
    }
  },
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    dataSharingQueryData: {
      type: Array,
      default: []
    },
    limtCount: {
      type: Number,
      default: 0
    },
  },
  watch: {
    dataSharingQueryData: {
      handler(val, val1) {
        // console.log(val)
        this.dataSharingQueryDataList = val
        this.countTitle = '查询条件(剩余查询次数：'
        this.countTitle = this.countTitle + (this.limtCount !== null ? this.limtCount : '不限') + '次)'
        console.log('dataSharingQueryDataList', this.dataSharingQueryDataList)
        const end = new Date()
        const start = new Date()
        this.dataSharingQueryDataList.forEach(e => {
          if (e.data_type === 'DATETIME') {
            if (e.search_value === '最近一个月') {
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
              e.timeList = [start, end]
            } else if (e.search_value === '最近一周') {
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
              e.timeList = [start, end]
            } else if (e.search_value === '最近三个月') {
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30 * 3)
              e.timeList = [start, end]
            } else if (e.search_value === '最近半年') {
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30 * 6)
              e.timeList = [start, end]
            } else if (e.search_value === '最近一年') {
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30 * 12)
              e.timeList = [start, end]
            } else {
              e.timeList = ''
            }
          }
        })
        this.dataSharingQueryDataListCppy = JSON.parse(JSON.stringify(this.dataSharingQueryDataList))
      }
    }
  },
  mounted() {},

  methods: {
    sumbit() {
      this.dataSharingQueryDataList.forEach(e => {
        if (e.data_type === 'DATETIME') {
          if (e.timeList != '') {
            e.search_value = parseTime(e.timeList[0], '{y}-{m}-{d}')
            e.search_second_value = parseTime(e.timeList[1], '{y}-{m}-{d}')
          }
        }
      })
      this.$emit('dataSharingQuerySumbit', this.limtCount)
    },
    cancle() {
      this.$emit('dataSharingQueryCancle')
    },
    selectChange(row) {
      // console.log(row)
    },
    reset() {
      this.dataSharingQueryDataList = this.dataSharingQueryDataListCppy
    }
  }
}
</script>

<style  scoped>
.cardtitle {
  color: #409eff;
}
.card-dialog /deep/ .el-dialog__header {
  /* background: #4b87c5; */
  text-align: left;
  /* display: none; */
}
.card-dialog /deep/ .el-dialog__body {
  padding: 0;
}
.deptform {
  width: 100%;
}
.el-card.is-always-shadow {
  box-shadow: 0 0 0 0;
}
.card-wrap-title {
  display: flex;
  align-items: center;
  font-size: 20px;
  font-weight: 600;
  margin-top: 20px;
}
.card-wrap-content {
  margin-left: 70px;
}
.card-wrap-title i {
  font-size: 30px;
  color: #ff9800;
  margin: 0 20px;
}
.dialog-footer-left {
  text-align: right;
}
</style>
