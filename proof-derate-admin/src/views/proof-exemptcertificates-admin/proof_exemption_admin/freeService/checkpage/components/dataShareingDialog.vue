<template>
  <div id="dataShareingDialog">
    <el-dialog
      title="查看数据共享结果"
      :visible.sync="dialogVisible"
      width="40%"
      class="card-dialog"
      :center="false"
      :show-close="true"
      :before-close="cancle"
    >
      <div class="wrap">
        <p class="wrap-title">
          <el-divider />
          <!-- <span class="wrap-title-time">查询时间：{{dataList.searchDate}}</span> -->
        </p>
        <div class="wrap-title-img firstTitle">
          <div class="firstTitle-content">
            <img :src="tag" alt />
            <span>查询条件</span>
          </div>
          <span class="wrap-title-time">查询时间：{{ dataList.searchDate }}</span>
        </div>
        <div class="wrap-title-content">
          <p v-for="(index,key) in dataSharingQueryData" :key="key" class>{{ index.display_name }}:{{ index.search_value }}</p>
          <!-- <p class>姓名:xxx</p> -->
        </div>
        <div class="wrap-title-img">
          <img :src="tag" alt />
          <span>查询结果</span>
        </div>
        <div v-if="dataList.searchDataList!=undefined&&dataList.searchDataList.length!=0">
          <div v-for="(i,key) in dataList.searchDataList" :key="key" class="wraplist">
            <div class="wrap-content">
              <p v-for="(k,key1) in i" :key="key1">{{ k.display_name }}：{{ k.display_value }}</p>
            </div>
            <el-divider />
          </div>
        </div>
        <div v-else class="nodata">
          <i class="el-icon-warning" />
          <span style="margin-right:5px">{{ dataList.errormes }}</span>暂无数据
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="cancle()">关闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    dataList: {
      type: Object,
      default: {}
    },
    dataSharingQueryData: {
      type: Array,
      default: function () {
        return []
      }
    }
  },
  data() {
    return {
      tag: require('@/assets/proof-exemptcertificates-admin-images/u149.png')
    }
  },
  mounted() {
    // console.log(this.dataList)
  },

  methods: {
    cancle() {
      this.$emit('dialogCancle')
    }
  }
}
</script>

<style  scoped>
/* .card-dialog /deep/ .el-dialog__header {
  background: #4b87c5;
  text-align: left;
}
.card-dialog /deep/ .el-dialog__title {
  color: #fff;
}
.card-dialog /deep/ .el-dialog__body {
  padding: 0;
} */
.card-dialog /deep/ .el-dialog__body {
  padding: 0 10px;
}
.card-dialog /deep/ .el-dialog__title {
  padding: 0 10px;
}
.wrap {
  height: 500px;
  overflow: auto;
}
.wrap-title {
  position: relative;
  padding: 0px 15px 0px;
}
.wrap-title-time {
  /* position: absolute; */
  right: 15px;
}
.wrap-title-img {
  display: inline-block;
  margin: 10px 14px;
  padding-bottom: 5px;
  border-bottom: 2px solid #382;
}

.wrap-title-img img {
  width: 20px;
  margin-right: 5px;
}
.wraplist {
  padding: 0 15px;
  /* height: 500px; */
  overflow: auto;
}
.wrap-title-content,
.wrap-content {
  display: flex;
  min-height: 90px;
  width: 100%;
  /* flex-direction: column; */
  flex-wrap: wrap;
}
.wrap-title-content {
  min-height: 10px;
  padding: 0 15px;
}
.wrap-title-content p {
  width: 50%;
}
.wrap-content p {
  width: 50%;
}
.wrap-content-left {
  flex: 1;
  /* padding: 0 15px; */
}
.wrap-content-right {
  flex: 1;
}
.el-divider {
  margin: 0;
  margin-top: 10px;
  margin-bottom: 10px;
}
.card-dialog /deep/ .el-dialog__footer {
  text-align: center;
}
.nodata {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 100px;
}
.firstTitle {
  display: flex;
  justify-content: space-between;
  border: 0;
}
.firstTitle-content {
  border-bottom: 2px solid #382;
  padding-bottom: 5px;
}
</style>
