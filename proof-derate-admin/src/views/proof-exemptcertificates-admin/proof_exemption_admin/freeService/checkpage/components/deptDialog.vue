<template>
  <div id="deptDialog">
    <el-dialog
      title="发起部门间协查"
      :visible.sync="dialogVisible"
      width="40%"
      class="card-dialog"
      :center="true"
      :show-close="false"
      :before-close="cancle"
    >
      <div class="card-wrap">
        <el-form ref="form" :model="freeServiceData" label-width="120px" class="deptform" :rules="rules">
          <el-card class="box-card">
            <el-row>
              <el-col :xs="24" :sm="12" :md="12" :lg="24">
                <div class="minddle-form">
                  <el-row align="middle">
                    <el-col :xs="24" :sm="12" :md="12" :lg="12">
                      <el-form-item label="办事人">
                        <el-input v-model="freeServiceData.handle_affairs_name" clearable disabled placeholder="请输入办事人" />
                      </el-form-item>
                    </el-col>
                    <el-col :xs="24" :sm="12" :md="12" :lg="12">
                      <el-form-item label="证件类型">
                        <el-input v-model="freeServiceData.identity_type_name" clearable disabled placeholder="请输入证件类型" />
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row align="middle">
                    <el-col :xs="24" :sm="12" :md="12" :lg="12">
                      <el-form-item label="证件号码">
                        <el-input v-model="freeServiceData.identity_number" disabled clearable placeholder="请输入证件号码" />
                      </el-form-item>
                    </el-col>
                    <el-col :xs="24" :sm="12" :md="12" :lg="12">
                      <el-form-item label="事项名称">
                        <el-input v-model="freeServiceData.item_name" disabled clearable placeholder="请输入事项名称" />
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row align="middle">
                    <el-col :xs="24" :sm="12" :md="12" :lg="12">
                      <el-form-item label="材料名称">
                        <el-input v-model="selectRow.material_name" disabled clearable placeholder="请输入材料名称" />
                      </el-form-item>
                    </el-col>
                    <el-col :xs="24" :sm="12" :md="12" :lg="12">
                      <el-form-item label="协查部门" prop="org_code">
                        <el-select v-model="freeServiceData.org_code" placeholder="选择协查部门" class="select" @change="selectChange">
                          <el-option :label="i.label" :value="i.value" v-for="(i,index) in orgList" :key="index"></el-option>
                          <!-- <el-option label="部门二" value="beijing"></el-option> -->
                        </el-select>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row align="middle">
                    <el-col :xs="24" :sm="12" :md="12" :lg="24">
                      <el-form-item label="协查需求描述" prop="from_demand">
                        <el-input type="textarea" :rows="4" v-model="freeServiceData.from_demand" clearable placeholder="请输入协查需求描述" />
                      </el-form-item>
                    </el-col>
                  </el-row>
                </div>
              </el-col>
            </el-row>
          </el-card>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="sumbit()">提交</el-button>
        <el-button @click="cancle()">返回</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  data() {
    return {
      deptForm: {
        eventName: '',
        dep: ''
      },
      rules: {
        org_code: [{ required: true, message: '请选择协查部门', trigger: 'change' }],
        from_demand: [{ required: true, message: '请输入协查需求描述', trigger: 'change' }]
      }
      // freeServiceData: {
      //   from_demand: ''
      // }
      // dialogVisible: false
    }
  },
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    freeServiceData: {
      type: Object,
      default: {}
    },
    orgList: {
      type: Array,
      default: []
    },
    selectRow: {
      type: Object,
      default: {}
    }
  },
  mounted() {},

  methods: {
    sumbit() {
      this.$refs.form.validate(valid => {
        const org = this.orgList.filter(i => i.value === this.freeServiceData.org_code)
        const parmas = {
          org_code: this.freeServiceData.org_code,
          org_name: org[0].label,
          from_demand: this.freeServiceData.from_demand,
          proof_list_id: this.selectRow.proof_list_id,
          replace_way_id: this.selectRow.id
        }
        this.$emit('deptSumbit', parmas)
      })
    },
    cancle() {
      this.$emit('deptCancle')
    },
    selectChange(row) {
      // console.log(row)
    }
  }
}
</script>

<style  scoped>
.cardtitle {
  /* color: #409eff; */
}
.card-dialog /deep/ .el-dialog__header {
  /* background: #4b87c5; */
  text-align: left;
}
.card-dialog /deep/ .el-dialog__body {
  padding: 0;
}
.deptform {
  width: 100%;
}
.el-card.is-always-shadow {
  box-shadow: 0 0 0 0;
}
</style>
