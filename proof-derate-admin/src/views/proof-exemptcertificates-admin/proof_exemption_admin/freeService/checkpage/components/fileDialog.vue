<template>
  <div id="deptDialog">
    <el-dialog
      title="一键归档"
      :visible.sync="dialogVisible"
      width="40%"
      class="card-dialog"
      :center="true"
      :show-close="false"
      :before-close="cancle"
    >
      <div class="card-wrap">
        <!-- <el-form ref="form" :model="deptForm" label-width="120px" class="deptform" :rules="rules"> -->
        <el-card class="box-card">
          <div slot="header" class="cardtitle">
            <span>选择归档文件</span>
          </div>
          <div class>
            <custom-table
              ref="table"
              :is-card-type="false"
              :table-data="tableData"
              :table-header="tableHeader"
              :stripe="false"
              :table-tools="tableTools"
              style="margin-top: 10px"
              @query="query"
              @selection-change="selectionChange"
              @refresh="query(1)"
            >
              <template #operation="{ row }">
                <div>
                  <el-button type="text" @click="detail(row)">查看</el-button>
                </div>
              </template>
            </custom-table>
          </div>
        </el-card>
        <!-- </el-form> -->
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="sumbit()">提交</el-button>
        <el-button @click="cancle()">返回</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import CustomTable from '@/components/Element/Table'
export default {
  data() {
    return {
      deptForm: {
        eventName: '',
        dep: ''
      },
      rules: {},
      tableData: {
        content: [], // 表格数据

        loading: false, // 控制表格加载效果
        total: 0, // 分页查询时的总数，去掉该属性，不显示分页条
        currentPage: 1, // 当前页码
        pageSize: 10,
        isShowIndex: false,
        maxHeight: '200px',
        multiple: true,
        pageDirection: 'desc',
        isShowSelection: true // 是否显示多选框，默认false
      },
      tableHeader: [
        {
          label: '电子证照目录编码',
          prop: 'license_code',
          minWidth: '100px'
        },
        {
          label: '电子证照名称',
          prop: 'license_name',
          minWidth: '100px'
        }
        //  { label: '归档', prop: 'investigationStatus',slot: 'investigationStatus', width: '120px', fixed: 'right'}
      ],
      tableTools: []
      // dialogVisible: false
    }
  },
  components: {
    CustomTable
  },
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    tableDataContent: {
      type: Array,
      default: []
    }
  },
  mounted() {
    this.$nextTick(() => {
      console.log('this.tableDataContent', this.tableDataContent)
      this.tableDataContent.forEach(e => {
        if (e.replace_cancel_way === '转化为电子证照') {
          this.tableData.content.push(e)
        }
      })
      // this.tableData.content = this.tableDataContent
    })
  },

  methods: {
    sumbit() {
      this.$emit('deptSumbit', this.selectData)
    },
    cancle() {
      this.$emit('deptCancle')
    },
    query() {},
    selectionChange(row) {
      // console.log(row)
      this.selectData = row
    },
    setData(data) {
      data.forEach(e => {
        if (e.replace_cancel_way === '转化为电子证照') {
          if (e.exempt_license_detail_list != null) {
            e.exempt_license_detail_list.forEach(e1 => {
              this.tableData.content.push(e1)
            })
          }
        }
      })
    }
  }
}
</script>

<style  scoped>
.cardtitle {
  color: #409eff;
}
.card-dialog /deep/ .el-dialog__header {
  background: #4b87c5;
  text-align: left;
}
.card-dialog /deep/ .el-dialog__body {
  padding: 0;
}
.deptform {
  width: 100%;
}
.el-card.is-always-shadow {
  box-shadow: 0 0 0 0;
}
</style>
