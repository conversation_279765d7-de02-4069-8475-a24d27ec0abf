<template>
  <div id="electronicCertificationDialog">
    <el-dialog
      title="开具电子证明"
      :visible.sync="dialogVisible"
      :width="freeServiceData.exempt_certificates_type === 'LEGAL_PERSON' ?'50%':'40%'"
      class="card-dialog"
      :center="false"
      :show-close="false"
      :before-close="cancle"
    >
      <!-- juridical-person -->
      <div class="wrap">
        <!-- <p class="wrap-title">
          <span>电子证明申请表</span>
          <el-divider></el-divider>
        </p> -->
        <div class="wrap-form" v-if="freeServiceData.exempt_certificates_type === 'NATURAL_PERSON'">
          <el-form ref="naturalPersonForm" :model="naturalPersonForm" label-width="80px" :rules="rules">
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="办事人">
                  <el-input :disabled="true" v-model="naturalPersonForm.h_affairs_name"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="证件类型">
                  <el-input :disabled="true" v-model="naturalPersonForm.h_affairs_identity_typeName"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="证件号码">
                  <el-input :disabled="true" v-model="naturalPersonForm.h_affairs_identity_number"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="事项名称">
                  <el-input :disabled="true" v-model="naturalPersonForm.item_name"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="材料名称">
                  <el-input :disabled="true" v-model="naturalPersonForm.material_name"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="开具部门">
                  <el-input v-model="naturalPersonForm.issue_org_name"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24">
              <el-col :span="24">
                <el-form-item label="需求描述" prop="description">
                  <el-input v-model="naturalPersonForm.description" type="textarea"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>

        <div class="wrap-form" v-if="freeServiceData.exempt_certificates_type === 'LEGAL_PERSON'">
          <el-form ref="legalpersonForm" :model="legalpersonForm" label-width="150px" :rules="rules">
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="办事单位">
                  <el-input :disabled="true" v-model="legalpersonForm.h_affairs_name"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="办事单位证件类型">
                  <el-input :disabled="true" v-model="legalpersonForm.h_affairs_identity_typeName"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="办事单位证件号码">
                  <el-input :disabled="true" v-model="legalpersonForm.h_affairs_identity_number"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="经办人">
                  <el-input :disabled="true" v-model="legalpersonForm.handler_name"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="经办人证件类型">
                  <el-input :disabled="true" v-model="legalpersonForm.handle_identity_typeName"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="经办人证件号码">
                  <el-input :disabled="true" v-model="legalpersonForm.handle_identity_number"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="事项名称">
                  <el-input :disabled="true" v-model="legalpersonForm.item_name"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="材料名称">
                  <el-input :disabled="true" v-model="legalpersonForm.material_name"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="开具部门">
                  <el-input :disabled="true" v-model="legalpersonForm.issue_org_name"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24">
              <el-col :span="24">
                <el-form-item label="需求描述" prop="description">
                  <el-input v-model="legalpersonForm.description" type="textarea"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
      </div>

      <div class="dialog-footer" v-if="showOnlyBack">
        <!-- <el-button type="primary" @click="sendFrom">提交</el-button> -->
        <el-button @click="cancle()">返回</el-button>
      </div>
      <div class="dialog-footer" v-else>
        <el-button type="primary" @click="sendFrom">提交</el-button>
        <el-button @click="cancle()">返回</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { issueLicenseApply } from '@/api/exemptCertificates'
export default {
  data() {
    return {
      naturalPersonForm: {
        h_affairs_name: '', //办事(人/单位)名称
        h_affairs_identity_type: '', // 办事人证件类型
        h_affairs_identity_typeName: '', //办事人证件类型 中文名称
        h_affairs_identity_number: '', //办事人证件号码
        item_name: '', //事项名称
        material_name: '', //材料名称
        issue_org_name: '', // 开具部门
        issue_org_code: '', //开具部门统一社会信用代码
        description: '', // 需求描述
        exempt_license_item_id: '', // 免证办材料电子证明清理方式Id
        serial_number: '', // 办理号
        proof_catalog_name: '', //证明目录名称
        proof_catalog_id: '', //证明目录id
        catalog_code: '', //目录编码
        item_code: '', //目录编码
        // material_id: '', //材料id
        from_org_name: '', //发起部门名称
        from_org_code: '', // 发起部门统一社会信用代码
        affairs_type: '' // 事务类型（自然人、法人）
      },

      legalpersonForm: {
        h_affairs_name: '', //办事(人/单位)名称
        h_affairs_identity_type: '', // 办事人证件类型
        h_affairs_identity_typeName: '', //办事人证件类型 中文名称
        h_affairs_identity_number: '', //办事人证件号码
        item_name: '', //事项名称
        material_name: '', //材料名称
        issue_org_name: '', // 开具部门
        issue_org_code: '', //开具部门统一社会信用代码
        description: '', // 需求描述
        exempt_license_item_id: '', // 免证办材料电子证明清理方式Id
        serial_number: '', // 办理号
        proof_catalog_name: '', //证明目录名称
        proof_catalog_id: '', //证明目录id
        catalog_code: '', //目录编码
        item_code: '', //目录编码
        material_id: '', //材料id
        from_org_name: '', //发起部门名称
        from_org_code: '', // 发起部门统一社会信用代码
        affairs_type: '', // 事务类型（自然人、法人）
        handler_name: '', // 经办人
        handle_identity_type: '', // 经办人证件类型,
        handle_identity_typeName: '', // 经办人证件类型名称
        handle_identity_number: '' //经办人证件号码
      },
      cardOptions: [
        {
          value: 'IDENTITY',
          label: '身份证'
        },
        {
          value: 'OFFICERS',
          label: '军官证'
        },
        {
          value: 'PASSPORT',
          label: '护照'
        },
        {
          value: 'EEP_HK_MACAO',
          label: '港澳通行证'
        },
        {
          value: 'OTHER_IDENTITY_LICENSE',
          label: '其他'
        }
      ],
      rules: {
        description: [{ required: true, message: '请输入需求描述', trigger: 'change' }]
      }
    }
  },
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    freeServiceData: {
      type: Object,
      default: {}
    },
    showOnlyBack: {
      type: Boolean,
      default: false
    },
    tableIndex: {
      type: Number,
      default: 0
    }
  },
  watch: {
    // dialogVisible() {}
    dialogVisible: {
      handler(val, val1) {
        if (val) {
          this.initData()
        } else {
          this.resettForm()
        }
      }
    }
  },
  mounted() {},

  methods: {
    resettForm() {
      if (this.freeServiceData.exempt_certificates_type === 'NATURAL_PERSON') {
        this.naturalPersonForm.description = ''
        this.$refs.naturalPersonForm.resetFields()
      } else {
        this.legalpersonForm.description = ''
        this.$refs.legalpersonForm.resetFields()
      }
    },
    cancle() {
      // this.resettForm()
      this.$emit('dialogCancle')
    },
    sendFrom() {
      if (this.freeServiceData.exempt_certificates_type === 'NATURAL_PERSON') {
        this.$refs.naturalPersonForm.validate(valid => {
          if (valid) {
            // this.issueLicenseApply(this.naturalPersonForm)
            this.$emit('sendiIssueLicenseApply', this.naturalPersonForm)
          }
        })
      } else {
        this.$refs.legalpersonForm.validate(valid => {
          if (valid) {
            // this.issueLicenseApply(this.legalpersonForm)
            this.$emit('sendiIssueLicenseApply', this.legalpersonForm)
          }
        })
      }
    },
    issueLicenseApply(data) {
      issueLicenseApply(data).then(res => {
        // console.log(res)
        if (res.meta.code === '200') {
          this.$message({
            message: '开具成功',
            type: 'success'
          })
        } else {
          this.$message({
            message: res.meta.message,
            type: 'warning'
          })
        }
      })
    },
    initData() {
      // this.freeServiceData.exempt_certificates_type = 'LEGAL_PERSON'
      // console.log(this.freeServiceData.exempt_certificates_type)
      // console.log(this.$store.state.user.userdata.userInfo)
      // console.log('tableIndex', this.tableIndex)
      console.log('this.freeServiceData',this.freeServiceData)
      const userdata = JSON.parse(localStorage.getItem('organization'))
      // 自然人类型
      if (this.freeServiceData.exempt_certificates_type === 'NATURAL_PERSON') {
        this.naturalPersonForm.h_affairs_name = this.freeServiceData.handle_affairs_name
        this.naturalPersonForm.affairs_type = this.freeServiceData.exempt_certificates_type
        this.naturalPersonForm.h_affairs_identity_type = this.freeServiceData.identity_type
        this.naturalPersonForm.h_affairs_identity_typeName = this.freeServiceData.identity_type_name
        this.naturalPersonForm.h_affairs_identity_number = this.freeServiceData.identity_number
        this.naturalPersonForm.item_name = this.freeServiceData.item_name
        this.naturalPersonForm.material_name = this.freeServiceData.proof_list_bo_list[this.tableIndex].license_item_detail.material_name
        this.naturalPersonForm.exempt_license_item_id = this.freeServiceData.proof_list_bo_list[this.tableIndex].license_item_detail.id
        this.naturalPersonForm.serial_number = this.freeServiceData.proof_list_bo_list[this.tableIndex].license_item_detail.serial_number
        this.naturalPersonForm.proof_catalog_id = this.freeServiceData.proof_list_bo_list[this.tableIndex].proof_catalog_id
        this.naturalPersonForm.proof_catalog_name = this.freeServiceData.proof_list_bo_list[this.tableIndex].proof_catalog_name
        this.naturalPersonForm.catalog_code = this.freeServiceData.proof_list_bo_list[this.tableIndex].license_item_detail.catalog_code
        this.naturalPersonForm.item_code = this.freeServiceData.proof_list_bo_list[this.tableIndex].license_item_detail.item_code
        this.naturalPersonForm.issue_org_code = userdata.credit_code
        // this.naturalPersonForm.material_id = this.freeServiceData.proof_list_bo_list[this.tableIndex].license_item_detail.material_id
        this.naturalPersonForm.issue_org_name = userdata.name
        this.naturalPersonForm.from_org_name = this.freeServiceData.to_user_org
        this.naturalPersonForm.from_org_code = this.freeServiceData.to_user_org_code
        // console.log('this.naturalPersonForm', this.naturalPersonForm)
        //
      } else if (this.freeServiceData.exempt_certificates_type === 'LEGAL_PERSON') {
        this.legalpersonForm.h_affairs_name = this.freeServiceData.biz_org_name
        this.legalpersonForm.affairs_type = this.freeServiceData.exempt_certificates_type
        this.legalpersonForm.h_affairs_identity_type = this.freeServiceData.biz_org_identity_type
        this.legalpersonForm.h_affairs_identity_typeName = this.freeServiceData.biz_org_identity_type_name
        this.legalpersonForm.h_affairs_identity_number = this.freeServiceData.biz_org_identity_num
        this.legalpersonForm.item_name = this.freeServiceData.item_name
        this.legalpersonForm.material_name = this.freeServiceData.proof_list_bo_list[this.tableIndex].license_item_detail.material_name
        this.legalpersonForm.exempt_license_item_id = this.freeServiceData.proof_list_bo_list[this.tableIndex].license_item_detail.id
        this.legalpersonForm.serial_number = this.freeServiceData.proof_list_bo_list[this.tableIndex].license_item_detail.serial_number
        this.legalpersonForm.proof_catalog_id = this.freeServiceData.proof_list_bo_list[this.tableIndex].proof_catalog_id
        this.legalpersonForm.proof_catalog_name = this.freeServiceData.proof_list_bo_list[this.tableIndex].proof_catalog_name
        this.legalpersonForm.catalog_code = this.freeServiceData.proof_list_bo_list[this.tableIndex].license_item_detail.catalog_code
        this.legalpersonForm.item_code = this.freeServiceData.proof_list_bo_list[this.tableIndex].license_item_detail.item_code
        this.legalpersonForm.issue_org_code = userdata.credit_code
        // this.legalpersonForm.material_id = this.freeServiceData.proof_list_bo_list[this.tableIndex].license_item_detail.material_id
        this.legalpersonForm.issue_org_name = userdata.name
        this.legalpersonForm.from_org_name = this.freeServiceData.to_user_org
        this.legalpersonForm.from_org_code = this.freeServiceData.to_user_org_code
        this.legalpersonForm.handler_name = this.freeServiceData.to_user_name
        this.legalpersonForm.handle_identity_type = this.freeServiceData.to_user_id_type
        this.legalpersonForm.handle_identity_typeName = this.freeServiceData.to_user_id_type_name
        this.legalpersonForm.handle_identity_number = this.freeServiceData.identity_number
        // console.log('this.legalpersonForm', this.legalpersonForm)
      }
    }
  }
}
</script>

<style  scoped>
/* .card-dialog /deep/ .el-dialog__header {
  background: #4b87c5;
  text-align: left;
}
.card-dialog /deep/ .el-dialog__title {
  color: #fff;
}
.card-dialog /deep/ .el-dialog__body {
  padding: 0;
} */
.wrap {
  height: 400px;
  /* overflow: auto; */
}
.wrap-form {
  padding: 10px 15px 0px;
}
.juridical-person {
  height: 450px;
}
.dialog-footer {
  padding-bottom: 10px;
  text-align: center;
}
.wrap-title {
  padding: 10px 15px 0px;
}
.wraplist {
  padding: 0 15px;
  /* height: 500px; */
  overflow: auto;
}
.wrap-content {
  display: flex;
  min-height: 90px;
  width: 100%;
  /* flex-direction: column; */
  flex-wrap: wrap;
}
.wrap-content p {
  width: 50%;
}
.wrap-content-left {
  flex: 1;
  /* padding: 0 15px; */
}
.wrap-content-right {
  flex: 1;
}
.el-divider {
  margin: 0;
  margin-top: 10px;
  margin-bottom: 10px;
}
/* .card-dialog /deep/ .el-dialog__footer {
  text-align: center;
} */
.nodata {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 100px;
}
</style>