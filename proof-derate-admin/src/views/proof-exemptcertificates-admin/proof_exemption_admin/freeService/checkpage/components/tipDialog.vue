<template>
  <div>
    <el-dialog
      title="警告提示"
      :visible.sync="dialogVisible"
      width="30%"
      class="card-dialog"
      :center="true"
      :show-close="true"
      :before-close="cancle"
    >
      <div class="card-wrap">
        <p class="card-wrap-title">
          <i class="el-icon-warning-outline"></i>
          <span>文件数量已超过5份，继续此操作将无法获取归档文件，是否继续？</span>
        </p>
        <p class="card-wrap-content">点击'确定'，可继续操作</p>
      </div>
      <div slot="footer" class="dialog-footer-left">
        <el-button type="primary" @click="sumbit()">确定</el-button>
        <el-button @click="cancle()">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  data() {
    return {}
  },
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    }
  },
  mounted() {},

  methods: {
    sumbit() {
      this.$emit('next')
    },
    cancle() {
      this.$emit('cancle')
    }
  }
}
</script>
<style  scoped>
.cardtitle {
  color: #409eff;
}
.card-dialog /deep/ .el-dialog__header {
  /* background: #4b87c5; */
  text-align: left;
  /* display: none; */
}
.card-dialog /deep/ .el-dialog__body {
  padding: 0;
}
.deptform {
  width: 100%;
}
.el-card.is-always-shadow {
  box-shadow: 0 0 0 0;
}
.card-wrap-title {
  display: flex;
  align-items: center;
  font-size: 20px;
  font-weight: 600;
  margin-top: 20px;
}
.card-wrap-content {
  margin-left: 70px;
}
.card-wrap-title i {
  font-size: 30px;
  color: #ff9800;
  margin: 0 20px;
}
.dialog-footer-left {
  text-align: right;
}
</style>