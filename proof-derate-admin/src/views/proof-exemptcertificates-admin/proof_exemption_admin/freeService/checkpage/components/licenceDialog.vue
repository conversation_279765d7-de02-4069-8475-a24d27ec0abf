<template>
  <div id="licenceDialog">
    <el-dialog
      title="选择证照类型"
      :visible.sync="dialogVisible"
      width="30%"
      class="licenDialog"
      :center="false"
      :show-close="true"
      :before-close="cancle"
    >
      <div class="wrap">
        <p>当前证照包含个人证照和企业证照，请选择证照类型：</p>
        <div class="wrap-tab">
          <div class="tab" :class="hasChosed===1?'chosed':''" @click="chosedItem(1)">
            <img :src="personal" alt srcset />
            <span>个人证照</span>
            <img v-if="hasChosed===1" :src="chosed" alt srcset class="chosed-icon" />
          </div>
          <div class="tab" :class="hasChosed===2?'chosed':''" @click="chosedItem(2)">
            <img :src="enterprise" alt srcset />
            <span>企业证照</span>
            <img v-if="hasChosed===2" :src="chosed" alt srcset class="chosed-icon" />
          </div>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" style="margin-right: 20px;" @click="sumbit()">查询</el-button>
        <el-button @click="cancle()">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  data() {
    return {
      personal: require('@/assets/proof-exemptcertificates-admin-images/u338.png'),
      enterprise: require('@/assets/proof-exemptcertificates-admin-images/u343.png'),
      chosed: require('@/assets/proof-exemptcertificates-admin-images/u332.png'),
      hasChosed: 1
    }
  },
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    }
  },

  mounted() {},

  methods: {
    cancle() {
      //   this.dialogVisible = false
      this.$emit('licensedialogVisible')
    },
    chosedItem(num) {
      this.hasChosed = num
    },
    sumbit() {
      if (this.hasChosed === 1) {
        this.$emit('licensedialogSumbit', 'PERSON')
      } else if (this.hasChosed === 2) {
        this.$emit('licensedialogSumbit', 'ORG')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.wrap-tab {
  display: flex;
  justify-content: space-between;
  padding: 10px 60px 0;
}
.tab {
  width: 150px;
  height: 150px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  border: solid 1px;
  border-radius: 5px;
  position: relative;
  cursor: pointer;
  img {
    width: 80px;
    height: 80px;
  }
  .chosed-icon {
    position: absolute;
    width: 15px;
    height: 15px;
    bottom: -7px;
    background: #fff;
  }
}
.chosed {
  background-color: rgba(240, 250, 246, 1);
  border-color: rgba(1, 164, 99, 1);
}
.dialog-footer {
  text-align: center;
}
</style>


<style>
.licenDialog .el-dialog__body {
  padding-top: 0;
}
</style>