<template>
  <div id="dataEditDialog">
    <el-dialog
      title="编辑信息"
      :visible.sync="dialogVisible"
      width="40%"
      class="card-dialog"
      :center="false"
      :show-close="false"
      :before-close="cancle"
    >
      <div class="wrap">
        <!-- <p class="wrap-title">
          <span>服务对象基本信息</span>
          <el-divider></el-divider>
        </p> -->
        <div class="wrap-form" v-if="freeServiceData.exempt_certificates_type === 'NATURAL_PERSON'">
          <el-form ref="naturalPersonForm" :model="naturalPersonForm" label-width="100px" :rules="rules">
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="业务办理号">
                  <el-input :disabled="true" v-model="naturalPersonForm.serial_number"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="办事人">
                  <el-input v-model="naturalPersonForm.handle_affairs_name"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="证件类型">
                  <!-- <el-input v-model="naturalPersonForm.h_affairs_identity_typeName"></el-input> -->
                  <el-select v-model="naturalPersonForm.identity_type" placeholder="请选择">
                    <el-option v-for="item in identityTypeoptionslist" :key="item.value" :label="item.label" :value="item.value"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="证件号码">
                  <el-input v-model="naturalPersonForm.identity_number"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
        <div class="wrap-form large" v-if="freeServiceData.exempt_certificates_type === 'LEGAL_PERSON'">
          <el-form ref="naturalPersonForm" :model="legalpersonForm" label-width="150px" :rules="rules">
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="业务办理号">
                  <el-input :disabled="true" v-model="legalpersonForm.serial_number"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="办事人">
                  <el-input v-model="legalpersonForm.handle_affairs_name"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="证件类型">
                  <!-- <el-input v-model="legalpersonForm.h_affairs_identity_typeName"></el-input> -->
                  <el-select v-model="legalpersonForm.identity_type" placeholder="请选择">
                    <el-option v-for="item in identityTypeoptionslist" :key="item.value" :label="item.label" :value="item.value"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="证件号码">
                  <el-input v-model="legalpersonForm.identity_number"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="企业或机构名称">
                  <el-input v-model="legalpersonForm.corp_name"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="企业或机构证件类型">
                  <!-- <el-input v-model="legalpersonForm.corp_id_type"></el-input> -->
                  <el-select v-model="legalpersonForm.corp_id_type" placeholder="请选择">
                    <el-option v-for="item in identityTypeoptionslist" :key="item.value" :label="item.label" :value="item.value"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="企业或机构证件号码">
                  <el-input v-model="legalpersonForm.corp_id"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
      </div>
      <div class="dialog-footer">
        <el-button type="primary" @click="sendFrom">提交</el-button>
        <el-button @click="cancle()">返回</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { yssYstUpdate } from '@/api/exemptCertificates'
export default {
  data() {
    return {
      rules: {},
      naturalPersonForm: {
        handle_affairs_name: '', //办事(人/单位)名称
        identity_type: '', // 办事人证件类型
        // h_affairs_identity_typeName: '', //办事人证件类型 中文名称
        identity_number: '', //办事人证件号码
        // item_name: '', //事项名称
        // material_name: '', //材料名称
        // issue_org_name: '', // 开具部门
        // issue_org_code: '', //开具部门统一社会信用代码
        // description: '', // 需求描述
        // exempt_license_item_id: '', // 免证办材料电子证明清理方式Id
        serial_number: '', // 办理号
        // proof_catalog_name: '', //证明目录名称
        // proof_catalog_id: '', //证明目录id
        // catalog_code: '', //目录编码
        // item_code: '', //目录编码
        // // material_id: '', //材料id
        // from_org_name: '', //发起部门名称
        // from_org_code: '', // 发起部门统一社会信用代码
        // affairs_type: '' // 事务类型（自然人、法人）
      },
      legalpersonForm: {
        handle_affairs_name: '', //办事(人/单位)名称
        identity_type: '', // 办事人证件类型
        // h_affairs_identity_typeName: '', //办事人证件类型 中文名称
        identity_number: '', //办事人证件号码
        corp_name: '',
        corp_id_type: '',
        // item_name: '', //事项名称
        // material_name: '', //材料名称
        // issue_org_name: '', // 开具部门
        // issue_org_code: '', //开具部门统一社会信用代码
        // description: '', // 需求描述
        // exempt_license_item_id: '', // 免证办材料电子证明清理方式Id
        serial_number: '', // 办理号
        corp_id:'',
        // proof_catalog_name: '', //证明目录名称
        // proof_catalog_id: '', //证明目录id
        // catalog_code: '', //目录编码
        // item_code: '', //目录编码
        // material_id: '', //材料id
        // from_org_name: '', //发起部门名称
        // from_org_code: '', // 发起部门统一社会信用代码
        // affairs_type: '', // 事务类型（自然人、法人）
        // handler_name: '', // 经办人
        // handle_identity_type: '', // 经办人证件类型,
        // handle_identity_typeName: '', // 经办人证件类型名称
        // handle_identity_number: '' //经办人证件号码
      },
      updateDataObj: {},
      identityTypeoptionslist:[]
    }
  },

  mounted() {},
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    freeServiceData: {
      type: Object,
      default: {}
    },
    updateData: {
      type: Object,
      default: {}
    },
    identityTypeoptions: {
      type: Array,
      default: []
    }
  },
  watch: {
    dialogVisible: {
      handler(val, val1) {
        if (val) {
          this.initData()
        } else {
          // this.resettForm()
        }
      }
    },
    updateData: {
      handler(val, val1) {
        this.updateDataObj = val
      }
    },
    identityTypeoptions: {
      handler(val, val1) {
        this.identityTypeoptionslist = val
      }
    }
  },
  methods: {
    cancle() {
      this.$emit('dialogCancle')
    },
    sendFrom() {
      this.yssYstUpdate(this.updateDataObj)
    },
    yssYstUpdate(row) {
      const serial_number = row.serial_number
      const param = {
        auth_token: row.auth_code,
        replace_way_id: row.id,
        serial_number: row.serial_number,
        auth_code_source: row.auth_code_source
      }
      let data = {}
      if (this.freeServiceData.exempt_certificates_type === 'NATURAL_PERSON') {
        data = this.naturalPersonForm
      } else {
        data = this.legalpersonForm
      }
      console.log('serial_number, param, data',serial_number, param, data)
      yssYstUpdate(data.serial_number, param, data).then(res => {
        if (res.meta.code === '200') {
          this.$message({
            message: '编辑成功',
            type: 'success'
          })
          this.$emit('dialogCancleAndUpdate')
        }
      })
    },
    initData() {
      // this.freeServiceData.exempt_certificates_type = 'LEGAL_PERSON'
      // console.log(this.freeServiceData.exempt_certificates_type)
      // console.log(this.$store.state.user.userdata.userInfo)
      // console.log('tableIndex', this.tableIndex)
      // 自然人类型
      if (this.freeServiceData.exempt_certificates_type === 'NATURAL_PERSON') {
        this.naturalPersonForm.handle_affairs_name = this.freeServiceData.handle_affairs_name
        // this.naturalPersonForm.affairs_type = this.freeServiceData.exempt_certificates_type
        this.naturalPersonForm.identity_type = this.freeServiceData.identity_type
        // this.naturalPersonForm.h_affairs_identity_typeName = this.freeServiceData.identity_type_name
        this.naturalPersonForm.identity_number = this.freeServiceData.identity_number
        // this.naturalPersonForm.item_name = this.freeServiceData.item_name
        this.naturalPersonForm.serial_number = this.freeServiceData.serial_number
        // this.naturalPersonForm.issue_org_code = this.$store.state.user.userdata.userInfo.orgCode
        // // this.naturalPersonForm.material_id = this.freeServiceData.proof_list_bo_list[this.tableIndex].license_item_detail.material_id
        // this.naturalPersonForm.issue_org_name = this.$store.state.user.userdata.userInfo.orgName
        // this.naturalPersonForm.from_org_name = this.freeServiceData.to_user_org
        // this.naturalPersonForm.from_org_code = this.freeServiceData.to_user_org_code
        // console.log('this.naturalPersonForm', this.naturalPersonForm)
        //
      } else if (this.freeServiceData.exempt_certificates_type === 'LEGAL_PERSON') {
        this.legalpersonForm.handle_affairs_name = this.freeServiceData.handle_affairs_name
        this.legalpersonForm.corp_name = this.freeServiceData.biz_org_name
        // this.legalpersonForm.affairs_type = this.freeServiceData.exempt_certificates_type
        this.legalpersonForm.identity_type = this.freeServiceData.identity_type
        // this.legalpersonForm.h_affairs_identity_typeName = this.freeServiceData.identity_type_name
        this.legalpersonForm.corp_id_type = this.freeServiceData.biz_org_identity_type
        this.legalpersonForm.identity_number = this.freeServiceData.identity_number
        this.legalpersonForm.corp_id = this.freeServiceData.biz_org_identity_num
        // this.legalpersonForm.item_name = this.freeServiceData.item_name
        this.legalpersonForm.serial_number = this.freeServiceData.serial_number
        // this.legalpersonForm.issue_org_code = this.$store.state.user.userdata.userInfo.orgCode
        // this.legalpersonForm.material_id = this.freeServiceData.proof_list_bo_list[this.tableIndex].license_item_detail.material_id
        // this.legalpersonForm.issue_org_name = this.$store.state.user.userdata.userInfo.orgName
        // this.legalpersonForm.from_org_name = this.freeServiceData.to_user_org
        // this.legalpersonForm.from_org_code = this.freeServiceData.to_user_org_code
        // this.legalpersonForm.handler_name = this.freeServiceData.to_user_name
        // this.legalpersonForm.handle_identity_type = this.freeServiceData.to_user_id_type
        // this.legalpersonForm.handle_identity_typeName = this.freeServiceData.to_user_id_type_name
        // this.legalpersonForm.handle_identity_number = this.freeServiceData.to_user_id_num
        // console.log('this.legalpersonForm', this.legalpersonForm)
      }
    }
  }
}
</script>

<style scoped>
/* .card-dialog /deep/ .el-dialog__header {
  background: #4b87c5;
  text-align: left;
}
.card-dialog /deep/ .el-dialog__title {
  color: #fff;
}
.card-dialog /deep/ .el-dialog__body {
  padding: 0;
} */
.wrap {
  min-height: 225px;
  /* overflow: auto; */
}
.wrap-form {
  padding: 10px 15px 0px;
}
.large {
  height: 300px;
}
.juridical-person {
  height: 450px;
}
.dialog-footer {
  padding-bottom: 10px;
  text-align: center;
}
.wrap-title {
  padding: 10px 15px 0px;
}
.wraplist {
  padding: 0 15px;
  /* height: 500px; */
  overflow: auto;
}
.wrap-content {
  display: flex;
  min-height: 90px;
  width: 100%;
  /* flex-direction: column; */
  flex-wrap: wrap;
}
.wrap-content p {
  width: 50%;
}
.wrap-content-left {
  flex: 1;
  /* padding: 0 15px; */
}
.wrap-content-right {
  flex: 1;
}
.el-divider {
  margin: 0;
  margin-top: 10px;
  margin-bottom: 10px;
}
/* .card-dialog /deep/ .el-dialog__footer {
  text-align: center;
} */
.nodata {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 100px;
}
</style>
