<template>
  <div class="content-wrapper">
    <section class="content-header">
      <el-form ref="form" :model="checkform" label-width="100px" class="el-check-form" :rules="rules">
        <el-card class="box-card">
          <div slot="header" class="cardtitle">
            <span>部门间协查申请表</span>
          </div>
          <el-row>
            <el-col :xs="24" :sm="12" :md="12" :lg="24">
              <div class="minddle-form">
                <el-row align="middle">
                  <el-col :xs="24" :sm="12" :md="12" :lg="12">
                    <el-form-item label="办事人" prop="eventName">
                      <el-input v-model="checkform.eventName" clearable placeholder="请输入办事人" />
                    </el-form-item>
                  </el-col>
                  <el-col :xs="24" :sm="12" :md="12" :lg="12">
                    <el-form-item label="证件类型" prop="eventName">
                      <el-input v-model="checkform.eventName" clearable placeholder="请输入证件类型" />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row align="middle">
                  <el-col :xs="24" :sm="12" :md="12" :lg="12">
                    <el-form-item label="证件号码" prop="eventName">
                      <el-input v-model="checkform.eventName" clearable placeholder="请输入证件号码" />
                    </el-form-item>
                  </el-col>
                  <el-col :xs="24" :sm="12" :md="12" :lg="12">
                    <el-form-item label="事项名称" prop="eventName">
                      <el-input v-model="checkform.eventName" clearable placeholder="请输入事项名称" />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row align="middle">
                  <el-col :xs="24" :sm="12" :md="12" :lg="12">
                    <el-form-item label="材料名称" prop="eventName">
                      <el-input v-model="checkform.eventName" clearable placeholder="请输入材料名称" />
                    </el-form-item>
                  </el-col>
                  <el-col :xs="24" :sm="12" :md="12" :lg="12">
                    <el-form-item label="协查部门" prop="eventName">
                      <el-select v-model="checkform.dep" placeholder="选择协查部门" class="select">
                        <el-option label="部门一" value="shanghai"></el-option>
                        <el-option label="部门二" value="beijing"></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row align="middle">
                  <el-col :xs="24" :sm="12" :md="12" :lg="24">
                    <el-form-item label="协查需求描述" prop="eventName">
                      <el-input type="textarea" :rows="4" v-model="checkform.eventName" clearable placeholder="请输入协查需求描述" />
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>
            </el-col>
          </el-row>
          <div class="foot-btn">
            <el-button type="primary">提交</el-button>
            <el-button @click="backline">返回</el-button>
          </div>
        </el-card>
      </el-form>
    </section>
  </div>
</template>

<script>
export default {
  name: 'depAssist',

  data() {
    return {
      checkform: {
        eventName: '',
        dep: ''
      },
      rules: []
    }
  },

  mounted() {},

  methods: {
    eventChose() {},
    backline() {
      this.$router.push({
        name: 'freeServiceContent'
      })
    }
  }
}
</script>

<style  scoped>
.select {
  width: 100%;
}
.foot-btn {
  height: 200px;
  text-align: center;
  line-height: 200px;
}
</style>