<template>
  <div class="content-wrapper">
    <section class="content-header">
      <el-form ref="form" :model="checkform" label-width="200px" class="el-check-form" :rules="rules">
        <el-card class="box-card card1" :body-style="{'padding': '30px 20px' }">
          <!-- <div slot="header" class="cardtitle">
            <span>办理意见</span>
          </div>-->
          <span class="margin-left-10 info-wrap">
            <img :src="arrow" alt />
            <span class="info-title">办理意见</span>
          </span>
          <el-row>
            <el-col :xs="24" :sm="12" :md="12" :lg="24">
              <!-- <el-row align="middle">
                <el-col :xs="24" :sm="12" :md="12" :lg="10">
                  <el-form-item label="经办人">
                    <el-input v-model="checkform.to_user_name" clearable placeholder="请输入经办人" disabled />
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="12" :md="12" :lg="10">
                  <el-form-item label="经办部门">
                    <el-input v-model="checkform.to_user_org" clearable placeholder="请输入经办部门" disabled />
                  </el-form-item>
                </el-col>
              </el-row>-->
              <el-row align="middle">
                <el-col :xs="24" :sm="12" :md="12" :lg="10">
                  <el-form-item label="办理意见">
                    <el-radio-group v-model="checkform.process_result">
                      <el-radio label="SUCCESS">同意</el-radio>
                      <el-radio label="FAILURE">不同意</el-radio>
                    </el-radio-group>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row align="middle">
                <el-col :xs="24" :sm="12" :md="12" :lg="20">
                  <el-form-item label="意见内容">
                    <el-input v-model="checkform.process_comment" type="textarea" :rows="4" placeholder="请输入意见内容" />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-col>
          </el-row>
          <div class="foot-btn">
            <el-button type="primary" @click="done()">办结并归档</el-button>
            <el-button @click="back()">返回</el-button>
          </div>
        </el-card>
      </el-form>
    </section>
    <tipMeDialog
      :has-res="hasRes"
      :dialog-visible="tipMeDialoVisible"
      :tip-str="tipStr"
      :is-sus="isSus"
      @tipMeDialogCancle="tiptipMeDialogCancle"
      @getMes="getMes"
    />
  </div>
</template>

<script>
import { handle, getReplaceWayArchivist } from '@/api/exemptCertificates'
import tipMeDialog from '../commponents/tipMeDialog'
import { dataURLtoDownload, base64toBlob } from '@/utils/index.js'
export default {
  name: 'HandingOpinions',
  components: {
    tipMeDialog
  },
  data() {
    return {
      checkform: {
        process_comment: '',
        to_user_name: '',
        to_user_account: '',
        to_user_org: '',
        to_user_org_code: '',
        process_result: 'SUCCESS'
      },
      formRoute: '',
      rules: {},
      tipMeDialoVisible: false,
      hasRes: true,
      isSus: false,
      tipStr: '当前仍存在部分材料未核验成功，现在办结将造成部分材料无法归档，是否确定办结？',
      arrow: require('@/assets/proof-exemptcertificates-admin-images/arrow.png')
    }
  },

  mounted() {
    // const userdata = this.$store.state.user.userdata
    // this.checkform.to_user_name = userdata.userAccount.name
    // this.checkform.to_user_account = userdata.userAccount.account
    // this.checkform.to_user_org = userdata.userInfo.orgName
    // this.checkform.to_user_org_code = userdata.userInfo.orgCode
    // const data = JSON.parse(this.$route.query.data)
    const accountInfo = JSON.parse(sessionStorage.getItem('accountInfo'))
    const userdata = JSON.parse(this.$store.state.user.organization)
    this.checkform.to_user_name = accountInfo.name
    this.checkform.to_user_account = accountInfo.account
    this.checkform.to_user_org = accountInfo.department
    this.checkform.to_user_org_code = userdata.credit_code
  },

  methods: {
    back() {
      this.formRoute = this.$route.query
      this.$router.push({
        name: 'freeServiceContent',
        query: {
          isDetail: this.formRoute.isDetail,
          serial_number: this.formRoute.serial_number
        }
      })
    },
    done() {
      // this.$router.push({
      //   name: 'freeArchives'
      // })
      const data = JSON.parse(this.$route.query.data)
      this.checkform = Object.assign(data, this.checkform)
      const isAllcheck = this.$route.query.isAllcheck
      if (isAllcheck === '0') {
        this.tipMeDialoVisible = true
      } else {
        this.getReplaceWayArchivist()
      }
    },
    handle() {
      console.log('this.checkform', this.checkform)
      // this.tipMeDialoVisible = true
      handle(this.checkform)
        .then(res => {
          if (res.meta.code === '200' && res.data != '') {
            this.$message({
              message: '办结成功',
              type: 'success'
            })
            this.$router.push({
              name: 'freeServiceitem1'
            })
            // if (this.checkform.process_result === 'FAILURE') {

            // }
          }
        })
        .catch(err => {})
    },
    getReplaceWayArchivist() {
      getReplaceWayArchivist({ serial_number: this.checkform.serial_number }).then(res => {
        // if(res)
        console.log(res)
        if (res.meta.code === '200') {
          // dataURLtoDownload(res.data.file_data_base64, res.data.file_name + '.docx')
          // pplication/octet-stream
          const blob = base64toBlob(res.data.file_data_base64, 'pplication/octet-stream')
          console.log('blob', blob)
          const fileName = res.data.file_name
          const eLink = document.createElement('a')
          eLink.style.display = 'none'
          eLink.href = window.URL.createObjectURL(blob)
          eLink.download = fileName == null || fileName == '' ? 'common' : fileName //有传fileName，就用fileName
          document.body.appendChild(eLink)
          eLink.click()
          document.body.removeChild(eLink)
          window.URL.revokeObjectURL(blob)
          this.handle()
        } else {
          this.$message({
            message: res.meta.message,
            type: 'error'
          })
        }
      })
    },
    tiptipMeDialogCancle() {
      this.tipMeDialoVisible = false
    },
    getMes() {
      this.getReplaceWayArchivist()
      this.tipMeDialoVisible = false
    }
  }
}
</script>

<style scoped>
.cardtitle {
  color: #409eff;
}
.foot-btn {
  height: 200px;
  text-align: center;
  line-height: 200px;
}
.info-wrap {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}
.info-wrap img {
  width: 35px;
  height: 35px;
  margin-right: 10px;
}
.info-title {
  font-size: 20px;
  color: #333333;
}
</style>
