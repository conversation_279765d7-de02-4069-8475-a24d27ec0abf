<template>
  <div class="content-wrapper padding-10">
    <!-- <papeTitle :title-name="titleName" :is-has-back="false" /> -->
    <CardTitle :title-name="titleName">
      <template></template>
    </CardTitle>
    <!-- <section class="content"> -->
    <div class="scan-img">
      <img :src="scan" alt />
    </div>
    <el-form ref="form" :model="checkform" label-width="100px" class="el-check-form" label-position="top" :rules="rules">
      <el-card class="box-card" :body-style="{'padding':'30px 20px'}">
        <!-- <div slot="header" class="cardtitle">
            <span>服务事项</span>
        </div>-->
        <span class="margin-left-10 info-wrap">
          <img :src="arrow" alt />
          <span class="info-title">服务事项</span>
        </span>
        <div class="minddle-form">
          <el-row type="flex">
            <el-col :xs="24" :sm="12" :md="12" :lg="24">
              <!-- <div class="minddle-form"> -->
              <el-form-item label="办事人类型" class="agent">
                <el-radio-group v-model="checkform.exempt_certificates_type">
                  <el-radio class="radio-item" :label="'NATURAL_PERSON'">自然人</el-radio>
                  <el-radio class="radio-item" :label="'LEGAL_PERSON'">法人</el-radio>
                </el-radio-group>
              </el-form-item>
              <!-- </div> -->
            </el-col>
          </el-row>
        </div>
        <el-row>
          <el-col :xs="24" :sm="12" :md="12" :lg="24">
            <div class="minddle-form">
              <el-row align="middle">
                <el-col :xs="24" :sm="12" :md="12" :lg="20">
                  <el-form-item label="事项名称" prop="item_name" class="agent">
                    <el-input v-model="checkform.item_name" clearable :readonly="true" placeholder="请输入事项名称" />
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="12" :md="12" :lg="4" class="item-mindle">
                  <el-button type="text" @click="eventChose">选择事项>></el-button>
                </el-col>
              </el-row>
            </div>
          </el-col>
        </el-row>
      </el-card>
      <el-card class="box-card" :body-style="{'padding':'00px 20px'}">
        <!-- <div slot="header" class="cardtitle">
            <span>一码通行</span>
        </div>-->
        <span class="margin-left-10 info-wrap">
          <img :src="arrow" alt />
          <span class="info-title">一码通行</span>
        </span>
        <el-row>
          <el-col :xs="24" :sm="12" :md="12" :lg="24">
            <div class="minddle-form">
              <el-row align="middle">
                <el-col :xs="24" :sm="12" :md="12" :lg="20">
                  <el-form-item label="授权码" prop="authorization_code" class="agent">
                    <el-input v-model="checkform.authorization_code" clearable placeholder="请输入授权码" />
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="12" :md="12" :lg="4" class="item-mindle">
                  <el-button type="text" @click="setEmpity">重置</el-button>
                </el-col>
              </el-row>
            </div>
          </el-col>
        </el-row>
        <div class="onekeytest">
          <div
            class="checkbtn"
            v-permission="'exempt:use:service:check'"
            :style="{background:$store.state.settings.skinColor}"
            @click="oneCheck"
          >一键核验</div>
        </div>
      </el-card>
    </el-form>
    <!-- </section> -->
    <el-dialog title="选择事项" :visible.sync="dialogVisible" width="50%" :top="is1366==true?'1vh':'15vh'" class="dialog">
      <div class="dialogwrap">
        <h3 class="select-title"></h3>
        <el-form ref="form1" :model="addForm" label-width="80px" class="el-check-form">
          <el-row :gutter="24" justify="center" type="flex">
            <el-col :span="9">
              <el-form-item label="事项名称">
                <el-input v-model="addForm.item_name" clearable placeholder="请输入事项名称" />
              </el-form-item>
            </el-col>
            <el-col :span="9">
              <el-form-item label="实施机构">
                <el-input v-model="addForm.item_org_name" clearable placeholder="请输入实施机构" />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-button type="primary" @click="query">查询</el-button>
            </el-col>
          </el-row>
        </el-form>
        <custom-table
          ref="table"
          :is-card-type="false"
          :table-data="tableData"
          :table-header="tableHeader"
          :stripe="false"
          :table-tools="tableTools"
          @selection-change="selectionChange"
          @query="query"
          @refresh="query(1)"
        />
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="getData()">确认</el-button>
        <el-button @click="dialogVisible = false">取 消</el-button>
      </span>
    </el-dialog>
    <tipDialog :dialog-visible="tipDialogVisible" @tipDialogCancle="tipDialogCancle" @tipClick="tipClick" />
    <tipMeDialog
      :has-res="hasRes"
      :dialog-visible="tipMeDialoVisible"
      :tip-str="tipStr"
      :is-sus="isSus"
      @tipMeDialogCancle="tiptipMeDialogCancle"
      @getMes="getMes"
    />
    <mesDialog
      :dialog-visible="mesDialogVisible"
      :is-legal-person="checkform.exempt_certificates_type==='NATURAL_PERSON'?false:true"
      @mesDialogCancle="mesDialogCancle"
      @sendData="sendData"
    />
  </div>
</template>

<script>
// import QueryForm from "@/views/proof_exemption_admin/itemBiz/itemCarding/components/QueryForm";
import CustomTable from '@/components/Element/Table'
import { oneKeyValidate, getServiceItemPage, oneKeyValidateContext } from '@/api/exemptCertificates'
import { MessageBox } from 'element-ui'
import tipDialog from './commponents/tipDialog'
import tipMeDialog from './commponents/tipMeDialog'
import mesDialog from './commponents/mesDialog'
import papeTitle from '@/components/papeTitle'
import CardTitle from '@/components/CardTitle'
// import { isPermission } from '@/utils/index.js'
export default {
  name: 'MattersCheck',
  components: {
    // QueryForm,
    CustomTable,
    tipDialog,
    tipMeDialog,
    mesDialog,
    papeTitle,
    CardTitle
    // isPermission,
  },
  data() {
    return {
      checkform: {
        authorization_code: '',
        item_name: '',
        item_code: '',
        exempt_certificates_type: 'NATURAL_PERSON'
      },
      searchForm: {
        name: '',
        region: ''
      },
      radio: '2',
      dialogVisible: false,
      rules: {
        authorization_code: [{ required: true, message: '请输入授权码', trigger: 'change' }],
        item_name: [{ required: true, message: '请输入事件名称', trigger: 'change' }]
      },
      addForm: {
        item_org_name: '',
        item_name: '',
        page_number: 1,
        page_size: 10
      },
      tableData: {
        content: [], // 表格数据

        loading: false, // 控制表格加载效果
        total: 10, // 分页查询时的总数，去掉该属性，不显示分页条
        currentPage: 1, // 当前页码
        pageSize: 10,
        isShowIndex: false,
        // height: '100px',
        maxHeight: '300px',
        multiple: false, // 是否多选 数据需要有id 属性值
        pageDirection: 'desc',
        isShowSelection: true // 是否显示多选框，默认false
      },
      tableHeader: [
        {
          label: '事项名称',
          prop: 'item_name',
          minWidth: '100px'
        },
        {
          label: '事项编码',
          prop: 'item_code',
          minWidth: '100px'
        },
        {
          label: '办理项名称',
          prop: 'situation_name',
          minWidth: '100px'
        },
        {
          label: '实施部门',
          prop: 'impl_org_name',
          minWidth: '100px'
        }
      ],
      tableTools: [],
      selectData: '',
      KeyValidateInterval: null,
      KeyValidateTimeout: null,
      is1366: false,
      tipDialogVisible: false,
      tipMeDialoVisible: false,
      mesDialogVisible: false,
      arrow: require('@/assets/proof-exemptcertificates-admin-images/arrow.png'),
      scan: require('@/assets/proof-exemptcertificates-admin-images/scan.png'),
      tipStr: '办事人无证照可授权，是否确定继续办理？',
      mesCode: '0',
      hasRes: true,
      isSus: false,
      titleName: '免证办服务'
    }
  },
  mounted() {
    this.screenWidth()
    const userdata = this.$store.state.user.userdata
    // this.addForm.credit_code = userdata.userInfo.tyshxxdm
    this.query()
  },
  beforeRouteLeave(to, from, next) {
    from.meta.keepAlive = false
    next()
  },
  beforeDestroy() {
    clearInterval(this.KeyValidateInterval)
    clearTimeout(this.KeyValidateTimeout)
    this.KeyValidateInterval = null
    this.KeyValidateTimeout = null
  },
  methods: {
    eventChose() {
      this.dialogVisible = true
    },
    handleClose() {},
    searchFrom() {},
    oneCheck() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          this.tipDialogVisible = true
          this.oneKeyValidateContext()
            .then(res => {
              this.mesCode = res.meta.code
              console.log('this.mesCode', this.mesCode)
              if (res.meta.code === '2011001002') {
                this.tipStr = '办事人无证照可授权，是否确定继续办理？'
                this.hasRes = true
                this.tipDialogVisible = false
                this.tipMeDialoVisible = true
                this.isSus = false
              } else if (res.meta.code === '2011001001') {
                this.tipStr = '办事人无证照可授权，是否确定继续办理？'
                this.hasRes = true
                this.tipDialogVisible = false
                this.tipMeDialoVisible = true
                this.isSus = false
              } else if (res.meta.code === '200') {
                this.checkform = Object.assign(this.checkform, res.data)
                this.tipStr = '授权成功！'
                this.hasRes = true
                this.isSus = true
                this.tipDialogVisible = false
                this.tipMeDialoVisible = true
                // this.oneKeyValidate()
              } else {
                this.tipDialogVisible = false
                this.tipStr = res.meta.message
                this.tipMeDialoVisible = true
                this.hasRes = false
                this.isSus = false
              }
            })
            .catch(err => {
              console.log(err)
              this.tipStr = '当前事项无需授权证照，是否继续办理？'
              this.tipDialogVisible = false
              // this.tipStr = res.meta.message
              this.tipMeDialoVisible = true
            })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    setEmpity() {
      this.checkform.authorization_code = ''
    },
    getData() {
      if (this.selectData != '') {
        this.dialogVisible = false
        this.checkform.item_name = this.selectData[0].item_name
        this.checkform.item_code = this.selectData[0].item_code
      } else {
        this.$message({
          message: '请选择事项',
          type: 'warning'
        })
      }
    },
    query() {
      this.getServiceItemPage()
    },
    selectionChange(row) {
      this.selectData = row
    },
    oneKeyValidate() {
      oneKeyValidate(this.checkform).then(res => {
        if (res.meta.code === '11518' && res.data != null) {
          this.checkform.context_code = res.data.context_code
        } else if (res.meta.code === '200' && res.data != null) {
          this.$router.push({
            name: 'freeServiceContent',
            query: {
              isLegalPerson: this.checkform.exempt_certificates_type,
              serial_number: res.data.serial_number
            }
          })
        } else {
          MessageBox.close()
          this.$alert(`${res.meta.message}`, '', {
            confirmButtonText: '确定',
            callback: action => {
              // clearInterval(this.KeyValidateInterval)
            }
          })
        }
      })
    },
    oneKeyValidateContext() {
      return oneKeyValidateContext(this.checkform)
    },
    getServiceItemPage() {
      this.tableData.loading = true
      this.addForm.page_number = this.tableData.currentPage
      this.addForm.page_size = this.tableData.pageSize
      getServiceItemPage(this.addForm)
        .then(res => {
          this.tableData.loading = false
          if (res.meta.code === '200' && res.data != null) {
            this.tableData.content = res.data.content
            this.tableData.total = Number(res.data.total_elements)
            this.tableData.content.forEach((e, index) => {
              e.id = index
            })
          }
        })
        .catch(() => {
          this.tableData.loading = false
        })
    },
    screenWidth() {
      if (screen.width == 1920) {
        this.is1366 = false
      } else if (screen.width == 1366) {
        this.is1366 = true
      } else {
        this.is1366 = false
      }
    },
    tipDialogCancle() {
      this.tipDialogVisible = false
    },
    tiptipMeDialogCancle() {
      this.tipMeDialoVisible = false
    },
    mesDialogCancle() {
      this.mesDialogVisible = false
    },
    tipClick() {
      this.tipMeDialoVisible = true
    },
    getMes() {
      // this.oneKeyValidate()
      console.log('this.mesCode', this.mesCode)
      if (this.mesCode === '2011001002') {
        this.mesDialogVisible = true
      } else if (this.mesCode === '2011001001') {
        this.mesDialogVisible = true
      } else if (this.mesCode === '0') {
        this.mesDialogVisible = true
      } else if (this.mesCode === '200') {
        this.oneKeyValidate()
      } else {
        this.tiptipMeDialogCancle()
      }
    },
    sendData(data) {
      console.log('sendData', data)
      this.checkform = Object.assign(this.checkform, data)
      console.log('this.checkform', this.checkform)
      this.oneKeyValidate()
    }
  }
}
</script>
<style lang="scss">
@import '@/styles/element-ui.scss';
</style>
<style scoped>
.cardtitle {
  color: #409eff;
}
.minddle-form {
  width: 50%;
  display: inline-block;
}
.el-check-form {
  text-align: center;
}
.el-card {
  border: 0px;
  /* text-emphasis: none; */
}
.el-card.is-always-shadow {
  box-shadow: 0 0 0 0;
}
.box-card1 /deep/.el-card__body {
  padding-bottom: 0px;
}
.box-card2 /deep/.el-card__header {
  padding-top: 0px;
  text-align: left;
}
.box-card1 /deep/.el-card__header {
  text-align: left;
}
.onekeytest {
  height: 200px;
  line-height: 200px;
  text-align: center;
}
.checkbtn {
  display: inline-block;
  width: 240px;
  height: 50px;
  background: inherit;
  /* background-color: rgba(22, 155, 213, 1); */
  background-color: #01a463;
  border: none;
  border-radius: 5px;
  text-align: center;
  line-height: 50px;
  color: #fff;
  cursor: pointer;
}
.list-li {
  max-height: 50px;
  border-bottom: 1px dashed;
  margin: 10px 0;
  cursor: pointer;
}
.select-title {
  text-align: center;
  margin-top: 0;
  margin-bottom: 20px;
}
.agent {
  text-align: left;
}
.dialog /deep/.el-dialog__body {
  padding-top: 0;
  padding-bottom: 0;
}
.dialog /deep/ .el-card__body {
  padding-top: 0;
  padding-bottom: 0;
}
.info-wrap {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}
.info-wrap img {
  width: 35px;
  height: 35px;
  margin-right: 10px;
}
.info-title {
  font-size: 20px;
  color: #333333;
}
.scan-img {
  position: absolute;
  width: 280px;
  /* height: 408px; */
  right: 10px;
  top: 75px;
}
.scan-img img {
  width: 100%;
}
.item-mindle {
  position: absolute;
  right: -2%;
  top: 45%;
  text-align: left;
}
.radio-item {
  padding: 10px;
  padding-right: 260px;
  border: 1px solid #dce0df;
  border-radius: 6px;
  margin-right: 20px;
}
</style>
