<template>
  <div class="content-wrapper padding-10">
    <CardTitle :title-name="title" :ifback="true" @back="back()"></CardTitle>
    <div id="freeArchives-wrap">
      <el-tabs tab-position="top">
        <el-tab-pane label="详情">
          <detailList @getTitle="getTitle"></detailList>
        </el-tab-pane>
        <el-tab-pane label="过程信息">
          <item-process-info ref="processInfo" />
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script>
import detailList from './components/detailList'
import ItemProcessInfo from './components/ItemProcessInfo.vue'
import CardTitle from '@/components/CardTitle'
export default {
  data() {
    return {
      title: ''
    }
  },
  components: {
    detailList,
    ItemProcessInfo,
    CardTitle
  },
  mounted() {
    // console.log('this.title', this.title)
    // this.title = this.$refs.detailList.freeServiceData.item_name
  },

  methods: {
    getTitle(data) {
      console.log('getTitle', data)
      this.title = data
    },
    back() {
      this.$router.push({ name: 'freeArchives' })
    }
  }
}
</script>

<style lang="scss" scoped>
.content-header-title {
  height: 45px;
  i {
    cursor: pointer;
  }
}
</style>