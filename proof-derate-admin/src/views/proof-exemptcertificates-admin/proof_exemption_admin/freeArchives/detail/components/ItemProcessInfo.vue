<template>
  <div>
    <div v-for="(item,index) in proofRecordList" :key="index">
      <el-row>
        <el-col :span="24">
          <el-card>
            <el-row type="flex" align="middle">
              <el-col :span="3">
                <!-- <span class="status" v-if="item['operation'] ==='待梳理确认'">提交申请</span>
                <span class="status" v-else-if="item['operation'] ==='仍需清理'">驳回清理</span>-->
                <span class="status">{{item['operation']}}</span>
              </el-col>
              <el-col :span="5">经办人: {{item['account_name']}}</el-col>
              <el-col :span="9" :offset="2">经办账号：{{item['account']}}</el-col>
              <el-col :span="2" :offset="2" v-if="item['operation'] ==='待梳理确认'||item['operation'] ==='已完成'">
                <!-- 查看清理初稿 -->
                <!-- <el-button type="text" @click="preliminaryDrafts">查看清理初稿</el-button> -->
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="5" :offset="3">提交时间: {{item['created_date']}}</el-col>
              <el-col :span="9" :offset="2">说明: {{item['procedure_message']==''?'无':item['procedure_message']}}</el-col>
              <el-col :span="2" :offset="2" v-if="index ===0"></el-col>
              <!-- <el-col :span="9" :offset="2" v-if="index ===0">
                <el-button type="text" @click="preliminaryDrafts">查看清理初稿</el-button>
              </el-col>-->
            </el-row>
            <!-- <el-row>
              <el-col :span="1" :offset="2" v-if="index ===0">
                <el-button type="text" @click="preliminaryDrafts">查看清理初稿</el-button>
              </el-col>
            </el-row>-->
          </el-card>
        </el-col>
      </el-row>
      <br />
    </div>
  </div>
</template>

<script>
// import {
//   getProcedureLog
// } from "@/api/itemBiz/list";
import moment from 'moment'
import { association } from '@/api/exemptCertificates'
export default {
  name: 'ItemProcessInfo',
  props: {
    itemCode: {
      type: String,
      default: ''
    },
    dictData: {
      Object,
      default: function () {
        return {}
      }
    },
    proofRoute: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      dataConfig: {
        id: '',
        title: '事项证明档案',
        activeName: 'desc'
      },
      tipInfo: {
        needShowTips: false,
        itemTips: ''
      },
      proofRecordList: [],
      proofRecordList0: null,
      proofRecordList1: null,
      proofRecordList2: null,
      proofStatusList: [] // 事项状态
    }
  },

  watch: {
    tipInfo: {
      handler(val) {
        if (!val) return
        // 把父组件传过来的tableData赋值给tableConfig
        // this.$emit("getTips",val)
      },
      deep: true,
      immediate: true
    }
  },

  mounted() {
    // this.initData()
    this.association()
  },
  methods: {
    initData: function () {
      this.proofStatusList = this.dictData.proofStatusList
      // this.getProofRecordInfo();
    },
    association() {
      if (this.$route.query.serial_number != null) {
        association(this.$route.query.serial_number)
          .then(res => {
            if (res.meta.code === '200' && res.data != null) {
              this.proofRecordList = res.data
              console.log(this.proofStatusList)
            }
          })
          .catch(err => {})
      }
    },
    formatRecordInfo(data) {
      return {
        ...data,
        // confirm_status,
        // operator_type,
        date: moment(data.created_date).format('MM.DD'),
        minute: moment(data.created_date).format('HH:mm'),
        time: moment(data.created_date).format('YYYY-MM-DD HH:mm')
      }
    },
    //传入一个需要排序的数组
    arraySort(obj, key) {
      obj.sort((a, b) => {
        let t1 = new Date(Date.parse(a[key].replace(/-/g, '/')))
        let t2 = new Date(Date.parse(b[key].replace(/-/g, '/')))
        return t1.getTime() - t2.getTime()
      })

      return obj
    },
    preliminaryDrafts() {
      let navKey = this.$route.meta.nav_key
      const route = this.$router.resolve({
        name: navKey + '_info_first_draft',
        query: {
          id: this.itemCode
        }
      })
      window.open(route.href, '_blank')
    }
  }
}
</script>

<style lang="scss" scoped>
.time {
  color: #919191;
}
.status {
  // color: #2d76ce;
  color: #01a463;
  font-size: 19px;
  font-weight: 900;
}
</style>
