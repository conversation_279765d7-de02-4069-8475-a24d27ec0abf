<template>
  <div class="content-wrapper1">
    <section class="content-header">
      <!-- <h1>{{freeServiceData.item_name}}</h1>
      <br />-->
      <!-- <el-card class="box-card box-card1"> -->
      <!-- <div slot="header" class="cardtitle">
          <span>服务对象</span>
      </div>-->
      <span class="margin-left-10 info-wrap info-wrap-first">
        <div class="info-wrap-title">
          <img :src="arrow" alt />
          <span class="info-title">事项基本信息</span>
        </div>
      </span>
      <el-descriptions class="margin-top margin-bottom-10" title :column="2" border>
        <el-descriptions-item label-class-name="left-label" :label-style="{width:'150px'}" content-class-name="right-content">
          <template slot="label">事项名称</template>
          {{ freeServiceData.item_name }}
        </el-descriptions-item>
        <el-descriptions-item label-class-name="left-label" :label-style="{width:'150px'}" content-class-name="right-content">
          <template slot="label">事项编码</template>
          {{ freeServiceData.item_code }}
        </el-descriptions-item>
        <el-descriptions-item label-class-name="left-label" :label-style="{width:'150px'}" content-class-name="right-content">
          <template slot="label">事项所属行政区划</template>
          {{ freeServiceData.item_division_name }}
        </el-descriptions-item>
        <el-descriptions-item label-class-name="left-label" :label-style="{width:'150px'}" content-class-name="right-content">
          <template slot="label">事项实施机构</template>
          {{ freeServiceData.item_org_name }}
        </el-descriptions-item>
      </el-descriptions>
      <span class="margin-left-10 info-wrap">
        <img :src="arrow" alt />
        <span class="info-title">服务对象</span>
      </span>
      <el-descriptions class="escriptions" title :column="2" border>
        <el-descriptions-item label-class-name="left-label" :label-style="{width:'150px'}" content-class-name="right-content">
          <template slot="label">业务办理号</template>
          {{ freeServiceData.serial_number }}
        </el-descriptions-item>
        <el-descriptions-item
          v-if="!isLegalPerson"
          label-class-name="left-label"
          :label-style="{width:'150px'}"
          content-class-name="right-content"
        >
          <template slot="label">办事人</template>
          {{ freeServiceData.handle_affairs_name }}
        </el-descriptions-item>
        <el-descriptions-item
          v-if="isLegalPerson"
          label-class-name="left-label"
          :label-style="{width:'150px'}"
          content-class-name="right-content"
        >
          <template slot="label">企业或机构名称</template>
          {{ freeServiceData.biz_org_name }}
        </el-descriptions-item>
        <el-descriptions-item
          v-if="isLegalPerson"
          label-class-name="left-label"
          :label-style="{width:'150px'}"
          content-class-name="right-content"
        >
          <template slot="label">企业或机构证件类型</template>
          {{ freeServiceData.biz_org_identity_type_name }}
        </el-descriptions-item>
        <el-descriptions-item
          v-if="isLegalPerson"
          label-class-name="left-label"
          :label-style="{width:'150px'}"
          content-class-name="right-content"
        >
          <template slot="label">企业或机构证件号码</template>
          {{ freeServiceData.biz_org_identity_num }}
        </el-descriptions-item>
        <el-descriptions-item
          v-if="!isLegalPerson"
          label-class-name="left-label"
          :label-style="{width:'150px'}"
          content-class-name="right-content"
        >
          <template slot="label">证件类型</template>
          {{ freeServiceData.identity_type_name }}
        </el-descriptions-item>
        <el-descriptions-item
          v-if="!isLegalPerson"
          label-class-name="left-label"
          :label-style="{width:'150px'}"
          content-class-name="right-content"
        >
          <template slot="label">证件号码</template>
          {{ freeServiceData.identity_number }}
        </el-descriptions-item>
        <el-descriptions-item
          v-if="isLegalPerson"
          label-class-name="left-label"
          :label-style="{width:'150px'}"
          content-class-name="right-content"
        >
          <template slot="label">经办人名称</template>
          {{ freeServiceData.handle_affairs_name }}
        </el-descriptions-item>
        <el-descriptions-item
          v-if="isLegalPerson"
          label-class-name="left-label"
          :label-style="{width:'150px'}"
          content-class-name="right-content"
        >
          <template slot="label">证件类型</template>
          {{ freeServiceData.to_user_id_type_name }}
        </el-descriptions-item>
        <el-descriptions-item
          v-if="isLegalPerson"
          label-class-name="left-label"
          :label-style="{width:'150px'}"
          content-class-name="right-content"
        >
          <template slot="label">证件号码</template>
          {{ freeServiceData.identity_number }}
        </el-descriptions-item>
      </el-descriptions>
      <el-card class="box-card box-card2" :body-style="{'padding':'20px 0px'}">
        <!-- <div slot="header" class="cardtitle">
          <span>事项办理材料</span>
          <img :src="iconImg" v-popover:popover />
          <el-popover ref="popover" placement="right" title="办理条件" width="706" trigger="hover" popper-class="popoverwrap">
            <p style="line-height:22px">
              {{freeServiceData.accept_condition}}
            </p>
          </el-popover>
        </div>-->
        <span class="margin-left-10 info-wrap" style="justify-content: space-between;">
          <div class="info-wrap-title">
            <img :src="arrow" class="icon" alt />
            <span class="info-title">
              事项办理材料
              <!-- <img :src="iconImg" class="icon" v-popover:popover /> -->
              <el-popover ref="popover" placement="right" title="办理条件" width="706" trigger="hover" popper-class="popoverwrap">
                <p style="line-height:22px">{{ freeServiceData.accept_condition }}</p>
              </el-popover>
            </span>
          </div>
          <el-button type="primary" icon="el-icon-download" v-if="hasfile" @click="getReplaceWayArchivist">一键下载</el-button>
        </span>
        <!-- <section class="content">
        <el-card class="box-card">-->
        <custom-table
          ref="table"
          :is-card-type="false"
          :table-data="tableData"
          :table-header="tableHeader"
          :stripe="false"
          :table-tools="tableTools"
          class="customTable"
          :span-method="objectSpanMethod"
          @refresh="query(1)"
          @query="query"
          @allcheck="allcheck"
        >
          <template #material_name="{ row }">
            <div v-if="row.replace_cancel_way==='转化为电子证照'">
              <span class="breakword">
                {{ row.material_name }}
                <i class="el-icon-postcard" />
              </span>
            </div>
            <div v-else>
              <!-- {{ row.material_name }} -->
              <span class="breakword">{{ row.material_name }}</span>
            </div>
          </template>
          <template #attachment_list="{ row ,$index}">
            <div
              v-for="(item,key) in row.attachment_list"
              :key="key"
              style="display: flex;justify-content: space-between;padding: 5px 29px;"
              @click="downloadList(row,item)"
            >
              <span style="color: #409eff;cursor:pointer;display: flex;align-items: center;">
                <img style="height:18px;margin-right:5px;" :src="fileImg" alt />
                <span style="width: 184px;white-space: normal;">{{ item.file_name }}</span>
              </span>
              <!-- <span style="color: #9E9E9E;cursor: pointer;" @click="delectRow(row,key)">x</span> -->
            </div>
          </template>
          <template #operate="{ row, $index }">
            <div v-if="row.replace_cancel_way==='转化为电子证照'">
              <div v-if="row.auth_cod!=''&&row.auth_code!=null&&row.valid_license_error_msg==null">
                <el-button type="text" v-permission="'exempt:use:service:view_license'" @click="getView(row)">查看证照</el-button>
              </div>
              <div v-else-if="(row.auth_cod===''||row.auth_cod==null)&&row.valid_license_error_msg===undefined">
                <el-button type="text" disabled @click="importData(row,$index)">系统未上传材料</el-button>
              </div>
              <div v-else-if="row.valid_license_error_msg!=''">
                <p>
                  <span class="word">
                    <span v-if="!(row.attachment_name!=''&&row.attachment_name!=null)" style="margin-left:10px;color:#ccc">用户拒绝授权</span>
                  </span>
                </p>
              </div>
            </div>
            <div v-else-if="row.replace_cancel_way==='部门间协查'">
              <div v-if="row.config_assist_user==true">
                <el-button v-if="row.assist_serial_number===null" type="text" disabled @click="application(row)">系统未发起协查</el-button>
                <el-button
                  v-else-if="row.audit_result!='WAIT'&&row.assist_serial_number!=null"
                  type="text"
                  @click="getInvestigationDetail(row)"
                >查看</el-button>
                <span v-else-if="row.audit_result==='WAIT'" style="margin-left:10px;color:#ccc">协查处理中</span>
              </div>
              <div v-else-if="row.config_assist_user==false">
                <p v-if="row.attachment_name!=''&&row.attachment_name!=null">
                </p>
                <p v-else>
                  <span style="margin-left:10px;color:#ccc">未配置协查人员处理</span>
                </p>
              </div>
            </div>
            <div v-else-if="row.replace_cancel_way==='数据共享'">
              <!-- <span>无</span> -->
              <el-button v-if="row.hasData" type="text" @click="getDeatil(row)">查看数据</el-button>
              <span v-else style="margin-left:10px;color:#ccc">系统未获取数据</span>
            </div>
            <div v-else-if="row.replace_cancel_way==='电子证明'">
              <el-button v-if="row.apply_license==true&&row.auth_code!==null" type="text" @click="getLicenseViewUrl(row)">查看电子证明</el-button>
              <span v-if="row.apply_license==true&&row.auth_code==null" style="margin-left:10px;color:#ccc">电子证明待签发</span>
            </div>
            <div v-else-if="row.replace_cancel_way==='告知承诺'">
            </div>
            <div v-else-if="row.replace_cancel_way==='直接取消'">
              <span>无</span>
            </div>
            <div v-else>
            </div>
          </template>
          <template #license_name="{ row }">
            <div v-if="row.license_name!=null&&row.replace_cancel_way=='转化为电子证照'" @click="getView(row)">
              <span style="color: #409eff;cursor: pointer;">
                <i class="el-icon-postcard" />
                {{ row.license_name }}
              </span>
            </div>
          </template>
          <template #verification_and_inspection="{ row }">
            <div>
              <el-checkbox v-model="row.verification_and_inspection" />
              <!-- {{row.verification_and_inspection +1}} -->
            </div>
          </template>
          <template #archivedFile="{ row }">
            <div>
              <el-checkbox v-model="row.archivedFile" />
              <!-- {{row.verification_and_inspection +1}} -->
            </div>
          </template>
          <!-- row.replace_cancel_way=='直接取消'|| -->
          <template #replace_cancel_way="{ row }">
            <span
              v-if="row.replace_cancel_way=='电子证明'||row.replace_cancel_way=='其他'||row.replace_cancel_way=='转化为电子证照'||row.replace_cancel_way=='自行调查'||row.replace_cancel_way=='部门间协查'||row.replace_cancel_way=='数据共享'"
              class="greenWord"
            >{{ row.replace_cancel_way }}</span>
            <span v-else-if="row.replace_cancel_way=='告知承诺'" class="greenWord">{{ row.replace_cancel_way }}</span>
            <span v-else-if="row.replace_cancel_way=='需要提交'" class="redWord">{{ row.replace_cancel_way }}</span>
            <span v-else>{{ row.replace_cancel_way }}</span>
          </template>
        </custom-table>
      </el-card>
      <el-card v-if="isDetail=='1'" class="box-card box-card3">
        <!-- <div slot="header" class="cardtitle">
          <span>办理意见</span>
        </div>-->
        <span class="margin-left-10 info-wrap">
          <img :src="arrow" alt />
          <span class="info-title">办理意见</span>
        </span>

        <el-descriptions class="escriptions" :column="2" border>
          <el-descriptions-item label-class-name="left-label" :label-style="{width:'150px'}" content-class-name="right-content">
            <template slot="label">操作人</template>
            {{ freeServiceData.operate_name }}
          </el-descriptions-item>
          <el-descriptions-item label-class-name="left-label" :label-style="{width:'150px'}" content-class-name="right-content">
            <template slot="label">操作部门</template>
            {{ freeServiceData.operate_org_name }}
          </el-descriptions-item>
          <el-descriptions-item label-class-name="left-label" :label-style="{width:'150px'}" content-class-name="right-content">
            <template slot="label">操作时间</template>
            {{ freeServiceData.operate_time }}
          </el-descriptions-item>
          <el-descriptions-item label-class-name="left-label" :label-style="{width:'150px'}" content-class-name="right-content">
            <template slot="label">经办人</template>
            {{ freeServiceData.to_user_name }}
          </el-descriptions-item>
          <el-descriptions-item label-class-name="left-label" :label-style="{width:'150px'}" content-class-name="right-content">
            <template slot="label">经办部门</template>
            {{ freeServiceData.to_user_org }}
          </el-descriptions-item>
          <el-descriptions-item label-class-name="left-label" :label-style="{width:'150px'}" content-class-name="right-content">
            <template slot="label">办理结果</template>
            {{ freeServiceData.process_result_text }}
          </el-descriptions-item>
          <el-descriptions-item label-class-name="left-label" :label-style="{width:'150px'}" content-class-name="right-content">
            <template slot="label">办理时间</template>
            {{ freeServiceData.process_time }}
          </el-descriptions-item>
          <el-descriptions-item label-class-name="left-label" :label-style="{width:'150px'}" content-class-name="right-content">
            <template slot="label">意见内容</template>
            {{ freeServiceData.process_comment }}
          </el-descriptions-item>
        </el-descriptions>
      </el-card>
    </section>
    <el-dialog title="查看电子证照" :visible.sync="dialogVisible" width="40%" class="card-dialog" :center="true" :show-close="false">
      <div class="card-wrap">
        <img :src="idcard" alt />
        <img :src="idcardBack" alt />
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="dialogVisible = false">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog title="上传承诺书模板" :visible.sync="dialogVisible1" width="40%" :before-close="cancle">
      <div class>
        <p>请选择您要导入的数据</p>
        <el-row :gutter="24" justify="center" align="middle" type="flex">
          <el-col :span="20">
            <el-input v-model="fileForm.fileName" placeholder />
          </el-col>
          <el-col :span="4">
            <el-upload
              :auto-upload="false"
              :show-file-list="false"
              class="upload-demo"
              action
              :on-change="handleChange"
              :file-list="fileList"
              accept=".doc, .docx, .pdf"
            >
              <el-button type="primary">浏览</el-button>
            </el-upload>
          </el-col>
        </el-row>
        <!-- <p class="tip">
          <i class="el-icon-info"></i> 温馨提示：请选择以.xlsx为后缀名的文件！
          <el-button type="text" @click="getUserTemplate">下载导入模板</el-button>
        </p>-->
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="cancle()">取 消</el-button>
        <el-button type="primary" :loading="fileLoading" @click="importFile()">导入</el-button>
      </span>
    </el-dialog>
    <deptDialog
      :dialog-visible="dialogVisible2"
      :free-service-data="freeServiceData"
      :org-list="orgList"
      :select-row="selectionRow"
      @deptSumbit="deptSumbit"
      @deptCancle="deptCancle"
    />
    <legalpersonDialog
      :dialog-visible="dialogVisible3"
      :free-service-data="freeServiceData"
      :org-list="orgList"
      :select-row="selectionRow"
      @deptSumbit="deptSumbit1"
      @deptCancle="deptCancle1"
    />
    <fileDialog
      ref="fileDialog"
      :dialog-visible="dialogVisible4"
      :table-data-content="tableData.content"
      @deptSumbit="deptSumbit2"
      @deptCancle="deptCancle2"
    />
    <electronicCertificationDialog
      :dialog-visible="dialogVisible6"
      :free-service-data="freeServiceData"
      :table-index="tableIndex"
      @dialogCancle="electronicCertificationDialogCancle"
      @sendiIssueLicenseApply="issueLicenseApply"
    />
    <dataSharingQueryDialog
      :dialog-visible="dialogVisible7"
      :data-sharing-query-data="dataSharingQueryData"
      @dataSharingQuerySumbit="dataSharingQuerySumbit"
      @dataSharingQueryCancle="dataSharingQueryCancle"
    />
    <datashareingDialog
      ref="datashareingDialog"
      :dialog-visible="dialogVisible5"
      :data-list="dataList"
      :data-sharing-query-data="dataSharingQueryData"
      @dialogCancle="datashareingDialogCancle"
    />
  </div>
</template>

<script>
import CustomTable from '@/components/Element/Table'
import deptDialog from '@/views/proof-exemptcertificates-admin/proof_exemption_admin/freeService/checkpage/components/deptDialog'
import legalpersonDialog from '@/views/proof-exemptcertificates-admin/proof_exemption_admin/freeService/checkpage/components/legalpersonDialog'
import fileDialog from '@/views/proof-exemptcertificates-admin/proof_exemption_admin/freeService/checkpage/components/fileDialog'
import electronicCertificationDialog from '@/views/proof-exemptcertificates-admin/proof_exemption_admin/freeService/checkpage/components/electronicCertificationDialog'
import dataSharingQueryDialog from '@/views/proof-exemptcertificates-admin/proof_exemption_admin/freeService/checkpage/components/dataSharingQueryDialog'
import datashareingDialog from '@/views/proof-exemptcertificates-admin/proof_exemption_admin/freeService/checkpage/components/dataShareingDialog'
import { getExemptIdentityType } from '@/api/common/dict'
import {
  holdUp,
  getView,
  getManagerDetail,
  uploadFile,
  download,
  attachmentArchiving,
  getLicenseViewUrl,
  getYssYstView,
  getDataSharedConfig,
  getDataSharedData,
  getDataSharedDataArchivist,
  getReplaceWayArchivist
} from '@/api/exemptCertificates'
import { dataURLtoDownload, base64toBlob } from '@/utils/index.js'
import { getViewBySerialNumber, getAssistOrg, getAssist } from '@/api/assistInvestigate'
import { downloadFile } from '@/api/common/download'
export default {
  name: 'CheckPage',
  components: {
    CustomTable,
    deptDialog,
    legalpersonDialog,
    fileDialog,
    electronicCertificationDialog,
    dataSharingQueryDialog,
    datashareingDialog
  },
  data() {
    return {
      iconImg: require('@/assets/proof-exemptcertificates-admin-images/icon.png'),
      fileImg: require('@/assets/images/fileList.png'),
      deptForm: {
        eventName: '',
        dep: ''
      },
      freeServiceData: {
        serial_number: '',
        handle_affairs_name: '',
        identity_type: '',
        identity_number: '',
        biz_org_name: '',
        biz_org_identity_type: '',
        biz_org_identity_type_name: '',
        biz_org_identity_num: '',
        to_user_account: '',
        to_user_name: '',
        to_user_org: '',
        to_user_org_code: '',
        item_name: '',
        identity_type_name: ''
      },
      dataSharingQueryData: [],
      tableIndex: 0,
      // rules: [],
      checkform: {
        code: '',
        eventName: ''
      },
      searchForm: {
        name: '',
        region: ''
      },
      fileForm: {
        fileName: '',
        file: ''
      },
      checkbox: {
        checked: true
      },
      dataList: {
        errormes: ''
      },
      loading: false,
      isDetail: '0',
      alltableCheck: true,
      dialogVisible: false,
      dialogVisible1: false,
      dialogVisible2: false,
      dialogVisible3: false,
      dialogVisible4: false,
      dialogVisible5: false,
      dialogVisible6: false,
      dialogVisible7: false,
      selectFileIndex: 0, // table中选择上传文件的数据序号
      fileLoading: false,
      idcard: require('@/assets/proof-exemptcertificates-admin-images/card.png'),
      idcardBack: require('@/assets/proof-exemptcertificates-admin-images/cardback.png'),
      arrow: require('@/assets/proof-exemptcertificates-admin-images/arrow.png'),
      fileList: [],
      tableData: {
        content: [], // 表格数据
        loading: false, // 控制表格加载效果
        total: 0, // 分页查询时的总数，去掉该属性，不显示分页条
        currentPage: 1, // 当前页码
        pageSize: 10,
        pageDirection: 'desc',
        isShowSelection: false, // 是否显示多选框，默认false
        isShowIndex: true
      },
      tableData11: {
        content: [], // 表格数据
        loading: false, // 控制表格加载效果
        total: 0, // 分页查询时的总数，去掉该属性，不显示分页条
        currentPage: 1, // 当前页码
        pageSize: 10,
        pageDirection: 'desc',
        isShowSelection: false, // 是否显示多选框，默认false
        isShowIndex: true
      },
      tableHeader11: [
        { label: 'ID', prop: 'id', minWidth: '160px' },
        { label: 'name', prop: 'name', minWidth: '160px' },
        { label: 'amount1', prop: 'amount1', minWidth: '160px' },
        { label: 'amount2', prop: 'amount2', minWidth: '160px' },
        { label: 'amount3', prop: 'amount3', minWidth: '160px' }
        // { label: 'amount4', prop: 'amount4',  minWidth: '160px' },
      ],
      tableHeader: [
        { label: '材料名称', prop: 'material_name', slot: 'material_name', minWidth: '160px', align: 'left' }, // 配置slot属性，可支持使用插槽
        { label: '证明目录', prop: 'proof_catalog_name', minWidth: '160px', align: 'left' },
        {
          label: '替代方式',
          prop: 'replace_cancel_way',
          slot: 'replace_cancel_way',
          minWidth: '200px',
          align: 'left'
        },
        { label: '文件', prop: 'attachment_list', slot: 'attachment_list', minWidth: '180px', align: 'left' },
        // { label: '电子证照', prop: 'license_name', slot: 'license_name', minWidth: '180px' },
        { label: '操作', slot: 'operate', width: '180px', fixed: 'right', align: 'left' }
        // {
        //   label: '是否核检',
        //   prop: 'verification_and_inspection',
        //   slot: 'verification_and_inspection',
        //   minWidth: '80px'
        // }
        // isHeaderslot 与 prop 不要同名
        // {
        //   label: '归档',
        //   prop: 'archivedFile',
        //   slot: 'archivedFile',
        //   width: '120px',
        //   fixed: 'right',
        //   isHeaderslot: 'archivedFile_c',
        //   headerchecked: true
        // }
        //  { label: '归档', prop: 'archivedFile',slot: 'archivedFile', width: '120px', fixed: 'right'}
      ],
      tableTools: [],
      rules: {
        code: [{ required: true, message: '请输入授权码', trigger: 'blur' }],
        eventName: [{ required: true, message: '请输入事件名称', trigger: 'blur' }]
      },
      isLegalPerson: false,
      exemptIdentityTypeList: [],
      investigation_detail_list: [],
      selectionRow: {},
      orgList: [],
      userIdType: [
        { value: '10', label: '身份证' },
        { value: '11', label: '军官证' },
        { value: '12', label: '士兵证' },
        { value: '13', label: '警官证' },
        { value: '14', label: '港澳居民来往内地通行证' },
        { value: '15', label: '台湾居民来往大陆通行证' },
        { value: '40', label: '其他有效个人身份证件' }
      ],
      proof_list_id: '',
      hasfile: false
    }
  },
  watch: {},
  mounted() {
    console.log('this.$route.meta.permission', this.$route.meta.permission)
    this.ininData()
  },
  beforeRouteLeave(to, from, next) {
    from.meta.keepAlive = false
    next()
  },
  methods: {
    eventChose() {
      this.dialogVisible = true
    },
    ininData() {
      const serial_number = this.$route.query.serial_number

      this.getExemptIdentityType().then(() => {
        this.getManagerDetail(serial_number)
      })
      this.isDetail = this.$route.query.isDetail
    },
    getExemptIdentityType() {
      return getExemptIdentityType().then(res => {
        if (res.meta.code === '200' && res.data != null) {
          this.exemptIdentityTypeList = res.data
        }
      })
    },
    handleClose() {},
    searchFrom() {},
    application(row) {
      this.getAssistOrg(row.id)
      this.selectionRow = row
      if (this.isLegalPerson) {
        this.dialogVisible3 = true
      } else {
        this.dialogVisible2 = true
      }
    },
    getDeatil(row) {
      console.log('getDeatil', row)
      // if (row.replace_cancel_way === '转化为电子证照') {
      //   this.dialogVisible = true
      // } else if (row.replace_cancel_way === '部门间协查') {
      //   this.$router.push({
      //     name: 'depAssist'
      //   })
      // }
      this.selectrow = row
      const data = {
        serial_number: row.serial_number,
        proof_list_id: row.proof_list_id
      }
      this.proof_list_id = row.proof_list_id
      getDataSharedConfig(data).then(res => {
        console.log(res)
        if (res.meta.code === '200' && res.data !== null) {
          // this.dialogVisible7 = true
          this.dataSharingQueryData = res.data
          this.dataSharingQuerySumbit()
        }
      })
    },
    downDataShar(row) {
      console.log('downDataShar', row)
      const data = {
        serial_number: row.serial_number,
        proof_list_id: row.proof_list_id
      }
      getDataSharedDataArchivist(data).then(res => {
        console.log(res)
        if (res.data !== null && res.meta.code === '200') {
          dataURLtoDownload(res.data.fileDataByBase64, res.data.file_name)
        }
      })
    },
    importFile() {
      this.tableData.content[this.selectFileIndex].attachment_name = this.fileForm.fileName
      this.tableData.content[this.selectFileIndex].raw = this.fileForm.file
      this.dialogVisible1 = false
      this.uploadFile(this.tableData.content[this.selectFileIndex].raw)
      // console.log(this.selectFileIndex, this.tableData.content[this.selectFileIndex])
    },
    importData(row, index) {
      this.dialogVisible1 = true
      this.selectFileIndex = index
    },
    // 下载当前上传的文件
    downFile(row, index) {
      if (this.tableData.content[index].raw == undefined) {
        this.download(this.tableData.content[index])
      } else {
        const blob = new Blob([this.tableData.content[index].raw])
        const downLink = document.createElement('a')
        downLink.download = this.tableData.content[index].attachment_name
        downLink.href = URL.createObjectURL(blob)
        // 触发点击
        document.body.appendChild(downLink)
        downLink.click()
        // 然后移除
        document.body.removeChild(downLink)
      }
      // if (this.fileForm.file != '') {

      // }
    },
    // 下载归档文件
    downArchivingFile(row, index) {
      row.archiving_id_list.forEach(e => {
        const fileForm = {
          exempt_down_file_replace_way_type: 'LICENSE_ATTACHMENT_ARCHIVING',
          file_id: e.archiving_id,
          replace_way_id: row.id
        }
        download(fileForm).then(res => {
          console.log('download', res)
          if (res.data != null) {
            dataURLtoDownload(res.data, e.archiving_name)
          }
        })
      })
    },
    downFileList(row, item) {
      console.log('item', item)
      if (item.raw == undefined) {
        this.downloadList(row, item)
      } else {
        const blob = new Blob([item.raw])
        const downLink = document.createElement('a')
        downLink.download = item.file_name
        downLink.href = URL.createObjectURL(blob)
        // 触发点击
        document.body.appendChild(downLink)
        downLink.click()
        // 然后移除
        document.body.removeChild(downLink)
      }
    },
    downloadList(row, item) {
      downloadFile({ id: item.id }).then(res => {
        dataURLtoDownload(res.data.file_data_base64, res.data.file_name)
      })
    },
    // downFileList(row, item) {
    //   console.log('item', item)
    //   if (item.raw == undefined) {
    //     this.downloadList(row, item)
    //   } else {
    //     const blob = new Blob([item.raw])
    //     const downLink = document.createElement('a')
    //     downLink.download = item.file_name
    //     downLink.href = URL.createObjectURL(blob)
    //     // 触发点击
    //     document.body.appendChild(downLink)
    //     downLink.click()
    //     // 然后移除
    //     document.body.removeChild(downLink)
    //   }
    // },
    // downloadList(row, item) {
    //   const fileForm = {
    //     exempt_down_file_replace_way_type: '',
    //     file_id: item.id,
    //     replace_way_id: row.id
    //   }
    //   if (row.replace_cancel_way === '无需清理') {
    //     fileForm.exempt_down_file_replace_way_type = 'DO_NOT_CLEAN'
    //   }
    //   if (row.replace_cancel_way === '其他') {
    //     fileForm.exempt_down_file_replace_way_type = 'OTHER'
    //   }
    //   if (row.replace_cancel_way === '告知承诺') {
    //     fileForm.exempt_down_file_replace_way_type = 'CLERK_COMMITMENT'
    //     fileForm.file_id = row.commit_attachment_id
    //   }
    //   if (row.replace_cancel_way === '直接取消') {
    //     fileForm.exempt_down_file_replace_way_type = 'DIRECTLY_CANCEL'
    //   }
    //   if (row.replace_cancel_way === '自行调查') {
    //     fileForm.exempt_down_file_replace_way_type = 'DEPT_SURVEY'
    //   }
    //   if (row.replace_cancel_way === '数据共享') {
    //     fileForm.exempt_down_file_replace_way_type = 'DATA_SHARING'
    //   }
    //   if (row.replace_cancel_way === '部门间协查') {
    //     fileForm.exempt_down_file_replace_way_type = 'INVESTIGATION'
    //   }

    //   if (row.replace_cancel_way === '转化为电子证照') {
    //     // 是否有归档文件
    //     if (row.archiving_id != '' && row.archiving_id != null) {
    //       fileForm.exempt_down_file_replace_way_type = 'LICENSE_ATTACHMENT_ARCHIVIN'
    //     } else {
    //       fileForm.exempt_down_file_replace_way_type = 'LICENSE'
    //     }
    //   }
    //   downloadFile({ id: item.id }).then(res => {
    //     if (row.replace_cancel_way != '告知承诺') {
    //       dataURLtoDownload(res.data.file_data_base64, res.data.file_name)
    //     } else {
    //       dataURLtoDownload(res.data.file_data_base64, res.data.file_name)
    //     }
    //   })
    // },
    handleChange(file, fileList) {
      this.fileForm.fileName = file.name
      this.fileForm.file = file.raw
    },
    getFileType(filePath) {
      const startIndex = filePath.lastIndexOf('.')
      if (startIndex != -1) {
        return filePath.substring(startIndex + 1, filePath.length).toLowerCase()
      } else {
        return ''
      }
    },
    getView(row) {
      console.log(row)
      if (row.auth_code_source === 'HFW') {
        const parmas = {
          auth_code: row.auth_code,
          replace_way_id: row.id
        }
        const serial_number = row.serial_number
        getView(parmas, serial_number).then(res => {
          // console.log(res)
          if (res.data != null && res.meta.code === '200') {
            window.open(res.data)
            row.verification_and_inspection = true
          } else {
            this.$message({
              message: res.meta.message,
              type: 'warning'
            })
          }
        })
      } else {
        // this.getYssYstView(row)
        const param = {
          auth_token: row.auth_code,
          replace_way_id: row.id,
          serial_number: row.serial_number,
          auth_code_source: row.auth_code_source
        }
        const outlink = this.$router.resolve({ name: 'outLink', query: { data: JSON.stringify(param) } })
        console.log('outlink.href',outlink.href)
        window.open(outlink.href, '_blank')
      }
    },
    cancle() {
      this.fileForm.fileName = ''
      this.fileForm.file = ''
      this.dialogVisible1 = false
    },
    back() {
      this.$router.push({
        name: 'freeArchives'
      })
    },
    holdUp() {
      const userdata = this.$store.state.user.userdata
      this.freeServiceData.to_user_name = userdata.userAccount.name
      this.freeServiceData.to_user_account = userdata.userAccount.account
      this.freeServiceData.to_user_org = userdata.userInfo.orgName
      this.freeServiceData.to_user_org_code = userdata.userInfo.orgCode
      this.freeServiceData.process_result = 'TEMPORARY_STORAGE'
      this.loading = true
      holdUp(this.freeServiceData)
        .then(res => {
          if (res.meta.code === '200' && res.data !== '') {
            this.$message({
              message: '暂存成功',
              type: 'success'
            })
            getManagerDetail(res.data)
              .then(res => {
                if (res.meta.code === '200' && res.data != null) {
                  this.loading = false
                  this.freeServiceData = Object.assign(this.freeServiceData, res.data)
                  // console.log('this.freeServiceData', this.freeServiceData)
                }
              })
              .catch(() => {
                this.loading = false
              })
          } else {
            this.$message({
              message: res.meta.message,
              type: 'warning'
            })
          }
        })
        .catch(() => {
          this.loading = false
        })
    },
    download(row) {
      const fileForm = {
        exempt_down_file_replace_way_type: '',
        file_id: row.attachment_id,
        replace_way_id: row.id
      }
      if (row.replace_cancel_way === '无需清理') {
        fileForm.exempt_down_file_replace_way_type = 'DO_NOT_CLEAN'
      }
      if (row.replace_cancel_way === '其他') {
        fileForm.exempt_down_file_replace_way_type = 'OTHER'
      }
      if (row.replace_cancel_way === '告知承诺') {
        fileForm.exempt_down_file_replace_way_type = 'CLERK_COMMITMENT'
        fileForm.file_id = row.commit_attachment_id
      }
      if (row.replace_cancel_way === '直接取消') {
        fileForm.exempt_down_file_replace_way_type = 'DIRECTLY_CANCEL'
      }
      if (row.replace_cancel_way === '自行调查') {
        fileForm.exempt_down_file_replace_way_type = 'DEPT_SURVEY'
      }
      if (row.replace_cancel_way === '部门间协查') {
        fileForm.exempt_down_file_replace_way_type = 'INVESTIGATION'
      }
      if (row.replace_cancel_way === '转化为电子证照') {
        // 是否有归档文件
        if (row.archiving_id != '' && row.archiving_id != null) {
          fileForm.exempt_down_file_replace_way_type = 'LICENSE_ATTACHMENT_ARCHIVIN'
        } else {
          fileForm.exempt_down_file_replace_way_type = 'LICENSE'
        }
      }
      download(fileForm).then(res => {
        if (row.replace_cancel_way != '告知承诺') {
          dataURLtoDownload(res.data, row.attachment_name)
        } else {
          dataURLtoDownload(res.data, row.commit_attachment_name)
        }
      })
    },
    downloadarchiving() {
      // download().then(res=>{
      //   console.log(res)
      // })
    },
    getInvestigationDetail(row) {
      row.verification_and_inspection = true
      const url = this.$router.resolve({
        name: 'investigationDetail',
        query: {
          type: 'archives',
          id: row.assist_id
        }
      })
      window.open(url.href, '_blank')
    },

    getManagerDetail(serial_number) {
      getManagerDetail(serial_number)
        .then(res => {
          if (res.meta.code === '200' && res.data != null) {
            this.loading = false
            this.freeServiceData = Object.assign(this.freeServiceData, res.data)
            console.log('this.freeServiceData', this.freeServiceData)
            this.$emit('getTitle', this.freeServiceData.item_name)
            // this.tableData.content = [
            //   {proof_catalog_name:'电子证明',
            //   material_name:'电子证明',
            //   replace_cancel_way:'电子证明'
            //   }
            // ]
            //  this.tableData.content = this.freeServiceData.proof_list_bo_list
            this.freeServiceData.proof_list_bo_list.forEach((e, index) => {
              // 免证办无需清理表
              if (e.do_not_clean_detail_list != null && e.do_not_clean_detail_list.length != 0) {
                //  e.replace_cancel_way = '无需清理'
                e.do_not_clean_detail_list.forEach(e1 => {
                  e1.proof_catalog_name = e.proof_catalog_name
                  e1.material_name = e.material_name
                  this.tableData.content.push(e1)
                })
              }
              // 免证办直接取消表
              if (e.directly_cancel_detail_list != null && e.directly_cancel_detail_list.length != 0) {
                // e.replace_cancel_way = '直接取消'
                e.directly_cancel_detail_list.forEach(e1 => {
                  e1.proof_catalog_name = e.proof_catalog_name
                  e1.material_name = e.material_name
                  this.tableData.content.push(e1)
                })
              }
              // 免证办替代方式之其它表
              if (e.other_detail_list != null && e.other_detail_list.length != 0) {
                // e.replace_cancel_way = '其它表'
                e.other_detail_list.forEach(e1 => {
                  e1.proof_catalog_name = e.proof_catalog_name
                  e1.material_name = e.material_name
                  this.tableData.content.push(e1)
                })
              }
              // 免证办材料电子证照清理方式
              if (e.license_detail_list != null && e.license_detail_list.length != 0) {
                //  e.replace_cancel_way = '转化为电子证照'
                e.license_detail_list.forEach(e1 => {
                  e1.proof_catalog_name = e.proof_catalog_name
                  e1.material_name = e.material_name
                  e1.archiving_id_list = []
                  if (e1.exempt_license_detail_list != null) {
                    e1.exempt_license_detail_list.forEach(e2 => {
                      e1.proof_catalog_name = e.proof_catalog_name
                      if (e2.archiving_id != null) {
                        e1.archiving_id_list.push(e2)
                      }
                    })
                  }
                  this.tableData.content.push(e1)
                })
              }

              // 免证办材料电子证明
              if (e.license_item_detail != null) {
                //  e.replace_cancel_way = '转化为电子证照'
                // e.license_item_detail.forEach(e1 => {
                //   e1.proof_catalog_name = e.proof_catalog_name
                //   e1.material_name = e.material_name
                //   e1.replace_cancel_way = e.replace_cancel_way

                // })
                e.license_item_detail.proof_catalog_name = e.proof_catalog_name
                e.license_item_detail.material_name = e.material_name
                e.license_item_detail.index = index
                // e.license_item_detail.verification_and_inspection = e.license_item_detail.apply_license
                this.tableData.content.push(e.license_item_detail)
              }

              // 替代方式之人工部门协查表
              if (e.investigation_detail_list != null && e.investigation_detail_list.length != 0) {
                //  e.replace_cancel_way = '部门间协查'
                e.investigation_detail_list.forEach(e1 => {
                  e1.proof_catalog_name = e.proof_catalog_name
                  e1.material_name = e.material_name
                  e1.audit_result = ''
                  this.tableData.content.push(e1)
                  e1.tableIndex = e.index
                  // e1.config_assist_user = false
                  this.investigation_detail_list.push(e1)
                  // if (e1.assist_serial_number != null && e1.assist_serial_number != undefined) {
                  //   this.getViewBySerialNumber(e1).then(() => {})
                  //   // e1.audit_result = 'WAIT'
                  // }
                })
              }
              // 替代方式之部门自行调查表
              if (e.dept_survey_detail_list != null && e.dept_survey_detail_list.length != 0) {
                //  e.replace_cancel_way = '自行调查'
                e.dept_survey_detail_list.forEach(e1 => {
                  e1.proof_catalog_name = e.proof_catalog_name
                  e1.material_name = e.material_name
                  this.tableData.content.push(e1)
                })
              }
              // 替代方式之数据共享-办理详情BO对象
              if (e.dataSharedDetailList != null && e.dataSharedDetailList.length != 0) {
                //  e.replace_cancel_way = '数据共享'
                e.dataSharedDetailList.forEach(e1 => {
                  e1.proof_catalog_name = e.proof_catalog_name
                  e1.material_name = e.material_name
                  e1.hasData = false
                  this.tableData.content.push(e1)
                })
              }
              // 替代方式之告知承诺表-办理详情BO对象
              if (e.commitment_detail_list != null && e.commitment_detail_list.length != 0) {
                //  e.replace_cancel_way = '告知承诺'
                e.commitment_detail_list.forEach(e1 => {
                  e1.proof_catalog_name = e.proof_catalog_name
                  e1.material_name = e.material_name
                  this.tableData.content.push(e1)
                })
              }
            })
            this.freeServiceData.process_result_text = this.freeServiceData.process_result === 'SUCCESS' ? '同意' : this.freeServiceData.process_result === 'FAILURE' ? '不同意' : '暂存'
            console.log('this.freeServiceData.process_result_text', this.freeServiceData.process_result_text)
            console.log('this.tableData.content', this.tableData.content)
            console.log('this.exemptIdentityTypeList', this.exemptIdentityTypeList)
            console.log(
              'this.exemptIdentityTypeList.filter(i => i.value === this.freeServiceData.identity_type)',
              this.exemptIdentityTypeList.filter(i => i.value === this.freeServiceData.identity_type)
            )
            if (this.freeServiceData.biz_org_identity_type) {
              this.freeServiceData.biz_org_identity_type_name = this.exemptIdentityTypeList.filter(i => i.value === this.freeServiceData.biz_org_identity_type)[0].label
            }
            this.freeServiceData.to_user_id_type_name = this.exemptIdentityTypeList.filter(i => i.value === this.freeServiceData.identity_type)[0].label
            this.freeServiceData.identity_type_name = this.exemptIdentityTypeList.filter(i => i.value === this.freeServiceData.identity_type)[0].label
            this.isLegalPerson = this.freeServiceData.exempt_certificates_type !== 'NATURAL_PERSON'
            // this.tableData.content.forEach(e2 => {})
            this.tableData.content.forEach(e2 => {
              if (e2.attachment_list) {
                if (e2.attachment_list.length > 0) {
                  this.hasfile = true
                }
              }
              if (e2.replace_cancel_way === '数据共享') {
                const data = {
                  serial_number: this.$route.query.serial_number,
                  proof_list_id: e2.proof_list_id
                }
                getDataSharedConfig(data)
                  .then(res => {
                    if (res.meta.code === '200' && res.data !== null) {
                      if (res.data.length !== 0) {
                        e2.hasData = true
                      } else {
                        e2.hasData = false
                      }
                    }
                  })
                  .catch(() => {
                    e2.hasData = false
                  })
              }
            })
          }
        })
        .catch(() => {
          this.loading = false
        })
        .then(() => {
          if (this.investigation_detail_list.length != 0) {
            this.investigation_detail_list.forEach(async e1 => {
              if (e1.assist_serial_number != null && e1.assist_serial_number != undefined) {
                // await this.getViewBySerialNumber(e1)
                const assist_serial_number = e1.assist_serial_number
                await this.getViewBySerialNumber(assist_serial_number, e1).then(res => {})
                // e1.audit_result = 'WAIT'
              }
            })
          }
        })
    },
    getViewBySerialNumber(assist_serial_number, e1) {
      // this.freeServiceData.serial_number
      return getViewBySerialNumber(assist_serial_number).then(res => {
        if (res.meta.code === '200' && res.data != null) {
          // console.log(res)
          e1.audit_result = res.data.audit_result
          e1.assist_id = res.data.id
        }
      })
    },
    uploadFile(raw) {
      const data = new FormData()
      data.append('file', raw)
      uploadFile(data).then(res => {
        if (this.tableData.content[this.selectFileIndex].replace_cancel_way === '告知承诺') {
          this.tableData.content[this.selectFileIndex].commit_attachment_name = this.tableData.content[this.selectFileIndex].attachment_name
          this.tableData.content[this.selectFileIndex].commit_attachment_id = this.tableData.content[this.selectFileIndex].res.data.sessionId
        } else {
          this.tableData.content[this.selectFileIndex].attachment_id = res.data.sessionId
          this.tableData.content[this.selectFileIndex].attachment_name = this.tableData.content[this.selectFileIndex].attachment_name
        }
        // attachment_id: null
        // attachment_name: null
      })
    },
    nextTab() {
      this.$router.push({
        name: 'handlingOpinions',
        query: {
          isLegalPerson: this.radio,
          data: JSON.stringify(this.freeServiceData)
        }
      })
    },
    query() {},
    goProofFile() {},
    allcheck(flag) {
      if (flag === true) {
        this.tableData.content.forEach(e => {
          e.archivedFile = true
        })
      } else {
        this.tableData.content.forEach(e => {
          e.archivedFile = false
        })
      }
    },
    deptCancle() {
      this.dialogVisible2 = false
    },
    deptSumbit(data) {
      this.getAssist(data, this.freeServiceData.serial_number)
      this.dialogVisible2 = false
    },
    deptCancle1() {
      this.dialogVisible3 = false
    },
    deptSumbit1(data) {
      console.log(data, this.freeServiceData.serial_number)
      this.getAssist(data, this.freeServiceData.serial_number)
      this.dialogVisible3 = false
    },
    deptCancle2() {
      this.dialogVisible4 = false
    },
    deptSumbit2(data) {
      this.dialogVisible4 = false
      const promiseArray = []
      data.forEach(async e => {
        const data = {
          replace_way_id: e.exempt_license_id,
          license_code: e.license_code
        }
        // await new Promise((resolve, reject) => {
        await this.attachmentArchiving(data, e.serial_number, e)
        // promiseArray.push(this.attachmentArchiving(data, e.serial_number))
      })
      // console.log('promiseArray', promiseArray)
    },
    dataSharingQuerySumbit(num) {
      console.log('dataSharingQueryData', this.dataSharingQueryData)
      this.dialogVisible7 = false
      const data = {
        proof_list_id: this.proof_list_id,
        serial_number: this.$route.query.serial_number,
        search_condition_list: this.dataSharingQueryData
      }
      if (num === 0 || num < 0) {
        data.search_limit = false
      }

      this.selectrow.data_shared_query = JSON.stringify(this.dataSharingQueryData)
      getDataSharedData(data).then(res => {
        this.dialogVisible5 = true
        this.tableData.content = []
        this.ininData()
        if (res.meta.code === '200' && res.data != null) {
          this.dataList = res.data
        } else {
          this.dataList = []
        }
      })
    },
    dataSharingQueryCancle() {
      this.dialogVisible7 = false
    },
    datashareingDialogCancle() {
      this.dialogVisible5 = false
    },
    getAssistOrg(id) {
      const parmas = {
        replace_way_id: id
      }
      getAssistOrg(parmas).then(res => {
        if (res.meta.code === '200' && res.data != null) {
          this.orgList = res.data
        }
      })
    },
    getAssist(params, serial_number) {
      getAssist(params, serial_number).then(res => {
        if (res.data != null && res.meta.code === '200') {
          this.$message({
            message: '申请协查成功',
            type: 'success'
          })
          // this.ininData()
          this.selectionRow.assist_serial_number = res.data
          this.getViewBySerialNumber(res.data, this.selectionRow)
        } else {
          this.$message({
            message: res.meta.message,
            type: 'error'
          })
        }
      })
    },
    attachmentArchiving(data, serial_number, item) {
      return new Promise((resolve, reject) => {
        attachmentArchiving(data, serial_number)
          .then(res => {
            if (res.meta.code === '200') {
              this.$message({
                message: '归档成功',
                type: 'success'
              })
            } else {
              this.$message({
                message: res.meta.message,
                type: 'error'
              })
            }
            resolve(res)
          })
          .catch(() => {
            this.$message({
              message: item.material_name + '归档失败',
              type: 'warning'
            })
          })
      })
    },
    showfileDialog() {
      this.dialogVisible4 = true
    },
    electronicCertificationDialogCancle() {
      this.dialogVisible6 = false
    },
    getElectronicCertification(row) {
      this.dialogVisible6 = true
      this.tableIndex = row.index
      // this.selectrow = row
    },
    getElectronicCertificationMes(row) {
      this.dialogVisible6 = true
      row.verification_and_inspection = true
    },
    getLicenseViewUrl(row) {
      getLicenseViewUrl(row.auth_code).then(res => {
        if (res.data != null && res.meta.code === '200') {
          row.verification_and_inspection = true
          window.open(res.data)
        }
      })
    },
    issueLicenseApply(data) {
      issueLicenseApply(data).then(res => {
        // console.log(res)
        if (res.meta.code === '200') {
          this.$message({
            message: '开具成功',
            type: 'success'
          })
          this.dialogVisible6 = false
          this.tableData.content = []
          this.ininData()
          // this.selectrow.verification_and_inspection = true
        } else {
          this.$message({
            message: res.meta.message,
            type: 'warning'
          })
        }
      })
    },
    getYssYstView(row) {
      const serial_number = row.serial_number
      const param = {
        auth_token: row.auth_code,
        replace_way_id: row.id,
        serial_number: row.serial_number,
        auth_code_source: row.auth_code_source
      }
      getYssYstView(serial_number, param).then(res => {
        console.log(res)
      })
    },
    getReplaceWayArchivist() {
      getReplaceWayArchivist({ serial_number: this.$route.query.serial_number }).then(res => {
        // if(res)
        console.log(res)
        if (res.meta.code === '200') {
          // dataURLtoDownload(res.data.file_data_base64, res.data.file_name + '.docx')
          // pplication/octet-stream
          const blob = base64toBlob(res.data.file_data_base64, 'pplication/octet-stream')
          console.log('blob', blob)
          const fileName = res.data.file_name
          const eLink = document.createElement('a')
          eLink.style.display = 'none'
          eLink.href = window.URL.createObjectURL(blob)
          eLink.download = fileName == null || fileName == '' ? 'common' : fileName //有传fileName，就用fileName
          document.body.appendChild(eLink)
          eLink.click()
          document.body.removeChild(eLink)
          window.URL.revokeObjectURL(blob)
          this.handle()
        } else {
          this.$message({
            message: res.meta.message,
            type: 'error'
          })
        }
      })
    },
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      // if (columnIndex === 1) {
      //     if (rowIndex  === 0 ) {
      //       console.log('2,1',rowIndex, columnIndex)
      //       return {
      //         rowspan: 6,
      //         colspan: 1
      //       };
      //     }
      //     else if(rowIndex <6){
      //       console.log('0 0',rowIndex, columnIndex)
      //       return {
      //         rowspan: 0,
      //         colspan: 0
      //       };
      //     }
      //   }
    }
    // checkClick() {
    //   console.log('1', this)
    //   this.checkbox.checked = false
    //   this.$set(this.checkbox, 'checked', false)
    //   console.log(this.checkbox.checked)
    // }
  }
}
</script>

<style scoped>
.cardtitle {
  color: #409eff;
}
.cardtitle img {
  width: 16px;
  margin-left: 5px;
  margin-bottom: 2px;
}
.mestitle {
  color: #666666;
}
.mescontent {
  color: #333333;
  font-weight: 550;
}
/* .minddle-form {
  width: 80%;
  display: inline-block;
} */
.el-check-form {
  /* text-align: center; */
}
.el-card {
  border: 0px;
  /* text-emphasis: none; */
}
.el-card.is-always-shadow {
  box-shadow: 0 0 0 0;
}
.box-card1 /deep/.el-card__body {
  padding-bottom: 0px;
  padding: 0px 0px;
}
.box-card2 /deep/.el-card__header {
  /* padding-top: 0px; */

  text-align: left;
}
.customTable /deep/.el-card__body {
  padding: 10px 0px;
}
.box-card3 /deep/.el-card__header {
  text-align: left;
}
.box-card3 /deep/.el-card__body {
  /* padding-bottom: 0px; */
  padding: 20px 0px;
}
.onekeytest {
  height: 200px;
  line-height: 200px;
  text-align: center;
}
.checkbtn {
  display: inline-block;
  width: 140px;
  height: 34px;
  background: inherit;
  background-color: rgba(22, 155, 213, 1);
  border: none;
  border-radius: 5px;
  text-align: center;
  line-height: 34px;
  color: #fff;
}
.list-li {
  max-height: 50px;
  border-bottom: 1px dashed;
  margin: 10px 0;
}
.card-wrap {
  /* text-align: center; */
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
}
.card-wrap img {
  margin: 10px 0;
}
.card-dialog /deep/ .el-dialog__header {
  background: #4b87c5;
  text-align: left;
}
.card-dialog /deep/ .el-dialog__title {
  color: #fff;
  font-size: 14px;
}
.foot-btn {
  height: 100px;
  text-align: center;
  line-height: 100px;
}
.content-wrapper1 /deep/.el-upload__input {
  display: none;
}
.word {
  color: #409eff;
}

.card-dialog /deep/ .el-dialog__body {
  padding: 0;
}
.deptform {
  width: 100%;
}
.box-card2 /deep/ .el-popover {
  padding: 20px;
}
.padding-left-20 {
  padding-left: 20px;
}
.greenWord {
  color: #19be6b;
}
.yellowWord {
  color: #ff9900;
}
.redWord {
  color: #f56c6c;
}
.info-wrap {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}
.info-wrap img {
  width: 35px;
  height: 35px;
  margin-right: 10px;
}
.info-title {
  font-size: 20px;
  color: #333333;
}
.info-wrap-title {
  display: flex;
  align-items: center;
}
.info-wrap-title .icon {
  /* height: 20px; */
}
.escriptions {
  margin-bottom: 10px;
}
.breakword {
  white-space: break-spaces;
}
</style>
<style >
.popoverwrap {
  padding: 20px 40px;
}
.popoverwrap .el-popover__title {
  font-weight: 600;
}
</style>
