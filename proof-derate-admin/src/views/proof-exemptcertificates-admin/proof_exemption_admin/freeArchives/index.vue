<template>
  <div class="content-wrapper padding-10">
    <!-- <papeTitle :title-name="titleName" :is-has-back="false" /> -->
    <CardTitle :title-name="titleName">
      <template></template>
    </CardTitle>
    <!-- <section class="content"> -->
    <el-form ref="form" :model="checkform" label-width="200px" class="el-check-form" :rules="rules">
      <el-card class="box-card card1">
        <el-row>
          <el-col :xs="24" :sm="12" :md="12" :lg="24">
            <el-row align="middle">
              <el-col :xs="24" :sm="12" :md="12" :lg="10">
                <el-form-item label="业务办理号">
                  <el-input v-model="checkform.serial_number" clearable placeholder="请输入业务办理号" />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="12" :lg="10">
                <el-form-item label="事项名称">
                  <el-input v-model="checkform.item_name" clearable placeholder="请输入事项名称" />
                </el-form-item>
              </el-col>

              <el-col :xs="24" :sm="12" :md="12" :lg="4">
                <div class="btn-wrap">
                  <el-button type="primary" @click="query(1)">查询</el-button>
                </div>
              </el-col>
            </el-row>
            <el-row align="middle">
              <!-- <el-col :xs="24" :sm="12" :md="12" :lg="10">
                <el-form-item label="证件号码">
                  <el-input v-model="checkform.identity_number_or_org_code" clearable placeholder="请输入证件号码" />
                </el-form-item>
              </el-col>-->
              <el-col :xs="24" :sm="12" :md="12" :lg="10">
                <el-form-item label="事项编码">
                  <el-input v-model="checkform.item_code" clearable placeholder="请输入事项编码" />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="12" :lg="10">
                <el-form-item label="事项所属行政区划">
                  <!-- <el-input v-model="checkform.item_division_code" clearable placeholder="请输入事项所属行政区划" /> -->
                  <AdministrativeDivisionCascader
                    :key="checkform.item_division_code"
                    ref="AdministrativeDivisionSelect"
                    :division-code="checkform.item_division_code"
                    :permissionCode="'auth:organization:list'"
                    @setDivisionCodeAndName="setDivisionCodeAndName"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row align="middle">
              <el-col :xs="24" :sm="12" :md="12" :lg="10">
                <el-form-item label="办事人/办事单位">
                  <el-input v-model="checkform.handle_affairs_name_or_biz_org_name	" clearable placeholder="请输入办事人/办事单位" />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="12" :lg="10">
                <el-form-item label="操作人">
                  <el-input v-model="checkform.operate_name" clearable placeholder="请输入操作人" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row align="middle">
              <el-col :xs="24" :sm="12" :md="12" :lg="10">
                <el-form-item label="办理进度">
                  <el-checkbox-group v-model="checkList">
                    <el-checkbox label="SUCCESS">通过</el-checkbox>
                    <el-checkbox label="FAILURE">不通过</el-checkbox>
                    <el-checkbox label="TEMPORARY_STORAGE">暂存</el-checkbox>
                  </el-checkbox-group>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="12" :lg="10">
                <el-form-item label="操作时间">
                  <el-date-picker
                    v-model="operateTime"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    type="datetimerange"
                    style="width:100%"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                  ></el-date-picker>
                </el-form-item>
              </el-col>
            </el-row>
          </el-col>
        </el-row>
        <div style="color: #888; padding:20px 10px 0" class="dashed-line">
          共
          <span class="text-red">{{ tableData.total }}</span> 条符合查询条件
        </div>
        <custom-table
          ref="table"
          :is-card-type="false"
          :table-data="tableData"
          :table-header="tableHeader"
          :stripe="false"
          :table-tools="tableTools"
          @query="query"
          @refresh="query(1)"
        >
          <template #operation="{ row }">
            <div>
              <el-button v-if="row.process_result!=='TEMPORARY_STORAGE'" type="text" @click="detail(row)">查看</el-button>
              <el-button v-if="row.process_result==='TEMPORARY_STORAGE'" type="text" @click="detail(row)">处理</el-button>
            </div>
          </template>
          <template #process_result="{ row }">
            <div>
              <p
                style="text-align: left;margin-left:10px;"
                :class="row.process_result==='FAILURE'?'redtext':''"
              >{{ row.process_result==='SUCCESS'?'通过':row.process_result==='TEMPORARY_STORAGE'?'暂存':row.process_result==='FAILURE'?'不通过':row.process_result }}</p>
              <div v-if="row.process_result==='TEMPORARY_STORAGE'||row.process_result==='SUCCESS'">
                <el-progress :percentage="row.biz_progress" color="#19A76D" />
              </div>
              <div v-if="row.process_result==='FAILURE'">
                <el-progress :percentage="100" color="#FF7266" />
              </div>
            </div>
          </template>
        </custom-table>
      </el-card>
    </el-form>
    <!-- </section> -->
  </div>
</template>

<script>
import CustomTable from '@/components/Element/Table'
import { getManagerPage } from '@/api/exemptCertificates'
import papeTitle from '@/components/papeTitle'
import CardTitle from '@/components/CardTitle'
export default {
  name: 'Freearchives',
  components: {
    CustomTable,
    papeTitle,
    CardTitle,
    AdministrativeDivisionCascader: () => import('@/components/AdministrativeDivisionCascader')
  },
  data() {
    return {
      checkform: {
        serial_number: '',
        handle_affairs_name_or_biz_org_name: '',
        dentity_number_or_org_code: '',
        item_code: '',
        item_name: '',
        item_division_code: '',
        operate_name: '',
        operate_begin_time: '',
        operate_end_time: '',
        page_num: 1,
        page_size: 10
      },
      operateTime: [],
      checkList: ['SUCCESS', 'FAILURE', 'TEMPORARY_STORAGE'],
      rules: {
        code: [{ required: true, message: '请输入授权码', trigger: 'blur' }],
        serial_number: [{ required: true, message: '请输入事件名称', trigger: 'blur' }]
      },
      tableData: {
        border: false,
        content: [], // 表格数据
        loading: false, // 控制表格加载效果
        total: 0, // 分页查询时的总数，去掉该属性，不显示分页条
        currentPage: 1, // 当前页码
        pageSize: 10,
        isShowIndex: true,
        pageDirection: 'desc',
        isShowSelection: false // 是否显示多选框，默认false
      },
      tableHeader: [
        { label: '业务办理号', prop: 'serial_number', align: 'left' }, // 配置slot属性，可支持使用插槽
        {
          label: '事项名称',
          prop: 'item_name',
          align: 'left'
        },
        {
          label: '事项编码',
          prop: 'item_code',
          align: 'left'
        },
        { label: '办事人/办事单位', prop: 'handle_affairs_name_or_biz_org_name', align: 'left' },
        { label: '证件号码/统一社会信用代码', prop: 'identity_number_or_org_code', align: 'left', fixed: 'right' },
        { label: '操作人', prop: 'operate_name', align: 'left' },
        { label: '操作部门', prop: 'operate_org_name', align: 'left' },
        { label: '操作时间', prop: 'operate_time', align: 'left' },
        {
          label: '办理进度',
          prop: 'process_result',

          fixed: 'right',
          align: 'left',
          slot: 'process_result'
        },
        {
          label: '操作',
          prop: 'operation',

          fixed: 'right',
          align: 'left',
          slot: 'operation'
        }

        //  { label: '业务办理号', prop: 'serial_number', align: 'left', minWidth: '160px' }, // 配置slot属性，可支持使用插槽
        // {
        //   label: '事项名称',
        //   prop: 'item_name',
        //   align: 'left',
        //   minWidth: '200px'
        // },
        // {
        //   label: '事项编码',
        //   prop: 'item_code',
        //   align: 'left',
        //   minWidth: '200px'
        // },
        // { label: '办事人/办事单位', prop: 'handle_affairs_name_or_biz_org_name', align: 'left', minWidth: '140px' },
        // { label: '证件号码/统一社会信用代码', prop: 'identity_number_or_org_code', align: 'left', width: '180px', fixed: 'right' },
        // { label: '操作人', prop: 'operate_name', align: 'left' },
        // { label: '操作部门', prop: 'operate_org_name', align: 'left' },
        // { label: '操作时间', prop: 'operate_time', align: 'left', minWidth: '160px' },
        // {
        //   label: '办理进度',
        //   prop: 'process_result',
        //   width: '220px',
        //   fixed: 'right',
        //   align: 'left',
        //   slot: 'process_result'
        // },
        // {
        //   label: '操作',
        //   prop: 'operation',
        //   width: '120px',
        //   fixed: 'right',
        //   align: 'left',
        //   slot: 'operation'
        // }
        //  { label: '归档', prop: 'process_result',slot: 'process_result', width: '120px', fixed: 'right'}
      ],
      tableTools: [],
      titleName: '免证办管理'
    }
  },

  mounted() {
    this.query()
  },

  methods: {
    query() {
      this.getManagerPage()
    },
    detail(row) {
      console.log(row)
      if (row.process_result === 'TEMPORARY_STORAGE') {
        this.$router.push({
          name: 'freeServiceContent',
          query: {
            isDetail: '0', // 0 为暂存 不显示详情
            serial_number: row.serial_number
          }
        })
      } else if (row.process_result === 'SUCCESS' || row.process_result === 'FAILURE') {
        this.$router.push({
          name: 'freeArchivesContent',
          query: {
            isDetail: '1', // 1  显示详情
            serial_number: row.serial_number
            // isLegalPerson: row.isLegalPerson
          }
        })
      }
    },
    getManagerPage() {
      this.checkform.page_num = this.tableData.currentPage
      this.checkform.page_size = this.tableData.pageSize
      this.checkform.process_result = this.checkList
      // console.log('this.operateTime', this.operateTime)
      if (this.operateTime && this.operateTime.length !== 0) {
        this.checkform.operate_begin_time = this.operateTime[0]
        this.checkform.operate_end_time = this.operateTime[1]
      } else {
        this.checkform.operate_begin_time = ''
        this.checkform.operate_end_time = ''
      }
      this.tableData.loading = true
      getManagerPage(this.checkform)
        .then(res => {
          this.tableData.loading = false
          if (res.meta.code === '200' && res.data != null) {
            this.tableData.content = res.data.content
            this.tableData.total = Number(res.data.total_elements)
          }
        })
        .catch(() => {
          this.tableData.loading = false
        })
    },
    setDivisionCodeAndName(data) {
      // console.log('data', data)
      this.checkform.item_division_code = data.code
    }
  }
}
</script>

<style scoped>
.card2 {
  margin-top: 20px;
}
.btn-wrap {
  text-align: center;
}
.box-card /deep/ .el-progress__text {
  color: #aaaaaa;
}
.redtext {
  color: #ff7266;
}
</style>
