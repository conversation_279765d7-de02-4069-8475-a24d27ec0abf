<template>
  <div class="warp">
    <el-row>
      <el-col :span="24">
        <el-row class="top" type="flex" justify="center">
          <el-col :span="12" class="bottom_right">
            <div class="chart-title">区域清理事项情况
              <img v-popover:popover2 :src="iconImg">
              <el-popover ref="popover2" placement="right" title="统计口径说明" width="556" trigger="hover"
                popper-class="popoverwrap">
                <p style="line-height:22px">
                  <span>● 未完成事项数：根据账号权限，显示事项状态为 “待清理+待梳理+待审核” 且事项存在关联材料的事项总数</span>
                  </br>● 涉及材料数（未完成）：根据账号权限，显示事项状态为 “待清理+待梳理+待审核” 且事项存在关联材料的事项涉及的材料总数
                  </br> ● 已完成事项数：根据账号权限，事项状态为“已完成”的事项总数
                  </br> ● 涉及材料数（已完成）：根据账号权限，事项状态为“已完成”的事项涉及的材料总数
                </p>
                </p>
              </el-popover>
            </div>
            <div class="es-pie">
              <es-pie :echart-data="pieData" :pie-type="pieType" @pieClick="pieClick" />
            </div>
            <div class="es-bar">
              <es-bar :option="topBar.option" :width="barWidth" title="区域清理事项情况" />
            </div>
          </el-col>
          <div style="width:20px" />
          <el-col :span="12">
            <div class="top_left">
              <div class="opt_title">待办 <img v-popover:popover1 :src="iconImg">
                <el-popover ref="popover1" placement="right" title="统计口径说明" width="456" trigger="hover"
                  popper-class="popoverwrap">
                  <p style="line-height:22px">
                    <!-- <span>待办</span> -->
                    <!-- </br>  -->
                    <span>● 证明材料清理：根据账号权限，显示事项状态为“待清理”且事项无关联材料的事项数</span>
                    </br> ● 证明材料梳理：根据账号权限，显示事项状态为“待梳理”的事项数
                    </br> ● 证明材料审核：根据账号权限，显示事项状态为“待审核”的事项数
                    </br> ● 协查任务：根据账号权限，显示协查任务状态为“待协查”的事项数
                  </p>
                  </p>
                </el-popover>
              </div>

              <div class="opt_content" type="flex">
                <div class="el-col clear" :span="12" @click="toPage('item_clear')"
                  v-if="isPermission($route.meta.permission, 'index:biz:proof_clear:list')">
                  <img src="@/assets/proof-derate-admin-images/5.png">
                  <span class="todo_name">证明材料清理</span>
                  <span class="todo_value">{{ itemClearCount || '0' }}</span>
                </div>
                <div class="el-col comb" :span="12" @click="toPage('item_carding')"
                  v-if="isPermission($route.meta.permission, 'index:biz:proof_comb:list')">
                  <img src="@/assets/proof-derate-admin-images/6.png">
                  <span class="todo_name">证明材料梳理</span>
                  <span class="todo_value">{{ itemCardingCount || '0' }}</span>
                </div>
                <div class="el-col examine" :span="12" @click="toPage('item_audit')"
                  v-if="isPermission($route.meta.permission, 'index:biz:proof_audit:list')">
                  <img src="@/assets/proof-derate-admin-images/7.png">
                  <span class="todo_name">证明材料审核</span>
                  <span class="todo_value">{{ itemAuditCount || '0' }}</span>
                </div>
                <div class="todo"
                  v-if="!isPermission($route.meta.permission, 'index:biz:proof_clear:list') && !isPermission($route.meta.permission, 'index:biz:proof_comb:list') && !isPermission($route.meta.permission, 'index:biz:proof_clear:list')">
                  <div class="opt_content empty" type="flex">
                    <img src="@/assets/proof-derate-admin-images/empty.png" alt="">
                  </div>
                  <div class="opt_bottom" style="text-align: center;">
                    <span>您暂时没有待办任务</span>
                  </div>
                </div>
              </div>

            </div>
            <div class="top_left assist margin-top-20">
              <div class="opt_title"><span>协查任务</span> <span
                  v-if="assistList.length !== 0 && isPermission($route.meta.permission, 'index:service:handle:list')"
                  class="more_line" @click="goAssit">查看更多></span> </div>
              <div v-if="assistList.length !== 0 && isPermission($route.meta.permission, 'index:service:handle:list')"
                class="opt_content" type="flex" :class="assistList.length === 0 ? 'empty' : ''">
                <div v-for="(i, key) in assistList" :key="key" class="opt_content_li">
                  <span>
                    <span v-if="(key + (page_number - 1) * 5 + 1) < 10" class="li_index">{{ '0' + (key + (page_number -
                      1) * 5 + 1)
                      }}</span>
                    <span v-else class="li_index">{{ (key + (page_number - 1) * 5 + 1) }}</span>
                    <span>{{ i.item_name }}</span>
                  </span>
                  <span>
                    <div
                      v-if="(i.residual_treatment_limit > 0 || i.residual_treatment_limit == 0) && (i.residual_treatment_hour_limit > 0 || i.residual_treatment_hour_limit == 0)"
                      class="timeLimit">
                      <span
                        :class="(i.residual_treatment_limit < 30 && (i.residual_treatment_hour_limit == 0)) || (i.residual_treatment_limit === 30 && (rrow.residual_treatment_hour_limit == 0)) ? 'warning' : ''">{{
                          i.residual_treatment_hour_limit + '小时' }}{{ i.residual_treatment_limit + '分钟' }}</span>
                    </div>
                    <div v-if="i.residual_treatment_limit < 0 || i.residual_treatment_hour_limit < 0" class="timeLimit">
                      <span class="warning">{{ '超过' + Math.abs(i.residual_treatment_hour_limit) + '小时' }}{{
                        Math.abs(i.residual_treatment_limit) + '分钟' }}</span>

                    </div>
                  </span>
                </div>

              </div>
              <div v-if="!isPermission($route.meta.permission, 'index:service:handle:list') || assistList.length === 0"
                class="opt_content empty" type="flex">
                <img src="@/assets/proof-derate-admin-images/empty.png" alt="">
              </div>
              <div v-if="assistList.length !== 0 && isPermission($route.meta.permission, 'index:service:handle:list')"
                class="opt_bottom">
                <el-pagination background layout="prev, pager, next" :total="total" :page-size="5"
                  @current-change="handleCurrentChange" />
              </div>
              <div v-else class="opt_bottom" style="text-align: center;">
                <span>您暂时没有协查任务</span>
              </div>
            </div>
          </el-col>

        </el-row>
      </el-col>
      <el-col :span="24">

        <el-row class="bottom" type="flex" justify="center">

          <el-col :span="12" class="bottom_left">
            <div class="chart-title">免证替代方式情况<img v-popover:popover3 :src="iconImg">
              <el-popover ref="popover3" placement="right" title="统计口径说明" width="556" trigger="hover"
                popper-class="popoverwrap">
                <p style="line-height:22px">
                  <span>● 取消清单数：事项状态为“已完成”的证明材料中，清理方式是“直接取消”+“替代取消”的清单总数</span>
                  </br> ● 环形占比：根据取消证明数，展示各替代方式和直接取消的材料的占比情况
                </p>
                </p>
              </el-popover>
            </div>

            <div class="" v-if="!hasShowChart"
              style="width: 100%;height: 100%;display: flex;align-items: center;justify-content: center;flex-direction: column;">
              <img src="@/assets/proof-derate-admin-images/empty.png" alt="" style="height: 160px;">
              <span>暂无数据</span>
            </div>
            <div class="es-pie" v-if="hasShowChart">
              <es-pie :echart-data="pieData1" :pie-type="pieType1" />
            </div>
            <div class="es-bar" v-if="hasShowChart">
              <es-bar :option="bottomBar.option" :width="barWidth" title="免证替代方式情况" />
            </div>
          </el-col>
          <div style="width:20px" />
          <el-col :span="12" class="top_right">
            <div class="opt_title">常用操作</div>
            <div class="opt_content" type="flex">
              <div class="el-col" :span="5" @click="toPage('item_clear')"
                v-if="isPermission($route.meta.permission, 'index:biz:proof_clear:list')">
                <img src="@/assets/proof-derate-admin-images/5.png">
                <span>证明材料清理</span>
              </div>
              <div class="el-col" :span="5" @click="toPage('item_carding')"
                v-if="isPermission($route.meta.permission, 'index:biz:proof_comb:list')">
                <img src="@/assets/proof-derate-admin-images/6.png">
                <span>证明材料梳理</span>
              </div>
              <div class="el-col" :span="5" @click="toPage('item_audit')"
                v-if="isPermission($route.meta.permission, 'index:biz:proof_audit:list')">
                <img src="@/assets/proof-derate-admin-images/7.png">
                <span>证明材料审核</span>
              </div>
              <div class="el-col" :span="5" @click="toPage('certification_List')"
                v-if="isPermission($route.meta.permission, 'index:proof_catalog:proof:list')">
                <img src="@/assets/proof-derate-admin-images/4.png">
                <span>证明目录管理</span>
              </div>
              <div class="el-col" :span="5" @click="toPage('item_list')"
                v-if="isPermission($route.meta.permission, 'index:archives:proof_list:list')">
                <img src="@/assets/proof-derate-admin-images/11.png">
                <span>事项证明清单</span>
              </div>
              <div class="el-col" :span="5" @click="toPage('ItemManagement_itemProve')"
                v-if="isPermission($route.meta.permission, 'index:biz:credential:list')">
                <img src="@/assets/proof-derate-admin-images/1.png">
                <span>证明材料管理</span>
              </div>
              <div class="el-col" :span="5" @click="tip()">
                <img src="@/assets/proof-derate-admin-images/9.png">
                <span>操作手册</span>
              </div>
              <div class="el-col" :span="5" @click="tip()">
                <img src="@/assets/proof-derate-admin-images/10.png">
                <span>视频资料</span>
              </div>
            </div>

          </el-col>
        </el-row>
      </el-col>
    </el-row>
  </div>
</template>
<script>
import EsPie from '@/views/proof-derate-admin/components/EsPie'
import EsBar from '@/views/proof-derate-admin/components/EsBar'
import { isPermission } from '@/utils/index.js'
import {
  getItemStat,
  getCleanStatusTopFiveArea,
  getReplaceWayTopFive,
  getAssistTask,
  getWaitForCleanItem,
  getCardingUnconfirmedItem,
  getApprovedItem,
  getAssistInvestigate
} from '@/api/common/statistics'
import { getCurrentAccount } from '@/api/common/dict.js'
export default {
  name: 'Main',
  components: {
    EsPie,
    EsBar
  },
  data() {
    return {
      itemClearCount: 0,
      itemCardingCount: 0,
      itemAuditCount: 0,
      itemExemption: 0,
      pieData1: [],
      pieData: [],
      itemCount: 0,
      barWidth: '386px',
      cancelProofListCount: 0,
      assist_task_count: 0,
      iconImg: require('@/assets/proof-derate-admin-images/icon.png'),
      topBar: {
        option: []
      },
      bottomBar: {
        option: []
      },
      option: {
        echartData: {
          xdata: ['已完成', '未完成'],
          subTitle: ['涉及材料', '涉及材料'],
          value: [0, 0],
          material: [0, 0]
        },
        series: [
          {
            name: '未完成',
            color: '#2fffa4',
            colorStops: [
              { offset: 0, color: 'rgba(29, 245, 160, .7)' },
              { offset: 0.5, color: 'rgba(29, 245, 160, .7)' },
              { offset: 0.5, color: 'rgba(29, 245, 160, .3)' },
              { offset: 1, color: 'rgba(29, 245, 160, .3)' }
            ]
          },
          {
            name: '已完成',
            color: '#32ffee',
            colorStops: [
              { offset: 0, color: 'rgba(50, 255, 238, .7)' },
              { offset: 0.5, color: 'rgba(50, 255, 238, .7)' },
              { offset: 0.5, color: 'rgba(50, 255, 238, .3)' },
              { offset: 1, color: 'rgba(50, 255, 238, .3)' }
            ]
          }
        ]

      },
      pieType: 'totalEvents',
      pieType1: 'totalCancel',
      assistList: [],
      total: 0,
      userdata: [],
      page_number: 0,
      account_info: [], // 用户信息带联系方式
      permissionList: [],// 所有的权限编码
      hasShowChart: true,
    }
  },
  computed: {
    hide_nav() {
      return this.$route.meta && this.$route.meta.nav === false
    }
  },
  mounted() {
    console.log('this.$route.meta', this.$route.meta)
    this.permissionList = this.$route.meta.permission
    // this.getCurrentAccount().then(res => {
    // console.log(this.isPermission(this.permissionList,'assist:service:handle:list'))
    // if(this.isPermission(this.permissionList,'assist:service:handle:list')){
    this.getAssistInvestigate(1)
    // }
    // })
    this.getData()
    this.barInit()
    const bodyStyle = document.body.style
    bodyStyle['width'] = `1903px`
    bodyStyle['overflow-x'] = `auto`


    // const { roles, rolePermissionVos } = this.$store.getters.userdata
    // this.userdata = { roles, rolePermissionVos }
    // console.log('this.userdata',this.userdata)
  },
  beforeDestroy() {
    const bodyStyle = document.body.style
    bodyStyle['width'] = `100%`
    bodyStyle['overflow-x'] = `hinden`
  },
  methods: {
    isPermission,
    getCurrentAccount() {
      return getCurrentAccount().then(res => {
        if (res.meta.code === '200') {
          this.account_info = res.data.account_info

        }
      })
    },
    goAssit() {
      this.$router.push({ name: 'proof_investigation_admin' })
    },
    tip() {
      this.$message({
        message: '暂无相关资料',
        type: 'warning'
      })
    },
    getAssistInvestigate(page_number) {
      console.log('this.account_info', this.account_info)
      const userdata = JSON.parse(localStorage.getItem('organization'))
      const data = {
        material_name: '', // 证明名称
        handle_affairs_name: '',
        assist_result: 'WAIT', // WAIT :待审核 SUCCESS :审核通过 FAIL :审核失败
        page_direction: 'DESC',
        page_number: page_number,
        page_size: 5,
        toAssistCreditCode: userdata.credit_code,
        // fromAssistCreditCode: userdata.credit_code
      }
      this.page_number = page_number
      console.log('data', data)
      if (this.isPermission(this.permissionList, 'index:service:handle:list')) {
        getAssistInvestigate(data).then(res => {
          if (res.meta.code === '200' && res.data !== null) {
            this.assistList = res.data.content
            this.total = Number(res.data.total_elements)
          } else {
            this.assistList = []
            this.total = 0
          }
        })
      }

    },
    handleCurrentChange(val) {
      this.getAssistInvestigate(val)
    },
    toPage(name) {
      this.$router.push({ name: name })
    },
    transform(key) {
      const labelList = {
        cancel_proof_list_count: '取消证明数',
        directly_cancel_count: '直接取消数',
        license_replace_cancel_count: '电子证照数',
        data_sharing_replace_cancel_count: '数据共享数',
        handle_affairs_promise_replace_cancel_count: '告知承诺数',
        // 部门协查数
        artificial_investigation_replace_cancel_count: '部门协查数',
        // 自行调查数
        department_investigation_replace_cancel_count: '自行调查数',
        // 电子证明数
        turn_license_item_count_count: '电子证明数'
      }
      return labelList[key]
    },
    barInit() {
      if (this.isPermission(this.permissionList, 'index:item_stat:view')) {
        getCleanStatusTopFiveArea({ page_size: '8' })
          .then(res => {
            this.topBar.option =
              res.data === null
                ? []
                : res.data.map((i, index) => {
                  return {
                    label: i.division,
                    value: i.clean_num,
                    unit: '项'
                  }
                })
            // this.topBar.option.push({
            //   label: "自行调查数1",
            //   unit: "种",
            //   value: 900
            // },
            // {
            //   label: "自行调查数2",
            //   unit: "种",
            //   value: 900
            // },{
            //   label: "自行调查数3",
            //   unit: "种",
            //   value: 900
            // })
          })
          .catch(() => {
            this.topBar.option = []
            console.log('==事项梳理情况统计error==')
          })
      }
      if (this.isPermission(this.permissionList, 'index:cancel_stat:view')) {
        getReplaceWayTopFive({ page_size: '6' })
          .then(res => {
            // res.data=[{cancel_way_describe:"directly_cancel_count",replace_material_count:8},{cancel_way_describe:"license_replace_cancel_count",replace_material_count:3}]
            this.bottomBar.option =
              res.data === null
                ? []
                : res.data.map((i, index) => {
                  return {
                    label: this.transform(i['cancel_way_describe']),
                    value: i.replace_material_count,
                    unit: '种'
                  }
                })
            this.pieData1 = res.data.map(i => {
              return { value: i.replace_material_count, name: this.transform(i['cancel_way_describe']) }
            })
            console.log('this.pieData1', this.pieData1)
            console.log('this.bottomBar.option', this.bottomBar.option)
            // this.pieData1=[this.pieData1[0],this.pieData1[1]]
            if (this.pieData1.length === 0 && this.bottomBar.option.length === 0) {
              this.hasShowChart = false
            }
          })
          .catch(() => {
            this.bottomBar.option = []
            console.log('==事项梳理情况统计error==')
          })
      }
      if (this.isPermission(this.permissionList, 'index:service:handle:list')) {
        getAssistTask().then(res => {
          if (res.meta.code == 200 && res.data != null) {
            this.assist_task_count = res.data.assist_task_count
          }
        }).catch(() => {
          console.log('==协查任务统计error==')
        })
      }
      if (this.isPermission(this.permissionList, 'index:biz:proof_clear:list')) {
        getWaitForCleanItem().then(res => {
          console.log('res', res)
          if (res.meta.code == 200 && res.data != null) {
            this.itemClearCount = res.data.item_wait_for_clean_count
          }
        }).catch(() => {
          console.log('==待清理error==')
        })
      }
      if (this.isPermission(this.permissionList, 'index:biz:proof_comb:list')) {
        getCardingUnconfirmedItem().then(res => {
          if (res.meta.code == 200 && res.data != null) {
            this.itemCardingCount = res.data.item_carding_unconfirmed_count
          }
        }).catch(() => {
          console.log('==待确认==')
        })
      }
      if (this.isPermission(this.permissionList, 'index:biz:proof_audit:list')) {
        getApprovedItem().then(res => {
          if (res.meta.code == 200 && res.data != null) {
            this.itemAuditCount = res.data.item_approved_count
            // this.itemCardingCount = res.data.item_carding_unconfirmed_count
            // this.itemClearCount = res.data.item_wait_for_clean_count
          }
        }).catch(() => {
          console.log('==待审核==')
        })
      }
    },

    getData() {
      console.log('this.permissionList', this.permissionList)
      if (this.isPermission(this.permissionList, 'index:item_stat:view')) {
        getItemStat()
          .then(res => {
            const columnarObject =
              res.data === null
                ? {
                  item_count: 0, // 全市事项数
                  item_clean_count: 0, // 已完成事项数
                  item_clean_material_count: 0, // 涉及材料数（已完成）
                  item_wait_for_clean_count: 0, // 未完成事项数
                  item_wait_for_clean_material_count: 0 // 涉及材料数（未完成）
                }
                : res.data
            this.pieData = [
              {
                label: '未完成',
                value: columnarObject.item_wait_for_clean_count,
                materialCount: columnarObject.item_wait_for_clean_material_count,
                name: ''
              },
              {
                label: '已完成',
                value: columnarObject.item_clean_count,
                materialCount: columnarObject.item_clean_material_count,
                name: ''
              }
            ]
            console.log('this.pieData', this.pieData)
          })
          .catch(() => {
            console.log('==事项梳理情况统计error==')
          })
      }
    },
    pieClick(param) {
      this.$router.push({ name: 'item_list' })
    }
  }
}
</script>
<style scoped>
.warp {
  /* width: 1920px;
  height: 1080px;
  overflow-x: auto;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 11111; */
  background: #f9fafc;
  margin-left: 0 !important;
  min-width: 100vw;
  height: calc(100vh - 50px);
}

#app .app-main {
  width: 100vw;
}

.margin-top50 {
  min-height: 50px;
}

.row-bg {
  padding: 10px 0;
  background-color: #f9fafc;
}

.content {
  margin-top: 0px;
  /* width: 1920px; */
  /* width: 100%; */
  height: 968px;
  background: #f6f7f8;
  /* background: pink; */
  overflow: hidden;
}

.top,
.bottom {
  height: 100%;
  margin: 20px 10px;
  /* display: flex; */
}

/* .top {
  margin: 10px 20px 10px;
} */
.bottom {
  margin-top: 0px;
}

.bottom_left {
  /* width: 100%; */
  /* height: 466px; */
  height: 360px;
  background: #ffffff;
  display: flex;
  position: relative;
}

.es-pie {
  /* width: 38%; */
  width: 55%;
  height: 100%;
  background: #ffffff;
  /* margin: 0 10px; */
}

.es-bar {
  /* width: 58%; */
  width: 41%;
  height: 100%;
  background: #ffffff;
  /* margin: 0 10px; */
}

.top_right,
.top_left {
  /* margin-left: 20px; */
  padding: 20px;
  /* width: 480px; */
  height: 346px;
  background: #ffffff;
  position: relative;
}

.top_right {
  /* height: 466px; */
  height: 360px;
}

.top_left {
  /* margin-left: 0px; */
  height: 185px;
}

.assist {
  height: 260px;
  padding-top: 10px;
  font-size: 14px;
}

.assist .opt_title {
  display: flex;
  width: 100%;
  align-items: center;
  justify-content: space-between;
}

.assist .opt_content {
  display: block !important;
  height: 160px;
  margin-top: 23px;
}

.assist .empty {
  /* background: url("../../assets/images/empty.png"); */
  text-align: center;
  background-repeat: no-repeat;
  background-size: 348px 200px;
  background-position: 50% 50%;
}

.top_left .empty {
  justify-content: center;
  height: 121px;
  text-align: center;
  /* background: url("../../assets/images/empty.png"); */
  background-repeat: no-repeat;
  background-size: 235px 121px;
  background-position: 50% 50%;
}

.top_left .empty img {
  height: 121px;
}

.assist .opt_bottom {
  height: 30px;
}

.assist .opt_content_li {
  display: flex;
  height: 30px;
  justify-content: space-between;
}

.assist .opt_content_li .li_index {
  width: 20px;
  margin-right: 10px;
}

.assist .opt_bottom {
  height: 40px;
  text-align: right;
}

.assist .more_line {
  font-size: 16px;
  color: #9E9E9E;
  cursor: pointer;
}

.bottom_right {
  /* margin-left: 20px; */
  height: 466px;
  background: #ffffff;
  display: flex;
  position: relative;
}

/* .bottom_right {
  margin-left: 20px;
  padding: 20px;
  height: 466px;
  background: #ffffff;
} */

.opt_title {
  margin: 0px 0 10px 0;
  width: 96px;
  height: 30px;
  font-size: 24px;
  font-family: SourceHanSansCN, SourceHanSansCN-Medium;
  font-weight: 500;
  text-align: left;
  color: #333333;
  line-height: 36px;

}

.opt_title img {
  width: 16px
}

/* .el-col:first-child,.top_right .opt_content .el-col:last-child  */
.top_right .opt_content .el-col {
  /* margin: 10px 10px 10px 20px; */
  /* margin-top:10px;
   margin-bottom:10px; */
  margin: 10px;
  width: 200px;
  /* width: 100px; */
  /* height: 45px; */
  height: 120px;
  background: ffffff;
  /* border: 1px solid #e6e6e6; */
  border-radius: 4px;

  font-size: 18px;
  font-family: SourceHanSansCN, SourceHanSansCN-Regular;
  font-weight: 400;
  text-align: left;
  color: #333333;
  line-height: 1%;
  cursor: pointer;
  display: flex;
  align-items: center;
  /* justify-content: center; */
  flex-direction: column;
}

#main-container-left .top_right .opt_content .el-col {
  width: 185px;
}

.top_right .opt_content .el-col:first-child {
  /* margin-right:35px; */
}

.top_right .opt_content .el-col img {
  width: 30px;
  height: 30px;
  margin: 10px;
}

.margin-top-20 {
  margin-top: 20px;
}

.top_left .opt_content .el-col {
  position: relative;
  /* margin: 18px 0 0 0px; */
  margin: 10px;
  cursor: pointer;
  /* width: 425px; */
  width: 275px;
  height: 95px;
  /* height: 120px; */
  /* background: #ffffff; */
  /* border: 1px solid #e6e6e6; */
  border-radius: 4px;
  /* background-image: url("~@/assets/proof-derate-admin-images/item-bg.png"); */
  background-repeat: no-repeat;
  background-size: 126px 56px;
  background-position: 96% 80%;
}

#main-container-left .top_left .opt_content .el-col {
  width: 255px;
}

.top_left .opt_content .el-col img {
  /* width: 52px;
  height: 52px; */
  /* width: 104px; */
  width: 30px;
  height: 30px;
  /* height: 104px; */
  /* margin: 28px 24px; */
  /* margin: 10px 24px; */
  margin: 15px 15px;
}

.top_left .opt_content .el-col .todo_name {
  position: relative;
  top: -25px;
  width: 96px;
  height: 16px;
  font-size: 16px;
  font-family: SourceHanSansCN, SourceHanSansCN-Regular;
  font-weight: 400;
  text-align: left;
  color: #333333;
  line-height: 24px;
}

.top_left .opt_content .todo_value {
  position: absolute;
  /* top: 58px;
  left: 150px; */
  top: 43px;
  left: 66px;
  width: 64px;
  height: 38px;
  font-size: 26px;
  font-family: DINPro, DINPro-Medium;
  font-weight: 600;
  text-align: left;
  color: #333333;
  line-height: 38px;
}

.top_left .opt_content .margin-right-10 {
  /* margin-right: 10px; */
}

.top_right .opt_content .el-col img {
  width: 60px;
  height: 60px;
  margin-top: 15px;
  margin-bottom: 20px;
}

.chart-wrap {
  position: relative;
}

.chart-title {
  position: absolute;
  left: 25px;
  z-index: 111;
  top: 20px;
  font-size: 24px;
}

.chart-title img {
  width: 16px;
  margin-left: 5px;
}

.el-col-12 {
  /* width: 48%; */
}

.el-col-24 {}

.top_left .opt_content {
  display: flex;
  flex-wrap: wrap;
}

.top_right .opt_content {
  display: flex;
  flex-wrap: wrap;
}

.clear {
  background: #e9f8ff;
}

.comb {
  background: #f2fff3;
}

.examine {
  background: #fff2eb;
}

.warning {
  color: crimson;
}

.todo {
  width: 100%;
  font-size: 14px;
  position: absolute;
  left: 0%;
  bottom: 30px;

}

.todo .opt_content {
  height: 95px;
  font-size: 14px;
  margin-bottom: 10px;
}

.todo .opt_content img {
  height: 95px;
}
</style>
<style lang="scss">
//  .Pagemain{
//    width: 1920px !important;
//    overflow-x: auto;
// }</style>
