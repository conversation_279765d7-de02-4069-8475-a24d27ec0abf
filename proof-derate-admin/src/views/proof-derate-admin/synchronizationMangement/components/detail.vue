<template>
  <div>
    <el-card class="box-card" shadow="never">
      <span class="margin-left-10 info-wrap">
        <img :src="arrow" alt>
        <span class="info-title">基本信息</span>
      </span>
      <el-descriptions class="descriptions" title :column="2" border>
        <el-descriptions-item>
          <template slot="label">事项编码</template>
          {{ queryForm.item_code }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">事项名称</template>
          {{ queryForm.item_name }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">办理事项编码</template>
          {{ queryForm.situation_code }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">办理事项名称</template>
          {{ queryForm.situation_name }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">实施机构</template>
          {{ queryForm.impl_org_name }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">事项类型</template>
          {{ queryForm.impl_org_name }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">事项版本</template>
          {{ queryForm.task_version }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">办证类型</template>
          {{ queryForm.project_type }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">同步状态</template>
          {{ queryForm.synchronize_status }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">最近更新时间</template>
          {{ queryForm.last_modification_time }}
        </el-descriptions-item>
      </el-descriptions>
      <div v-loading="dataLoading" class>
        <span class="margin-left-10 info-wrap">
          <img :src="arrow" alt>
          <span class="info-title">证明材料</span>
        </span>
        <custom-table
          ref="table"
          :is-card-type="false"
          :table-data="tableData"
          :table-header="tableHeader"
          :stripe="false"
          :table-tools="tableTools"
          style="margin-top: 10px"
          @query="query"
          @refresh="query(1)"
        >
          <template #license_name="{ row }">
            <div>
              <el-link :underline="false" type="primary">{{ row.license_name }}</el-link>
            </div>
          </template>
          <!-- <template #change_details="{ row }">
            <div>{{row.change_details}}</div>
          </template>-->

          <template #sample_file="{ row }">
            <div v-if="row.sample_file_list.length>0">
              <el-link :underline="false" type="primary" @click="downSampleFile(row)">
                <i class="el-icon-download" />样例.doc
              </el-link>
            </div>
            <div v-else>无</div>
          </template>
          <template #blank_file="{ row }">
            <div v-if="row.blank_file_list.length>0">
              <el-link :underline="false" type="primary" @click="downblankFile(row)">
                空白表格
                <i class="el-icon-download" />
              </el-link>
            </div>
            <div v-else>无</div>
          </template>

          <template #material_type="{ row }">
            <span v-if="row.material_type=='CERTIFICATE_PROVE'">证件证书证明</span>
            <span v-if="row.material_type=='APPLY_FORM_DOCUMENTS'">申请表格文书</span>
            <span v-if="row.material_type=='OTHER'">其他</span>
          </template>
        </custom-table>
        <div class="foot-btn">
          <!-- ItemManagement/itemProve/info?id= -->
          <el-button type="primary" v-permission="'catalog:item:synchronize:view'" @click="goInfo()">查看清理办件</el-button>
          <el-button
            v-if="isStatus=='WAIT_SYNCHRONIZE'"
            type="primary"
            v-permission="'catalog:item:synchronize:sync'"
            @click="operate"
          >同步</el-button>
          <el-button
            v-if="isStatus=='SYNCHRONIZE_FAILED'"
            v-permission="'catalog:item:synchronize:update'"
            type="primary"
            @click="update"
          >更新</el-button>
          <el-button v-if="isStatus=='WAIT_SYNCHRONIZE' ||isStatus=='SYNCHRONIZE_FAILED'" v-permission="'catalog:item:synchronize:ignore'" type="danger" plain @click="ignore">忽略</el-button>
          <!-- <el-button @click="back()">返回</el-button> -->
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import CustomTable from '@/components/Element/Table'
import { synchronizeDetails, operate, ignore, update } from '@/api/synchronizeData'
// import { downURLfile } from '@/utils/index.js'
import { getSynchronizeStatus } from '@/api/common/dict'
export default {
  components: {
    CustomTable
  },
  data() {
    return {
      rules: {},
      queryForm: {
        item_code: '',
        item_name: '',
        situation_code: '',
        situation_name: '',
        impl_org_name: '',
        item_type: '',
        task_version: '',
        project_type: '',
        synchronize_status: '',
        last_modification_time: ''
      },
      dataLoading: false,
      arrow: require('@/assets/proof-derate-admin-images/arrow.png'),
      /**
       * 同步成功
       */
      // SYNCHRONIZE_SUCCESS("同步成功"),

      /**
       * 同步失败
       */
      // SYNCHRONIZE_FAILED("同步失败"),

      /**
       * 未同步
       */
      // WAIT_SYNCHRONIZE("未同步"),

      /**
       * 忽略
       */
      // IGNORE_SYNCHRONIZE("忽略");
      isStatus: 'WAIT_SYNCHRONIZE', // 1 成功 2 失败 3 待同步
      tableData: {
        content: [], // 表格数据
        loading: false, // 控制表格加载效果
        total: 0, // 分页查询时的总数，去掉该属性，不显示分页条
        currentPage: 1, // 当前页码
        pageSize: 10,
        isShowIndex: true,
        pageDirection: 'desc',
        isShowSelection: false // 是否显示多选框，默认false
      },
      tableHeader: [
        // { label: '证明名称', prop: 'certificate_name', minWidth: '160px' }, // 配置slot属性，可支持使用插槽
        {
          label: '证明材料名称',
          prop: 'material_name',
          minWidth: '100px'
        },
        { label: '材料类型', prop: 'material_type', minWidth: '120px', slot: 'material_type' },
        { label: '关联电子证照', prop: 'license_name', width: '120px', fixed: 'right', slot: 'license_name' },
        {
          label: '样例',
          prop: 'sample_file',
          minWidth: '80px',
          slot: 'sample_file'
        },
        // isHeaderslot 与 prop 不要同名
        {
          label: '空白表格',
          prop: 'blank_file',
          width: '120px',
          slot: 'blank_file'
          //   fixed: 'right',
          //   slot: 'audit_result'
        }
      ],
      tableTools: [],
      dataItem: '',
      checkDataList: []
    }
  },

  mounted() {
    this.isStatus = this.$route.query.synchronize_status
    this.getSynchronizeStatus()

    if (this.$route.query.synchronize_status === 'SYNCHRONIZE_FAILED') {
      this.tableHeader.push({
        label: '变更详情',
        prop: 'change_details',
        width: '150px'
      })
    }
  },

  methods: {
    query() {},
    getSynchronizeStatus() {
      getSynchronizeStatus().then(res => {
        if (res.meta.code === '200' && res.data != null) {
          this.checkDataList = res.data
          this.synchronizeDetails()
        }
      })
    },
    back() {
      this.$router.push({ name: 'synchronizationMangement' })
    },
    showTip() {
      if (this.$route.query.synchronize_status === 'SYNCHRONIZE_FAILED') {
        console.log(this.dataItem)
        if (this.dataItem.change_details == '') {
          this.$message({
            //   duration: 0,
            iconClass: 'el-icon-warning-outline',
            customClass: 'tipmessage',
            dangerouslyUseHTMLString: true,
            message: '<span>' + '同步失败：' + this.dataItem.fail_result,
            // message: '<strong>这是 <i>HTML</i> 片段</strong>',
            type: 'warning'
          })
        } else {
          this.$message({
            //   duration: 0,
            iconClass: 'el-icon-warning-outline',
            customClass: 'tipmessage',
            dangerouslyUseHTMLString: true,
            message: '<span>' + '同步失败：' + this.dataItem.fail_result + ',<br/>' + '变动详情 :' + this.dataItem.change_details + '</span>',
            // message: '<strong>这是 <i>HTML</i> 片段</strong>',
            type: 'warning'
          })
        }
      }
    },
    goInfo() {
      this.$router.push({
        // path: 'itemBiz/itemList/info/way',
        name: 'item_list_info_way',
        query: {
          id: this.queryForm.item_code,
          type: 'show'
        }
      })
    },
    operate() {
      const data = {
        middle_item_id: this.$route.query.middle_item_id
      }
      operate(data).then(res => {
        // console.log(res)
        if (res.meta.code === '200' && res.data === 'SYNCHRONIZE_SUCCESS') {
          this.$message({
            showClose: true,
            message: '同步成功',
            type: 'success'
          })
        } else if (res.meta.code === '200' && res.data === 'SYNCHRONIZE_FAILED') {
          this.$message({
            showClose: true,
            message: '同步失败',
            type: 'error'
          })
        } else {
          this.$message({
            showClose: true,
            message: res.meta.message,
            type: 'error'
          })
        }
        this.back()
      })
    },
    ignore() {
      const data = {
        middle_item_id: this.$route.query.middle_item_id
      }
      ignore(data).then(res => {
        // console.log(res)
        if (res.meta.code === '200') {
          this.$message({
            showClose: true,
            message: '忽略成功',
            type: 'success'
          })
        } else {
          this.$message({
            showClose: true,
            message: res.meta.message,
            type: 'error'
          })
        }
        this.back()
      })
    },
    update() {
      const data = {
        middle_item_id: this.$route.query.middle_item_id
      }
      update(data).then(res => {
        if (res.meta.code === '200') {
          this.$message({
            showClose: true,
            message: '更新成功',
            type: 'success'
          })
        } else {
          this.$message({
            showClose: true,
            message: res.meta.message,
            type: 'error'
          })
        }
        this.back()
      })
    },
    synchronizeDetails() {
      const data = this.$route.query
      if (data) {
        const form = {
          item_code: data.item_code,
          middle_item_id: data.middle_item_id,
          synchronize_status: data.synchronize_status
        }
        this.dataLoading = true
        synchronizeDetails(form)
          .then(res => {
            this.dataLoading = false
            if (res.meta.code === '200' && res.data != null) {
              //    this.queryForm   data.item
              this.queryForm = Object.assign(this.queryForm, res.data.item)
              this.dataItem = res.data
              this.showTip()
              // if (this.queryForm.synchronize_status == 'SYNCHRONIZE_SUCCESS') {
              //   this.queryForm.synchronize_status = '同步成功'
              // } else if (this.queryForm.synchronize_status == 'SYNCHRONIZE_FAILED') {
              //   this.queryForm.synchronize_status = '同步失败'
              // } else if (this.queryForm.synchronize_status == 'WAIT_SYNCHRONIZE') {
              //   this.queryForm.synchronize_status = '待同步'
              // } else if (this.queryForm.synchronize_status == 'IGNORE_SYNCHRONIZE') {
              //   this.queryForm.synchronize_status = '忽略'
              // }
              this.queryForm.synchronize_status = this.checkDataList.find(i => this.queryForm.synchronize_status === i.value).label
              if (this.queryForm.project_type == 'UNKNOWN_ITEM') {
                this.queryForm.project_type = '未知办件类型'
              } else if (this.queryForm.project_type == 'IMMEDIATE_ITEM') {
                this.queryForm.project_type = '即办件'
              } else if (this.queryForm.project_type == 'COMMITMENT_ITEM') {
                this.queryForm.project_type = '承诺件'
              }

              if (this.queryForm.item_type == 'ADMINISTRATIVE_LICENSE') {
                this.queryForm.item_type = '行政许可'
              } else if (this.queryForm.item_type == 'ADMINISTRATIVE_PUNISH') {
                this.queryForm.item_type = '行政处罚'
              } else if (this.queryForm.item_type == 'ADMINISTRATIVE_FORCE') {
                this.queryForm.item_type = '行政强制'
              } else if (this.queryForm.item_type == 'ADMINISTRATIVE_COLLECTION') {
                this.queryForm.item_type = '行政征收'
              } else if (this.queryForm.item_type == 'ADMINISTRATIVE_SUPPLY') {
                this.queryForm.item_type = '行政给付'
              } else if (this.queryForm.item_type == 'ADMINISTRATIVE_CHECK') {
                this.queryForm.item_type = '行政检查'
              } else if (this.queryForm.item_type == 'ADMINISTRATIVE_CONFIRMATION') {
                this.queryForm.item_type = '行政确认'
              } else if (this.queryForm.item_type == 'ADMINISTRATIVE_AWARD') {
                this.queryForm.item_type = '行政奖励'
              } else if (this.queryForm.item_type == 'ADMINISTRATIVE_ADJUDICATION') {
                this.queryForm.item_type = '行政裁决'
              } else if (this.queryForm.item_type == 'OTHER_ADMINISTRATIVE_POWER') {
                this.queryForm.item_type = '其他行政权力'
              } else if (this.queryForm.item_type == 'PUBLIC_SERVICE') {
                this.queryForm.item_type = '公共服务'
              }
              //   console.log(this.queryForm)
              this.$emit('setTitle', this.queryForm.item_name)
              this.tableData.content = res.data.material_list
              //   console.log(res.data.material_list)
            }
          })
          .catch(err => {
            this.dataLoading = false
          })
      }
    },
    downSampleFile(row) {
      if (row.sample_file_list.length != 0) {
        console.log(row.sample_file_list)
        const itemList = row.sample_file_list
        itemList.forEach((e, index) => {
          // this.downURLfile(e.intranet_path, e.attachment_name, index)
          this.createIFrame(e.intranet_path, 1000, 1000)
        })
      } else {
        this.$message({
          showClose: true,
          message: '暂无文件下载',
          type: 'warning'
        })
      }
    },
    downblankFile(row) {
      if (row.blank_file_list.length != 0) {
        // console.log(row.blank_file_list)
        const itemList = row.blank_file_list
        // intranet_path attachment_name
        itemList.forEach(e => {
          // this.downURLfile(e.intranet_path, e.attachment_name)
          this.createIFrame(e.intranet_path, 1000, 1000)
        })
      } else {
        this.$message({
          showClose: true,
          message: '暂无文件下载',
          type: 'warning'
        })
      }
    },
    // 下载当前上传的文件
    downURLfile(url, name, index) {},
    createIFrame(url, triggerDelay, removeDelay) {
      setTimeout(function() {
        // 动态添加iframe,设置src,然后删除
        const frame = document.createElement('iframe') // 创建a对象
        frame.setAttribute('style', 'display: none')
        frame.setAttribute('src', url)
        frame.setAttribute('id', 'iframeName')
        frame.setAttribute('referrerpolicy', 'no-referrer')
        document.body.appendChild(frame)
        setTimeout(function() {
          const node = document.getElementById('iframeName')
          node.parentNode.removeChild(node)
        }, removeDelay)
      }, triggerDelay)
    }
  }
}
</script>

<style lang="scss" scoped>
.box-card {
  border-radius: 0px;
}
.box-title {
  color: #409eff;
}
.foot-btn {
  display: flex;
  justify-content: center;
}
</style>

<style lang="scss" scoped>
.tipmessage {
  background-color: #fdf6ec;
  border-color: #faecd8;
  align-items: start;
  line-height: 30px;
}
.tipmessage i {
  margin-top: 5px;
  margin-right: 15px;
  color: #e6a23c;
  font-size: 20px;
}
.tipmessage .el-message__content {
  line-height: 25px;
  color: #444;
}
.descriptions {
  margin-bottom: 20px;
}
.info-wrap {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  img {
    margin-right: 10px;
  }
  .info-title {
    font-size: 20px;
    color: #333333;
  }
}
</style>
