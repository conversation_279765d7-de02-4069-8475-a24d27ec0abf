<template>
  <div id="process">
    <el-card class="process-item" :body-style="{padding:'10px 20px 10px 20px'}" v-if="isData">
      <el-row class="process-wrap" :gutter="24" type="flex">
        <el-col :span="2">
          <el-row class="process-wrap" type="flex" align="middle">
            <el-col :span="24">
              <div class="word-wrap">
                <span>{{processData.procedure_message}}</span>
              </div>
            </el-col>
          </el-row>
        </el-col>
        <el-col :span="11">
          <div class="process-title">
            <span>经办人：{{processData.account_name}}</span>
          </div>
          <div>
            <span>提交时间：{{processData.procedure_date}}</span>
          </div>
        </el-col>
        <el-col :span="11">
          <div class="process-title">
            <span>经办人账号：{{processData.account}}</span>
          </div>
        </el-col>
      </el-row>
      <!-- </div> -->
    </el-card>

    <!-- <el-card class="process-item" :body-style="{padding:'10px 20px 10px 20px'}">
      <el-row class="process-wrap" :gutter="24" type="flex">
        <el-col :span="2">
          <el-row class="process-wrap" type="flex" align="middle">
            <el-col :span="24">
              <div class="word-wrap">
                <span>同步失败</span>
              </div>
            </el-col>
          </el-row>
        </el-col>
        <el-col :span="11">
          <div class="process-title">
            <span>经办人：部门人员A</span>
          </div>
          <div>
            <span>提交时间：2022-01-01 10:00:00</span>
          </div>
        </el-col>
        <el-col :span="11">
          <div class="process-title">
            <span>经办人账号：bm123</span>
          </div>
        </el-col>
      </el-row>
    </el-card>-->
  </div>
</template>

<script>
import { getLog } from '@/api/synchronizeData'
export default {
  data() {
    return {
      processData: {
        procedure_message: '',
        account_name: '',
        procedure_date: '',
        account: ''
      },
      isData: false
    }
  },

  mounted() {
    // console.log(this.$route.query.synchronize_status)
    const itemCode = this.$route.query.item_code
    const synchronizeStatus = this.$route.query.synchronize_status
    if (synchronizeStatus === 'IGNORE_SYNCHRONIZE') {
      // 同步流程-忽略 状态为忽略 请求忽略过程信息
      this.getLog(itemCode, 'SYNCHRONIZE_IGNORE')
    } else if (synchronizeStatus === 'SYNCHRONIZE_SUCCESS') {
      //同步流程-更新  如果同步成功 请求更新过程信息
      this.getLog(itemCode, 'SYNCHRONIZE_UPDATE')
    }
  },

  methods: {
    getLog(itemCode, operator_log) {
      const query = {
        item_code: itemCode,
        operator_log: operator_log
      }
      getLog(query).then(res => {
        if (res.meta.code === '200' && res.data != null) {
          this.processData = res.data
          if (operator_log === 'SYNCHRONIZE_UPDATE') {
            this.processData.procedure_message = '同步事项'
          }
          this.isData = true
          // console.log(this.processData)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
#process {
  padding: 0 20px 20px;
}
.process-item {
  margin: 30px 0;

  .word-wrap {
    width: 80px;
    // height: 80px;
    span {
      font-size: 28px;
      color: #409eff;
    }
  }
  .process-title {
    margin-bottom: 10px;
    margin-top: 10px;
  }
  .process-wrap {
    height: 80px;
  }
}
</style>