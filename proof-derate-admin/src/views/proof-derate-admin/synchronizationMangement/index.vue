<template>
  <div class="content-wrapper synchronizationMangement padding-10">
    <!-- <papeTitle :title-name="titleName" :is-has-back="false" /> -->
    <CardTitle :title-name="titleName">
      <template></template>
    </CardTitle>
    <!-- <section class="content-header"> -->
    <el-card class="box-card" shadow="never">
      <el-form ref="form" :model="queryForm" label-width="120px" class="el-check-form" :rules="rules">
        <el-row :gutter="24" justify="center" type="flex">
          <el-col :span="24">
            <el-form-item label="事项名称">
              <el-input v-model="queryForm.item_name" clearable placeholder="请输入事项名称" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24" justify="center" type="flex">
          <el-col :span="12">
            <el-form-item label="事项编码">
              <el-input v-model="queryForm.item_code" clearable placeholder="请输入事项编码" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="实施机构">
              <el-input v-model="queryForm.dept_name" clearable placeholder="请输入实施机构" />
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-button type="primary" @click="synchronizeListBySearch()">查询</el-button>
          </el-col>
        </el-row>
        <el-row :gutter="24" justify="center" type="flex">
          <el-col :span="12">
            <el-form-item label="同步状态">
              <el-checkbox-group v-model="checkList">
                <!-- <el-checkbox label="NOT_SYNCHRONIZE">待同步</el-checkbox>
                  <el-checkbox label="SYNCHRONIZE_SUCCESS">同步成功</el-checkbox>
                  <el-checkbox label="SYNCHRONIZE_FAILED">同步失败</el-checkbox>
                <el-checkbox label="IGNORE_SYNCHRONIZE">忽略</el-checkbox>-->
                <el-checkbox v-for="(i,index) in checkDataLsit1" :key="index" :label="i.value">{{ i.label }}</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="事项版本">
              <el-radio-group v-model="radio">
                <el-radio label="search_all_version">全部版本</el-radio>
                <el-radio label="search_max_new_version">最新版本</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="4" />
        </el-row>
        <el-row :gutter="24" />
        <el-row :gutter="24">
          <el-col :span="24">
            <el-form-item label="事项类型">
              <el-checkbox-group v-model="item_type">
                <el-checkbox v-for="(i, key) in mattersTypeList" :key="key" :label="i.value">{{ i.label }}</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div style="color: #888; padding:20px 10px 0" class="dashed-line">
        共
        <span class="text-red">{{ tableData.total }}</span>条符合查询条件
        <span v-if="tableData.content.length!=0">，以下是第1至第{{ tableData.content.length }}项</span>
      </div>
      <custom-table
        ref="table"
        :is-card-type="false"
        :table-data="tableData"
        :table-header="tableHeader"
        :stripe="false"
        :table-tools="tableTools"
        style="margin-top: 10px"
        @query="query"
        @refresh="query(1)"
      >
        <template #operation="{ row }">
          <div>
            <el-button type="text" @click="detail(row)" v-permission="'catalog:item:synchronize:info'">查看</el-button>
          </div>
        </template>
        <!-- <template #synchronize_status="{ row }">
            <div>
              <span v-if="row.synchronize_status=='SYNCHRONIZE_SUCCESS'">同步成功</span>
              <span v-if="row.synchronize_status=='IGNORE_SYNCHRONIZE'">忽略</span>
              <span v-if="row.synchronize_status=='SYNCHRONIZE_FAILED'">同步失败</span>
              <span v-if="row.synchronize_status=='NOT_SYNCHRONIZE'">待同步</span>
            </div>
        </template>-->
        <template #item_type="{ row }">
          <div>
            <span v-if="row.item_type=='ADMINISTRATIVE_LICENSE'">行政许可</span>
            <span v-if="row.item_type=='ADMINISTRATIVE_PUNISH'">行政处罚</span>
            <span v-if="row.item_type=='ADMINISTRATIVE_FORCE'">行政强制</span>
            <span v-if="row.item_type=='ADMINISTRATIVE_COLLECTION'">行政征收</span>
            <span v-if="row.item_type=='ADMINISTRATIVE_SUPPLY'">行政给付</span>
            <span v-if="row.item_type=='ADMINISTRATIVE_CHECK'">行政检查</span>
            <span v-if="row.item_type=='ADMINISTRATIVE_CONFIRMATION'">行政确认</span>
            <span v-if="row.item_type=='ADMINISTRATIVE_AWARD'">行政奖励</span>

            <span v-if="row.item_type=='ADMINISTRATIVE_ADJUDICATION'">行政裁决</span>
            <span v-if="row.item_type=='OTHER_ADMINISTRATIVE_POWER'">其他行政权力</span>
            <span v-if="row.item_type=='PUBLIC_SERVICE'">公共服务</span>
          </div>
        </template>

        <template #item_status="{ row }">
          <div>
            <span v-if="row.item_status==='WORK'">在用</span>
            <span v-if="row.item_status==='SUSPEND'">暂停</span>
            <span v-if="row.item_status==='CANCEL'">取消</span>
          </div>
        </template>
      </custom-table>
    </el-card>
    <!-- </section> -->
  </div>
</template>

<script>
import Enum from '@/utils/enum'
import CustomTable from '@/components/Element/Table'
import { synchronizeList, listCount } from '@/api/synchronizeData'
import { getSynchronizeStatus, getMattersTypeList } from '@/api/common/dict'
import papeTitle from '@/components/papeTitle'
import CardTitle from '@/components/CardTitle'
export default {
  name: 'ProofInvestigationHandel',
  components: {
    CustomTable,
    papeTitle,
    CardTitle
  },

  data() {
    return {
      queryForm: {
        item_name: '', // 事项名称
        dept_name: '', // 实施机构
        item_code: '',
        synchronize_status: '', // 同步状态
        page_direction: 'DESC',
        item_type: [],
        search_all_version: false,
        search_max_new_version: false,
        page_number: 1,
        page_size: 10
      },
      radio: 'search_all_version',
      rules: {},
      numberOfElements: '',
      tableData: {
        border: false,
        content: [], // 表格数据
        loading: false, // 控制表格加载效果
        total: 0, // 分页查询时的总数，去掉该属性，不显示分页条
        currentPage: 1, // 当前页码
        pageSize: 10,
        isShowIndex: true,
        pageDirection: 'desc',
        isShowSelection: false // 是否显示多选框，默认false
      },
      tableHeader: [
        // { label: '证明名称', prop: 'certificate_name', minWidth: '160px' }, // 配置slot属性，可支持使用插槽
        {
          label: '事项编码',
          prop: 'item_code',
          minWidth: '180px',
          align: 'left'
        },
        { label: '事项名称', prop: 'item_name', align: 'left', minWidth: '120px' },
        { label: '办理项名称', prop: 'situation_name', align: 'left', minWidth: '120px' },
        { label: '实施机构', prop: 'dept_name', align: 'left', width: '120px' },
        {
          label: '事项类型',
          prop: 'item_type',
          minWidth: '80px',
          align: 'left',
          slot: 'item_type'
        },
        // isHeaderslot 与 prop 不要同名
        {
          label: '事项状态',
          prop: 'item_status',
          width: '120px',
          align: 'left',
          //   fixed: 'right',
          slot: 'item_status'
        },
        { label: '最近更新时间', prop: 'last_modification_time', align: 'left', width: '120px' },
        {
          label: '同步状态',
          prop: 'synchronize_status',
          width: '120px',
          align: 'left',
          formatter: (row, col, val) => {
            if (val != null && val != undefined) {
              return this.checkDataList.find(i => i.value === val).label
            } else {
              return val
            }
          }
        },
        {
          label: '操作',
          prop: 'operation',
          width: '120px',
          fixed: 'right',
          align: 'left',
          slot: 'operation'
        }
      ],
      tableTools: [],
      // checkList: ['NOT_SYNCHRONIZE', 'SYNCHRONIZE_SUCCESS', 'SYNCHRONIZE_FAILED', 'IGNORE_SYNCHRONIZE'],
      checkList: [],
      item_type: [],
      checkDataList: [],
      checkDataLsit1: [],
      mattersTypeList: [],
      titleName: '事项同步管理'
    }
  },

  mounted() {
    // const userdata = this.$store.state.user.userdata.rolePermissionVos
    // console.log('当前用户信息', userdata)
    // userdata.forEach(e => {
    //   console.log(e.target, '.........', e.action)
    // })
    this.getMattersTypeList()
    this.getSynchronizeStatus()
  },
  methods: {
    getMattersTypeList() {
      getMattersTypeList()
        .then(res => {
          const data = res.data || []
          // Enum.mattersTypeList.splice(0)
          // Enum.mattersTypeList.push(...data)
          this.mattersTypeList.push(...data)
          this.mattersTypeList.forEach(e => {
            this.item_type.push(e.value)
          })
          console.log('this.mattersTypeList', this.mattersTypeList, 'this.item_type', this.item_type)
        })
        .catch(() => {
          // Enum.mattersTypeList.splice(0)
        })
    },
    detail(row) {
      this.$router.push({
        name: 'synchronizationInfo',
        query: {
          item_code: row.item_code,
          middle_item_id: row.middle_item_id,
          synchronize_status: row.synchronize_status
        }
      })
    },
    query() {
      this.synchronizeList()
    },
    synchronizeListBySearch() {
      this.tableData.currentPage = 1
      this.tableData.pageSize = 10
      this.synchronizeList()
    },
    getSynchronizeStatus() {
      getSynchronizeStatus().then(res => {
        if (res.meta.code === '200' && res.data != null) {
          this.checkDataList = res.data
          // 去除同步无效
          this.checkDataLsit1 = this.checkDataList.filter(i => i.value != 'SYNCHRONIZE_INVALID')
          this.checkDataLsit1.forEach(e => {
            this.checkList.push(e.value)
          })
          this.query()
        }
      })
    },
    synchronizeList() {
      this.queryForm.page_number = this.tableData.currentPage
      this.queryForm.page_size = this.tableData.pageSize
      this.queryForm.synchronize_status = this.checkList.join(',')
      this.tableData.loading = true
      this.queryForm.search_all_version = this.radio === 'search_all_version'
      this.queryForm.search_max_new_version = this.radio === 'search_max_new_version'
      this.queryForm.item_type = this.item_type.join(',')
      synchronizeList(this.queryForm)
        .then(res => {
          this.tableData.loading = false
          if (res.meta.code === '200' && res.data != null) {
            this.tableData.content = res.data.content
            this.tableData.total = Number(res.data.total_elements)
            this.numberOfElements = res.data.numberOfElements
            // listCount(this.queryForm).then(res => {
            //   this.tableData.total = Number(res.data.total_elements)
            // })
          } else {
            this.tableData.content = []
          }
        })
        .catch(err => {
          this.tableData.loading = false
        })
    }
  }
}
</script>

<style scoped>
.content-wrapper {
  /* padding: 10px; */
}
</style>
