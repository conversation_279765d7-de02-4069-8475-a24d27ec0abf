<template>
  <div class="content-wrapper synchronizationinfo padding-10">
    <CardTitle :title-name="title" :ifback="true" @back="back()"></CardTitle>
    <!-- <section class="content"> -->
      <el-tabs v-model="activeName" tab-position="top" @tab-click="handleClick" class="tabs">
        <el-tab-pane label="详情" name="first">
          <synchronizationDetail @setTitle="setTitle"></synchronizationDetail>
        </el-tab-pane>
        <el-tab-pane label="过程信息" name="second">
          <processDetail></processDetail>
        </el-tab-pane>
      </el-tabs>
    <!-- </section> -->
  </div>
</template>

<script>
import synchronizationDetail from './components/detail.vue'
import processDetail from './components/process.vue'
import CardTitle from '@/components/CardTitle'
export default {
  data() {
    return {
      activeName: 'first',
      title: ''
    }
  },
  components: {
    synchronizationDetail,
    processDetail,
    CardTitle
  },
  mounted() {
    // console.log(this.$route.query.synchronize_status)
  },

  methods: {
    handleClick() {},
    setTitle(data) {
      this.title = data
    },
    back() {
      this.$router.push({ name: 'synchronizationMangement' })
    }
  }
}
</script>

<style  scoped>
.box-title {
  /* color: #409eff; */
}
.foot-btn {
  display: flex;
  justify-content: center;
}
.tabs /deep/ .el-tabs__content {
  /* //   padding: 15px; */
  padding-top: 0;
  padding-left: 0;
  padding-right: 0;
}
.content-header i {
  cursor: pointer;
}
</style>