<template>
  <div :id="id" ref="chart" :style="{height: height, width: width}" />
</template>

<script>
const Echarts = require('echarts/lib/echarts') // 基础实例 注意不要使用import
require('echarts/lib/chart/bar')
require('echarts/lib/component/grid')

export default {
  name: 'EsBar',
  props: {
    id: {
      type: String,
      default: 'EsBar'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '100%'
    },
    title: {
      type: String,
      default: ''
    },
    type: {
      type: String,
      default: '' // clean|way
    },
    option: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      chart: null,
      optionConfig: {
        grid: {
          left: '12%',
          // right: "5%",
          bottom: '5%',
          top: '4%',
          containLabel: true
        },
        // backgroundColor: '#ffffff',
        xAxis: {
          show: false,
          type: 'value'
        },
        yAxis: [
          {
            type: 'category',
            inverse: true,
            // margin: 10,
            axisLabel: {
              show: true,
              width: '180',
              position: 'right',
              overflow: 'truncate',
              textStyle: {
                color: '#ffffff',
                fontSize: '12'
              }
            },
            splitLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            axisLine: {
              show: false
            },
            // bar name
            data: []
          },
          {
            type: 'category',
            inverse: true,
            axisTick: 'none',
            axisLine: 'none',
            show: true,

            axisLabel: {
              textStyle: {
                color: '#ffff',
                fontSize: '16'
              }
            },
            // bar value
            data: []
          }
        ],
        series: [
          {
            name: '值',
            type: 'bar',
            zlevel: 1,
            itemStyle: {
              normal: {
                barBorderRadius: 30,
                color: params => {
                  // build a color map as your need.
                  //定义一个颜色集合
                  var colorList = ['#0080FF', '#ffcb48', '#12c9fe', '#1DB9FF']
                  var colorList2 = ['#FFD4FF', '#ff900e', '#0599de', '#B3F3FF']
                  //对每个bar显示一种颜色
                  var idx = params.dataIndex < 0 ? params.dataIndex : 3
                  var colorS = colorList[idx]
                  var colorS2 = colorList2[idx]
                  return new Echarts.graphic.LinearGradient(1, 0, 0, 0, [
                    { offset: 0, color: colorS },
                    { offset: 1, color: colorS2 }
                  ])
                }
              }
            },
            barWidth: 20,
            // bar value
            data: []
          },
          {
            name: '背景',
            type: 'bar',
            barWidth: 20,
            barGap: '-100%',
            // bar getMax
            data: [],
            itemStyle: {
              normal: {
                color: '#0f2c50bf',
                barBorderRadius: 30
              }
            }
          }
        ]
      }
    }
  },
  created() {},
  watch: {
    option: {
      handler(val) {
        if (!val) return
        // 把父组件传过来的option赋值给optionConfig
        // this.optionConfig = val;
        if (this.$refs.chart) {
          if (val.length !== 0) {
            this.initCharts(val)
          }
        }
      },
      deep: true,
      immediate: true
    },
    type: {
      handler(newVal, oldVal) {
        // 把父组件传过来的option赋值给optionConfig
        this.type1 = newVal
      }
    }
  },
  mounted() {},
  methods: {
    initCharts(val) {
      console.log('initCharts', val)
      this.chart = Echarts.init(this.$refs.chart)
      let sourceData = val
      // 数据整理
      let xData = []
      let yData = []
      var unit = []
      sourceData.forEach(item => {
        yData.push(item.value)
        xData.push(item.label === null ? item.label : item.label.substring(0, 12))
        unit.push(item.unit)
      })
      var charts = {
        // 按顺序排列从大到小
        cityList: xData,
        cityData: yData
      }
      var top10CityList = charts.cityList
      var top10CityData = charts.cityData
      // var color = ["#ff9500", "#02d8f9", "#027fff"];
      if (this.title === '免证替代方式情况') {
        var color = ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272']
      } else {
        var color = ['#ff9500', '#02d8f9', '#027fff']
      }

      var color1 = ['#ffb349', '#70e9fc', '#4aa4ff']

      let lineY = []
      let lineT = []
      for (var i = 0; i < charts.cityList.length; i++) {
        console.log(charts.cityList[i])
        var x = i
        if (this.title != '免证替代方式情况') {
          if (x > 1) {
            x = 2
          }
          var data = {
            name: charts.cityList[i],
            color: color[x],
            value: top10CityData[i],
            barGap: '-100%',
            itemStyle: {
              normal: {
                show: true,
                // color: color[x],
                color: new Echarts.graphic.LinearGradient(
                  0,
                  0,
                  1,
                  0,
                  [
                    {
                      offset: 0,
                      color: color[x]
                    },
                    {
                      offset: 1,
                      color: color1[x]
                    }
                  ],
                  false
                ),
                barBorderRadius: 10
              },
              emphasis: {
                shadowBlur: 15,
                shadowColor: 'rgba(0, 0, 0, 0.1)'
              }
            }
          }
        } else {
          var data = {
            name: charts.cityList[i],
            color: color[x],
            value: top10CityData[i],
            barGap: '-100%',
            itemStyle: {
              normal: {
                show: true,
                color: color[x],
                // color: new Echarts.graphic.LinearGradient(
                //   0,
                //   0,
                //   1,
                //   0,
                //   [
                //     {
                //       offset: 0,
                //       color: color[x]
                //     },
                //     {
                //       offset: 1,
                //       color: color1[x]
                //     }
                //   ],
                //   false
                // ),
                barBorderRadius: 10
              },
              emphasis: {
                shadowBlur: 15,
                shadowColor: 'rgba(0, 0, 0, 0.1)'
              }
            }
          }
        }

        var data1 = {
          value: top10CityData[0],
          itemStyle: {
            color: '#001235',
            barBorderRadius: 10
          }
        }
        lineY.push(data)
        lineT.push(data1)
      }
      var backTop = []
      for (var i = top10CityData.length - 1; i >= 0; i--) {
        backTop.push(top10CityData[i])
      }
      let option = {
        title: [
          {
            text: this.title,
            x: '-5',
            y: '5%',
            textStyle: {
              fontSize: 24,
              fontWeight: 500
            },
            show: false
          }
        ],
        // tooltip: {
        //   trigger: "item",
        //   formatter: function(p) {
        //     if (p.seriesName === "total") {
        //       return "";
        //     }
        //     return p.name + "<br/>" + p.value + unit[0];
        //   },
        //   textStyle: {
        //     fontSize: 20,
        //     color: "#333333"
        //   }
        // },
        grid: {
          borderWidth: 0,
          top: '12%',
          // left: "32%",
          left: '42%',
          // right: "15%",
          right: '20%',
          bottom: '10%'
        },
        color: color,
        yAxis: [
          {
            type: 'category',
            inverse: true,
            axisTick: {
              show: false
            },
            axisLine: {
              show: false
            },
            axisLabel: {
              show: false,
              inside: false
            },
            data: top10CityList
          },
          {
            type: 'category',
            axisLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            axisLabel: {
              interval: 0,
              color: '#333333',
              align: 'right',
              margin: 60,
              fontSize: 18,
              formatter: function (val) {
                return val + unit[0]
              }
            },
            splitArea: {
              show: false
            },
            splitLine: {
              show: false
            },
            data: backTop
          }
        ],
        xAxis: {
          type: 'value',
          axisTick: {
            show: false
          },
          axisLine: {
            show: false
          },
          splitLine: {
            show: false
          },
          axisLabel: {
            show: false
          }
        },
        series: [
          // {
          //   name: "total",
          //   type: "bar",
          //   zlevel: 1,
          //   barGap: "-100%",
          //   barWidth: "10px",
          //   data: lineT,
          //   legendHoverLink: false
          // },
          {
            name: 'bar',
            type: 'bar',
            zlevel: 2,
            barWidth: '10px',
            data: lineY,
            label: {
              width: '50px',
              normal: {
                color: '#b3ccf8',
                show: true,
                position: [0, 0],

                textStyle: {
                  fontSize: 16
                },
                formatter: function (a) {
                  var num = ''
                  var str = ''
                  num = 'NO.' + (a.dataIndex + 1)
                  if (a.dataIndex === 0) {
                    str = '{color1|' + num + '} {color5|' + a.name + '}'
                  } else if (a.dataIndex === 1) {
                    str = '{color2|' + num + '} {color5|' + a.name + '}'
                  } else if (a.dataIndex === 2) {
                    str = '{color3|' + num + '} {color5|' + a.name + '}'
                  } else {
                    str = '{color4|' + num + '} {color5|' + a.name + '}'
                  }
                  return str
                },
                rich: {
                  color1: {
                    padding: [0, 0, 0, -160],

                    color: '#FF7149',
                    fontWeight: 700,
                    fontSize: 18
                  },
                  color2: {
                    padding: [0, 0, 0, -160],
                    color: '#FFB31C',
                    fontWeight: 700,
                    fontSize: 18
                  },
                  color3: {
                    padding: [0, 0, 0, -160],
                    color: '#90B9E3',
                    fontWeight: 700,
                    fontSize: 18
                  },
                  color4: {
                    padding: [0, 0, 0, -160],
                    color: '#CCCCCC',
                    fontWeight: 700,
                    fontSize: 18
                  },
                  color5: {
                    color: '#333333',
                    fontSize: 18
                  }
                }
              }
            }
          }
        ]
      }
      window.addEventListener('resize', () => {
        this.chart.resize()
      })
      option && this.chart.setOption(option, false)
    },
    randomNum() {
      var num = parseInt(Math.random() * 10000000)
      var a1 = num % 10
      if (num < 10) return [a1]
      var a2 = parseInt((num % 100) / 10)
      if (num < 100) return [a2, a1]
      var a3 = parseInt((num % 1000) / 100)
      if (num < 1000) return [a3, a2, a1]
      var a4 = parseInt(num / 1000)
      return a4 + a3 + a2 + a1
    }
  }
}
</script>
