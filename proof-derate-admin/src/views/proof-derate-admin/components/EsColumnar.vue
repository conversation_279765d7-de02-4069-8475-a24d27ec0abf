<template>
  <div :id="id" ref="chart" :style="{height: height, width: width}" />
</template>

<script>
const Echarts = require("echarts/lib/echarts"); // 基础实例 注意不要使用import
require("echarts/lib/chart/bar");
require("echarts/lib/chart/pictorialBar");
require("echarts/lib/component/grid");
require("echarts/lib/component/title");
require("echarts/lib/component/tooltip");
require("echarts/lib/component/legend");
export default {
  name: "EsColumnar",
  props: {
    id: {
      type: String,
      default: "columnar"
    },
    width: {
      type: String,
      default: "100%"
    },
    height: {
      type: String,
      default: "100%"
    },
    echartData: {
      type: Array,
      default: () => {
        return [];
      }
    },
    option: {
      type: Array,
      default: () => {
        return {
          echartData: {
            xdata: ["0", "1"],
            subTitle: ["副标题", "副标题1"],
            value: [0, 0],
            material: [0, 0]
          },
          series: [
            {
              name: "未完成",
              color: "#2fffa4",
              colorStops: [
                { offset: 0, color: "rgba(29, 245, 160, .7)" },
                { offset: 0.5, color: "rgba(29, 245, 160, .7)" },
                { offset: 0.5, color: "rgba(29, 245, 160, .3)" },
                { offset: 1, color: "rgba(29, 245, 160, .3)" }
              ]
            },
            {
              name: "已完成",
              color: "#32ffee",
              colorStops: [
                { offset: 0, color: "rgba(50, 255, 238, .7)" },
                { offset: 0.5, color: "rgba(50, 255, 238, .7)" },
                { offset: 0.5, color: "rgba(50, 255, 238, .3)" },
                { offset: 1, color: "rgba(50, 255, 238, .3)" }
              ]
            }
          ]
        };
      }
    }
  },
  data() {
    return {
      chart: null
    };
  },
  watch: {
    option: {
      handler(val) {
        if (!val) return;
        this.initCharts();
        // window.addEventListener("resize", () => {
        //   this.chart.resize();
        // });
      }
    }
  },
  mounted() {
    // this.initCharts();
  },
  methods: {
    initCharts() {
      let that = this;
      this.chart = Echarts.init(this.$refs.chart);

      const subTitle = this.option.echartData.subTitle
      const value = this.option.echartData.value;
      const material = this.option.echartData.material;
      const dataArr = {
        xdata: this.option.echartData.xdata,
        value: value,
        vaccination: [
          {
            value: value[0],
            label: {
              show: true,
              position: "left",
              padding: [-100, 30, 0, 0],
              formatter: [
                "{a|"+this.option.echartData.xdata[0]+" {c}项}\n",
                "{b|"+subTitle[0]+": " + material[0] + "}"
              ].join("\n"),
              rich: {
                a: {
                  color: "blue",
                  fontSize: 24
                },
                b: {
                  padding: [16, 0, 0, 30],
                  color: "balck",
                  fontSize: 18
                }
              }
            }
          },
          {
            value: value[1],
            label: {
              show: true,
              position: "right",
              padding: [-50, 0, 0, 30],
              formatter: [
                "{a|"+this.option.echartData.xdata[1]+" {c}项}\n",
                "{b|"+subTitle[1]+": " + material[1] + "}"
              ].join("\n"),
              rich: {
                a: {
                  color: "blue",
                  fontSize: 24
                },
                b: {
                  padding: [16, 0, 0, 30],
                  color: "balck",
                  fontSize: 18
                }
              }
            }
          }
        ],
      };

      // tooltip
      const tooltip = {
        trigger: "axis",
        textStyle: { fontSize: "100%" },
        formatter: params => {
          let rander = params
            .map(item =>
              item.seriesType !== "pictorialBar"
                ? `<div>${item.seriesName} ${
                    item.seriesType !== "line" ? item.value : item.value + "%"
                  }</div>`
                : ""
            )
            .join("");
          return `
            <div>${params[0].axisValue}</div>
            ${rander}
        `;
        }
      };
      const label = {
        show: false,
        position: "outside",
        formatter: "{a|{b}}",
        rich: {
          hr: {
            backgroundColor: "t",
            borderRadius: 3,
            width: 3,
            height: 3,
            padding: [3, 3, 0, -12]
          },
          a: {
            padding: [-30, 15, -20, 15]
          }
        }
      };
      const grid = { top: "18%", left: "45%", right: "45%", bottom: "6%" };
      // xAxis
      const xAxis = {
        axisTick: { show: false },
        axisLine: {
          show: false,
          lineStyle: { color: "rgba(255,255,255, .2)" }
        },
        axisLabel: { show: false, textStyle: { fontSize: 12, color: "#fff" } },
        data: dataArr.xdata
      };

      // yAxis
      const yAxis = [
        {
          axisTick: { show: false },
          axisLine: { show: false },
          splitLine: {
            show: false,
            lineStyle: { color: "rgba(255,255,255, .05)" }
          },
          axisLabel: { show: false, textStyle: { fontSize: 16, color: "#fff" } }
        }
      ];

      // series
      const series = [
        {
          z: 1,
          name: "上部1",
          type: "pictorialBar",
          symbolPosition: "end",
          data: dataArr.value,
          symbol: "diamond",
          symbolOffset: ["0%", "-50%"],
          symbolSize: ["140%", "10%"],
          label: {
            show: false
          },
          itemStyle: {
            borderColor: function(params) {
              this.index = params.dataIndex;
              return that.option.series[params.dataIndex].color;
            },
            color: function(params) {
              return that.option.series[params.dataIndex].color;
            }
          }
        },
        {
          z: 1,
          type: "bar",
          name: "",
          // barWidth: 50,
          barCategoryGap: "0%",
          data: dataArr.vaccination,
          itemStyle: {
            color: function(params) {
              return {
                type: "linear",
                x: 0,
                x2: 1,
                y: 0,
                y2: 0,
                colorStops: that.option.series[params.dataIndex].colorStops
              };
            }
          },
          label: {
            show: false
          }
        }
      ];
      const option = {
        tooltip,
        label,
        xAxis,
        yAxis,
        series,
        grid
        // backgroundColor: "rgba(0, 0, 0, .7)"
      };

      option && this.chart.setOption(option,true);
    }
  }
};
</script>
