<template>
  <div>
    <el-row>
      <el-col :span="21">
        <h3 style="z-index:9999">{{dataConfig.title}}: {{ form.material_name }}</h3>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24">
        <el-tabs v-model="dataConfig.activeName" type="border-card" @tab-click="handleClick">
          <el-tab-pane label="详情" name="desc">
            <el-divider content-position="left">基本信息</el-divider>
            <el-row>
              <el-col :span="8" :offset="2">所属事项: {{ form.item_name }}</el-col>
              <el-col :span="8" :offset="2">实施机构: {{ form.impl_org_name }}</el-col>
            </el-row>
            <br />

            <el-row>
              <el-col :span="8" :offset="2">事项类型: {{ form.item_type }}</el-col>
              <el-col :span="8" :offset="2">实施区划: {{ form.division_code }}</el-col>
            </el-row>
            <br />

            <el-divider content-position="left">证明材料清理</el-divider>
            <edit-clear-info ref="clearInfo" :row="clearData" />
          </el-tab-pane>
        </el-tabs>
      </el-col>
    </el-row>
    <!--梳理确认-->
    <div class="fixed-bottom">
      <div class="btn-group">
        <el-button :loading="examineApproveLoading" type="primary" @click="submitEditForm">完成</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from "vuex";
//import CustomTable from "@/components/Element/Table";
import EditClearInfo from "@/views/proof-derate-admin/itemBiz/itemClear/components/EditClearInfo";
import {
  getProofStatusList,
  getItemTypeList,
  getReplaceCancelWay,
  getUnitTypeList
} from "@/api/common/dict";
import { codeItemList } from "@/api/admin/org";
import {
  getProofListFormView,
  goLicenseItemView,
  proofListConfirmCreate,
  getProofSubmitInfo,
  getProofConfirmInfo,
  getProofAuditInfo,
  getProofListFirstDrafts
} from "@/api/itemBiz/list";
import { proofListUpdate } from "@/api/itemBiz/clear";
import { getAttachmentBase64 } from "@/api/common/download";
import { proofAuditListExamine } from "@/api/itemBiz/audit";
import { dataURLtoDownload } from "@/utils/index";
import moment from "moment";

export default {
  name: "CleanMethodEdit",
  components: {
    //CustomTable,
    EditClearInfo
  },
  props: {
    data: {
      type: Object,
      default: function() {
        return {};
      }
    },
    type: {
      type: String,
      default: "show"
    },
    proofRoute: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      dataConfig: {
        id: "",
        title: "证明档案",
        activeName: "desc"
      },
      clearData: {
        proof_list_id: "",
        material_id: "",
        proof_name: "",
        proof_clear_type: "",
        direct_description: "",
        replace_cancel_way: "",
        clerk_commitment: {
          commit_book_description: "",
          commit_attachment_id: "",
          commit_attachment_name: ""
        },
        replace_data_shared: {
          industry_dept_name: "",
          industry_dept_code: "",
          proof_provide_type: null,
          data_shared_description: ""
        },
        replace_dept_survey: {
          dept_name: "",
          dept_code: "",
          dept_cancel_description: ""
        },
        replace_investigation: {
          industry_dept_name: "",
          industry_dept_code: "",
          proof_provide_type: null,
          investigation_description: ""
        },
        replace_license: {
          license_description: "",
          license_code: "",
          license_name: ""
        },
        replace_other: {
          other_clear_description: ""
        },
        user_info: {
          account_dept_code: "",
          account_dept_name: ""
        }
      },
      form: {
        material_name: "",
        item_name: "",
        impl_org_name: "",
        item_type: "",
        item_type_text: ""
      },
      material: {
        law_id: "",
        law_name: null,
        proof_clear_type: "",
        replace_cancel_way: ""
      },
      proof_catalog_vo: {
        name: ""
      },
      tableData: {
        content: [] // 表格数据
      },
      // 表头配置
      tableHeader: [
        { label: "证照名称", prop: "license_name", minWidth: "200px" }, // 配置slot属性，可支持使用插槽
        {
          label: "操作",
          prop: "operateColumn", // prop为“operateColumn”时，可配置actions按钮列表
          minWidth: "50px",
          fixed: "right",
          actions: [
            {
              type: "text",
              label: "查看",
              action: "show" // 按钮该按钮时，派发事件的名称
            }
          ]
        }
      ],
      replaceCancelWayList: [
        /* { label: '转化为电子证照/其他证件', value: 'TURN_LICENSE_OR_OTHER_LICENSE_WAY' },
        { label: '办事人承诺', value: 'HANDLE_AFFAIRS_PROMISE' },
        { label: '数据共享(证明开具部门)', value: 'DATA_SHARING' },
        { label: '人工协查(证明开具部门)', value: 'ARTIFICIAL_INVESTIGATION' },
        { label: '部门自行调查', value: 'DEPARTMENT_INVESTIGATION' },
        { label: '数据共享(其他)', value: 'OTHER_WAY' }*/
      ],
      proofStatusList: [], // 事项状态
      actualizeList: [], // 行政区划字典
      getItemTypeList: [], // 事项类型
      unitTypeList: [], // 证明开具单位类型:
      auditList: [
        { label: "同意", value: "APPROVED" },
        { label: "不同意", value: "UNAPPROVED" }
      ],
      examineApproveLoading: false,
      proofRecordList: Array(3)
    };
  },

  watch: {
    data: {
      handler(val) {
        if (!val) return;
        // 把父组件传过来的tableData赋值给tableConfig
        Object.keys(val).forEach(prop => {
          if (val[prop] !== undefined) {
            this.$set(this.dataConfig, prop, val[prop]);
          }
        });
      },
      deep: true,
      immediate: true
    },
    "form.division_code"(val) {
      const actualizeList = this.actualizeList;
      if (actualizeList.length > 0 && val !== "") {
        let info = _.find(
          actualizeList,
          i => i.value === this.form.division_code
        );
        this.form.division_code = info === undefined ? val : info.label;
      }
    },
    "form.item_type"(val) {
      const getItemTypeList = this.getItemTypeList;
      if (getItemTypeList.length > 0 && val !== "") {
        let info = _.find(
          getItemTypeList,
          i => i.value === this.form.item_type
        );
        this.form.item_type = info === undefined ? val : info.label;
      }
    }
  },
  computed: {
    ...mapState({
      account: state =>
        state.user &&
        state.user.userdata &&
        state.user.userdata.userAccount &&
        state.user.userdata.userAccount.account,
      roles: state =>
        state.user && state.user.userdata && state.user.userdata.roles,
      name: state =>
        state.user &&
        state.user.userdata &&
        state.user.userdata.userAccount &&
        state.user.userdata.userAccount.name,
      dept: state =>
        state.user &&
        state.user.userdata &&
        state.user.userdata.userInfo &&
        state.user.userdata.userInfo.orgName
    })
  },
  mounted() {
    this.initData();
  },
  methods: {
    codeItemList(id) {
      codeItemList({ value: id })
        .then(res => {
          const data = res.content || [];
          this.form.division_code =
            data.length > 0 ? data[0].name : this.form.division_code;
        })
        .catch(() => {});
    },
    initData: async function() {
      let proofStatusListRes = await getProofStatusList();
      let itemTypeListRes = await getItemTypeList();
      let replaceCancelWayRes = await getReplaceCancelWay();
      let unitTypeListRes = await getUnitTypeList();
      this.proofStatusList = proofStatusListRes.data || [];
      this.getItemTypeList = itemTypeListRes.data || [];
      this.replaceCancelWayList = replaceCancelWayRes.data || [];
      this.unitTypeList = unitTypeListRes.data || [];
      //被await阻塞的同步代码
      this.getProofListFormView();
    },
    handleClick(tab, event) {
      //console.log(tab, event);
    },

    getProofListFormView() {
      getProofListFormView(this.dataConfig.id, {
        item_clear_status: "WAIT_FOR_CLEAN"
      }).then(res => {
        this.reorganizeData(res);
      });
    },
    reorganizeData(res) {
      const item_material_vo = res.data.item_material_vo;
      const material = res.data.proof_list_vo;
      this.form = res.data.item_vo || {};
      this.form.material_name = item_material_vo.material_name;
      this.codeItemList(this.form.division_code); //转换区划
      this.proof_catalog_vo = res.data.proof_catalog_vo || {};

      const proof_list_vo = res.data.proof_list_vo;
      const license_name =
        proof_list_vo.license_name != null
          ? proof_list_vo.license_name.split(",")
          : "";
      const license_code =
        proof_list_vo.license_code != null
          ? proof_list_vo.license_code.split(",")
          : "";
      this.clearData = {
        proof_list_id: this.dataConfig.id,
        material_id: res.data.item_material_vo.id,
        proof_name: res.data.item_material_vo.material_name,
        proof_clear_type: proof_list_vo.proof_clear_type,
        direct_description: proof_list_vo.direct_description,
        replace_cancel_way: proof_list_vo.replace_cancel_way.split(","),
        clerk_commitment: {
          commit_book_description: proof_list_vo.commit_book_description,
          commit_attachment_id: proof_list_vo.commit_attachment_id,
          commit_attachment_name: proof_list_vo.commit_attachment_name
        },
        replace_data_shared: {
          industry_dept_name: proof_list_vo.industry_dept_name_dataShared,
          industry_dept_code: proof_list_vo.industry_dept_code_dataShared,
          proof_provide_type: proof_list_vo.proof_provide_type_dataShared,
          data_shared_description: proof_list_vo.data_shared_description
        },
        replace_dept_survey: {
          dept_name: proof_list_vo.dept_name,
          dept_code: proof_list_vo.dept_code,
          dept_cancel_description: proof_list_vo.dept_cancel_description
        },
        replace_investigation: {
          industry_dept_name: proof_list_vo.industry_dept_name_investigation,
          industry_dept_code: proof_list_vo.industry_dept_code_investigation,
          proof_provide_type: proof_list_vo.proof_provide_type_investigation,
          investigation_description: proof_list_vo.investigation_description
        },
        replace_license: {
          license_description: proof_list_vo.license_description,
          license_code: license_code,
          license_name: license_name
        },
        replace_other: {
          other_clear_description: proof_list_vo.other_clear_description
        }
      };

    },

    hasWay(arr, val) {
      return arr.indexOf(val) !== -1;
    },
    show(row) {
      goLicenseItemView(row.license_code).then(res => {
        window.open(res.data.url, "_blank");
      });
    },
    backPrev() {
      this.$router.go(-1);
    },

    goEditClearWay() {
      //只有清单和梳理，证明状态为待梳理的情况下可以编辑修改证明
      let name = this.type === "list" ? "item_list_edit" : "item_carding_edit";
      this.$router.push({
        name: name,
        query: {
          id: this.dataConfig.id
        }
      });
    },
    filterSubmitData(data) {
      let handleData = [];
      if (data && data.length > 0) {
        data.forEach(i => {
          let item = {};
          let directlyCancleData = {
            proof_list_id: i.proof_list_id,
            direct_description: i.direct_description,
            material_id: i.material_id,
            proof_clear_type: i.proof_clear_type,
            proof_name: i.proof_name
          };
          let replaceCancelData = {};
          let replaceCancelWay = i.replace_cancel_way.filter(i=> i != "");
          let replaceLicense = { replace_license: i.replace_license };
          let clerk_commitment = { clerk_commitment: i.clerk_commitment };
          let replaceDataShared = {
            replace_data_shared: i.replace_data_shared
          };
          let replaceInvestigation = {
            replace_investigation: i.replace_investigation
          };
          let replaceDeptSurvey = {
            replace_dept_survey: i.replace_dept_survey
          };
          let replaceOther = { replace_other: i.replace_other };
          let userInfo = {
            user_info: {
              account_name: this.name,
              account_dept_code: "",
              account_dept_name: this.dept
            }
          };
          if (i.proof_clear_type === "DIRECTLY_CANCEL") {
            item = Object.assign(directlyCancleData, userInfo);
          } else if (
            i.proof_clear_type === "REPLACE_CANCEL" &&
            replaceCancelWay.length > 0
          ) {
            item = Object.assign(
              { replace_cancel_way: replaceCancelWay },
              directlyCancleData,
              userInfo
            );
            if (
              replaceCancelWay.indexOf("TURN_LICENSE_OR_OTHER_LICENSE_WAY") !=
              -1
            ) {
              item = Object.assign(item, replaceLicense);
            }
            if (replaceCancelWay.indexOf("HANDLE_AFFAIRS_PROMISE") != -1) {
              item = Object.assign(item, clerk_commitment);
            }
            if (replaceCancelWay.indexOf("DATA_SHARING") != -1) {
              item = Object.assign(item, replaceDataShared);
            }
            if (replaceCancelWay.indexOf("ARTIFICIAL_INVESTIGATION") != -1) {
              item = Object.assign(item, replaceInvestigation);
            }
            if (replaceCancelWay.indexOf("DEPARTMENT_INVESTIGATION") != -1) {
              item = Object.assign(item, replaceDeptSurvey);
            }
            if (replaceCancelWay.indexOf("OTHER_WAY") != -1) {
              item = Object.assign(item, replaceOther);
            }
          }
          handleData.push(item);
        });
      }
      return handleData;
    },
    submitEditForm() {
      this.$refs["clearInfo"].formRefs.validate(valid => {
        if (valid) {
          this.$confirm("确定修改?", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning"
          }).then(() => {
            const formData = this.$refs.clearInfo.getFormData();
            let sendData = this.filterSubmitData([formData]);
            proofListUpdate(sendData[0]).then(res => {
              let type = res.meta.code === "200" ? "success" : "warning";
              this.$message({
                type: type,
                message: res.meta.msg
              });
              if (type === "success") this.$router.go(-2);
            });
          });
        }
      });
    },
    getAttachmentBase64() {
      getAttachmentBase64({ proof_list_id: this.dataConfig.id }).then(res => {
        if (res.meta.code === "200") {
          dataURLtoDownload(res.data, this.material.commit_attachment_name);
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>

.time {
  color: #919191;
}
.status {
  color: #2d76ce;
  font-size: 19px;
  font-weight: 900;
}
.fixed-bottom {
  width: 100%;
  transform: scale3d(1, 1, 1);
  .btn-group {
    background: rgba(255, 255, 255, 0.95);
    border-top: 1px solid #096dd9;
    padding: 20px 10px 10px 50px;
    // position: absolute;
    bottom: 0;
    right: 0;
    z-index: 100;
    text-align: right;
    height: 80px;
    width: inherit;
  }
}
</style>
