<template>
  <div>
    <section class="content-header title-algin-midle">
      <h1 style="z-index:9999">{{dataConfig.title}}: {{ item_vo.item_name }}</h1>

      <div>
        <el-button type="warning" plain @click="backPrev" icon="el-icon-back">返回</el-button>
      </div>
      <!-- <br /> -->
    </section>
    <section class="content">
      <el-row>
        <el-col :span="24">
          <el-tabs v-model="dataConfig.activeName" type="border-card" @tab-click="handleClick">
            <el-tab-pane label="详情" name="desc">
              <el-divider content-position="left">基本信息</el-divider>
              <el-row>
                <el-col :span="8" :offset="2">实施机构: {{ item_vo.impl_org_name }}</el-col>
                <el-col :span="8" :offset="2">事项类型: {{ item_vo.item_type }}</el-col>
              </el-row>
              <br />
              <clean-method
                v-for="(data,index) in multipleData"
                :key="index"
                :data="data"
                :dict-data="dictData"
                :type="type"
                :multipleData="data"
                :proofRoute="proofRoute"
                :isDisShowProofCatalog="true"
                :ref="'formRelevance_'+index"
              />
            </el-tab-pane>
          </el-tabs>
        </el-col>
      </el-row>
    </section>
  </div>
</template>

<script>
import { getProofStatusList, getItemTypeList, getReplaceCancelWay, getUnitTypeList, getProofClearType } from '@/api/common/dict'
import { getProofListFirstDraftsView } from '@/api/itemBiz/list'
import CleanMethod from '@/views/proof-derate-admin/components/CleanMethod'

export default {
  name: 'FirstDraftDetail',
  components: {
    CleanMethod
  },
  props: {
    itemCode: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      dataConfig: {
        id: '',
        title: '事项证明档案',
        activeName: 'desc'
      },
      data: {
        id: '',
        title: '证明档案',
        activeName: 'desc'
      },
      type: '',
      item_clear_status: '',
      proofRoute: 'item_carding_info_first_draft',
      replaceCancelWayList: [], // 替代取消方式
      proofStatusList: [], // 事项状态
      actualizeList: [], // 行政区划字典
      getItemTypeList: [], // 事项类型
      unitTypeList: [], // 证明开具单位类型:
      proofClearTypeList: [], // 取消方式:
      auditList: [
        { label: '同意', value: 'APPROVED' },
        { label: '不同意', value: 'UNAPPROVED' }
      ],
      item_vo: {},
      proof_data: [],
      multipleData: []
    }
  },
  watch: {
    'item_vo.item_type'(val) {
      const getItemTypeList = this.getItemTypeList
      if (getItemTypeList.length > 0 && val !== '') {
        let info = _.find(getItemTypeList, i => i.value === this.item_vo.item_type)
        this.item_vo.item_type = info === undefined ? val : info.label
      }
    }
  },
  created() {
    this.initData()
  },
  methods: {
    backPrev() {
      this.$router.go(-1)
    },
    handleClick(tab, event) {
      //console.log(tab, event);
    },
    initData: async function () {
      let proofStatusListRes = await getProofStatusList()
      let itemTypeListRes = await getItemTypeList()
      let replaceCancelWayRes = await getReplaceCancelWay()
      let unitTypeListRes = await getUnitTypeList()
      let proofClearTypeListRes = await getProofClearType()
      this.proofStatusList = proofStatusListRes.data || []
      this.getItemTypeList = itemTypeListRes.data || []
      this.replaceCancelWayList = replaceCancelWayRes.data || []
      this.unitTypeList = unitTypeListRes.data || []
      this.proofClearTypeList = proofClearTypeListRes.data || []
      //被await阻塞的同步代码
      this.dictData = {
        replaceCancelWayList: this.replaceCancelWayList, // 替代取消方式
        proofStatusList: this.proofStatusList, // 事项状态
        getItemTypeList: this.getItemTypeList, // 事项类型
        unitTypeList: this.unitTypeList, // 证明开具单位类型:
        proofClearTypeList: this.proofClearTypeList // 取消方式:
      }
      this.needShowTips = true
      this.getProofListFormView()
    },
    getfunction(list) {
      var tmp = []
      list
        .concat()
        .sort()
        .sort(function (a, b) {
          if (a == b && tmp.indexOf(a) === -1) tmp.push(a)
        })
      return tmp
    },
    getProofListFormView() {
      getProofListFirstDraftsView(this.itemCode).then(res => {
        this.item_vo = res.data.item_vo
        let proof_data = res.data.proof_data
        console.log('proof_data', proof_data)
        let item_detail = []
        if (proof_data.length > 0) {
          proof_data.forEach(i => {
            item_detail.push(this.reorganizeData(i))
          })
        }
        this.proof_data = item_detail

        let audit_info = res.data.audit_info || []
        //如果存在审核信息，则设置审核信息并设置审核只读标记
        if (audit_info && Object.keys(audit_info).length > 0) {
          this.auditForm.auditDisabled = true
          this.auditForm.audit = audit_info.operator_type
          this.auditForm.remark = audit_info.audit_description
        }
        this.getMultipleData()
      })
    },
    getMultipleData() {
      // console.log('this.proof_data', this.proof_data)
      const proof_list_id_List = this.proof_data.map(e => {
        return e.material.material_id
      })

      let itemList = this.getfunction(proof_list_id_List)
      let multipleItemList = []
      let unmultipleItemList = []
      // console.log('13123', this.getfunction(proof_list_id_List))
      itemList.forEach(e => {
        let item = this.proof_data.filter(i => i.material.material_id === e)
        multipleItemList.push({ materialData: item })
      })
      this.proof_data.forEach(e => {
        if (itemList.indexOf(e.material.material_id) === -1) {
          unmultipleItemList.push({ materialData: [e] })
        }
      })
      const li = multipleItemList.concat(unmultipleItemList)
      console.log('multipleItemList', multipleItemList, 'unmultipleItemList', unmultipleItemList, 'li', li)
      this.multipleData = li
      // proof_list_id_List.filter(i => i === 'be2c47e6-4746-4c65-a631-1790ad7d22e8')
      // console.log(
      //   'proof_list_id_List',
      //   proof_list_id_List,
      //   proof_list_id_List.indexOf('be2c47e6-4746-4c65-a631-1790ad7d22e8'),
      //   proof_list_id_List.filter(i => i === 'be2c47e6-4746-4c65-a631-1790ad7d22e8')
      // )
    },
    reorganizeData(res) {
      const item_material_vo = res.item_material_vo
      const proof_list_vo = res.proof_list_vo
      let material = {}
      let formRelevance = {
        relevance: ''
      }
      let selectForm = {
        proof_catalog_name: ''
      }
      let selectList = {
        id: ''
      }
      let proof_catalog_vo = res.proof_catalog_vo[0] || {}
      if (proof_catalog_vo) {
        formRelevance.relevance = proof_catalog_vo.name
        selectForm.proof_catalog_name = proof_catalog_vo.name
        selectList.id = proof_catalog_vo.id
      }

      material =
        {
          item_proof_status: proof_list_vo.item_proof_status, // 梳理状态
          // 证明材料清理
          material_name: item_material_vo.material_name,
          material_id: item_material_vo.id,
          law_name: item_material_vo.law_name,
          proof_clear_type: proof_list_vo.proof_clear_type, // 取消类型 DIRECTLY_CANCEL|REPLACE_CANCEL|DO_NOT_CLEAN
          proof_clear_type_text: this.proofClearTypeList.filter(i => i.value === proof_list_vo.proof_clear_type)[0].label || proof_list_vo.proof_clear_type, // 取消类型 DIRECTLY_CANCEL|REPLACE_CANCEL|DO_NOT_CLEAN
          direct_description: proof_list_vo.direct_description, // 直接取消说明,
          not_clean_description: proof_list_vo.not_clean_description, // 无需清理说明,
          proof_list_remark: proof_list_vo.proof_list_remark
        } || {}

      if (proof_list_vo.proof_clear_type === 'REPLACE_CANCEL') {
        let replaceCancelWayList = proof_list_vo.replace_cancel_way.split(',')
        let replaceCancelWayListText = ''
        _.forEach(replaceCancelWayList, i => {
          const way = _.find(this.replaceCancelWayList, j => j.value === i)
          const wayLabel = way ? way.label : proof_list_vo.replace_cancel_way
          replaceCancelWayListText += wayLabel + ','
        })
        material = Object.assign(material, {
          proof_list_id: proof_list_vo.proof_list_id,
          item_proof_status: proof_list_vo.item_proof_status, // 梳理状态
          replace_cancel_way: proof_list_vo.replace_cancel_way, // 替代取消方式
          replace_cancel_way_text: replaceCancelWayListText.substring(0, replaceCancelWayListText.length - 1), // 替代取消方式的文本
          license_name: proof_list_vo.license_name, // 电子证照名称，逗号隔开
          license_code: proof_list_vo.code, // 电子证照code，逗号隔开
          license_description: proof_list_vo.license_description, // 转换为电子证照说明
          commit_book_description: proof_list_vo.commit_book_description, // 承诺书说明
          commit_attachment_id: proof_list_vo.commit_attachment_id, // 承诺书说明
          commit_attachment_name: proof_list_vo.commit_attachment_name, // 承诺书说明
          industry_dept_name: proof_list_vo.industry_dept_name, // 所属行业部门名称
          industry_dept_code: proof_list_vo.industry_dept_code, // 所属行业部门代码
          // proof_provide_type_investigation:
          //   proof_list_vo.proof_provide_type_investigation !== null
          //     ? this.unitTypeList.find(i => i.value === proof_list_vo.proof_provide_type_investigation).label
          //     : proof_list_vo.proof_provide_type_investigation,
          proof_provide_type_dataShared:
            proof_list_vo.proof_provide_type_dataShared !== null
              ? this.unitTypeList.find(i => i.value === proof_list_vo.proof_provide_type_dataShared).label
              : proof_list_vo.proof_provide_type_dataShared, // 证明开具单位类型

          data_shared_description: proof_list_vo.data_shared_description, // 数据共享说明
          investigation_description: proof_list_vo.investigation_description, // 人工协查说明
          dept_cancel_description: proof_list_vo.dept_cancel_description, // 自行调查说明 （部门自行调查)
          other_clear_description: proof_list_vo.other_clear_description // 其它说明
        })
      }
      const license_name = proof_list_vo.license_name != null ? proof_list_vo.license_name.split(',') : ''
      const license_code = proof_list_vo.license_code != null ? proof_list_vo.license_code.split(',') : ''
      let content = _.map(license_name, function (i, index) {
        return { license_name: i, license_code: license_code[index] }
      })
      return {
        proof_catalog_vo: proof_catalog_vo,
        formRelevance: { relevance: proof_catalog_vo.name },
        selectForm: { proof_catalog_name: proof_catalog_vo.name },
        selectList: { id: proof_catalog_vo.id },
        material: material,
        tableData: { content: content }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.fixed-bottom {
  width: 100%;
  transform: scale3d(1, 1, 1);
  .btn-group {
    background: rgba(255, 255, 255, 0.95);
    border-top: 1px solid #096dd9;
    padding: 20px 10px 10px 50px;
    // position: absolute;
    bottom: 0;
    right: 0;
    z-index: 100;
    text-align: right;
    height: 80px;
    width: inherit;
  }
}
</style>
