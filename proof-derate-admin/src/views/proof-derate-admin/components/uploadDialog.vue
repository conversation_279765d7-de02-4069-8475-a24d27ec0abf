<!-- 单文件上传组件 -->
<template>
  <div class="uploadDialog">
    <div style="margin:-23px 0 10px">{{ uploadTitle }}</div>
    <el-upload
      ref="upload"
      class="upload-demo"
      action
      :accept="limitFileType"
      :file-list="fileList"
      :on-change="handleChange"
      :show-file-list="false"
      :auto-upload="false"
    >
      <el-input ref="upload" slot="trigger" v-model="input" placeholder="请选择文件" class="input" />
      <el-button slot="trigger" type="primary" class="btnFile">浏览</el-button>
      <div v-if="template" slot="tip" class="el-upload__tip" style="position: absolute;margin-bottom: 100px">
        <img src="@/assets/proof-derate-admin-images/u3029.png" alt>
        温馨提示：请选择以后缀名为{{ whitelist.join('/') }}文件且上传文件大小不得超过1M！
        <span>
          <a class="temp_style" @click="downloadTemplate">下载导入模板</a>
        </span>
      </div>
      <div v-if="tips_message" slot="tip" class="el-upload__tip" style="position: absolute">
        <img src="@/assets/proof-derate-admin-images/u3029.png" alt>
        {{ tips_message }}
      </div>
    </el-upload>
  </div>
</template>
<script>
import { getIsWhitelist, getFileType } from '@/utils/index'
import { validPrefix } from '@/utils/validate'
export default {
  props: {
    template: {
      type: Boolean,
      default: true
    },
    tips_message: {
      type: String,
      default: ''
    },
    limitFileType: {
      type: String,
      // default: '.xls,.xlsx'
      default: '.doc,.docx,.pdf'
    },
    uploadTitle: {
      type: String,
      default: '请选择您要导入的数据文件'
    },
    whitelist: {
      type: Array,
      default: ['doc', 'docx', 'pdf']
    }
  },
  data() {
    return {
      input: '',
      file: '',
      fileList: [],
      downloadFlag: false
      // whitelist:['doc','docx','pdf']
    }
  },
  mounted() {
    console.log('limitFileType-----', this.limitFileType)
  },
  methods: {
    handleChange(file) {
      const fileName = getFileType(file.name).fileName
      const isLt10m = file.size / (1024 * 1024) < 1
      if (!isLt10m) {
        this.$message.error('文件大小不能超过1m！')
      } else {
        if (fileName.indexOf('http') != -1 || validPrefix(fileName)) {
          this.$message.error('文件名开头包含特殊符号！')
        } else {
          if (!getIsWhitelist(file.name, this.whitelist)) {
            this.$message.error(`请重新选择以${this.whitelist.join(',')}为后缀名的文件！`)
          } else {
            this.file = file
            this.input = file.name
          }
        }
      }

      // this.file = file;
      // this.input = file.name;
    },
    downloadTemplate() {
      this.downloadFlag = true
      this.$emit('toFather', this.downloadFlag)
    },
    // 清空
    clearFiles() {
      // this.$refs.upload.clearFiles()
      this.input = null
    }
  }
}
</script>
<style lang="scss" scoped>
.el-upload__tip {
  margin: 20px 0;
}
.input {
  width: 83%;
  position: absolute;
  left: 3%;
  top: 39%;
}
.btnFile {
  position: absolute;
  left: 88%;
  top: 39%;
}
.el-upload__input {
  display: none !important;
}
.el-upload {
  width: 98%;
}
.temp_style {
  color: #409eff !important;
}
</style>
<style>
  .uploadDialog .el-upload__input{
    display: none !important;
  }
</style>

