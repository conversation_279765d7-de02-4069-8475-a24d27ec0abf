<template>
  <div :id="id" ref="chart" :style="{height: height, width: width}" />
</template>

<script>
const Echarts = require("echarts/lib/echarts"); // 基础实例 注意不要使用import
require("echarts/lib/chart/pie");
require("echarts/lib/component/title");
require("echarts/lib/component/tooltip");
require("echarts/lib/component/legend");
export default {
  name: "EsPie",
  props: {
    id: {
      type: String,
      default: "pie"
    },
    width: {
      type: String,
      default: "100%"
    },
    height: {
      type: String,
      default: "100%"
    },
    echartData: {
      type: Array,
      default: () => {
        return [];
      }
    },
    title: {
      type: String,
      default: ""
    },
    pieType:{
      type: String,
      default: "totalEvents" // totalEvents 事项总数 totalCancel
    },
    option: {
      type: Object,
      default: () => {
        return {
          backgroundColor: "#051c3d",
          color: ["#02D8FD", "#0D7CDB", "#F8C815", "#91CC75", "#00FFFF"]
        };
      }
    }
  },
  data() {
    return {
      chart: null
    };
  },
  watch: {
    echartData: {
      handler(val) {
        if (!val) return;
        this.initCharts();
        // window.addEventListener("resize", () => {
        //   this.chart.resize();
        // });
      }
    }
  },
  mounted() {
    // this.initCharts();
  },
  methods: {
    initCharts() {
      this.chart = Echarts.init(this.$refs.chart);
      let data = this.echartData;
      var legendData = [];
      let numData = [];
      console.log('this.echartData',this.echartData)
      this.echartData.forEach(i => {
        legendData.push(i.name);
        numData.push(i.value);
      });
      let sum = numData.reduce(function(prev, cur, index, array) {
        return prev + cur;
      });
      // let title = legendData.length === 2 ? "事项总数" : "取消证明";
      let title = this.pieType === 'totalEvents' ? "事项总数" : "取消清单"
      
      var colorList = [
        "#FD866A",
        "#73ACFF",
        "#FDD56A",
        "#FDB36A",
        "#73DDFF",
        "#9E87FF"
      ];

      const option = {
        title: [
          {
            text: sum,
            x: "center",
            // x: "%",
            y: "43%",
            textStyle: {
              fontSize: 20
            },
            // 点击标题需设置triggerEvent熟悉
            triggerEvent:true
          },
          {
            text: title,
            x: "center",
            y: "52%",
            // top: "20",
            textStyle: {
              // fontSize: 20
              fontSize:16
            },
            triggerEvent:true
          }
        ],
        // title:{
        //    text: this.title,
        //     x: "10",
        //     y: "5%",
        //     textStyle: {
        //       fontSize: 24,
        //       fontWeight: 500
        //     }
        // },
        calculable: true,
        tooltip: {
          trigger: "item",
          formatter: " {c}种 ({d}%)"
        },
        legend: {
          show: true,
          icon: "circle",
          x: "center",
          y: "88%",
          // y: "78%",
          data: legendData,
          textStyle: {
            color: "gray"
          }
        },
        series: [
          {
            name: "",
            type: "pie",
            // center: ["45%", "60%"],
            // radius: ["35%", "50%"],
            // right:'5%',
            width:'100%',
            height: "100%",
            radius: ["25%", "40%"],
            label: {
              color: "inherit",
              formatter: function(params) {
                console.log('data',data)
                let label = data[params.dataIndex].label;
                let value = data[params.dataIndex].value;
                let material = data[params.dataIndex].materialCount;
                let one = [
                  "{c|" + label + "}{c|" + value + " 项}\n",
                  "{d|涉及材料 " + material + " 种}"
                ].join("\n");
                let two = "{b|" + value + "}{unit| 种} ";
                // return legendData.length === 2 ? one : two;
                return title === '事项总数' ? one : two;
                
              },
               
              // backgroundColor: "#F6F8FC",
              // borderColor: "#8C8D8E",
              // borderWidth: 1,
              // borderRadius: 4,
              // padding: [0, 8],
              padding: [0, 5],
              rich: {
                b: {
                  color: "#333333",
                  // fontSize: 24,
                  fontSize: 24,
                  fontWeight: "500",
                  lineHeight: 31
                },
                unit: {
                  color: "#333333",
                  fontSize: 16,
                  fontWeight: "400",
                  lineHeight: 24
                },
                c: {
                  color: "#333333",
                  fontSize: 14
                },
                d: {
                  color: "#8c8c8c",
                  fontSize: 14
                }
              }
            },
            labelLine: {
              normal: {
                // length: 20,
                // length2: 30,
                length: 15,
                length2: 20,
                lineStyle: {
                  width: 1
                }
              }
            },
            data: this.echartData
          }
        ]
      };
      console.log('option',JSON.stringify(option),legendData )
      option && this.chart.setOption(option);
      this.chart.on('click', (param)=> { 
          if(param.componentType== "title"){
            this.$emit('pieClick', param);
          }
      })
    }
  }
};
</script>
