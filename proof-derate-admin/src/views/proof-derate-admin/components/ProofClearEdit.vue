<template>
  <div class="content-wrapper">
    <section class="content-header">
      <span class="breadcrumb" align="right">
        <el-button type="warning" plain @click="backPrev" icon="el-icon-back">返回</el-button>
      </span>
    </section>
    <section class="content">
      <el-row>
        <el-col :span="21">
          <h3>{{title}}: {{ form.item_name }}</h3>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-tabs v-model="activeName" type="border-card" @tab-click="handleClick">
            <el-tab-pane label="详情" name="desc">
              <el-divider content-position="left">基本信息</el-divider>
              <el-row>
                <el-col :span="8" :offset="2">实施机构: {{ form.impl_org_name }}</el-col>
                <el-col :span="8" :offset="2">事项类型: {{ form.item_type }}</el-col>
              </el-row>
              <br />

              <!--证明材料清理-->
              <template v-if="pageType == 'WAIT_FOR_CLEAN' || pageType == 'DO_NOT_CLEAN'">
                <el-divider content-position="left">证明材料清理</el-divider>
                <el-row>
                  <el-col :span="24">
                    <custom-table
                      ref="table1"
                      :is-card-type="false"
                      :table-data="tableData1"
                      :table-header="tableHeader1"
                      @show="show"
                    >
                      <template #license_name="{ row }">
                        <el-button
                          type="text"
                          @click="goLicenseItemView(row)"
                        >{{ row.license_name }}</el-button>
                      </template>
                    </custom-table>
                  </el-col>
                </el-row>
              </template>
              <!--证明材料-->
              <template v-else>
                <el-divider content-position="left">证明材料</el-divider>
                <el-row>
                  <el-col :span="24">
                    <custom-table
                      ref="table"
                      :is-card-type="false"
                      :table-data="tableData"
                      :table-header="tableHeader"
                      @show="show"
                    />
                  </el-col>
                </el-row>
              </template>
            </el-tab-pane>
          </el-tabs>
        </el-col>
      </el-row>
    </section>
  </div>
</template>

<script>
import { mapState } from "vuex";
import CustomTable from "@/components/Element/Table";
import { getItemView } from "@/api/itemBiz/clear";
import { goLicenseItemView } from "@/api/itemBiz/list";
import {
  getItemTypeList,
  getProofClearType,
  getItemMaterialType
} from "@/api/common/dict";

export default {
  name: "ProofClearEdit",
  components: {
    CustomTable
  },
  props: {
    // 表格标题
    title: {
      type: String,
      default: "事项证明档案"
    }
  },
  data() {
    return {
      pageType: "PUBLIC",
      form: {
        item_name: "",
        impl_org_name: "",
        item_type: ""
      },
      activeName: "desc",
      tableData: {
        content: [] // 表格数据
      },
      // 表头配置
      tableHeader: [
        { label: "证明材料", prop: "material_name", minWidth: "200px" }, // 配置slot属性，可支持使用插槽
        {
          label: "清理类型",
          prop: "proof_clear_type",
          minWidth: "200px",
          formatter: (row, col, val) => {
            return val === null || val === undefined
              ? ""
              : this.proofClearType.find(i => i.value === val).label;
          }
        },
        {
          label: "操作",
          prop: "operateColumn", // prop为“operateColumn”时，可配置actions按钮列表
          minWidth: "50px",
          fixed: "right",
          actions: [
            {
              type: "text",
              label: "查看",
              action: "show" // 按钮该按钮时，派发事件的名称
            }
          ]
        }
      ],

      // 待清理，已清理
      tableData1: {
        content: [] // 表格数据
      },
      // 表头配置
      tableHeader1: [
        { label: "序号", prop: "index", minWidth: "80px" },
        { label: "材料名称", prop: "material_name", minWidth: "200px" }, // 配置slot属性，可支持使用插槽
        {
          label: "材料类型",
          prop: "material_type",
          minWidth: "200px",
          formatter: (row, col, val) => {
            return val === null || val === undefined
              ? ""
              : this.itemMaterialType.find(i => i.value === val).label;
          }
        },
        {
          label: "已关联电子证照",
          slot: "license_name",
          prop: "license_name",
          minWidth: "200px"
        },
        { label: "事项证明状态", prop: "ietm_proof_status", minWidth: "200px" }
        // {
        //   label: "操作",
        //   prop: "operateColumn", // prop为“operateColumn”时，可配置actions按钮列表
        //   minWidth: "50px",
        //   fixed: "right",
        //   actions: [
        //     {
        //       type: "text",
        //       label: "查看",
        //       action: "show" // 按钮该按钮时，派发事件的名称
        //     }
        //   ]
        // }
      ],
      itemTypeList: [],
      proofClearType: [],
      itemMaterialType: []
    };
  },
  beforeRouteLeave(to, from, next) {
    if (to.name == "item_clear") {
      to.meta.keepAlive = true;
    } else {
      to.meta.keepAlive = false;
    }
    next();
  },
  computed: {
		...mapState({
			account: state =>
				state.user &&
				state.user.userdata &&
				state.user.userdata.userAccount &&
				state.user.userdata.userAccount.account,
			roles: state =>
				state.user && state.user.userdata && state.user.userdata.roles,
			name: state =>
				state.user &&
				state.user.userdata &&
				state.user.userdata.userAccount &&
				state.user.userdata.userAccount.name,
			dept: state =>
				state.user &&
				state.user.userdata &&
				state.user.userdata.userInfo &&
				state.user.userdata.userInfo.orgName
		})
	},
  created() {
    const item_code = this.$route.query["id"];
    const item_clear_status = this.$route.query["item_clear_status"];
    this.pageType = item_clear_status;
    this.getItemTypeList();
    this.getProofClearType();
    this.getItemMaterialType();
    this.getItemView(item_code, item_clear_status);
  },
  methods: {
    getItemTypeList() {
      getItemTypeList()
        .then(res => {
          this.itemTypeList = res.data || [];
        })
        .catch(() => {
          this.itemTypeList = [];
        });
    },
    getProofClearType() {
      getProofClearType()
        .then(res => {
          this.proofClearType = res.data || [];
        })
        .catch(() => {
          this.proofClearType = [];
        });
    },
    getItemMaterialType() {
      getItemMaterialType()
        .then(res => {
          this.itemMaterialType = res.data || [];
        })
        .catch(() => {
          this.itemMaterialType = [];
        });
    },
    getItemView(item_code, item_clear_status) {
      getItemView(item_code, { item_clear_status })
        .then(res => {
          this.form = res.data.item_vo || {};
          if (this.form.item_type) {
            this.form.item_type = this.itemTypeList.find(
              i => i.value === this.form.item_type
            ).label;
          }
          if (
            this.pageType === "WAIT_FOR_CLEAN" ||
            this.pageType === "DO_NOT_CLEAN"
          ) {
            this.tableData1.content = _.map(
              res.data.item_material_vo_list,
              (item, index) => {
                return {
                  index: index + 1,
                  ietm_proof_status: "",
                  ...item
                };
              }
            );
          } else {
            this.tableData.content = _.map(
              res.data.item_material_vo_list,
              (item, index) => {
                return {
                  id: item.proof_list_id,
                  ...item
                };
              }
            );
          }
        })
        .catch(() => {});
    },
    handleClick(tab, event) {
      console.log(tab, event);
    },
    show(row) {
      this.$router.push({
        name: "common_clean_method",
        query: {
          id: row.id
        }
      });
    },
    goLicenseItemView(row) {
      goLicenseItemView(row.license_code).then(res => {
        window.open(res.data.url, "_blank");
      });
    },
    backPrev() {
      this.$router.go(-1);
    }
  }
};
</script>

<style lang="scss" scoped>

</style>