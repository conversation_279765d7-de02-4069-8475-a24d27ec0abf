<template>
  <div>
    <el-row>
      <el-col :span="24">
        <!-- <el-tabs v-model="dataConfig.activeName" type="border-card" @tab-click="handleClick"> -->
        <!-- <el-tab-pane :label="item_detail.materialData[0].material.material_name" name="desc"> -->
        <span class="margin-left-10 info-wrap">
          <img :src="arrow" alt />
          <span class="info-title">{{ item_detail.materialData[0].material.material_name }}</span>
        </span>
        <template>
          <!-- <el-divider content-position="left">证明材料清理</el-divider> -->
          <el-row>
            <!-- <el-form label-width="120px">
              <el-col :span="8" :offset="2">
                <el-form-item label="材料提供依据:">{{ item_detail.materialData[0].material.law_name==null?"无":"" }}</el-form-item>
              </el-col>
              <el-col :span="8" :offset="2">
                <el-form-item label="证明清理类型:">{{ item_detail.materialData[0].material.proof_clear_type_text }}</el-form-item>
              </el-col>
            </el-form>-->

            <el-descriptions class="descriptions" title :column="2" border>
              <el-descriptions-item :label-style="{ width: '140px' }">
                <template slot="label">材料提供依据</template>
                {{ item_detail.materialData[0].material.law_name == null ? "无" : "" }}
              </el-descriptions-item>
              <el-descriptions-item :label-style="{ width: '140px' }">
                <template slot="label">证明清理类型</template>
                {{ item_detail.materialData[0].material.proof_clear_type_text }}
              </el-descriptions-item>
              <el-descriptions-item v-if="item_detail.materialData[0].material.proof_clear_type === 'DO_NOT_CLEAN'"
                :label-style="{ width: '140px' }">
                <template slot="label">无需清理说明</template>
                {{ item_detail.materialData[0].material.not_clean_description }}
              </el-descriptions-item>
              <el-descriptions-item
                v-else-if="item_detail.materialData[0].material.proof_clear_type === 'DIRECTLY_CANCEL'"
                :label-style="{ width: '140px' }">
                <template slot="label">直接取消说明</template>
                {{ item_detail.materialData[0].material.direct_description }}
              </el-descriptions-item>
              <el-descriptions-item :label-style="{ width: '140px' }">
                <template slot="label">备注</template>
                {{
                  item_detail.materialData[0].material.proof_list_remark ?
                    item_detail.materialData[0].material.proof_list_remark : '无'
                }}
              </el-descriptions-item>
            </el-descriptions>

            <!-- <el-col
                  :span="8"
                  :offset="2"
            >所属证明目录: {{ item_detail.materialData[0].proof_catalog_vo.name ? item_detail.materialData[0].proof_catalog_vo.name : "无"}}</el-col>-->
          </el-row>
          <br />
          <!-- <el-row>
                <el-col :span="20" :offset="2">证明清理类型: {{ item_detail.materialData[0].material.proof_clear_type_text }}</el-col>
          </el-row>-->
          <!-- <br /> -->
          <template v-if="item_detail.materialData[0].material.proof_clear_type === 'DO_NOT_CLEAN'">
            <!-- <el-row>
              <el-form label-width="120px">
                <el-col :span="20" :offset="2">
                  <el-form-item label="无需清理说明:">{{ item_detail.materialData[0].material.not_clean_description }}</el-form-item>
                </el-col>
              </el-form>
            </el-row>-->
            <!-- <br /> -->
          </template>
          <template v-else-if="item_detail.materialData[0].material.proof_clear_type === 'DIRECTLY_CANCEL'">
            <!-- <el-row>
              <el-form label-width="120px">
                <el-col :span="20" :offset="2">
                  <el-form-item label="直接取消说明: ">{{ item_detail.materialData[0].material.direct_description }}</el-form-item>
                </el-col>
              </el-form>
            </el-row>
            <br />-->
          </template>
          <template v-else>
            <!-- <el-row type="flex" align="middle">
                  <el-col :span="2" :offset="2">关联证明目录：</el-col>
                  <el-col :span="8">
                    <el-input v-model="item_detail.formRelevance.relevance" :disabled="true" placeholder="请输入内容" readonly></el-input>
                  </el-col>
            </el-row>-->

            <el-form ref="formRelevance" :model="item_detail.materialData[0].formRelevance" label-width="150px"
              class="formRelevance" :rules="formRelevanceRules">
              <!-- v-model="item_detail.formRelevance.relevance" -->
              <el-row :span="24">
                <el-col :span="22">
                  <el-form-item label="关联证明目录" prop="relevance">
                    <el-input v-model="relevance" placeholder="请输入内容"
                      :disabled="item_detail.materialData[0].material.proof_clear_type === null" readonly />
                  </el-form-item>
                </el-col>
                <div class="select">
                  <el-button v-if="!isDisShowProofCatalog" type="text" @click="selectEvent">选择</el-button>
                </div>
              </el-row>
              <el-row :span="24">
                <!-- :offset="2" -->
                <el-col :span="22">
                  <el-form-item label="替代方式">
                    <custom-table ref="table" :is-card-type="false" :table-data="tableCleanMethodData"
                      :table-header="tableCleanMethodHeader" :span-method="objectSpanMethod" class="customTable"
                      @show="show">
                      <template #license_name="{ row }">
                        <el-button type="text" @click="goLicenseItemView(row)">{{ row.license_name }}</el-button>
                      </template>
                    </custom-table>
                  </el-form-item>
                </el-col>
              </el-row>
              <!-- <el-row :span="24">
                <el-col :span="20">
                  <el-form-item
                    label="备注："
                  >{{item_detail.materialData[0].material.proof_list_remark?item_detail.materialData[0].material.proof_list_remark:'无'}}</el-form-item>
                </el-col>
              </el-row>-->
            </el-form>
          </template>
        </template>
        <!-- </el-tab-pane> -->
        <!-- </el-tabs> -->
      </el-col>
    </el-row>

    <!-- 关联证明目录弹框 -->
    <el-dialog title="关联目录" :width="is1366 == true ? '55%' : '40%'" :visible.sync="selectDialog">
      <el-transfer ref="transfer" v-model="value" :filterable="true" :titles="['待选', '已选']"
        filter-placeholder="请输入证明目录名称" :data="generateData" class="transfer" @change="selectionChange">
        <div slot="left-footer" class="transfer-input">
          <el-input v-model="selectForm.proof_catalog_name" placeholder="请输入内容" />
          <el-button class="transfer-footer" size="small" @click="searchTransfer">查询</el-button>
        </div>
      </el-transfer>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="selectFirmDouble1()">确 定</el-button>
        <el-button @click="selectDialog = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import CustomTable from '@/components/Element/Table'
import { codeItemList } from '@/api/admin/org'
import { goLicenseItemView, getProofListFirstDrafts, getCatalogReplace } from '@/api/itemBiz/list'
import { getAttachmentBase64, getPreliminaryDraftsAttachment } from '@/api/common/download'
import { proofAuditListExamine } from '@/api/itemBiz/audit'
import { dataURLtoDownload } from '@/utils/index'
import { getGetproofCatalogPage, getGetproofCatalogFind } from '@/api/certificationManagement/certificationList'
export default {
  name: 'CleanMethod',
  components: {
    CustomTable
  },
  props: {
    data: {
      type: Object,
      default: function () {
        return {}
      }
    },
    multipleData: {
      type: Object,
      default: function () {
        return {}
      }
    },
    dictData: {
      Object,
      default: function () {
        return {}
      }
    },
    type: {
      type: String,
      default: 'show'
    },
    proofRoute: {
      type: String,
      default: ''
    },
    isDisShowProofCatalog: {
      type: Boolean,
      default: false
    }
  },

  data() {
    return {
      dataConfig: {
        id: '',
        title: '事项证明档案',
        activeName: 'desc'
      },
      auditForm: {
        // 是否禁用
        auditDisabled: false,
        audit: '',
        remark: ''
      },
      auditRules: {
        audit: [{ required: true, message: '请选择审核结果', trigger: 'change' }]
      },
      // item_detail: {},
      item_detail: {
        formRelevance: {
          relevance: ''
        }
      },
      relevance: '',
      form: {
        material_name: '',
        item_name: '',
        impl_org_name: '',
        item_type: '',
        item_type_text: ''
      },
      material: {
        law_id: '',
        law_name: null,
        proof_clear_type: '',
        replace_cancel_way: ''
      },
      proof_catalog_vo: {
        name: ''
      },
      tableData: {
        content: [] // 表格数据
      },
      // 表头配置
      tableHeader: [
        { label: '证照名称', prop: 'license_name', minWidth: '200px' }, // 配置slot属性，可支持使用插槽
        {
          label: '操作',
          prop: 'operateColumn', // prop为“operateColumn”时，可配置actions按钮列表
          minWidth: '50px',
          fixed: 'right',
          actions: [
            {
              type: 'text',
              label: '查看',
              action: 'show' // 按钮该按钮时，派发事件的名称
            }
          ]
        }
      ],
      tableCleanMethodData: {
        content: [] // 表格数据
      },
      tableCleanMethodHeader: [
        { label: '证明目录', prop: 'name', minWidth: '200px' },
        { label: '替代方式', prop: 'alternativeMethods', minWidth: '200px' }
        // { label: '电子证照', prop: 'license_name', slot: 'license_name', minWidth: '200px' }
      ],
      replaceCancelWayList: [
        /* { label: '转化为电子证照/其他证件', value: 'TURN_LICENSE_OR_OTHER_LICENSE_WAY' },
        { label: '办事人承诺', value: 'HANDLE_AFFAIRS_PROMISE' },
        { label: '数据共享(证明开具部门)', value: 'DATA_SHARING' },
        { label: '人工协查(证明开具部门)', value: 'ARTIFICIAL_INVESTIGATION' },
        { label: '部门自行调查', value: 'DEPARTMENT_INVESTIGATION' },
        { label: '数据共享(其他)', value: 'OTHER_WAY' }*/
      ],
      proofStatusList: [], // 事项状态
      actualizeList: [], // 行政区划字典
      getItemTypeList: [], // 事项类型
      unitTypeList: [], // 证明开具单位类型:
      proofClearTypeList: [], // 取消方式:
      auditList: [],
      examineApproveLoading: false,
      proofRecordList: Array(3),
      // 关联电子证照
      selectDialog: false,
      selectLoading: false,
      formRelevance: {
        relevance: ''
      },
      formRelevanceRules: {
        relevance: [{ required: true, message: '请选择证明目录', trigger: 'blur' }]
      },
      selectForm: {
        proof_catalog_name: ''
      },
      tableDataSelect: {
        content: [], // 表格数据
        loading: false, // 控制表格加载效果
        total: 0, // 分页查询时的总数，去掉该属性，不显示分页条
        currentPage: 1, // 当前页码
        pageSize: 10,
        pageDirection: 'desc',
        multiple: true, // 是否多选 数据需要有id 属性值
        isShowSelection: true // 是否显示多选框，默认false
      },
      tableDirectoryData: {
        content: [], // 表格数据
        loading: false, // 控制表格加载效果
        total: 0, // 分页查询时的总数，去掉该属性，不显示分页条
        currentPage: 1, // 当前页码
        pageSize: 10,
        pageDirection: 'desc',
        multiple: true, // 是否多选 数据需要有id 属性值
        isShowSelection: true // 是否显示多选框，默认false
      },
      tableHeaderSelect: [
        {
          label: '',
          slot: 'select',
          prop: 'select',
          minWidth: '50px',
          fixed: 'right'
        },
        {
          label: '证明目录名称',
          prop: 'name',
          minWidth: '200px'
        },
        {
          label: '操作',
          slot: 'operate',
          prop: 'operate',
          minWidth: '50px',
          fixed: 'right'
        }
      ],
      radio: '',
      selectList: [],
      catalogReplaceInfo: {}, // 替换目录数据
      catalogReplaceInfoList: [], // 替换目录数据列表
      proof_list_id: '',
      material_id: '',
      value: [],
      generateData: [],
      getSelectList: [],
      is1366: false,
      proof_list_remark: '',
      arrow: require('@/assets/proof-derate-admin-images/arrow.png')
    }
  },

  watch: {
    data: {
      handler(val) {
        if (!val) return
        // 把父组件传过来的tableData赋值给tableConfig
        // this.item_detail = val
      },
      deep: true,
      immediate: true
    },
    // multipleData:{
    //    handler(val) {
    //     // if (!val) return
    //     console.log('1111111',val)
    //      this.item_detail = val
    //      console.log('this.item_detail', this.item_detail)
    //    }
    // },
    'form.division_code'(val) {
      const actualizeList = this.actualizeList
      if (actualizeList.length > 0 && val !== '') {
        const info = _.find(actualizeList, i => i.value === this.form.division_code)
        this.form.division_code = info === undefined ? val : info.label
      }
    },
    'form.item_type'(val) {
      const getItemTypeList = this.getItemTypeList
      if (getItemTypeList.length > 0 && val !== '') {
        const info = _.find(getItemTypeList, i => i.value === this.form.item_type)
        this.form.item_type = info === undefined ? val : info.label
      }
    },
    selectDialog: {
      handler(val) {
        if (val) {
          console.log('selectDialog', val)
          let time = null
          time = setTimeout(() => {
            this.watchLeftAllChecked()
            this.watchRightAllChecked()
            // const dom = $('div .is-filterable')[0]
            const dom = this.$refs.transfer.$el.firstChild.childNodes[1].childNodes[1]
            dom.addEventListener('scroll', () => {
              if (dom.scrollTop + dom.clientHeight === dom.scrollHeight) {
                console.log('到底了')
                this.tableDataSelect.pageSize = 10
                this.tableDataSelect.currentPage++
                this.query(this.tableDataSelect.currentPage)
              }
            })
          }, 0)
          // time = null
        }
      }
    }
  },
  computed: {
    ...mapState({
      account: state => state.user && state.user.userdata && state.user.userdata.userAccount && state.user.userdata.userAccount.account,
      roles: state => state.user && state.user.userdata && state.user.userdata.roles,
      name: state => state.user && state.user.userdata && state.user.userdata.userAccount && state.user.userdata.userAccount.name,
      dept: state => state.user && state.user.userdata && state.user.userdata.userInfo && state.user.userdata.userInfo.orgName
    })
  },
  mounted() {
    this.item_detail = this.multipleData
    console.log('item_detail', this.item_detail)
    // this.getSelectList = this.item_detail.materialData
    this.item_detail.materialData.forEach(i => {
      if (i.proof_catalog_vo != null) {
        this.value.push(i.proof_catalog_vo.id)
        this.getSelectList.push({ label: i.proof_catalog_vo.name, key: i.proof_catalog_vo.id, pinyin: i.proof_catalog_vo.name, proof_catalog_id: i.proof_catalog_vo.id })
        // console.log('i.proof_list_id',i.proof_list_id)
        this.selectList.push({ proof_catalog_id: i.proof_catalog_vo.id, proof_list_id: i.material.proof_list_id })
      }
    })
    console.log('this.selectList', this.selectList)
    console.log('this.value', this.value, this.item_detail.materialData)
    if (this.value.length > 2) {
      this.tableDataSelect.pageSize = this.value.length + 10
    }
    if (this.selectList.length !== 0) {
      this.selectFirmDouble1()
    }
    this.proof_list_id = this.item_detail.materialData[0].material.proof_list_id
    this.material_id = this.item_detail.materialData[0].material.material_id
    this.initData()
    this.screenWidth()
  },
  methods: {
    // 监听左侧全选是否选中
    watchLeftAllChecked() {
      this.leftscrollAllCheckedWatch && this.leftscrollAllCheckedWatch()
      const transferVm = this.$refs.transfer
      transferVm.$refs.leftPanel.handleAllCheckedChange = () => {
        return false
      }
    },
    // 监听右侧全选是否选中
    watchRightAllChecked() {
      this.rightscrollAllCheckedWatch && this.rightscrollAllCheckedWatch()
      const transferVm = this.$refs.transfer
      transferVm.$refs.rightPanel.handleAllCheckedChange = () => {
        return false
      }
    },
    searchTransfer() {
      this.generateData = []
      this.query(1)
    },
    codeItemList(id) {
      codeItemList({ value: id })
        .then(res => {
          const data = res.content || []
          this.form.division_code = data.length > 0 ? data[0].name : this.form.division_code
        })
        .catch(() => { })
    },
    initData: async function () {
      let required
      if (this.type === 'confirm') {
        required = false
      } else {
        required = true
      }
      this.formRelevanceRules = {
        relevance: [{ required: required, message: '请选择证明目录', trigger: 'blur' }]
      }
      this.proofStatusList = this.dictData.proofStatusList
      this.getItemTypeList = this.dictData.getItemTypeList
      this.replaceCancelWayList = this.dictData.replaceCancelWayList
      // this.tableCleanMethodData.content = this.replaceCancelWayList
      // this.tableCleanMethodData.content.forEach(e => {
      //   e.relevance = this.item_detail.formRelevance.relevance
      // })
      this.tableCleanMethodData.content = []
      this.item_detail.formRelevance = { relevance: '' }
      // console.log('this.replaceCancelWayList', this.replaceCancelWayList)
      let rowspan
      this.item_detail.materialData.forEach(e2 => {
        rowspan = 0
        _.cloneDeep(this.replaceCancelWayList).forEach(e => {
          if (this.hasWay(e2.material.replace_cancel_way, e.value)) {
            // e.relevance = e2.formRelevance.relevance
            // console.log(e.value)
            const item = {
              label: e.label,
              relevance: e2.formRelevance.relevance,
              license_name: ''
            }
            rowspan = rowspan + 1
            if (this.hasWay('TURN_LICENSE_OR_OTHER_LICENSE_WAY', e.value)) {
              // console.log('e2.material', e2)
              item.license_name = e2.material.license_name
              item.license_code = e2.material.license_code
            }
            if (this.hasWay('TURN_LICENSE_ITEM', e.value)) {
              item.license_item_name = e2.material.license_item_name
              item.license_item_code = e2.material.license_item_code
              // console.log('e2.material', e2)
            }
            // this.tableCleanMethodData.content.push(item)
          }
        })
        // this.tableCleanMethodData.content[this.tableCleanMethodData.content.length - rowspan].rowspan = rowspan
      })
      // console.log('this.tableCleanMethodData.content', this.tableCleanMethodData.content)
      // this.item_detail.materialData.forEach(e2 => {
      //   this.item_detail.formRelevance.relevance = e2.formRelevance.relevance + ',' + this.item_detail.formRelevance.relevance
      // })
      // this.item_detail.formRelevance.relevance = this.item_detail.formRelevance.relevance.slice(0, this.item_detail.formRelevance.relevance.length - 1)

      this.unitTypeList = this.dictData.unitTypeList
      this.proofClearTypeList = this.dictData.proofClearTypeList
      // 被await阻塞的同步代码
      if (this.type === 'firstDraft') {
        this.getProofListFirstDrafts()
      } else {
        this.getProofListFormView()
      }
    },
    handleClick(tab, event) {
      // console.log(tab, event);
    },
    getProofListFirstDrafts() {
      getProofListFirstDrafts(this.dataConfig.id, {
        item_clear_status: 'WAIT_FOR_CLEAN'
      }).then(res => {
        this.reorganizeData(res)
      })
    },
    // 请求的接口======zwm
    getProofListFormView() { },
    selectFirmDouble1() {
      // console.log('111111', this.selectList)
      this.formList = []
      this.tableCleanMethodData.content = []
      this.form.relevance = ''
      // this.form.replace_cancel_way = []
      // this.form.replace_cancel_way.push('TURN_LICENSE_OR_OTHER_LICENSE_WAY')
      // console.log('value', this.value)

      if (JSON.stringify(this.selectList) != '{}') {
        this.selectList.forEach((e, index) => {
          console.log('this.selectList', e)
          if (e.proof_catalog_id !== '' && e.proof_catalog_id) {
            getGetproofCatalogFind(e.proof_catalog_id)
              .then(res => {
                const {
                  proof_catalog_license_relation_list,
                  proof_catalog_clerk_commitment_list,
                  proof_catalog_data_shared_list,
                  proof_catalog_artificial_list,
                  proof_catalog_dept_survey_list,
                  proof_catalog_other_relation_list,
                  proof_catalog_license_item_relation
                } = res.data
                let replace_cancel_way = []
                // this.form.proof_list_remark = this.proof_list_remark
                let rowspan = 0
                if (proof_catalog_license_relation_list != null) {
                  rowspan = rowspan + proof_catalog_license_relation_list.length
                }
                if (proof_catalog_clerk_commitment_list != null) {
                  rowspan = rowspan + proof_catalog_clerk_commitment_list.length
                }
                if (proof_catalog_data_shared_list != null) {
                  rowspan = rowspan + proof_catalog_data_shared_list.length
                }
                if (proof_catalog_artificial_list != null) {
                  rowspan = rowspan + proof_catalog_artificial_list.length
                }
                if (proof_catalog_dept_survey_list != null) {
                  rowspan = rowspan + proof_catalog_dept_survey_list.length
                }
                if (proof_catalog_other_relation_list != null) {
                  rowspan = rowspan + proof_catalog_other_relation_list.length
                }
                if (proof_catalog_license_item_relation != null) {
                  rowspan = rowspan + 1
                }

                // console.log('proof_catalog_clerk_commitment_list', proof_catalog_clerk_commitment_list, 'proof_catalog_artificial_list', proof_catalog_artificial_list, res.data)
                // console.log('this.$refs.customComponent1', this.$refs.customComponent1)
                if (proof_catalog_license_relation_list != null && proof_catalog_license_relation_list.length != 0) {
                  proof_catalog_license_relation_list[0].name = res.data.name

                  // proof_catalog_license_relation_list[0].alternativeMethods = '电子证照'
                  proof_catalog_license_relation_list[0].rowspan = rowspan
                  proof_catalog_license_relation_list.forEach(e => {
                    e.alternativeMethods = '电子证照'
                    this.tableCleanMethodData.content.push(e)
                  })
                  // console.log('进来了电子证照', proof_catalog_license_relation_list, this.$refs.customComponent1)
                  // 电子证照
                  let license_description = ''
                  let license_code = []
                  let license_name = []
                  let data = []
                  proof_catalog_license_relation_list.forEach(i => {
                    license_code.push(i.license_code)
                    license_name.push(i.license_name)
                  })
                  license_code = license_code.join(',')
                  license_name = license_name.join(',')
                  license_description = ''
                  data = proof_catalog_license_relation_list.map(i => {
                    return { label: i.license_name, value: i.license_code }
                  })
                  // this.form.replace_license = { license_description, license_code, license_name }
                  replace_cancel_way.push('TURN_LICENSE_OR_OTHER_LICENSE_WAY')
                } else {
                  // this.$refs.customComponent1.form.license = [];
                  // 特殊处理该组件
                  replace_cancel_way = replace_cancel_way.filter(i => i != 'TURN_LICENSE_OR_OTHER_LICENSE_WAY')
                }

                if (proof_catalog_license_item_relation != null && proof_catalog_license_item_relation.length != 0) {
                  proof_catalog_license_item_relation.name = res.data.name

                  proof_catalog_license_item_relation.alternativeMethods = '电子证明'
                  // 保证第一条数据带上rowspan属性 实现表格合并
                  if (proof_catalog_license_relation_list == null || proof_catalog_license_relation_list.length == 0) {
                    proof_catalog_license_item_relation.rowspan = rowspan
                  }
                  this.tableCleanMethodData.content.push(proof_catalog_license_item_relation)
                  // console.log('进来了电子证明', proof_catalog_license_item_relation, this.$refs.customComponent1)
                  // 电子证照
                  let license_description = ''
                  let license_code = ''
                  let license_name = ''
                  license_code = proof_catalog_license_item_relation.license_code
                  license_name = proof_catalog_license_item_relation.license_name
                  license_description = proof_catalog_license_item_relation.license_description
                  // this.form.replace_license_item = { license_description, license_code, license_name }
                  replace_cancel_way.push('TURN_LICENSE_ITEM')
                  // }
                } else {
                  // 特殊处理该组件
                  replace_cancel_way = replace_cancel_way.filter(i => i != 'TURN_LICENSE_ITEM')
                }

                if (proof_catalog_clerk_commitment_list != null && proof_catalog_clerk_commitment_list.length != 0) {
                  proof_catalog_clerk_commitment_list[0].name = res.data.name
                  proof_catalog_clerk_commitment_list[0].alternativeMethods = '承诺书'
                  if ((proof_catalog_license_relation_list == null || proof_catalog_license_relation_list.length == 0) && (proof_catalog_license_item_relation == null || proof_catalog_license_item_relation.length == 0)) {
                    proof_catalog_clerk_commitment_list[0].rowspan = rowspan
                  }

                  this.tableCleanMethodData.content.push(proof_catalog_clerk_commitment_list[0])
                  // console.log('进来了承诺书', proof_catalog_clerk_commitment_list)
                  replace_cancel_way.push('HANDLE_AFFAIRS_PROMISE')
                  // this.form.clerk_commitment.commit_attachment_id = proof_catalog_clerk_commitment_list[0].commit_attachment_id
                  // this.form.clerk_commitment.commit_attachment_name = proof_catalog_clerk_commitment_list[0].commit_attachment_name
                  // this.form.clerk_commitment.commit_book_description = proof_catalog_clerk_commitment_list[0].commit_book_description
                } else {
                  replace_cancel_way = replace_cancel_way.filter(i => i != 'HANDLE_AFFAIRS_PROMISE')
                  // this.form.clerk_commitment.commit_attachment_id = ''
                  // this.form.clerk_commitment.commit_attachment_name = ''
                  // this.form.clerk_commitment.commit_book_description = ''
                }

                if (proof_catalog_data_shared_list != null && proof_catalog_data_shared_list.length != 0) {
                  proof_catalog_data_shared_list[0].name = res.data.name
                  // proof_catalog_data_shared_list[0].alternativeMethods = '自行调查'
                  proof_catalog_data_shared_list[0].alternativeMethods = '数据共享'
                  if ((proof_catalog_clerk_commitment_list == null || proof_catalog_clerk_commitment_list.length == 0) && (proof_catalog_license_relation_list == null || proof_catalog_license_relation_list.length == 0) && (proof_catalog_license_item_relation == null || proof_catalog_license_item_relation.length == 0)) {
                    proof_catalog_data_shared_list[0].rowspan = rowspan
                  }
                  this.tableCleanMethodData.content.push(proof_catalog_data_shared_list[0])
                  // console.log('进来了部门自行调查', proof_catalog_clerk_commitment_list)
                  replace_cancel_way.push('DATA_SHARING')
                  // this.form.replace_data_shared.data_shared_description = proof_catalog_data_shared_list[0].note
                } else {
                  replace_cancel_way = replace_cancel_way.filter(i => i != 'DATA_SHARING')
                  // this.form.replace_data_shared.data_shared_description = ''
                }
                if (proof_catalog_artificial_list != null && proof_catalog_artificial_list.length != 0) {
                  proof_catalog_artificial_list[0].name = res.data.name
                  proof_catalog_artificial_list[0].alternativeMethods = '人工协查'
                  if (
                    (proof_catalog_data_shared_list == null || proof_catalog_data_shared_list.length == 0) &&
                    (proof_catalog_clerk_commitment_list == null || proof_catalog_clerk_commitment_list.length == 0) &&
                    (proof_catalog_license_relation_list == null || proof_catalog_license_relation_list.length == 0) &&
                    (proof_catalog_license_item_relation == null || proof_catalog_license_item_relation.length == 0)
                  ) {
                    proof_catalog_artificial_list[0].rowspan = rowspan
                  }

                  this.tableCleanMethodData.content.push(proof_catalog_artificial_list[0])
                  // console.log('进来了人工协查', proof_catalog_artificial_list)
                  replace_cancel_way.push('ARTIFICIAL_INVESTIGATION')
                  // this.form.replace_investigation.investigation_description = proof_catalog_artificial_list[0].note
                } else {
                  replace_cancel_way = replace_cancel_way.filter(i => i != 'ARTIFICIAL_INVESTIGATION')
                  // this.form.replace_investigation.investigation_description = ''
                }

                if (proof_catalog_dept_survey_list != null && proof_catalog_dept_survey_list.length != 0) {
                  proof_catalog_dept_survey_list[0].name = res.data.name
                  proof_catalog_dept_survey_list[0].alternativeMethods = '自行调查'
                  if (
                    (proof_catalog_artificial_list == null || proof_catalog_artificial_list.length == 0) &&
                    (proof_catalog_data_shared_list == null || proof_catalog_data_shared_list.length == 0) &&
                    (proof_catalog_clerk_commitment_list == null || proof_catalog_clerk_commitment_list.length == 0) &&
                    (proof_catalog_license_relation_list == null || proof_catalog_license_relation_list.length == 0) &&
                    (proof_catalog_license_item_relation == null || proof_catalog_license_item_relation.length == 0)
                  ) {
                    proof_catalog_dept_survey_list[0].rowspan = rowspan
                  }
                  this.tableCleanMethodData.content.push(proof_catalog_dept_survey_list[0])
                  // console.log('进来了部门自行调查', proof_catalog_dept_survey_list)
                  replace_cancel_way.push('DEPARTMENT_INVESTIGATION')
                  // this.form.replace_dept_survey.dept_cancel_description = proof_catalog_dept_survey_list[0].dept_cancel_description
                } else {
                  replace_cancel_way = replace_cancel_way.filter(i => i != 'DEPARTMENT_INVESTIGATION')
                  // this.form.replace_dept_survey.dept_cancel_description = ''
                }

                if (proof_catalog_other_relation_list != null && proof_catalog_other_relation_list.length != 0) {
                  proof_catalog_other_relation_list[0].name = res.data.name
                  proof_catalog_other_relation_list[0].alternativeMethods = '其他'
                  if (
                    (proof_catalog_dept_survey_list == null || proof_catalog_dept_survey_list.length == 0) &&
                    (proof_catalog_artificial_list == null || proof_catalog_artificial_list.length == 0) &&
                    (proof_catalog_data_shared_list == null || proof_catalog_data_shared_list.length == 0) &&
                    (proof_catalog_clerk_commitment_list == null || proof_catalog_clerk_commitment_list.length == 0) &&
                    (proof_catalog_license_relation_list == null || proof_catalog_license_relation_list.length == 0) &&
                    (proof_catalog_license_item_relation == null || proof_catalog_license_item_relation.length == 0)
                  ) {
                    proof_catalog_other_relation_list[0].rowspan = rowspan
                  }
                  this.tableCleanMethodData.content.push(proof_catalog_other_relation_list[0])
                  // console.log('进来了其他', proof_catalog_other_relation_list)
                  replace_cancel_way.push('OTHER_WAY')
                  // this.form.replace_other.other_clear_description = proof_catalog_other_relation_list[0].other_clear_description
                } else {
                  replace_cancel_way = replace_cancel_way.filter(i => i != 'OTHER_WAY')
                  // this.form.replace_other.other_clear_description = ''
                }

                this.form.relevance = res.data.name + ',' + this.form.relevance
                // this.form.relevance = this.form.relevance.slice(0, this.form.relevance.length - 1)
                // this.form.proof_catalog_id = e.proof_catalog_id
                this.replace_cancel_way_status = 1
                this.selectDialog = false
                // this.value = []
                // this.generateData = []
                console.log('this.tableCleanMethodData.content', this.tableCleanMethodData.content)
                console.log('this.form.relevance', this.form.relevance)
                // this.item_detail.formRelevance.relevance = ''
                // this.item_detail.formRelevance.relevance = this.form.relevance
                console.log('this.item_detail.formRelevance.relevance', this.item_detail.formRelevance.relevance)
                this.relevance = this.form.relevance
              })
              .then(() => {
                // console.log('this.tableDirectoryData.content1111', this.tableDirectoryData.content)
              })
              .catch(err => {
                console.log(err)
              })
          }
        })
        this.selectDialog = false
      } else {
        this.$message({
          message: '请选择一条证明目录',
          type: 'warning'
        })
      }
    },
    reorganizeDoulbData(res) {
      const item_material_vo = res.data.item_material_vo
      const material = res.data.proof_list_vo
      this.proof_catalog_vo = res.data.proof_catalog_vo_list[0] || {}
      if (this.proof_catalog_vo) {
        this.formRelevance.relevance = this.proof_catalog_vo.name
        // this.selectForm.proof_catalog_name = this.proof_catalog_vo.name
        this.selectList.id = this.proof_catalog_vo.id
      }

      this.material =
        {
          item_proof_status: material.item_proof_status, // 梳理状态
          // 证明材料清理
          material_name: item_material_vo.material_name,
          law_name: item_material_vo.law_name,
          proof_clear_type: material.proof_clear_type, // 取消类型 DIRECTLY_CANCEL|REPLACE_CANCEL|DO_NOT_CLEAN
          proof_clear_type_text: this.proofClearTypeList.filter(i => i.value === material.proof_clear_type)[0].label || material.proof_clear_type, // 取消类型 DIRECTLY_CANCEL|REPLACE_CANCEL|DO_NOT_CLEAN
          direct_description: material.direct_description, // 直接取消说明,
          not_clean_description: material.not_clean_description, // 无需清理说明,
          proof_list_remark: this.proof_list_remark
        } || {}

      if (material.proof_clear_type === 'REPLACE_CANCEL') {
        const replaceCancelWayList = material.replace_cancel_way.split(',')
        let replaceCancelWayListText = ''
        _.forEach(replaceCancelWayList, i => {
          const way = _.find(this.replaceCancelWayList, j => j.value === i)
          const wayLabel = way ? way.label : material.replace_cancel_way
          replaceCancelWayListText += wayLabel + ','
        })
        console.log('this.material', this.material)
        this.material = Object.assign(this.material, {
          material_id: item_material_vo.id,
          proof_list_id: this.proof_catalog_vo.id,
          item_proof_status: material.item_proof_status, // 梳理状态
          replace_cancel_way: material.replace_cancel_way, // 替代取消方式
          replace_cancel_way_text: replaceCancelWayListText.substring(0, replaceCancelWayListText.length - 1), // 替代取消方式的文本
          license_name: material.license_name, // 电子证照名称，逗号隔开
          license_code: material.license_code, // 电子证照code，逗号隔开
          license_description: material.license_description, // 转换为电子证照说明
          license_item_code: material.license_item_code, // 电子证明code，
          license_item_name: material.license_item_name, // 电子证明名称，
          license_item_description: material.license_item_description, // 转换为电子证明说明
          commit_book_description: material.commit_book_description, // 承诺书说明
          commit_attachment_id: material.commit_attachment_id, // 承诺书说明
          commit_attachment_name: material.commit_attachment_name, // 承诺书说明
          industry_dept_name: material.industry_dept_name, // 所属行业部门名称
          industry_dept_code: material.industry_dept_code, // 所属行业部门代码
          // proof_provide_type_investigation:
          //   material.proof_provide_type_investigation !== null ? this.unitTypeList.find(i => i.value === material.proof_provide_type_investigation).label : material.proof_provide_type_investigation,
          proof_provide_type_dataShared:
            material.proof_provide_type_dataShared !== null ? this.unitTypeList.find(i => i.value === material.proof_provide_type_dataShared).label : material.proof_provide_type_dataShared, // 证明开具单位类型

          data_shared_description: material.data_shared_description, // 数据共享说明
          investigation_description: material.investigation_description, // 人工协查说明
          dept_cancel_description: material.dept_cancel_description, // 自行调查说明 （部门自行调查)
          other_clear_description: material.other_clear_description // 其它说明
        })
      }
      const license_name = material.license_name != null ? material.license_name.split(',') : ''
      const license_code = material.license_code != null ? material.license_code.split(',') : ''
      this.tableData.content = _.map(license_name, function (i, index) {
        return { license_name: i, license_code: license_code[index] }
      })

      this.item_detail.materialData.push({
        proof_catalog_vo: this.proof_catalog_vo,
        formRelevance: { relevance: this.proof_catalog_vo.name },
        // selectForm: { proof_catalog_name: this.proof_catalog_vo.name },
        selectList: { id: this.proof_catalog_vo.id },
        material: this.material,
        tableData: { content: this.tableData.content }
      })
      this.tableCleanMethodData.content = []
      let rowspan
      console.log('this.item_detail.materialData', this.item_detail.materialData)
      this.item_detail.materialData.forEach(e2 => {
        rowspan = 0
        _.cloneDeep(this.replaceCancelWayList).forEach((e, index) => {
          if (this.hasWay(e2.material.replace_cancel_way, e.value)) {
            // console.log('e.label', e.label)
            // e.relevance = e2.formRelevance.relevance
            // if(this.hasWay('TURN_LICENSE_OR_OTHER_LICENSE_WAY', e.value)){
            //   e.license_name = e2.material.license_name
            // }

            const item = {
              label: e.label,
              relevance: e2.formRelevance.relevance,
              license_name: ''
            }
            rowspan = rowspan + 1
            if (this.hasWay('TURN_LICENSE_OR_OTHER_LICENSE_WAY', e.value)) {
              item.license_name = e2.material.license_name
              item.license_code = e2.material.license_code
              // console.log('e2.material', e2)
            }
            if (this.hasWay('TURN_LICENSE_ITEM', e.value)) {
              item.license_item_name = e2.material.license_item_name
              item.license_item_code = e2.material.license_item_code
              // console.log('e2.material', e2)
            }
            this.tableCleanMethodData.content.push(item)
          }
        })
        this.tableCleanMethodData.content[this.tableCleanMethodData.content.length - rowspan].rowspan = rowspan
      })
      // console.log('this.tableCleanMethodData.content', 'rowspan', rowspan, this.tableCleanMethodData.content)
      this.item_detail.formRelevance.relevance = ''
      this.item_detail.materialData.forEach(e2 => {
        this.item_detail.formRelevance.relevance = e2.formRelevance.relevance + ',' + this.item_detail.formRelevance.relevance
      })
      this.item_detail.formRelevance.relevance = this.item_detail.formRelevance.relevance.slice(0, this.item_detail.formRelevance.relevance.length - 1)
      return {
        proof_catalog_vo: this.proof_catalog_vo,
        formRelevance: { relevance: this.proof_catalog_vo.name },
        // selectForm: { proof_catalog_name: this.proof_catalog_vo.name },
        selectList: { id: this.proof_catalog_vo.id },
        material: this.material,
        tableData: { content: this.tableData.content }
      }
    },
    reorganizeData(res) {
      const item_material_vo = res.data.item_material_vo
      const material = res.data.proof_list_vo
      this.proof_catalog_vo = res.data.proof_catalog_vo_list[0] || {}
      if (this.proof_catalog_vo) {
        this.formRelevance.relevance = this.proof_catalog_vo.name
        // this.selectForm.proof_catalog_name = this.proof_catalog_vo.name
        this.selectList.id = this.proof_catalog_vo.id
      }

      this.material =
        {
          item_proof_status: material.item_proof_status, // 梳理状态
          // 证明材料清理
          material_name: item_material_vo.material_name,
          law_name: item_material_vo.law_name,
          proof_clear_type: material.proof_clear_type, // 取消类型 DIRECTLY_CANCEL|REPLACE_CANCEL|DO_NOT_CLEAN
          proof_clear_type_text: this.proofClearTypeList.filter(i => i.value === material.proof_clear_type)[0].label || material.proof_clear_type, // 取消类型 DIRECTLY_CANCEL|REPLACE_CANCEL|DO_NOT_CLEAN
          direct_description: material.direct_description, // 直接取消说明,
          not_clean_description: material.not_clean_description // 无需清理说明,
        } || {}

      if (material.proof_clear_type === 'REPLACE_CANCEL') {
        const replaceCancelWayList = material.replace_cancel_way.split(',')
        let replaceCancelWayListText = ''
        _.forEach(replaceCancelWayList, i => {
          const way = _.find(this.replaceCancelWayList, j => j.value === i)
          const wayLabel = way ? way.label : material.replace_cancel_way
          replaceCancelWayListText += wayLabel + ','
        })
        console.log('this.material', this.material)
        this.material = Object.assign(this.material, {
          proof_list_id: material.proof_list_id,
          item_proof_status: material.item_proof_status, // 梳理状态
          replace_cancel_way: material.replace_cancel_way, // 替代取消方式
          replace_cancel_way_text: replaceCancelWayListText.substring(0, replaceCancelWayListText.length - 1), // 替代取消方式的文本
          license_name: material.license_name, // 电子证照名称，逗号隔开
          license_code: material.code, // 电子证照code，逗号隔开
          license_description: material.license_description, // 转换为电子证照说明
          commit_book_description: material.commit_book_description, // 承诺书说明
          commit_attachment_id: material.commit_attachment_id, // 承诺书说明
          commit_attachment_name: material.commit_attachment_name, // 承诺书说明
          industry_dept_name: material.industry_dept_name, // 所属行业部门名称
          industry_dept_code: material.industry_dept_code, // 所属行业部门代码
          proof_provide_type_investigation:
            material.proof_provide_type_investigation !== null ? this.unitTypeList.find(i => i.value === material.proof_provide_type_investigation).label : material.proof_provide_type_investigation,
          proof_provide_type_dataShared:
            material.proof_provide_type_dataShared !== null ? this.unitTypeList.find(i => i.value === material.proof_provide_type_dataShared).label : material.proof_provide_type_dataShared, // 证明开具单位类型

          data_shared_description: material.data_shared_description, // 数据共享说明
          investigation_description: material.investigation_description, // 人工协查说明
          dept_cancel_description: material.dept_cancel_description, // 自行调查说明 （部门自行调查)
          other_clear_description: material.other_clear_description // 其它说明
        })
      }
      const license_name = material.license_name != null ? material.license_name.split(',') : ''
      const license_code = material.license_code != null ? material.license_code.split(',') : ''
      this.tableData.content = _.map(license_name, function (i, index) {
        return { license_name: i, license_code: license_code[index] }
      })
      this.tableCleanMethodData.content = []
      _.cloneDeep(this.replaceCancelWayList).forEach(e => {
        if (this.hasWay(this.material.replace_cancel_way, e.value)) {
          e.relevance = this.proof_catalog_vo.name
          this.tableCleanMethodData.content.push(e)
        }
      })

      return {
        proof_catalog_vo: this.proof_catalog_vo,
        formRelevance: { relevance: this.proof_catalog_vo.name },
        // selectForm: { proof_catalog_name: this.proof_catalog_vo.name },
        selectList: { id: this.proof_catalog_vo.id },
        material: this.material,
        tableData: { content: this.tableData.content }
      }
    },

    hasWay(arr, val) {
      return arr.indexOf(val) !== -1
    },
    show(row) {
      goLicenseItemView(row.license_code).then(res => {
        window.open(res.data.url, '_blank')
      })
    },
    backPrev() {
      this.$router.go(-1)
    },
    preliminaryDrafts(proof_list_id) {
      const route = this.$router.resolve({
        name: this.proofRoute,
        query: {
          id: proof_list_id
        }
      })
      window.open(route.href, '_blank')
    },
    goEditClearWay() {
      // 只有清单和梳理，证明状态为待梳理的情况下可以编辑修改证明
      const name = this.type === 'list' ? 'item_list_edit' : 'item_carding_edit'
      this.$router.push({
        name: name,
        query: {
          id: this.dataConfig.id
        }
      })
    },

    examineApprove() {
      const _this = this
      _this.catalogReplaceInfoList = []
      this.$refs['formRelevance'].validate(valid => {
        if (valid) {
          const proof_list_id_list = []
          const proof_catalog_id_list = []
          console.log('this.tableCleanMethodData.content', this.tableCleanMethodData.content)
          console.log('_this.item_detail', _this.item_detail)
          console.log('this.selectList', this.selectList)
          this.selectList.forEach(e => {
            const item = {
              item_material_id: _this.item_detail.materialData[0].material.material_id,
              proof_catalog_id: e.proof_catalog_id,
              proof_list_id: e.proof_list_id ? e.proof_list_id : '',
              proof_list_remark: _this.item_detail.materialData[0].material.proof_list_remark
            }
            _this.catalogReplaceInfoList.push(item)
          })

          // _this.item_detail.materialData.forEach(e => {
          //   proof_list_id_list.push(e.material.proof_list_id)
          //   proof_catalog_id_list.push(e.proof_catalog_vo.id)
          //   let item = {
          //     proof_list_id: e.material.proof_list_id,
          //     item_material_id: e.material.material_id,
          //     proof_catalog_id: e.proof_catalog_vo.id,
          //     proof_list_remark: e.material.proof_list_remark
          //   }
          //   _this.catalogReplaceInfoList.push(item)
          // })

          _this.catalogReplaceInfo = {
            proof_list_id: proof_list_id_list,
            // proof_catalog_id: _this.selectList.id
            proof_catalog_id: proof_catalog_id_list
            // confirm_status: "CARDING_UNCONFIRMED",
            // tease_account_name: _this.name
          }

          // this.$confirm("确定梳理完成?", "提示", {
          //   confirmButtonText: "确定",
          //   cancelButtonText: "取消",
          //   type: "warning"
          // }).then(() => {
          //   if (
          //     JSON.stringify(this.selectList) != "{}" ||
          //     _this.material.proof_clear_type === "DIRECTLY_CANCEL"
          //   ) {
          //     // proofListConfirmCreate({
          //     //   proof_list_id: _this.dataConfig.id,
          //     //   proof_catalog_id: _this.selectList.id,
          //     //   confirm_status: "CARDING_UNCONFIRMED",
          //     //   tease_account_name: this.name
          //     // }).then(res => {
          //     //   let type = res.meta.code === "200" ? "success" : "warning";
          //     //   this.$message({
          //     //     type: type,
          //     //     message: "操作成功"
          //     //   });
          //     //   if (type === "success") this.$router.go(-1);
          //     // });
          //   } else {
          //     this.$message({
          //       message: "请选择一条证明目录",
          //       type: "warning"
          //     });
          //   }
          // });
        } else {
          console.log('error submit!!')
          _this.catalogReplaceInfo = {
            proof_list_id: _this.item_detail.material.proof_list_id,
            proof_catalog_id: ''
          }
          return false
        }
      })
    },
    getvalidateForm() {
      let bo
      this.$refs['formRelevance'].validate(valid => {
        bo = valid
      })
      if (!bo) {
        return this.$refs['formRelevance']
      } else {
        return false
      }
    },
    submitAuditForm() {
      this.$refs['auditForm'].validate(valid => {
        if (valid) {
          this.$confirm('确定提交审核结果?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            const sendData = {
              proof_list_id: this.dataConfig.id,
              operate_time: '',
              audit_description: this.auditForm.remark,
              audit_operator: this.name,
              audit_operator_account: this.account,
              operator_type: this.auditForm.audit,
              account_dept_code: '',
              account_dept_name: this.dept
            }
            proofAuditListExamine(sendData).then(res => {
              const type = res.meta.code === '200' ? 'success' : 'warning'
              this.$message({
                type: type,
                message: res.meta.msg
              })
              if (type === 'success') this.$router.go(-1)
            })
          })
        }
      })
    },
    getAttachmentBase64() {
      if (this.type === 'firstDraft') {
        getPreliminaryDraftsAttachment({
          proof_list_id: this.item_detail.material.proof_list_id
        }).then(res => {
          if (res.meta.code === '200') {
            dataURLtoDownload(res.data, this.item_detail.material.commit_attachment_name)
          }
        })
      } else {
        getAttachmentBase64({ proof_list_id: this.item_detail.material.proof_list_id }).then(res => {
          if (res.meta.code === '200') {
            dataURLtoDownload(res.data, this.item_detail.material.commit_attachment_name)
          }
        })
      }
    },
    // 关联电子证照
    selectQuery(data) {
      this.$refs['selectForm'].validate(valid => {
        if (valid) {
          this.query(1, 'search', data)
        } else {
          return false
        }
      })
    },
    query(currentPage, type, data) {
      if (_.isNumber(currentPage)) {
        this.tableDataSelect.currentPage = currentPage
      }
      const formData = Object.assign({}, this.selectForm)
      const sendData = {
        ...formData,
        page_size: this.tableDataSelect.pageSize,
        page_number: this.tableDataSelect.currentPage,
        page_direction: this.tableDataSelect.pageDirection
      }
      Object.keys(sendData).forEach(item => {
        if (!sendData[item]) delete sendData[item]
      })
      getGetproofCatalogPage(sendData)
        .then(res => {
          const data = res.data === null ? [] : res.data.content
          this.tableDataSelect.content = res.data.content
          this.tableDataSelect.total = res.data.totalElements
          let selectListArray = []
          console.log('this.getSelectList', this.getSelectList)
          // this.getSelectList.forEach(i => {
          //   if (i.proof_catalog_vo.id != null) {
          //     selectListArray.push({ label: i.proof_catalog_vo.name, key: i.proof_catalog_vo.id, pinyin: i.proof_catalog_vo.name, proof_catalog_id: i.proof_catalog_vo.id })
          //   }
          // })
          if (this.getSelectList != undefined) {
            selectListArray = this.getSelectList
          }
          console.log('selectListArray', selectListArray)
          this.tableDataSelect.content.forEach(e => {
            this.generateData.push({ label: e.name, key: e.id, pinyin: e.name, proof_catalog_id: e.id })
          })
          selectListArray.forEach(e0 => {
            this.generateData.push(e0)
          })
          this.generateData = _.uniqBy(this.generateData, 'key')
          console.log('this.generateData', this.generateData, 'generateDataIndex', this.generateDataIndex)
          console.log('this.multipleData', this.multipleData)
        })
        .catch(() => { })
    },
    selectEvent() {
      this.query(1)
      /* if(this.selectForm.proof_catalog_name){
        this.query(1, "search", this.selectForm.proof_catalog_name);
      }else{
        this.query(1)
      }*/
      this.selectDialog = true
      console.log('this.multipleData', this.multipleData)
    },
    selectionChange(val1) {
      console.log('selectionChange', val1, this.generateData)
      this.getSelectList = []
      this.selectList = []
      val1.forEach(i => {
        const item = {
          proof_catalog_id: i
        }
        this.getSelectList.push(this.generateData.filter(item => item.key === i)[0])
        this.selectList.push(item)
        // i.relevance = i.name
      })
    },
    // selectionChange(val) {
    //   this.selectList = val
    // },
    selectFirmDouble() {
      // this.item_detail = this.multipleData

      this.item_detail.formRelevance.relevance = ''
      console.log('this.item_detail', this.item_detail)
      this.proof_list_remark = this.item_detail.materialData[0].material.proof_list_remark
      console.log('proof_list_remark', this.proof_list_remark)
      this.item_detail.materialData = []
      if (JSON.stringify(this.selectList) != '{}') {
        this.selectList.forEach(e => {
          getCatalogReplace({
            // proof_list_id: this.proof_list_id,
            item_material_id: this.material_id,
            proof_catalog_id: e.proof_catalog_id
          }).then(res => {
            this.reorganizeDoulbData(res)
            // this.item_detail = this.reorganizeDoulbData(res)
            this.formRelevance.relevance = this.selectList.name
            this.selectDialog = false
            // this.value = []
            this.generateData = []
            this.$refs['formRelevance'].clearValidate()
          })
        })
      } else {
        this.$message({
          message: '请选择一条证明目录',
          type: 'warning'
        })
      }
    },
    selectFirm() {
      if (JSON.stringify(this.selectList) != '{}') {
        getCatalogReplace({
          proof_list_id: this.item_detail.material.proof_list_id,
          proof_catalog_id: this.selectList.id
        }).then(res => {
          this.item_detail = this.reorganizeData(res)
          this.formRelevance.relevance = this.selectList.name
          this.selectDialog = false
          this.$refs['formRelevance'].clearValidate()
        })
      } else {
        this.$message({
          message: '请选择一条证明目录',
          type: 'warning'
        })
      }
    },
    // 查看
    selectLook(row) {
      this.$router.push({
        name: 'certification_List_info',
        query: {
          id: row.id
        }
      })
    },
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      // console.log(row, column)
      if (columnIndex == 0) {
        // console.log(row.rowspan)
        if (row.rowspan !== undefined) {
          return {
            rowspan: row.rowspan,
            colspan: 1
          }
        } else {
          return {
            rowspan: 0,
            colspan: 0
          }
        }
      }
    },
    goLicenseItemView(row) {
      goLicenseItemView(row.license_code).then(res => {
        window.open(res.data.url, '_blank')
      })
    },
    screenWidth() {
      if (screen.width == 1920) {
        this.is1366 = false
        console.log('1920*1080')
      } else if (screen.width == 1366) {
        console.log('1366*768')
        this.is1366 = true
      } else {
        this.is1366 = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.customTable ::v-deep .el-card__body {
  padding-top: 10px;
  padding-left: 0;
  padding-right: 0;
}

.time {
  color: #919191;
}

.status {
  color: #2d76ce;
  font-size: 19px;
  font-weight: 900;
}

.box-card {
  margin-bottom: 20px;
}

.fixed-bottom {
  width: 100%;
  transform: scale3d(1, 1, 1);

  .btn-group {
    background: rgba(255, 255, 255, 0.95);
    border-top: 1px solid #096dd9;
    padding: 20px 10px 10px 50px;
    // position: absolute;
    bottom: 0;
    right: 0;
    z-index: 100;
    text-align: right;
    height: 80px;
    width: inherit;
  }
}

.formRelevance {
  position: relative;

  .select {
    cursor: pointer;
    position: absolute;
    top: 15%;
    right: 3%;

    button {
      padding: 0;
    }

    a {
      color: #409eff;
    }
  }
}

.selectForm {
  position: relative;

  .catalogBtn {
    position: absolute;
    right: -80px;
    top: -1%;
  }
}
</style>
<style lang="scss" scoped>
// ::v-deep >>> .el-radio {
//   margin-left: 6px;
// }
/*>>> .el-radio .el-radio__label {
  display: none;
}*/
.dialog-footer {
  text-align: center;
}

.transfer ::v-deep .el-button--primary.is-disabled {
  display: block;
}

.transfer ::v-deep .el-button+.el-button {
  margin-left: 0;
}

.transfer ::v-deep .el-transfer-panel__header .el-checkbox__input {
  display: none;
}

.transfer ::v-deep .el-transfer-panel {
  width: 278px;
}

.transfer ::v-deep .el-transfer-panel__body {
  width: 264px;
  // height: 214px;
}

.transfer ::v-deep .el-transfer-panel .el-transfer-panel__footer {
  top: 50px;
  bottom: 0;
  width: 0%;
  left: initial;
  right: 251px;
  display: inline-block;
}

.transfer-input {
  display: flex;
  width: 230px;

  .transfer-footer {
    margin-left: 5px;
  }
}

.transfer ::v-deep .el-transfer-panel:first-child .el-transfer-panel__filter {
  width: 75%;
  height: 30px;
}

.transfer ::v-deep .el-transfer-panel:first-child .el-transfer-panel__filter input {
  display: none;
}

.transfer ::v-deep .el-transfer-panel__filter .el-input__icon {
  display: none;
}

.transfer ::v-deep .el-transfer-panel .el-transfer-panel__header .el-checkbox .el-checkbox__label span {
  display: none;
}

.transfer ::v-deep .el-transfer-panel__list {
  overflow-y: scroll;
}

.info-wrap {
  display: flex;
  align-items: center;
  margin-bottom: 10px;

  img {
    margin-right: 10px;
  }

  .info-title {
    font-size: 20px;
    color: #333333;
  }
}

.margin-left-10 {
  margin-left: 10px;
}

.descriptions {
  margin-top: 10px;
  padding: 0 10px;
}
</style>
