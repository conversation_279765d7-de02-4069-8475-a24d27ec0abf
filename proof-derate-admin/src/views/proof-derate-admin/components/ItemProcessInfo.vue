<template>
  <div>
    <div v-for="(item,index) in proofRecordList" :key="index">
      <!--提交记录-->
      <!-- {{proofRecordList0}} -->
      <!-- <el-row v-if="item.hasOwnProperty('submit_date')">
        <el-col :span="2">
          <el-row>&nbsp;</el-row>
          <el-row>
            <el-col>
              <div style="text-align:center;">
                {{item['date']}}
                <br />
                <span class="time">{{item['minute']}}</span>
              </div>
            </el-col>
          </el-row>
          <el-row>&nbsp;</el-row>
        </el-col>
        <el-col :span="22">
          <el-card>
            <el-row>
              <el-col :span="3">
                <span class="status">提交申请</span>
              </el-col>
              <el-col :span="8">经办人: {{item['account_name']}}</el-col>
              <el-col :span="9" :offset="2">实施机构：{{item['account_dept_name']}}</el-col>
            </el-row>
            <el-row>
              <el-col :span="8" :offset="3">提交时间: {{item['time']}}</el-col>
              <el-col :span="9" :offset="2" v-if="index ===0">
                <el-button type="text" @click="preliminaryDrafts">查看清理初稿</el-button>
              </el-col>
            </el-row>
          </el-card>
        </el-col>
      </el-row>-->
      <!--梳理记录-->
      <!-- <el-row v-else-if="item.confirm_status">
        <el-col :span="2">
          <el-row>&nbsp;</el-row>
          <el-row>
            <el-col>
              <div style="text-align:center;">
                {{item['date']}}
                <br />
                <span class="time">{{item['minute']}}</span>
              </div>
            </el-col>
          </el-row>
          <el-row>&nbsp;</el-row>
        </el-col>
        <el-col :span="22">
          <el-card>
            <el-row>
              <el-col :span="3">
                <span class="status">梳理</span>
              </el-col>
              <el-col :span="8">经办人: {{item['tease_account_name']}}</el-col>
              <el-col :span="9" :offset="2">确认状态: {{transferDictToLabel(item['confirm_status'])}}</el-col>
            </el-row>
            <el-row>
              <el-col :span="8" :offset="3">确认时间: {{item['time']}}</el-col>
            </el-row>
          </el-card>
        </el-col>
      </el-row>-->
      <!--审核记录-->
      <!-- <el-row v-else-if="item.operator_type">
        <el-col :span="2">
          <el-row>&nbsp;</el-row>
          <el-row>
            <el-col>
              <div style="text-align:center;">
                {{item['date']}}
                <br />
                <span class="time">{{item['minute']}}</span>
              </div>
            </el-col>
          </el-row>
          <el-row>&nbsp;</el-row>
        </el-col>
        <el-col :span="22">
          <el-card>
            <el-row>
              <el-col :span="3">
                <span class="status">审核</span>
              </el-col>
              <el-col :span="8">经办人: {{item['audit_operator']}}</el-col>
              <el-col :span="9" :offset="2">实施机构：{{item['account_dept_name']}}</el-col>
            </el-row>
            <el-row>
              <el-col :span="8" :offset="3">审核时间: {{item['time']}}</el-col>
              <el-col :span="9" :offset="2">审核状态: {{transferDictToLabel(item['operator_type'])}}</el-col>
            </el-row>
            <el-row>
              <el-col :span="8" :offset="3">意见内容: {{item['audit_description']}}</el-col>
            </el-row>
          </el-card>
        </el-col>
      </el-row>-->

      <el-row>
        <!-- <el-col :span="2">
          <el-row>&nbsp;</el-row>
          <el-row>
            <el-col>
              <div style="text-align:center;">
                {{item['date']}}
                <br />
                <span class="time">{{item['minute']}}</span>
              </div>
            </el-col>
          </el-row>
          <el-row>&nbsp;</el-row>
        </el-col>-->
        <el-col :span="24">
          <el-card>
            <el-row type="flex" align="middle">
              <el-col :span="3">
                <!-- <span class="status" v-if="item['operation'] ==='待梳理确认'">提交申请</span>
                <span class="status" v-else-if="item['operation'] ==='仍需清理'">驳回清理</span>-->
                <span class="status">{{item['operation']}}</span>
              </el-col>
              <el-col :span="5">经办人: {{item['account_name']}}</el-col>
              <el-col :span="9" :offset="2">经办账号：{{item['account']}}</el-col>
              <el-col :span="2" :offset="2" v-if="item['operation'] ==='待梳理确认'||item['operation'] ==='已完成'">
                <!-- 查看清理初稿 -->
                <el-button type="text" @click="preliminaryDrafts">查看清理初稿</el-button>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="5" :offset="3">提交时间: {{item['time']}}</el-col>
              <el-col :span="9" :offset="2">说明: {{item['procedure_message']==''?'无':item['procedure_message']}}</el-col>
              <el-col :span="2" :offset="2" v-if="index ===0"></el-col>
              <!-- <el-col :span="9" :offset="2" v-if="index ===0">
                <el-button type="text" @click="preliminaryDrafts">查看清理初稿</el-button>
              </el-col>-->
            </el-row>
            <!-- <el-row>
              <el-col :span="1" :offset="2" v-if="index ===0">
                <el-button type="text" @click="preliminaryDrafts">查看清理初稿</el-button>
              </el-col>
            </el-row>-->
          </el-card>
        </el-col>
      </el-row>
      <br />
    </div>
  </div>
</template>

<script>
import { getProofSubmitInfoByCode, getProofConfirmInfoByCode, getProofAuditInfoByCode, getProcedureLog } from '@/api/itemBiz/list'
import moment from 'moment'

export default {
  name: 'ItemProcessInfo',
  props: {
    itemCode: {
      type: String,
      default: ''
    },
    dictData: {
      Object,
      default: function () {
        return {}
      }
    },
    proofRoute: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      dataConfig: {
        id: '',
        title: '事项证明档案',
        activeName: 'desc'
      },
      tipInfo: {
        needShowTips: false,
        itemTips: ''
      },
      proofRecordList: [],
      proofRecordList0: null,
      proofRecordList1: null,
      proofRecordList2: null,
      proofStatusList: [] // 事项状态
    }
  },

  watch: {
    tipInfo: {
      handler(val) {
        if (!val) return
        // 把父组件传过来的tableData赋值给tableConfig
        // this.$emit("getTips",val)
      },
      deep: true,
      immediate: true
    }
  },

  mounted() {
    this.initData()
  },
  methods: {
    initData: function () {
      this.proofStatusList = this.dictData.proofStatusList
      this.getProofRecordInfo()
    },
    async getProofRecordInfo() {
      let sendData = { item_code: this.itemCode }
      // await getProofSubmitInfoByCode(sendData).then(res => {
      //   let data = res.data || [];
      //   this.proofRecordList = data;
      // });
      // await getProofConfirmInfoByCode(sendData).then(res => {
      //   let data = res.data || [];
      //   this.proofRecordList = this.proofRecordList.concat(data);
      //   if (data.length > 0 && data[data.length-1]["confirm_status"] === "WAIT_FOR_CLEAN") {
      //     this.tipInfo.needShowTips = true;
      //     this.tipInfo.itemTips =
      //       "此事项清理在“梳理环节”被驳回，请完善后重新提交";
      //     this.$emit("getTips", this.tipInfo);
      //   }
      // });
      // await getProofAuditInfoByCode(sendData).then(res => {
      //   let data = res.data || [];
      //   this.proofRecordList = this.proofRecordList.concat(data);
      //   if (data.length > 0 && data[data.length-1]["operator_type"] === "UNAPPROVED") {
      //     this.tipInfo.needShowTips = true;
      //     this.tipInfo.itemTips =
      //       "此事项清理在“审核环节”被驳回，请完善后重新提交";
      //     this.$emit("getTips", this.tipInfo);
      //   }
      // });
      await getProcedureLog(this.itemCode).then(res => {
        let data = res.data || []
        if (data.length !== 0) {
          this.proofRecordList = data
          // console.log('this.proofRecordList',this.proofRecordList)
          if (this.proofRecordList[0].operation === '仍需清理') {
            this.tipInfo.needShowTips = true
            this.tipInfo.itemTips = '此事项清理在“梳理环节”被驳回，请完善后重新提交'
            this.$emit('getTips', this.tipInfo)
          } else if (this.proofRecordList[0].operation === '审核不通过') {
            this.tipInfo.needShowTips = true
            this.tipInfo.itemTips = '此事项清理在“审核环节”被驳回，请完善后重新提交'
            this.$emit('getTips', this.tipInfo)
          }
        }
      })
      this.proofRecordList = this.proofRecordList.sort().map(i => {
        return { ...this.formatRecordInfo(i) }
      })
      this.proofRecordList = this.arraySort(this.proofRecordList, 'created_date')
      // console.log('this.proofRecordList',this.proofRecordList)
    },
    transferDictToLabel(val) {
      let result = this.proofStatusList.filter(i => i.value === val)
      return result.length > 0 ? result[0].label : val
    },
    formatRecordInfo(data) {
      return {
        ...data,
        // confirm_status,
        // operator_type,
        date: moment(data.created_date).format('MM.DD'),
        minute: moment(data.created_date).format('HH:mm'),
        time: moment(data.created_date).format('YYYY-MM-DD HH:mm')
      }
    },
    //传入一个需要排序的数组
    arraySort(obj, key) {
      obj.sort((a, b) => {
        let t1 = new Date(Date.parse(a[key].replace(/-/g, '/')))
        let t2 = new Date(Date.parse(b[key].replace(/-/g, '/')))
        return t1.getTime() - t2.getTime()
      })

      return obj
    },
    preliminaryDrafts() {
      let navKey = this.$route.meta.nav_key
      const route = this.$router.resolve({
        name: navKey + '_info_first_draft',
        query: {
          id: this.itemCode
        }
      })
      window.open(route.href, '_blank')
    }
  }
}
</script>

<style lang="scss" scoped>
$skinColor: #01a463;
.time {
  color: #919191;
}
.status {
  color: $skinColor;
  font-size: 19px;
  font-weight: 900;
  position: absolute;
  left: 10px;
}
</style>
