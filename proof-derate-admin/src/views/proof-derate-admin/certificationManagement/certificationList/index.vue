<template>
  <div class="content-wrapper certificationList padding-10">
    <!-- <papeTitle :title-name="titleName" :is-has-back="false">
      <span>
        <el-button type="primary" icon="el-icon-plus" v-permission="'catalog:proof_catalog:proof:add'" @click="goProofCatalog">新建</el-button>
      </span>
    </papeTitle>-->
    <CardTitle :title-name="titleName">
      <template>
        <el-button type="primary" icon="el-icon-plus" v-permission="'catalog:proof_catalog:proof:add'" @click="goProofCatalog">新建</el-button>
      </template>
    </CardTitle>
    <!-- <section class="content"> -->
    <el-card class="box-card" shadow="never">
      <query-form ref="queryForm" style="padding:0 10px" @click="search" />
      <div style="color: #888; padding:20px 10px 0" class="dashed-line">
        共
        <span class="text-red">{{ tableData.total }}</span> 条符合查询条件
      </div>
      <custom-table
        ref="table"
        :is-card-type="false"
        :table-data="tableData"
        :table-header="tableHeader"
        :stripe="false"
        :table-tools="tableTools"
        style="margin-top: 10px"
        @query="query"
        @refresh="query(1)"
      >
        <template #operate="{row,$index}">
          <div>
            <el-button type="text" v-permission="'catalog:proof_catalog:proof:view'" @click="goItemLook(row,$index)">查看</el-button>
          </div>
        </template>
      </custom-table>
    </el-card>
    <!-- </section> -->
    <el-dialog title="证明目录导入" :visible.sync="dialogCertificationList" width="50%" center>
      <uploadDialog ref="uploadDialog" @toFather="download" />

      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="importFlie">导入</el-button>
        <el-button @click="dialogCertificationList = false">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import QueryForm from '@/views/proof-derate-admin/certificationManagement/certificationList/components/QueryForm'
import CustomTable from '@/components/Element/Table'
import uploadDialog from '@/views/proof-derate-admin/components/uploadDialog'
import { hasDataPermission } from '@/utils/index'
import Enum from '@/utils/enum'
import { getGetproofCatalogPage, getClean_Commit_Attachment } from '@/api/certificationManagement/certificationList'
import papeTitle from '@/components/papeTitle'
import CardTitle from '@/components/CardTitle'
export default {
  name: 'CertificationList',
  components: {
    QueryForm,
    CustomTable,
    uploadDialog,
    papeTitle,
    CardTitle
  },
  data() {
    return {
      tableData: {
        border: false,
        content: [], // 表格数据
        loading: false, // 控制表格加载效果
        total: 0, // 分页查询时的总数，去掉该属性，不显示分页条
        currentPage: 1, // 当前页码
        pageSize: 10,
        pageDirection: 'desc',
        isShowSelection: false, // 是否显示多选框，默认false
        isShowIndex: true
      },
      tableHeader: [
        // { label: "序号", prop: "index", minWidth: "80px" },
        { label: '证明目录管理', prop: 'name', minWidth: '160px', align: 'left' },
        { label: '证明目录编码', prop: 'code', minWidth: '160px', align: 'left' },
        {
          label: '证明开具单位类型',
          prop: 'unit_type',
          minWidth: '160px',
          align: 'left',
          formatter: (row, col, val) => {
            return val === null || val === undefined ? '' : Enum.unitTypeList.find(i => i.value === val).label
          }
        },
        {
          label: '操作',
          slot: 'operate',
          prop: 'operate',
          minWidth: '50px',
          fixed: 'right',
          align: 'left'
        }
      ],
      tableTools: [],
      dialogCertificationList: false,
      unitTypeList: [],
      titleName: '证明目录'
    }
  },
  mounted() {
    this.search()
    // this.clearEession()
  },
  methods: {
    // 承诺书文件session清除
    clearEession() {
      getClean_Commit_Attachment()
        .then(res => {})
        .catch(() => {})
    },
    // 事项类型
    getMattersTypeList(res) {
      const data = res.data || []
      Enum.unitTypeList.splice(0)
      Enum.unitTypeList.push(...data)
      const item_type = [] // 事项标准类型
      _.forEach(Enum.unitTypeList, item => {
        item_type.push(item.value)
      })
      this.unitTypeList = item_type
    },
    // objectSpanMethod({ row, column, rowIndex, columnIndex }) {
    //   const span = column["property"] + "-span";
    //   if (row[span]) {
    //     return row[span];
    //   }
    // },
    search(data) {
      this.query(1, 'search', data)
    },
    query(currentPage, type, data) {
      // if (_.isNumber(currentPage)) {
      //   this.tableData.currentPage = currentPage
      // }
      const formData = Object.assign({}, this.$refs['queryForm'].form)
      const sendData = {
        ...formData,
        page_size: this.tableData.pageSize,
        page_number: this.tableData.currentPage,
        page_direction: this.tableData.pageDirection
      }
      Object.keys(sendData).forEach(item => {
        if (!sendData[item]) delete sendData[item]
      })
      getGetproofCatalogPage(sendData)
        .then(res => {
          const data = res.data === null ? [] : res.data.content
          this.tableData.content = res.data.content
          this.tableData.total = Number(res.data.total_elements)
        })
        .catch(() => {})
    },
    // 证明目录详情
    goItemLook(row) {
      this.$router.push({
        name: 'certification_List_info',
        query: {
          id: row.id
        }
      })
    },
    // // 跳转到证明档案
    // goProofFile(row) {
    //   this.$router.push({
    //     name: "item_carding_info_proof",
    //     query: {
    //       id: row.item_code,
    //       item_clear_status: "CLEAN"
    //     }
    //   });
    // },
    // 新增证明目录页面
    goProofCatalog() {
      this.$router.push({
        name: 'certification_List_add',
        query: {
          flagCatalog: 'add'
        }
      })
    },
    importData() {
      this.dialogCertificationList = true
    },
    hasDataPermission(value, permission_code) {
      return hasDataPermission(value, permission_code)
    },
    // 下载模板
    download(str) {
      if (str) {
        down_import_template()
          .then(res => {
            var raw = window.atob(res.data)
            var uInt8Array = new Uint8Array(raw.length)
            for (var i = 0; i < raw.length; i++) {
              uInt8Array[i] = raw.charCodeAt(i)
            }
            const link = document.createElement('a')
            const blob = new Blob([uInt8Array], {
              type: 'application/vnd.ms-excel'
            })
            link.style.display = 'none'
            link.href = URL.createObjectURL(blob)
            link.setAttribute('download', '证明材料模板.xlsx')
            document.body.appendChild(link)
            link.click()
            document.body.removeChild(link)
            this.$message({
              message: '下载模板成功',
              type: 'warning'
            })
          })
          .catch(() => {
            this.$message({
              message: '下载模板失败',
              type: 'warning'
            })
          })
      }
    },
    // 导入证明材料
    importFlie() {
      if (this.$refs.uploadDialog.input) {
        const name = this.$refs.uploadDialog.input
        const file = this.$refs.uploadDialog.file.raw
        const fd = new FormData()
        fd.append('file', file)
        fd.append('name', name)
        commit_attachment(fd)
          .then(res => {
            this.$message({
              message: res.data,
              type: 'success'
            })
            this.dialogCertificationList = false
          })
          .catch(() => {})
      } else {
        this.$message({
          message: '请选择文件，再导入！',
          type: 'warning'
        })
      }
    }
  }
}
</script>
<style>
.certificationList {
  /* padding: 10px; */
}
</style>
<style scoped>
.breadcrumbtn {
  top: 4px;
}
</style>
