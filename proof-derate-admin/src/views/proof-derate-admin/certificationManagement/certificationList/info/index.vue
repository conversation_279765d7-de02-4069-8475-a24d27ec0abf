<template>
  <div class="content-wrapper padding-10">
    <CardTitle :title-name="title" :ifback="true" @back="goToList()">
      <template>
        <el-button type="primary" @click="edit" icon="el-icon-edit" v-permission="'catalog:proof_catalog:proof:update'">修改</el-button>
        <el-button type="warning" plain @click="goToList" icon="el-icon-back">返回</el-button>
      </template>
    </CardTitle>
    <!-- <section class="content"> -->
      <info-list />
    <!-- </section> -->
  </div>
</template>
<script>
import infoList from '@/views/proof-derate-admin/certificationManagement/certificationList/components/infoList'
import { getGetproofCatalogFind } from '@/api/certificationManagement/certificationList'
import CardTitle from '@/components/CardTitle'
export default {
  name: 'ItemClearedInfoWay',
  components: {
    infoList,
    CardTitle
  },
  data() {
    return {
      title: ''
      // data: {
      //   id: "",
      //   title: "证明档案",
      //   activeName: "desc",
      // },
      // type: "firstDraft"
    }
  },
  mounted() {
    this.getTitle()
    console.log('')
    // this.data.id = this.$route.query["id"];
  },
  methods: {
    backPrev() {
      this.$router.go(-1)
    },
    goToList() {
      this.$router.push({
        name: 'certification_List'
      })
    },
    edit() {
      this.$router.push({
        name: 'certification_List_edit',
        query: {
          flagCatalog: 'edit',
          id: this.$route.query.id
        }
      })
    },
    getTitle() {
      getGetproofCatalogFind(this.$route.query.id).then(res => {
        console.log(res)
        if (res.data !== null) {
          this.title = res.data.name
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.content-header {
  // position: absolute;
  // top: 60px;
  // right: 0;
  // z-index: 111;
}
</style>
