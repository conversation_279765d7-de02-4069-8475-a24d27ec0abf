<template>
  <!-- <div> -->
  <detail-list />
  <!-- <section class="content-header">
      <span class="breadcrumb" align="right">
        <el-button plain type="primary" @click="backPrev" icon="el-icon-edit">保存</el-button>
        <el-button type="warning" plain @click="backPrev" icon="el-icon-back">返回</el-button>
      </span>
      <br />
    </section>
    <section class="content">
      <el-tabs type="border-card">
        <detail-list />
      </el-tabs>
  </section>-->
  <!-- </div> -->
</template>
<script>
import detailList from '../components/detail_add_edit'
export default {
  name: 'add',
  components: {
    detailList
  },
  data() {
    return {}
  },
  beforeRouteLeave(to, from, next) {
    // 设置下一个路由的 meta
    // console.log(to, from)
    // to.meta.keepAlive = false // 缓存
    if(to.name!=='certification_data_sharing'){
      localStorage.setItem('dataCache', '')
    }
    next()
  }
}
</script>
