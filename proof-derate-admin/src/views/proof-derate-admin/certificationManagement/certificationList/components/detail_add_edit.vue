<template>
  <div class="content-wrapper detail_add_edit padding-10">
    <!-- <section class="content-header title-algin-midle">
      <h1>
        <i class="el-icon-arrow-left" @click="goToList()" />
        <span v-if="$route.query.flagCatalog == 'add'">新增证明目录</span>
        <span v-if="$route.query.flagCatalog !== 'add'">{{ form.name }}</span>
      </h1>
      <span>
        <el-button type="primary" icon="el-icon-edit" @click.native="backPrev">保存</el-button>
        <el-button type="warning" plain icon="el-icon-back" @click="goToList">返回</el-button>
      </span>
    </section> -->
    <CardTitle :title-name="$route.query.flagCatalog == 'add' ? '新增证明目录' : form.name" :ifback="true" @back="goToList()">
      <template>
        <el-button type="primary" icon="el-icon-edit" @click.native="backPrev">保存</el-button>
        <el-button type="warning" plain icon="el-icon-back" @click="goToList">返回</el-button>
      </template>
    </CardTitle>
    <!-- <section class="content"> -->
    <el-tabs tab-position="top">
      <!-- <el-divider content-position="left">基础信息</el-divider> -->
      <span class="margin-left-10 info-wrap" style="margin-top: 0px">
        <img :src="arrow" alt />
        <span class="info-title">基础信息</span>
      </span>
      <el-form ref="form" :model="form" label-width="140px" style="margin-top: 20px">
        <el-row>
          <el-col :xs="24" :sm="12" :md="12" :lg="12">
            <el-form-item label="证明目录名称" prop="name" :rules="[{ required: true, message: '请输入证明目录名称', trigger: 'blur' }]">
              <el-input v-model="form.name" clearable placeholder="证明目录名称" />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="12">
            <el-form-item label="证明目录编码">
              <el-input v-model="form.code" :disabled="$route.query.flagCatalog === 'add' || $route.query.flagCatalog === 'edit'" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :xs="24" :sm="12" :md="12" :lg="12">
            <el-form-item label="证明开具单位类型" prop="unit_type" :rules="[{ required: true, message: '请选择证明开具单位类型', trigger: ['blur', 'change'] }]">
              <el-select v-model="form.unit_type" placeholder="请选择">
                <el-option v-for="item in unitTypeList" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="12">
            <el-form-item label="创建人名称">
              <el-input v-model="form.account_name" disabled />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <!-- <br /> -->
      <div class="el-tabs">
        <span class="margin-left-10 info-wrap">
          <img :src="arrow" alt />
          <span class="info-title">电子证照</span>
        </span>
        <!-- <el-divider content-position="left">电子证照</el-divider> -->
        <div v-if="tableDataElectronic.content.length != 0" class="addIcon" @click="getSingleElectronic">
          <img src="@/assets/proof-derate-admin-images/edit.png" alt style="width: 25px; height: 25px" />
        </div>
      </div>
      <el-row>
        <el-col :span="24">
          <custom-table ref="table1" :is-card-type="false" :table-data="tableDataElectronic" :table-header="tableHeaderElectronic" />
          <div v-if="tableDataElectronic.content.length === 0" class="item-add" @click="getSingleElectronic">
            <!-- <span> -->
            <!-- <img src="@/assets/proof-derate-admin-images/add.png" alt> -->
            <svg-icon class="add" icon-class="add" :style="{ fill: $store.state.settings.skinColor, 'margin-right': '5px' }" />
            <span>添加</span>
            <!-- </span> -->
          </div>
        </el-col>
      </el-row>
      <div class="el-tabs">
        <span class="margin-left-10 info-wrap">
          <img :src="arrow" alt />
          <span class="info-title">电子证明</span>
        </span>
      </div>
      <el-row>
        <el-col :span="24">
          <custom-table ref="table1" :is-card-type="false" :table-data="tableDataElectronicCertification" :table-header="tableHeaderElectronicCertification">
            <template #operate="{ row }">
              <el-button type="text" @click="electronicCertificationLook(row)">查看</el-button>
              <el-button v-if="tableDataElectronicCertification.content.length !== 0" type="text" @click="getSingleElectronicCertification">编辑</el-button>
              <el-button type="text" style="color: red" @click="delectTableDataElectronicCertification">删除</el-button>
            </template>
          </custom-table>
          <div v-if="tableDataElectronicCertification.content.length === 0" class="item-add" @click="getSingleElectronicCertification">
            <svg-icon class="add" icon-class="add" :style="{ fill: $store.state.settings.skinColor, 'margin-right': '5px' }" />
            <span>添加</span>
          </div>
        </el-col>
      </el-row>
      <div class="el-tabs">
        <!-- v-if="tableDataData.content.length==0" -->
        <!-- <div class="addIcon" @click="getDataShar"> -->
        <!-- <div class="addIcon" @click="setDataSharing" v-if="tableDataData.content.length===0">
            <img src="@/assets/proof-derate-admin-images/u746.png" alt />
          </div>-->
        <!-- <el-divider content-position="left">数据共享</el-divider> -->
        <span class="margin-left-10 info-wrap">
          <img :src="arrow" alt />
          <span class="info-title">数据共享</span>
        </span>
      </div>

      <el-row>
        <el-col :span="24">
          <custom-table ref="table1" :is-card-type="false" :table-data="tableDataData" :table-header="tableHeaderData" @edit="dataSharEdit" @delete="dataSharDelete" />
          <div v-if="tableDataData.content.length === 0" class="item-add" @click="setDataSharing">
            <svg-icon class="add" icon-class="add" :style="{ fill: $store.state.settings.skinColor, 'margin-right': '5px' }" />
            <span>添加</span>
          </div>
        </el-col>
      </el-row>
      <div class="el-tabs">
        <span class="margin-left-10 info-wrap">
          <img :src="arrow" alt />
          <span class="info-title">部门协查</span>
        </span>
        <!-- <el-divider content-position="left">部门协查</el-divider> -->
        <!-- <div class="addIcon" @click="getArtificial" v-if="tableDataArtificial.content.length==0">
            <img src="@/assets/proof-derate-admin-images/u746.png" alt />
          </div>-->
      </div>
      <el-row>
        <el-col :span="24">
          <custom-table ref="tableDataArtificial" :is-card-type="false" :expand="true" :table-data="tableDataArtificial" :table-header="tableHeaderArtificial">
            <template #examine_level="{ row }">
              {{ numberToChinese(row.examine_level) + '级审核' }}
            </template>

            <template #operate="{ row, $index }">
              <el-button type="text" @click="artificialEdit(row, $index)">编辑</el-button>
              <el-button type="text" style="color: red" @click="artificialDelete(row, $index)">删除</el-button>
            </template>
            <template #expand>
              <div v-for="(item, key) in auditRTempList" :key="key" style="margin-left: 420px; color: #c0c4cc">
                <div style="margin: 5px 0px">
                  <span style="margin-right: 10px">第{{ numberToChinese(item.audit_level) }}级审核实施区划</span>
                  <span style="color: #606266">{{ item.audit_divi_name }}</span>
                </div>
                <div style="margin: 5px 0px">
                  <span style="margin-right: 10px">第{{ numberToChinese(item.audit_level) }}级审核实施部门</span>
                  <span style="color: #606266">{{ item.audit_org_name }}</span>
                </div>
              </div>
            </template>
          </custom-table>
          <div v-if="tableDataArtificial.content.length == 0" class="item-add" @click="getArtificial">
            <svg-icon class="add" icon-class="add" :style="{ fill: $store.state.settings.skinColor, 'margin-right': '5px' }" />
            <span>添加</span>
          </div>
        </el-col>
      </el-row>
      <div class="el-tabs">
        <span class="margin-left-10 info-wrap">
          <img :src="arrow" alt />
          <span class="info-title">告知承诺</span>
        </span>
        <!-- <el-divider content-position="left">告知承诺</el-divider> -->
        <!-- <div class="addIcon" @click="getPromise" v-if="tableDataPromise.content.length==0">
            <img src="@/assets/proof-derate-admin-images/u746.png" alt />
          </div>-->
      </div>

      <el-row>
        <el-col :span="24">
          <custom-table ref="table1" :is-card-type="false" :table-data="tableDataPromise" :table-header="tableHeaderPromise">
            <template #operate="{ row, $index }">
              <el-button type="text" @click="promiseEdit(row, $index)">编辑</el-button>
              <el-button type="text" style="color: red" @click="promiseDelete(row, $index)">删除</el-button>
            </template>
            <template #commit_attachment_name="{ row }">
              <el-button type="text" @click="goattachment_id(row)">{{ row.commit_attachment_name }}</el-button>
            </template>
          </custom-table>
          <div v-if="tableDataPromise.content.length == 0" class="item-add" @click="getPromise">
            <svg-icon class="add" icon-class="add" :style="{ fill: $store.state.settings.skinColor, 'margin-right': '5px' }" />
            <span>添加</span>
          </div>
        </el-col>
      </el-row>
      <div class="el-tabs">
        <span class="margin-left-10 info-wrap">
          <img :src="arrow" alt />
          <span class="info-title">自行调查</span>
        </span>
        <!-- <el-divider content-position="left">自行调查</el-divider> -->
        <!-- <div class="addIcon" @click="getInvestigation" v-if="tableDataSurvey.content.length==0">
            <img src="@/assets/proof-derate-admin-images/u746.png" alt />
          </div>-->
      </div>
      <el-row>
        <el-col :span="24">
          <custom-table ref="table1" :is-card-type="false" :table-data="tableDataSurvey" :table-header="tableHeaderSurvey">
            <template #operate="{ row, $index }">
              <el-button type="text" @click="SurveyEdit(row, $index)">编辑</el-button>
              <el-button type="text" style="color: red" @click="SurveyDelete(row, $index)">删除</el-button>
            </template>
          </custom-table>
          <div v-if="tableDataSurvey.content.length == 0" class="item-add" @click="getInvestigation">
            <svg-icon class="add" icon-class="add" :style="{ fill: $store.state.settings.skinColor, 'margin-right': '5px' }" />
            <span>添加</span>
          </div>
        </el-col>
      </el-row>
      <div class="el-tabs">
        <span class="margin-left-10 info-wrap">
          <img :src="arrow" alt />
          <span class="info-title">其他</span>
        </span>
        <!-- <el-divider content-position="left">其他</el-divider> -->
        <!-- <div class="addIcon" @click="getOther" v-if="tableDataOther.content.length==0">
            <img src="@/assets/proof-derate-admin-images/u746.png" alt />
          </div>-->
      </div>
      <el-row>
        <el-col :span="24">
          <custom-table ref="table1" :is-card-type="false" :table-data="tableDataOther" :table-header="tableHeaderOther">
            <template #operate="{ row, $index }">
              <el-button type="text" @click="otherEdit(row, $index)">编辑</el-button>
              <el-button type="text" style="color: red" @click="otherDelete(row, $index)">删除</el-button>
            </template>
          </custom-table>
          <div v-if="tableDataOther.content.length == 0" class="item-add" @click="getOther">
            <svg-icon class="add" icon-class="add" :style="{ fill: $store.state.settings.skinColor, 'margin-right': '5px' }" />
            <span>添加</span>
          </div>
        </el-col>
      </el-row>
    </el-tabs>
    <!-- </section> -->
    <!-- 选择电子证照弹框-->
    <el-dialog title="选择证照目录" width="53%" :visible.sync="selectLicenseDialog">
      <el-form ref="licenseForm" :model="licenseForm" label-width="120px" @submit.native.prevent>
        <el-form-item prop="keyword" label="证照目录名称" :show-message="false" :rules="[{ required: true, message: '请选择', trigger: ['blur'] }]">
          <el-input v-model="licenseForm.keyword" placeholder="请输入证照目录名称" style="width: 500px; margin-right: 10px" clearable />
          <el-button type="primary" :loading="transferLoading" @click="queryLicense">查询</el-button>
        </el-form-item>
      </el-form>
      <CustomTransferWithSelectedData ref="transferCom" v-model="selectedLicenseList" v-loading="transferLoading" :props="{ key: 'basic_code', label: 'license_item_name' }" :titles="['待选项', '已选项']" :data="licenseData" style="margin-top: 20px"></CustomTransferWithSelectedData>
      <div slot="footer" class="dialog-footer">
        <el-button @click="selectLicenseDialog = false">取 消</el-button>
        <el-button type="primary" @click="handelSelectLicenseConfirm">确 定</el-button>
      </div>
    </el-dialog>
    <!-- 数据共享弹框-->
    <el-dialog :title="dataSharingFormFlag === 'edit' ? '数据共享修改' : '数据共享新增'" width="60%" :visible.sync="dataSharingDialog">
      <el-form ref="dataSharingForm" :model="dataSharingForm" label-width="120px" @submit.native.prevent>
        <el-row>
          <el-col :xs="24" :sm="12" :md="12" :lg="12">
            <el-form-item label="实施区划" prop="division_code" :rules="[{ required: true, message: '请选择实施区划', trigger: 'change' }]">
              <division-selector v-model="dataSharingForm.division_code" :max-level="2" @change="dataSharingDivisionChange" @childValue="dataShareCodeSon" />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="12">
            <el-form-item label="实施机构" prop="investigation_dept_code" :rules="[{ required: true, message: '请选择实施机构', trigger: 'change' }]" :show-message="true">
              <el-select v-model="dataSharingForm.investigation_dept_code" placeholder="请选择">
                <el-option v-for="item in dataSharingOrganizationList" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :xs="24" :sm="12" :md="12" :lg="24">
            <el-form-item label="数据协查说明" prop="note" :rules="[{ required: true, message: '请填写数据协查说明', trigger: 'blur' }]">
              <el-input v-model="dataSharingForm.note" clearable placeholder="请填写数据协查说明" type="textarea" />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="24">
            <el-form-item label="数据参数说明" prop="investigation_parameter" :rules="[{ required: true, message: '请填写数据参数说明', trigger: 'blur' }]">
              <el-input v-model="dataSharingForm.investigation_parameter" type="textarea" placeholder="请填写数据参数说明" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="dataSharingDialogConfirm()">确 定</el-button>
        <el-button @click="dataSharingDialog = false">取 消</el-button>
      </div>
    </el-dialog>
    <!--人工协查弹框-->
    <el-dialog :title="ArtificialIdFlag === 'edit' ? '部门协查修改' : '部门协查新增'" width="60%" :visible.sync="ArtificialDialog">
      <el-form ref="ArtificialForm" :model="ArtificialForm" label-width="120px" @submit.native.prevent>
        <el-row>
          <el-col :xs="24" :sm="12" :md="12" :lg="12">
            <el-form-item label="实施区划" prop="division_code" :rules="[{ required: true, message: '请选择实施区划', trigger: 'change' }]">
              <!-- <division-selector v-model="ArtificialForm.division_code" @change="divisionChange" @childValue="codeSon" /> -->
              <AdministrativeDivisionCascader :key="ArtificialForm.division_code" ref="AdministrativeDivisionSelect" :division-code="ArtificialForm.division_code" :permission-code="'catalog:proof_catalog:proof:list'" @setDivisionCodeAndName="setDivisionCodeAndName" />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="12">
            <el-form-item label="实施机构" prop="investigation_dept_code" :show-message="true">
              <el-select v-model="ArtificialForm.investigation_dept_code" placeholder="请选择">
                <el-option v-for="item in organizationList" :key="item.credit_code" :label="item.name" :value="item.credit_code" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :xs="24" :sm="12" :md="12" :lg="24">
            <el-form-item label="部门协查说明" prop="note" :rules="[{ required: true, message: '请填写部门协查说明', trigger: 'blur' }]">
              <el-input v-model="ArtificialForm.note" clearable placeholder="请填写部门协查说明" type="textarea" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :xs="24" :sm="12" :md="12" :lg="8">
            <el-form-item label="协查时限" prop="assist_time_hour" :rules="[{ required: true, message: '请填写协查时限', trigger: 'blur' }]">
              <el-input v-model="ArtificialForm.assist_time_hour" placeholder="请填写协查时限">
                <template slot="append">小时</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="6">
            <el-form-item label label-width="10px" prop="assist_time_minute" :rules="[{ required: true, message: '请填写协查时限', trigger: 'blur' }]">
              <el-input v-model="ArtificialForm.assist_time_minute" placeholder="请填写协查时限">
                <template slot="append">分钟</template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :xs="24" :sm="12" :md="12" :lg="24">
            <el-form-item label>
              <span>注意：协查时限在法定工作日正常上班时间时间段内予以计算。</span>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="ArtificialDialogConfirm()">确 定</el-button>
        <el-button @click="ArtificialDialog = false">取 消</el-button>
      </div>
    </el-dialog>
    <!--告知承诺弹框-->
    <el-dialog :title="promiseFlag === 'edit' ? '告知承诺修改' : '告知承诺新增'" width="60%" :visible.sync="promiseDialog">
      <el-form ref="promiseForm" :model="promiseForm" label-width="120px" @submit.native.prevent>
        <el-row>
          <el-col :xs="24" :sm="12" :md="12" :lg="24">
            <el-form-item label="承诺书模板" prop="commit_attachment_name" :rules="[{ required: true, message: '请上传承诺书模板', trigger: 'blur' }]">
              <span>
                {{ promiseForm.commit_attachment_name }}
              </span>
              <el-button model="promiseForm.commit_attachment_name" type="text" @click="uploading">点击上传</el-button>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="24">
            <el-form-item label="承诺书说明" prop="commit_book_description" :rules="[{ required: true, message: '请填写承诺书说明', trigger: 'blur' }]">
              <el-input v-model="promiseForm.commit_book_description" clearable placeholder="请填写承诺书说明" type="textarea" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="promiseConfirm()">确 定</el-button>
        <el-button @click="promiseDialog = false">取 消</el-button>
      </div>
    </el-dialog>
    <!--告知承诺上传弹框-->
    <el-dialog title="告知承诺导入" :visible.sync="promiseUploadingDialog" width="50%" center>
      <uploadDialog ref="uploadDialog" :template="templateFlag" />
      <span slot="footer" class="dialog-footer">
        <p class="tip">
          <i class="el-icon-info" />
          温馨提示：请选择以doc/docx/pdf为后缀名的文件且上传文件不超过1M！
        </p>
        <el-button type="primary" @click="promiseimportFlie">导入</el-button>
        <el-button @click="promiseUploadingDialog = false">取 消</el-button>
      </span>
    </el-dialog>
    <!--自行调查弹框-->
    <el-dialog :title="InvestigationFlag === 'edit' ? '自行调查修改' : '自行调查新增'" width="60%" :visible.sync="InvestigationDialog">
      <el-form ref="InvestigationForm" :model="InvestigationForm" label-width="120px" @submit.native.prevent>
        <el-row>
          <el-col :xs="24" :sm="12" :md="12" :lg="24">
            <el-form-item label="自行调查说明" prop="dept_cancel_description" :rules="[{ required: true, message: '请填写自行调查说明', trigger: 'blur' }]">
              <el-input v-model="InvestigationForm.dept_cancel_description" clearable placeholder="请填写自行调查说明" type="textarea" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="InvestigationFormConfirm()">确 定</el-button>
        <el-button @click="InvestigationDialog = false">取 消</el-button>
      </div>
    </el-dialog>
    <!--其它弹框-->

    <el-dialog :title="otherFlag === 'edit' ? '其他修改' : '其他新增'" width="60%" :visible.sync="otherDialog">
      <el-form ref="otherForm" :model="otherForm" label-width="120px" @submit.native.prevent>
        <el-row>
          <el-col :xs="24" :sm="12" :md="12" :lg="24">
            <el-form-item label="其他说明" prop="other_clear_description" :rules="[{ required: true, message: '请填写其他说明', trigger: 'blur' }]">
              <el-input v-model="otherForm.other_clear_description" clearable placeholder="请填写其他说明" type="textarea" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="otherConfirm()">确 定</el-button>
        <el-button @click="otherDialog = false">取 消</el-button>
      </div>
    </el-dialog>
    <licenseCatalogueDialog :license-catalogue-dialog-show="licenseCatalogueDialog" @closeDialog="closeDialog" @getSlectItem="getSlectItem" />
    <licenseDialog v-if="licenseDialogVisible" :license-dialog-visible="licenseDialogVisible" :selected-value="tableDataElectronic.content" @closelicenseDialog="closelicenseDialog" @getlicenseData="getlicenseData" />
    <electronicCertificationDialog ref="electronicCertificationDialog" :electronic-certification-dialog-show="electronicCertificationDialogShow" @closeDialog="closeElectronicCertificationDialog" @getElectronicCertificationSlectItem="getElectronicCertificationSlectItem" />
    <dataShareDialog :data-share-dialog-visible="dataShareDialogVisible" @getDataShare="getDataShare" @closeDataShareDialog="closeDataShareDialog" />
    <!-- 部门协查弹窗 -->
    <assistDialog ref="assistDialog" :assistDialogShow="assistDialogShow" @closeDialog="closeAssistDialog" @relationElectronicProof="relationElectronicProof" @getAssistData="getAssistData"> </assistDialog>
  </div>
</template>
<script>
import CustomTable from '@/components/Element/Table'
import CustomTransfer from '@/views/proof-derate-admin/itemBiz/itemClear/components/CustomTransfer'
import CustomTransferWithSelectedData from '@/views/proof-derate-admin/itemBiz/itemClear/components/CustomTransferWithSelectedData'
import Enum from '@/utils/enum'
import { getIndustryDept } from '@/api/common/license'
import divisionSelector from '@/components/DivisionSelector'
import { getOrgListNoAuth } from '@/api/admin/org.js'
import uploadDialog from '@/views/proof-derate-admin/components/uploadDialog'
import { getGetproofCatalogCreate, getGetproofCatalogEdit, getCommit_Attachment, getDown_Attachment, getGetproofCatalogFind } from '@/api/certificationManagement/certificationList'
import { goLicenseItemView, licenseProofItemView } from '@/api/itemBiz/list'
import { getOrganizationList } from '@/api/commonPack/platManege'
import licenseCatalogueDialog from '@/views/proof-derate-admin/ItemManagement/itemProve/components/licenseCatalogueDialog.vue'
import electronicCertificationDialog from './electronicCertificationDialog_0'
import dataShareDialog from './dataShareDialog'
import licenseDialog from './licenseDialog'
import CardTitle from '@/components/CardTitle'
import assistDialog from './assistDialog.vue'
import { numberToChinese } from '@/utils'
export default {
  name: 'InfoList',
  components: {
    CustomTable,
    CustomTransfer,
    CustomTransferWithSelectedData,
    divisionSelector,
    uploadDialog,
    licenseCatalogueDialog,
    electronicCertificationDialog,
    dataShareDialog,
    licenseDialog,
    CardTitle,
    AdministrativeDivisionCascader: () => import('@/components/AdministrativeDivisionCascader'),
    assistDialog,
  },
  data() {
    return {
      templateFlag: false,
      uploadTips: false,
      division_name: '',
      dataConfig: {
        id: '',
        title: '证明档案',
        activeName: 'desc',
      },
      form: {
        name: '',
        code: '',
        unit_type: '',
        account_name: '',
        // industry_dept_name: ""
      },
      unitTypeList: Enum.unitTypeList, // 证明开具单位类型
      // departmentList: Enum.departmentList, //所属行业部门
      divisionCode: '', //
      // 电子证照
      tableDataElectronic: {
        border: true,
        content: [], // 表格数据
        loading: false, // 控制表格加载效果
        total: 0, // 分页查询时的总数，去掉该属性，不显示分页条
        currentPage: 1, // 当前页码
        pageSize: 10,
        pageDirection: 'desc',
      },
      // 表头配置
      // tableHeaderElectronic: [
      //   { label: '电子证照目录名称', prop: 'license_name', minWidth: '200px' }, // 配置slot属性，可支持使用插槽
      //   { label: '电子证照目录编码', prop: 'code', minWidth: '200px' },
      //   {
      //     label: '操作',
      //     slot: 'operate',
      //     prop: 'operate',
      //     minWidth: '50px',
      //     fixed: 'right'
      //   }
      // ],
      // 电子证明
      tableDataElectronicCertification: {
        border: true,
        content: [], // 表格数据
        loading: false, // 控制表格加载效果
        total: 0, // 分页查询时的总数，去掉该属性，不显示分页条
        currentPage: 1, // 当前页码
        pageSize: 10,
        pageDirection: 'desc',
      },
      // 电子证明表头配置
      tableHeaderElectronicCertification: [
        { label: '电子证明名称', prop: 'license_name', minWidth: '200px' }, // 配置slot属性，可支持使用插槽
        { label: '行业部门', prop: 'dept_name', minWidth: '200px' },
        {
          label: '操作',
          slot: 'operate',
          prop: 'operate',
          minWidth: '50px',
          fixed: 'right',
        },
      ],
      // 表头配置
      tableHeaderElectronic: [
        { label: '电子证照目录名称', prop: 'license_name', minWidth: '200px' }, // 配置slot属性，可支持使用插槽
        { label: '电子证照目录编码', prop: 'license_code', minWidth: '200px' },
        // {
        //   label: '操作',
        //   slot: 'operate',
        //   prop: 'operate',
        //   minWidth: '50px',
        //   fixed: 'right'
        // }
      ],
      // 数据共享
      tableDataData: {
        border: true,
        content: [], // 表格数据
        loading: false, // 控制表格加载效果
        total: 0, // 分页查询时的总数，去掉该属性，不显示分页条
        currentPage: 1, // 当前页码
        pageSize: 10,
        pageDirection: 'desc',
      },

      // 表头配置
      tableHeaderData: [
        {
          label: '系统名称',
          prop: 'system_name',
          minWidth: '200px',
        }, // 配置slot属性，可支持使用插槽
        { label: '数据主题名称', prop: 'data_theme_name', minWidth: '200px' },
        {
          label: '接口访问地址',
          prop: 'system_api_url',
          minWidth: '200px',
        },
        {
          label: '操作',
          prop: 'operateColumn', // prop为“operateColumn”时，可配置actions按钮列表
          minWidth: '100px',
          fixed: 'right',
          actions: [
            {
              label: '编辑',
              action: 'edit', // 按钮该按钮时，派发事件的名称
            },
            {
              label: '删除',
              action: 'delete', // 按钮该按钮时，派发事件的名称
            },
          ],
        },
      ],
      // 人工协查
      tableDataArtificial: {
        border: true,
        content: [], // 表格数据
        loading: false, // 控制表格加载效果
        total: 0, // 分页查询时的总数，去掉该属性，不显示分页条
        currentPage: 1, // 当前页码
        pageSize: 10,
        pageDirection: 'desc',
      },
      // 表头配置
      tableHeaderArtificial: [
        { label: '审核环节数', prop: 'examine_level', slot: 'examine_level', minWidth: '200px' },
        {
          label: '协查部门',
          prop: 'investigation_dept_name',
          minWidth: '200px',
          formatter: (row, col, val) => {
            return val === null || val.length === 0 ? '全部' : val
          },
        }, // 配置slot属性，可支持使用插槽
        { label: '部门协查说明', prop: 'note', minWidth: '200px' },
        { label: '电子证明', prop: 'proof', minWidth: '200px' },
        {
          label: '操作',
          slot: 'operate',
          prop: 'operate',
          minWidth: '50px',
          fixed: 'right',
        },
      ],
      // 告知承诺
      tableDataPromise: {
        border: true,
        content: [], // 表格数据
        loading: false, // 控制表格加载效果
        total: 0, // 分页查询时的总数，去掉该属性，不显示分页条
        currentPage: 1, // 当前页码
        pageSize: 10,
        pageDirection: 'desc',
      },
      // 表头配置
      tableHeaderPromise: [
        {
          label: '承诺书说明',
          prop: 'commit_book_description',
          minWidth: '200px',
        }, // 配置slot属性，可支持使用插槽
        {
          label: '承诺书模板',
          slot: 'commit_attachment_name',
          prop: 'commit_attachment_name',
          minWidth: '200px',
          formatter: (row, col, val) => {
            return row.handing_item === null || val === undefined ? row.item_name : row.item_name + '【' + row.handing_item + '】'
          },
        },
        {
          label: '操作',
          slot: 'operate',
          prop: 'operate',
          minWidth: '50px',
          fixed: 'right',
        },
      ],
      tableDataSurvey: {
        border: true,
        content: [], // 表格数据
        loading: false, // 控制表格加载效果
        total: 0, // 分页查询时的总数，去掉该属性，不显示分页条
        currentPage: 1, // 当前页码
        pageSize: 10,
        pageDirection: 'desc',
      },
      // 表头配置
      tableHeaderSurvey: [
        {
          label: '自行调查说明',
          prop: 'dept_cancel_description',
          minWidth: '200px',
        }, // 配置slot属性，可支持使用插槽
        {
          label: '操作',
          slot: 'operate',
          prop: 'operate',
          minWidth: '50px',
          fixed: 'right',
        },
      ],
      tableDataOther: {
        border: true,
        content: [], // 表格数据
        loading: false, // 控制表格加载效果
        total: 0, // 分页查询时的总数，去掉该属性，不显示分页条
        currentPage: 1, // 当前页码
        pageSize: 10,
        pageDirection: 'desc',
      },
      // 表头配置
      tableHeaderOther: [
        {
          label: '其它说明',
          prop: 'other_clear_description',
          minWidth: '200px',
        }, // 配置slot属性，可支持使用插槽
        {
          label: '操作',
          slot: 'operate',
          prop: 'operate',
          minWidth: '50px',
          fixed: 'right',
        }, // 配置slot属性，可支持使用插槽
      ],
      selectLicenseDialog: false, // 选择证照dialog
      licenseForm: {
        keyword: '',
      },
      licenseData: [],
      // 已选择的目录
      licenseDataSelected: [],
      transferLoading: false,
      selectedLicenseList: [],
      validateLicense: (rule, value, callback) => {
        if (_.isEmpty(this.licenseForm.keyword)) {
          callback(new Error('请选择关联的电子证照'))
        } else {
          callback()
        }
      },
      // 数据共享弹框
      dataSharingDialog: false,
      dataSharingForm: {
        id: '',
        investigation_dept_name: '',
        investigation_dept_code: '',
        note: '',
        investigation_parameter: '',
        division_code: '',
        division_name: '',
      },
      dataSharingId: '',
      dataSharingOrganizationList: [],
      organizationList: [],
      dataSharingFormFlag: 'add',
      // 人工协查弹框
      ArtificialDialog: false,
      ArtificialForm: {
        id: '',
        investigation_dept_name: '',
        investigation_dept_code: '',
        note: '',
        division_code: '',
        division_name: '',
        assist_time_hour: '',
        assist_time_minute: '',
        // assist_time_limit: '' //协查时限
      },
      ArtificialIndex: '',
      ArtificialIdFlag: 'add',
      // 告知承诺
      promiseDialog: false,
      promiseForm: {
        commit_book_description: '',
        commit_attachment_id: '',
        commit_attachment_name: '',
      },
      promiseFlag: 'add',
      promiseIndex: '',
      promiseUploadingDialog: false,
      file: '',
      // 自行调查
      InvestigationDialog: false,
      InvestigationForm: { id: '', dept_cancel_description: '' },
      InvestigationFlag: 'add',
      InvestigationIndex: '',
      // 其他调查
      otherDialog: false,
      otherForm: {
        other_clear_description: '',
      },
      otherFlag: 'add',
      otherIndex: '',
      licenseCatalogueDialog: false,
      electronicCertificationDialogShow: false,
      dataShareDialogVisible: false,
      licenseDialogVisible: false,
      assistDialogShow: false,
      arrow: require('@/assets/proof-derate-admin-images/arrow.png'),
      isRelationElectronicProof: false, // 是否部门协查点击电子证明弹窗
      auditRTempList: [], // 审核实施部门和区划
    }
  },
  watch: {
    'dataSharingForm.division_code': {
      handler(val) {
        if (val) {
          // this.dataSharingForm.investigation_dept_code = "";
        } else {
          this.dataSharingOrganizationList = []
        }
      },
      immediate: true,
    },
    'ArtificialForm.division_code': {
      handler(val) {
        if (val) {
          // this.ArtificialForm.investigation_dept_code = "";
          this.getOrganizationList(this.ArtificialForm.division_code)
        } else {
          this.organizationList = []
        }
      },
      immediate: true,
    },
  },

  mounted() {
    if (this.$route.query.flagCatalog === 'edit') {
      if (this.$route.query.id) {
        this.goGetproofCatalogFind(this.$route.query.id)
      }
    } else {
      this.initAdd()
      if (localStorage.getItem('dataCache') != '') {
        Object.assign(this.$data, JSON.parse(localStorage.getItem('dataCache')))
      }
      if (this.$route.params.saveData != undefined) {
        console.log('this.$route', this.$route)
        const saveData = this.$route.params.saveData
        this.tableDataData.content = []
        this.tableDataData.content.push({
          system_api_url: saveData.system_api_url,
          system_code: saveData.system_code,
          system_name: saveData.system_name,
          data_theme_name: saveData.data_theme_name,
          data_theme_code: saveData.data_theme_code,
          config_json: saveData.config_json,
        })
      }
    }
  },
  activated() {
    // console.log('缓存',this.$route.params.saveData)
    if (this.$route.params.saveData != undefined || this.$route.params.saveData === null) {
      const saveData = this.$route.params.saveData
      this.tableDataData.content = []
      this.tableDataData.content.push({
        system_api_url: saveData.system_api_url,
        system_code: saveData.system_code,
        system_name: saveData.system_name,
        data_theme_name: saveData.data_theme_name,
        data_theme_code: saveData.data_theme_code,
        config_json: saveData.config_json,
      })
    } else {
      this.initAdd()
    }
  },
  deactivated() {},

  methods: {
    numberToChinese,
    // 新增初始化
    initAdd() {
      this.$refs['form'].resetFields()
      this.form = {}
      this.tableDataElectronic.content = [] // 电子证照
      this.tableDataData.content = [] // 数据共享
      this.tableDataArtificial.content = []
      this.tableDataPromise.content = []
      this.tableDataSurvey.content = []
      this.tableDataOther.content = []
      this.tableDataElectronicCertification.content = []
    },
    // 编辑初始化
    goGetproofCatalogFind(findId) {
      getGetproofCatalogFind(findId)
        .then((res) => {
          this.form = res.data
          if (res.data.proof_catalog_license_relation_list != null) {
            this.tableDataElectronic.content = res.data.proof_catalog_license_relation_list // 电子证照
            this.selectedLicenseList = res.data.proof_catalog_license_relation_list.map((value) => {
              return {
                id: value.id,
                basic_code: value.license_code,
                license_item_name: value.license_name,
                industry_dept: value.dept_name,
              }
            })
          }
          // if (res.data.proof_catalog_data_shared_list != null) {
          if (this.$route.params.saveData != undefined) {
            const saveData = this.$route.params.saveData
            this.tableDataData.content.push({
              system_api_url: saveData.system_api_url,
              system_code: saveData.system_code,
              system_name: saveData.system_name,
              data_theme_name: saveData.data_theme_name,
              data_theme_code: saveData.data_theme_code,
              config_json: saveData.config_json,
            })
          } else {
            if (res.data.proof_catalog_data_shared_list != null) {
              this.tableDataData.content = res.data.proof_catalog_data_shared_list // 数据共享
            } else {
              this.tableDataData.content = []
            }
          }
          // this.tableDataData.content = res.data.proof_catalog_data_shared_list //数据共享
          // }
          if (res.data.proof_catalog_artificial_list != null) {
            this.tableDataArtificial.content = res.data.proof_catalog_artificial_list
            if (res.data.proof_catalog_artificial_list.length > 0) {
              this.auditRTempList = _.orderBy(res.data.proof_catalog_artificial_list[0].audit_r_temp_list,'audit_level','asc');
              // _.orderBy(res.data.proof_catalog_artificial_list[0].audit_r_temp_list,'audit_level','desc')
              console.log('this.auditRTempList',this.auditRTempList)
              let data = res.data.proof_catalog_artificial_list[0]
              console.log('data.issue_p_license11',data.issue_p_license)
              let proof = data.issue_p_license ? '' + (data.issue_p_license_way == 'SYSTEM_GENERATE' ? '系统生成' : '人工开具') + '，' + data.license_name : '不开具'
              res.data.proof_catalog_artificial_list[0].proof = proof
            }
          }
          if (res.data.proof_catalog_clerk_commitment_list != null) {
            this.tableDataPromise.content = res.data.proof_catalog_clerk_commitment_list
          }
          if (res.data.proof_catalog_dept_survey_list != null) {
            this.tableDataSurvey.content = res.data.proof_catalog_dept_survey_list
          }
          if (res.data.proof_catalog_other_relation_list != null) {
            this.tableDataOther.content = res.data.proof_catalog_other_relation_list
          }
          if (res.data.proof_catalog_license_item_relation != null) {
            this.tableDataElectronicCertification.content = res.data.proof_catalog_license_item_relation
          }
        })
        .catch(() => {})
    },
    divisionChange(d) {
      // this.dataSharingForm.division_code = d.code || "";
      this.ArtificialForm.division_code = d.code || ''
      // this.params.divisionCode = this.params.divisionCode.replace(/(0+)$/g, ""); //末尾去0
      this.organizationList = []
      // this.dataSharingForm.investigation_dept_code = "";
      this.ArtificialForm.investigation_dept_code = ''
      if (d.code) {
        getOrgListNoAuth({
          pageSize: 1000,
          pageNumber: 0,
          divisionCode: d.code,
        }).then((res) => {
          this.organizationList = res.content.map((i) => {
            return { label: i.name, value: i.tyshxydm }
          })
        })
      }
    },
    dataSharingDivisionChange(d) {
      this.dataSharingForm.division_code = d.code || ''
      this.dataSharingOrganizationList = []
      this.dataSharingForm.investigation_dept_code = ''
      if (d.code) {
        getOrgListNoAuth({
          pageSize: 1000,
          pageNumber: 0,
          divisionCode: d.code,
        }).then((res) => {
          this.dataSharingOrganizationList = res.content.map((i) => {
            return { label: i.name, value: i.tyshxydm }
          })
        })
      }
    },
    // 电子证照新增
    getElectronic() {
      this.selectLicenseDialog = true
      if (this.$refs['licenseForm'] !== undefined) {
        this.$refs['licenseForm'].resetFields()
      }
      if (this.$refs['transferCom']) {
        this.$nextTick(() => {
          // this.selectedLicenseList = [];
          this.licenseData = []
          // this.$refs["transferCom"].clearQuery("left");
          // this.$refs["transferCom"].clearQuery("right");
        })
      }
    },
    getSingleElectronic() {
      // this.licenseCatalogueDialog = true
      this.licenseDialogVisible = true
    },
    delectTableDataElectronic() {
      this.tableDataElectronic.content = []
    },
    getSingleElectronicCertification() {
      this.electronicCertificationDialogShow = true
      // console.log('this.form', this.form)
      // if (Object.keys(this.form).length !== 0) {
      //   this.$refs.electronicCertificationDialog.editInit(this.form)
      // }
    },
    delectTableDataElectronicCertification() {
      this.tableDataElectronicCertification.content = []
    },
    // 电子证照查看
    electronicLook(row) {
      goLicenseItemView(row.license_code).then((res) => {
        window.open(res.data.url, '_blank')
      })
    },
    electronicCertificationLook(row) {
      licenseProofItemView(row.license_code).then((res) => {
        window.open(res.data.url, '_blank')
      })
    },
    queryLicense() {
      this.transferLoading = true
      getIndustryDept({ license_item_name: this.licenseForm.keyword })
        .then((res) => {
          if (this.selectedLicenseList && this.selectedLicenseList.length > 0) {
            const selectIds = this.selectedLicenseList.map((i) => i.basic_code)
            this.licenseData = res.data.filter((f) => !selectIds.includes(f.basic_code)) || []
          } else {
            this.licenseData = res.data || []
          }
          this.transferLoading = false
        })
        .catch((e) => {
          this.licenseData = []
          this.transferLoading = false
        })
    },
    handelSelectLicenseConfirm() {
      this.selectLicenseDialog = false
      const arr = []
      this.selectedLicenseList.forEach((i) => {
        arr.push({
          id: i.id,
          license_name: i.license_item_name,
          license_code: i.basic_code,
          dept_name: i.industry_dept,
        })
      })
      this.tableDataElectronic.content = arr
    },
    // 数据共享
    getDataShar() {
      this.dataSharingForm.division_code = ''
      this.dataSharingDialog = true
    },
    // 新增数据共享
    setDataSharing() {
      this.$route.query.dataSharType = 'edit'
      if (this.tableDataData.content.length === 0) {
        this.$route.query.dataSharType = 'add'
      }
      // this.$router.push({ path: '/proof-derate-admin/certificationManagement/data_sharing', query: this.$route.query })
      localStorage.setItem('dataCache', JSON.stringify(this.$data))
      this.dataShareDialogVisible = true
      // this.$router.push({ name: 'certification_data_sharing', query: this.$route.query, params: {} })
    },
    // 新增/编辑
    dataSharingDialogConfirm() {
      const _this = this
      this.$refs['dataSharingForm'].validate((valid) => {
        if (valid) {
          this.dataSharingOrganizationList.forEach((item) => {
            if (item.value === this.dataSharingForm.investigation_dept_code) {
              this.dataSharingForm.investigation_dept_name = item.label
            }
          })
          // _this.dataSharingForm.division_name = _this.division_name;
          var newValue = {
            id: _this.dataSharingForm.id,
            investigation_dept_name: _this.dataSharingForm.investigation_dept_name,
            investigation_dept_code: _this.dataSharingForm.investigation_dept_code,
            note: _this.dataSharingForm.note,
            investigation_parameter: _this.dataSharingForm.investigation_parameter,
            division_code: _this.dataSharingForm.division_code,
            division_name: _this.dataSharingForm.division_name,
          }
          if (_this.dataSharingFormFlag === 'edit') {
            _this.tableDataData.content = _.map(_this.tableDataData.content, (item) => {
              return item.id == this.dataSharingId ? newValue : item
            })
            // $.extend(true, newValue, this.dataSharingForm);
            // this.$set(this.tableDataData.content, this.delIndex, newValue);
          } else {
            this.tableDataData.content.push(newValue)
          }

          this.dataSharingDialog = false
        } else {
          return false
        }
      })
    },
    // 数据共享编辑
    dataSharEdit(row) {
      // this.dataSharingFormFlag = 'edit'
      // this.dataSharingId = row.id
      // this.dataSharingForm = row
      // this.dataSharingDialog = true
      // let data = { code: row.division_code }
      // getOrgListNoAuth({
      //   pageSize: 1000,
      //   pageNumber: 0,
      //   divisionCode: data.code
      // }).then(res => {
      //   this.dataSharingOrganizationList = res.content.map(i => {
      //     return { label: i.name, value: i.tyshxydm }
      //   })
      // })
      // this.setDataSharing()
      this.dataShareDialogVisible = true
    },
    // 数据共享删除
    dataSharDelete(row) {
      this.tableDataData.content = _.filter(this.tableDataData.content, (item) => item.id != row.id)
    },
    // 人工协查
    getArtificial() {
      this.ArtificialForm.division_code = ''
      // this.ArtificialDialog = true
      this.assistDialogShow = true
    },
    // 删除
    artificialDelete(row, index) {
      this.tableDataArtificial.content.splice(index, 1)
    },
    // 编辑
    artificialEdit(row, index) {
      // this.ArtificialDialog = true
      this.assistDialogShow = true
      this.ArtificialIndex = index
      this.ArtificialIdFlag = 'edit'
      this.ArtificialForm = row
      if (Object.keys(this.form).length !== 0) {
        this.$refs.assistDialog.editInit(this.form)
      }
      // const data = { code: row.division_code }
      // getOrgListNoAuth({
      //   pageSize: 1000,
      //   pageNumber: 0,
      //   divisionCode: data.code,
      // }).then((res) => {
      //   this.organizationList = res.content.map((i) => {
      //     return { label: i.name, value: i.tyshxydm }
      //   })
      // })
    },
    // 确定
    ArtificialDialogConfirm() {
      const _this = this
      _this.$refs['ArtificialForm'].validate((valid) => {
        if (valid) {
          // console.log('_this.organizationList',_this.organizationList,_this.ArtificialForm.investigation_dept_code)
          _this.organizationList.forEach((item) => {
            if (item.credit_code === _this.ArtificialForm.investigation_dept_code) {
              _this.ArtificialForm.investigation_dept_name = item.name
            }
          })
          // const thisData = _this.organizationList.findIndex(i => i.value === _this.ArtificialForm.division_code)
          // _this.ArtificialForm.division_name = thisData.label;
          var newValue = {
            id: _this.ArtificialForm.id,
            investigation_dept_name: _this.ArtificialForm.investigation_dept_name,
            investigation_dept_code: _this.ArtificialForm.investigation_dept_code,
            note: _this.ArtificialForm.note,
            division_code: _this.ArtificialForm.division_code,
            division_name: _this.ArtificialForm.division_name,
            // assist_time_limit: _this.ArtificialForm.assist_time_limit
            assist_time_hour: _this.ArtificialForm.assist_time_hour,
            assist_time_minute: _this.ArtificialForm.assist_time_minute,
          }
          if (_this.ArtificialIdFlag === 'edit') {
            _this.$set(_this.tableDataArtificial.content, _this.ArtificialIndex, newValue)
          } else {
            _this.tableDataArtificial.content.push(newValue)
          }
          this.ArtificialDialog = false
        } else {
          return false
        }
      })
    },
    // 自行调查
    getInvestigation() {
      this.InvestigationDialog = true
      this.InvestigationFlag = 'add'
      if (this.$refs['InvestigationForm'] !== undefined) {
        this.$refs['InvestigationForm'].resetFields()
      }
    },
    // 删除
    SurveyDelete(row, index) {
      this.tableDataSurvey.content.splice(index, 1)
    },
    // 编辑
    SurveyEdit(row, index) {
      this.InvestigationDialog = true
      this.InvestigationIndex = index
      this.InvestigationFlag = 'edit'
      this.InvestigationForm.dept_cancel_description = row.dept_cancel_description
      this.InvestigationForm.id = row.id
    },
    // 确定
    InvestigationFormConfirm() {
      this.$refs['InvestigationForm'].validate((valid) => {
        if (valid) {
          var newValue = {
            dept_cancel_description: this.InvestigationForm.dept_cancel_description,
          }
          if (this.InvestigationFlag === 'edit') {
            newValue = {
              dept_cancel_description: this.InvestigationForm.dept_cancel_description,
              id: this.InvestigationForm.id,
            }
            this.$set(this.tableDataSurvey.content, this.InvestigationIndex, newValue)
          } else {
            this.tableDataSurvey.content.push(newValue)
          }
          this.InvestigationDialog = false
        } else {
          return false
        }
      })
    },
    // 其他
    getOther() {
      this.otherDialog = true
      this.otherFlag = 'add'
      if (this.$refs['otherForm'] !== undefined) {
        this.$refs['otherForm'].resetFields()
      }
    },
    // 删除
    otherDelete(row, index) {
      this.tableDataOther.content.splice(index, 1)
    },
    // 编辑
    otherEdit(row, index) {
      this.otherDialog = true
      this.otherIndex = index
      this.otherFlag = 'edit'
      this.otherForm.other_clear_description = row.other_clear_description
    },
    // 确定
    otherConfirm() {
      this.$refs['otherForm'].validate((valid) => {
        if (valid) {
          var newValue = {
            other_clear_description: this.otherForm.other_clear_description,
          }
          if (this.otherFlag === 'edit') {
            this.$set(this.tableDataOther.content, this.otherIndex, newValue)
          } else {
            this.tableDataOther.content.push(newValue)
          }
          this.otherDialog = false
        } else {
          return false
        }
      })
    },
    // 承诺
    getPromise() {
      this.promiseDialog = true
      this.promiseFlag = 'add'
      if (this.$refs['promiseForm'] !== undefined) {
        this.$refs['promiseForm'].resetFields()
      }
    },
    // 删除
    promiseDelete(row, index) {
      this.tableDataPromise.content.splice(index, 1)
    },
    // 编辑
    promiseEdit(row, index) {
      this.promiseDialog = true
      this.promiseIndex = index
      this.promiseFlag = 'edit'
      this.promiseForm.commit_book_description = row.commit_book_description
      this.promiseForm.commit_attachment_id = row.commit_attachment_id
    },
    // 确定
    promiseConfirm() {
      this.$refs['promiseForm'].validate((valid) => {
        if (valid) {
          var newValue = {
            commit_book_description: this.promiseForm.commit_book_description,
            commit_attachment_id: this.promiseForm.commit_attachment_id,
            // file: this.file,
            commit_attachment_name: this.promiseForm.commit_attachment_name,
          }
          if (this.promiseFlag === 'edit') {
            this.$set(this.tableDataPromise.content, this.promiseIndex, newValue)
          } else {
            this.tableDataPromise.content.push(newValue)
          }
          this.promiseDialog = false
        } else {
          return false
        }
      })
    },
    uploading() {
      this.promiseUploadingDialog = true
    },
    // 导入
    promiseimportFlie() {
      if (this.$refs.uploadDialog.input) {
        const name = this.$refs.uploadDialog.input
        this.promiseForm.commit_attachment_name = this.$refs.uploadDialog.input
        const file = this.$refs.uploadDialog.file.raw
        this.file = this.$refs.uploadDialog.file
        const fd = new FormData()
        fd.append('file', file)
        fd.append('name', name)
        getCommit_Attachment(fd)
          .then((res) => {
            if (res.data != null && res.meta.code === '200') {
              this.$message({
                message: '上传成功',
                type: 'success',
              })
              this.promiseForm.commit_attachment_id = res.data.sessionId
              this.promiseUploadingDialog = false
            } else {
              this.$message({
                message: res.meta.message,
                type: 'error',
              })
            }
          })
          .catch(() => {})
      } else {
        this.$message({
          message: '请选择文件，再导入！',
          type: 'warning',
        })
      }
    },
    // 下载模板
    goattachment_id(row) {
      if (this.$route.query.flagCatalog === 'add') {
        const data = row.file.raw || row.file
        const url = window.URL.createObjectURL(new Blob([data]))
        const link = document.createElement('a')
        link.style.display = 'none'
        link.href = url
        link.setAttribute('download', data.name)
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
      } else {
        var raw = window.atob(row.file_data)
        var uInt8Array = new Uint8Array(raw.length)
        for (var i = 0; i < raw.length; i++) {
          uInt8Array[i] = raw.charCodeAt(i)
        }
        const link = document.createElement('a')
        const blob = new Blob([uInt8Array], {
          type: 'application/vnd.ms-excel',
        })
        link.style.display = 'none'
        link.href = URL.createObjectURL(blob)
        link.setAttribute('download', row.commit_attachment_name)
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
      }
    },
    // 保存
    backPrev() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          console.log('this.tableDataElectronicCertification', this.tableDataElectronicCertification)
          if (this.tableDataArtificial.content.length != 0 || this.tableDataPromise.content.length != 0 || this.tableDataData.content.length != 0 || this.tableDataSurvey.content.length != 0 || this.tableDataElectronic.content.length != 0 || this.tableDataOther.content.length != 0 || this.tableDataElectronicCertification.content.length != 0) {
            const formData = Object.assign({}, this.form)
            const sendData = {
              ...formData, // 基本信息
              proof_catalog_artificial_list: this.tableDataArtificial.content, // 人工协查
              proof_catalog_clerk_commitment_list: this.tableDataPromise.content, // 告知承诺
              proof_catalog_data_shared_list: this.tableDataData.content, // 数据共享
              proof_catalog_dept_survey_list: this.tableDataSurvey.content, // 自行调查
              proof_catalog_license_relation_list: this.tableDataElectronic.content, // 电子证照
              proof_catalog_other_relation_list: this.tableDataOther.content, // 其他
              proof_catalog_license_item_relation: this.tableDataElectronicCertification.content.length !== 0 ? [this.tableDataElectronicCertification.content[0]] : [], // 电子证明
            }
            if (this.$route.query.flagCatalog == 'edit') {
              // 修改
              for (var key in sendData) {
                if (key.indexOf('_list') != -1) {
                  var arr = sendData.key
                  if (Array.isArray(sendData[key])) {
                    for (var i = 0; i < sendData[key].length; i++) {
                      sendData[key][i]['proof_catalog_id'] = this.$route.query.id
                    }
                  }
                }
              }
              getGetproofCatalogEdit(this.$route.query.id, sendData)
                .then((res) => {
                  if (res.data != null) {
                    this.$message({
                      message: '保存成功',
                      type: 'success',
                    })
                    this.$router.push({
                      name: 'certification_List_info',
                      query: {
                        id: res.data,
                      },
                    })
                  } else {
                    this.$message.error(res.meta.message)
                  }
                })
                .catch(() => {})
            } else {
              console.log('sendData', sendData)
              // 新增
              getGetproofCatalogCreate(sendData)
                .then((res) => {
                  if (res.data != null) {
                    this.$message({
                      message: '保存成功',
                      type: 'success',
                    })
                    this.$router.push({
                      name: 'certification_List_info',
                      query: {
                        id: res.data,
                      },
                    })
                  } else {
                    this.$message.error(res.meta.message)
                  }
                })
                .catch(() => {})
            }
          } else {
            this.$message({
              message: '请选择关联信息',
              type: 'warning',
            })
          }
        } else {
          return false
        }
      })
    },
    goToList() {
      if (this.$route.query.flagCatalog == 'add' || this.$route.query.flagCatalog == '') {
        this.$router.push({
          name: 'certification_List',
        })
      } else {
        // this.$router.go(-1)
        this.$router.push({
          name: 'certification_List',
        })
      }
    },
    dataShareCodeSon(v) {
      this.dataSharingForm.division_name = v
    },
    codeSon(v) {
      this.ArtificialForm.division_name = v
    },
    closeDialog() {
      this.licenseCatalogueDialog = false
    },
    closelicenseDialog() {
      this.licenseDialogVisible = false
    },
    closeDataShareDialog() {
      this.dataShareDialogVisible = false
    },
    closeAssistDialog() {
      this.assistDialogShow = false
    },
    // 关联电子证明目录打开弹窗
    relationElectronicProof() {
      console.log('relationElectronicProof')
      this.electronicCertificationDialogShow = true
      this.isRelationElectronicProof = true
    },
    closeElectronicCertificationDialog() {
      this.electronicCertificationDialogShow = false
      this.isRelationElectronicProof = false
    },
    // 获取部门协查弹窗内容
    getAssistData(data) {
      console.log('getAssistData', data)
      let item = {
        examine_level: data.examineLevel,
        investigation_dept_name: data.audit_r_temp_list[0].label, // 默认展示第一级协查部门
        note: data.assist_desc,
        proof: data.issue_p_license ? '' + (data.issue_p_license_way == 'SYSTEM_GENERATE' ? '系统生成' : '人工开具') + '，' + data.license_name : '不开具',
        audit_r_temp_list: data.audit_r_temp_list,
        issue_p_license: data.issue_p_license,
        issue_p_license_way: data.issue_p_license_way,
        assist_time_hour: data.assist_time_hour,
        assist_time_minute: data.assist_time_minute,
        license_name: data.license_name,
        license_code: data.license_code,
      }
      this.tableDataArtificial.content = [item]
      this.auditRTempList = data.audit_r_temp_list
      this.assistDialogShow = false
    },
    getElectronicCertificationSlectItem(data) {
      if (!this.isRelationElectronicProof) {
        this.tableDataElectronicCertification.content = []
        this.electronicCertificationDialogShow = false
        data.forEach((i) => {
          this.tableDataElectronicCertification.content.push({
            id: i.id,
            license_name: i.name,
            license_code: i.code,
            dept_name: i.industryDept,
          })
        })
      } else {
        console.log('setRelationElectronicProof')
        this.electronicCertificationDialogShow = false
        this.$refs.assistDialog.setRelationElectronicProof(data)
      }
    },

    // getElectronicCertificationSlectItem(data, electronicCertificationData) {
    //   this.tableDataElectronicCertification.content = []
    //   this.electronicCertificationDialogShow = false
    //   let audit_r_temp_list = []
    //   electronicCertificationData.auditList.forEach((e) => {
    //     let item = {
    //       audit_level: e.audit_level,
    //       audit_org_code: e.value,
    //       audit_org_name: e.label,
    //       audit_divi_code: e.division_code,
    //       audit_divi_name: e.division_name,
    //     }
    //     audit_r_temp_list.push(item)
    //   })
    //   data.forEach((i) => {
    //     this.tableDataElectronicCertification.content.push({
    //       id: i.id,
    //       license_name: i.name,
    //       license_code: i.code,
    //       dept_name: i.industryDept,
    //       audit_r_temp_list: audit_r_temp_list,
    //       assist_time_hour: electronicCertificationData.assist_time_hour,
    //       assist_time_minute: electronicCertificationData.assist_time_minute,
    //       assist_desc: electronicCertificationData.assist_desc,
    //     })
    //   })
    //   console.log('this.tableDataElectronicCertification.content', this.tableDataElectronicCertification.content)
    //   console.log('electronicCertificationData', electronicCertificationData)
    // },
    getDataShare(data) {
      this.dataShareDialogVisible = false
      data.config_json = JSON.stringify(data)
      console.log('getDataShare', data)
      this.tableDataData.content = [data]
    },
    getlicenseData(data) {
      console.log('getlicenseData', data)
      this.tableDataElectronic.content = []
      data.forEach((i) => {
        this.tableDataElectronic.content.push({
          id: i.code,
          license_name: i.name,
          license_code: i.code,
          dept_name: i.industryDept,
        })
      })
      console.log('this.tableDataElectronic.content', this.tableDataElectronic.content)
      this.licenseDialogVisible = false
    },
    getSlectItem(data) {
      // console.log(data)
      this.tableDataElectronic.content = []
      // this.form.item_material_list[this.licenseCatalogueIndex][this.licenseCatalogueName] = data[0]
      // this.form.item_material_list[this.licenseCatalogueIndex]['licenseCatalogueName'] = data[0].license_item_name
      // console.log(this.form.item_material_list)
      this.licenseCatalogueDialog = false
      // this.tableDataElectronic.content = data;
      data.forEach((i) => {
        this.tableDataElectronic.content.push({
          id: i.id,
          license_name: i.name,
          license_code: i.code,
          dept_name: i.industryDept,
        })
      })
    },
    // 选择区划
    setDivisionCodeAndName(data) {
      // console.log('data', data)
      this.ArtificialForm.division_code = data.code
      this.organizationList = []
      this.getOrganizationList(this.ArtificialForm.division_code)
    },
    // 获取实施机构
    getOrganizationList(id) {
      const data = {
        division_code: id,
        permission_code: 'catalog:proof_catalog:proof:list',
        scope: true,
      }
      getOrganizationList(data).then((res) => {
        if (res.meta.code === '200') {
          this.organizationList = res.data
        }
      })
    },
  },
}
</script>
<style scoped lang="scss">
.detail_add_edit {
  .el-tabs {
    position: relative;
  }

  .addIcon {
    position: absolute;
    right: 20px;
    bottom: 5px;
  }

  .el-select {
    display: block;
  }
}

.tip {
  text-align: left;

  i {
    color: #e6a23c;
  }
}

.info-wrap {
  display: flex;
  color: #333333;
  font-size: 20px;
  margin-top: 40px;

  // margin-bottom: 30px;
  img {
    margin-right: 10px;
  }
}

.item-add {
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  height: 64px;
  margin: 0 20px 0px;
  border-left: 1px dashed #01a463;
  border-right: 1px dashed #01a463;
  border-bottom: 1px dashed #01a463;
  color: #01a463;

  img {
    margin-right: 10px;
  }
}
</style>
<style>
.detail_add_edit .el-transfer-panel {
  width: 300px;
}
</style>
<style scoped>
.breadcrumbbtn {
  top: 4px;
}

.detail_add_edit /deep/.el-card__body {
  padding-bottom: 0;
}
</style>
