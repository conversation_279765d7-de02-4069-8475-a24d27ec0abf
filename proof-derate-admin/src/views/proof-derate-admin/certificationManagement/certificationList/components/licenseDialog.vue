<template>
  <div id="licenseDialog">
    <!-- 关联证明目录弹框 -->
    <el-dialog title="关联电子证照目录" :visible.sync="licenseDialogVisible" :width="dialogwidth" class="dialog" @close="closeDialog">
      <el-transfer
        :filterable="true"
        :titles="['待选', '已选']"
        filter-placeholder="请输入证明目录名称"
        v-model="value"
        :data="generateData"
        @change="selectionChange"
        class="transfer"
        ref="transfer"
      >
        <div slot="left-footer" class="transfer-input">
          <el-input v-model="selectForm.search_catalog_name" placeholder="请输入内容"></el-input>
          <el-button class="transfer-footer" size="small" @click="searchTransfer">查询</el-button>
        </div>
      </el-transfer>
      <div slot="footer" class="dialog-footer">
        <!-- <el-button type="primary" @click="selectFirm()">确 定</el-button> -->
        <el-button type="primary" @click="getData()">确 定</el-button>
        <el-button @click="closeDialog()">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getCatalogPage, getUnionCatalogPage } from '@/api/license/index'
export default {
  props: {
    licenseDialogVisible: {
      type: Boolean,
      default: false
    },
    selectedValue: {
      type: Array,
      default: []
    }
  },
  watch: {
    licenseDialogVisible: {
      handler(val) {
        console.log('licenseDialogVisible', val)
        if (val) {
          console.log('licenseDialogVisible', val)
          let time = null
          time = setTimeout(() => {
            this.watchLeftAllChecked()
            this.watchRightAllChecked()
            // const dom = $('div .is-filterable')[0]
            const dom = this.$refs.transfer.$el.firstChild.childNodes[1].childNodes[1]
            dom.addEventListener('scroll', () => {
              if (dom.scrollTop + dom.clientHeight === dom.scrollHeight) {
                console.log('到底了')
                // this.tableDataSelect.pageSize = 10
                // this.tableDataSelect.currentPage++
                // this.query(this.tableDataSelect.currentPage)
              }
            })
          }, 0)
          // time = null
        }
      }
    }
  },
  data() {
    return {
      //   selectDialog: false,
      generateData: [],
      value: [],
      is1366: false,
      dialogwidth: '',
      selectForm: {
        proof_catalog_name: '',
        search_catalog_name: '',
        search_union_catalog_name: '',
        page_number: 1,
        page_size: 10
      },
      getSelectList: []
    }
  },

  mounted() {
    console.log('licenseDialogVisible', this.licenseDialogVisible)
    console.log('this.selectedValue', this.selectedValue)
    if (this.selectedValue.length !== 0) {
      this.selectedValue.forEach(i => {
        this.getSelectList.push({ label: i.license_name, key: i.license_code, code: i.license_code, name: i.license_name })
        this.value.push(i.license_code)
      })
    }
    this.getCatalogPage()
    this.screenWidth()
    window.addEventListener('resize', () => {
      this.screenWidth()
    })
    // console.log('licenseDialogVisible', val)
    let time = null
    time = setTimeout(() => {
      this.watchLeftAllChecked()
      this.watchRightAllChecked()
      // const dom = $('div .is-filterable')[0]
      const dom = this.$refs.transfer.$el.firstChild.childNodes[1].childNodes[1]
      console.log('dom', dom)
      dom.addEventListener('scroll', () => {
        if (dom.scrollTop + dom.clientHeight === dom.scrollHeight) {
          console.log('到底了')
          this.selectForm.page_size = 10
          this.selectForm.page_number++
          this.getCatalogPage()
        }
      })
    }, 0)
    // time = null
  },

  methods: {
    // 监听左侧全选是否选中
    watchLeftAllChecked() {
      this.leftscrollAllCheckedWatch && this.leftscrollAllCheckedWatch()
      const transferVm = this.$refs.transfer
      transferVm.$refs.leftPanel.handleAllCheckedChange = () => {
        return false
      }
    },
    // 监听右侧全选是否选中
    watchRightAllChecked() {
      this.rightscrollAllCheckedWatch && this.rightscrollAllCheckedWatch()
      const transferVm = this.$refs.transfer
      transferVm.$refs.rightPanel.handleAllCheckedChange = () => {
        return false
      }
    },
    selectionChange(val) {
      this.getSelectList = []
      console.log('selectionChange', val, this.generateData)
      // console.log('this.value', this.value)
      // this.selectList = []
      val.forEach(i => {
        // console.log(this.generateData.filter(item => item.key === i))
        // const item = {
        //   proof_catalog_id: i
        // }
        // this.selectList.push(item)
        this.getSelectList.push(this.generateData.filter(item => item.id === i)[0])
        // i.relevance = i.name
      })
    },
    searchTransfer() {
      this.generateData = []
      this.selectForm.page_number = 1
      this.getCatalogPage()
    },
    getData() {
      const data = []
      this.generateData.forEach(e => {
        if (this.value.indexOf(e.code) !== -1) {
          data.push(e)
        }
      })
      console.log('this.generateData', this.generateData)
      console.log('this.value', this.value)
      console.log('data', data)
      this.$emit('getlicenseData', data)
    },
    closeDialog() {
      this.$emit('closelicenseDialog')
    },
    screenWidth() {
      if (document.documentElement.clientWidth > 1580) {
        this.is1366 = 1
        this.dialogwidth = '40%'
      } else if (document.documentElement.clientWidth < 1580 && document.documentElement.clientWidth > 900) {
        this.is1366 = 2
        this.dialogwidth = '60%'
      } else if (document.documentElement.clientWidth < 900 && document.documentElement.clientWidth > 600) {
        this.is1366 = 3
        this.dialogwidth = '60%'
      } else if (document.documentElement.clientWidth < 600) {
        this.is1366 = 4
        this.dialogwidth = '80%'
      }
      console.log('this.is1366', this.is1366)
    },
    getCatalogPage() {
      getCatalogPage(this.selectForm)
        .then(res => {
          //   this.tableData.loading = false
          // console.log('res', res)
          if (res.meta.code === '200') {
            // { label: e.name, key: e.id, pinyin: e.name, proof_catalog_id: e.id }
            res.data.content.forEach(e => {
              e.id = e.code
              e.key = e.id
              e.label = e.name
              e.license_code = e.code
              e.license_name = e.name
              this.generateData.push(e)
            })

            console.log('this.getSelectList', this.getSelectList)
            this.getSelectList.forEach(e0 => {
              this.generateData.push(e0)
            })
            this.generateData = _.uniqBy(this.generateData, 'key')

            console.log('this.generateData', this.generateData)
          }
        })
        .catch(err => {})
    }
  }
}
</script>

<style lang="scss" scoped>
.transfer ::v-deep .el-button--primary.is-disabled {
  display: block;
}
.transfer ::v-deep .el-button + .el-button {
  margin-left: 0;
}
.transfer ::v-deep .el-transfer-panel__header .el-checkbox__input {
  display: none;
}
.transfer ::v-deep .el-transfer-panel {
  width: 278px;
}
.transfer ::v-deep .el-transfer-panel__body {
  width: 264px;
  // height: 214px;
}
.transfer ::v-deep .el-transfer-panel .el-transfer-panel__footer {
  top: 50px;
  bottom: 0;
  width: 0%;
  left: initial;
  right: 251px;
  display: inline-block;
}
.transfer-input {
  display: flex;
  width: 230px;
  .transfer-footer {
    margin-left: 5px;
  }
}
.transfer ::v-deep .el-transfer-panel:first-child .el-transfer-panel__filter {
  width: 75%;
  height: 30px;
}

.transfer ::v-deep .el-transfer-panel:first-child .el-transfer-panel__filter input {
  display: none;
}

.transfer ::v-deep .el-transfer-panel__filter .el-input__icon {
  display: none;
}
.transfer ::v-deep .el-transfer-panel .el-transfer-panel__header .el-checkbox .el-checkbox__label span {
  display: none;
}
.transfer ::v-deep .el-transfer-panel__list {
  overflow-y: scroll;
}
</style>