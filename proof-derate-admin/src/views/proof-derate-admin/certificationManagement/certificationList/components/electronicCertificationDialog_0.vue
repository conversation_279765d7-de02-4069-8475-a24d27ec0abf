<template>
  <div id="licenseCatalogueDialog">
    <el-dialog title="选择证明" :visible.sync="electronicCertificationDialogShow" width="50%" @open="open" @close="closeDialog">
      <div class="licenseCatalogueDialog">
        <el-form ref="form1" :model="licenseCatalogueform" label-width="100px" class="el-check-form">
          <el-row :gutter="24" justify="center" type="flex">
            <el-col :span="18">
              <el-form-item label="证明目录名称">
                <el-input v-model="licenseCatalogueform.search_catalog_name" clearable placeholder="请输入证明目录名称" />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-button type="primary" @click="query()">查询</el-button>
            </el-col>
          </el-row>
        </el-form>
        <span style="color: #888">
          共
          <span class="text-red">{{tableData.total}}</span>条符合查询条件
          <span v-if="tableData.content.length!=0">，以下是第1至第{{tableData.content.length}}项</span>
        </span>
        <custom-table
          ref="table1"
          class="custom-table"
          :is-card-type="false"
          :table-data="tableData"
          :table-header="tableHeader"
          :stripe="false"
          :table-tools="tableTools"
          style="margin-top: 10px"
          @query="query"
          @selection-change="selectionChange1"
          @refresh="query(1)"
        ></custom-table>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="getElectronicCertificationSlectItem()">确定</el-button>
        <el-button @click="closeDialog()">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { proofCatalogPage } from '@/api/assistInvestigate'
import { getIndustryDept } from '@/api/common/license'
// import { getCatalogPage, getUnionCatalogPage } from '@/api/license/index'
import { getProofCatalogPage } from '@/api/license/index'
import CustomTable from '@/components/Element/Table'
export default {
  data() {
    return {
      licenseCatalogueform: {
        // proof_catalog_name: '',
        // page_direction: 'DESC',
        search_catalog_name: '',
        // search_union_catalog_name: '',
        page_number: 1,
        page_size: 10
      },
      licenseForm: {
        keyword: ''
      },
      cardName: 'first',
      tableData: {
        content: [], // 表格数据

        loading: false, // 控制表格加载效果
        total: 0, // 分页查询时的总数，去掉该属性，不显示分页条
        currentPage: 1, // 当前页码
        pageSize: 10,
        isShowIndex: false,
        maxHeight: '290px',
        multiple: false,
        pageDirection: 'desc',
        isShowSelection: true, // 是否显示多选框，默认false multiple 需为falase
        reserveSelection: false
      },
      tableHeader: [
        {
          label: '证明目录名称',
          prop: 'name',
          minWidth: '100px'
        }
        // {
        //   label: '证明目录名称',
        //   prop: 'name',
        //   minWidth: '100px'
        // }
        //  { label: '归档', prop: 'investigationStatus',slot: 'investigationStatus', width: '120px', fixed: 'right'}
      ],
      tableTools: [],
      numberOfElements: '',

      tableData1: {
        content: [], // 表格数据

        loading: false, // 控制表格加载效果
        total: 0, // 分页查询时的总数，去掉该属性，不显示分页条
        currentPage: 1, // 当前页码
        pageSize: 10,
        isShowIndex: false,
        maxHeight: '290px',
        multiple: false,
        pageDirection: 'desc',
        isShowSelection: true, // 是否显示多选框，默认false multiple 需为falase
        reserveSelection: false
      },
      tableHeader1: [
        {
          label: '证明目录名称',
          prop: 'name',
          minWidth: '100px'
        },
        {
          label: '所含证照',
          prop: 'contains_catalog',
          minWidth: '100px'
        }
        //  { label: '归档', prop: 'investigationStatus',slot: 'investigationStatus', width: '120px', fixed: 'right'}
      ],
      tableTools1: [],
      numberOfElements1: '',
      selectItem: ''
    }
  },
  props: {
    electronicCertificationDialogShow: {
      type: Boolean,
      default: false
    }
  },
  components: {
    CustomTable
  },
  mounted() {
    // this.proofCatalogPage()
    // this.getIndustryDept()
    // this.getCatalogPage()
    this.getProofCatalogPage()
  },

  methods: {
    open() {},
    query() {
      // this.getCatalogPage()
      // this.getUnionCatalogPage()
      this.getProofCatalogPage()
    },

    getProofCatalogPage() {
      this.licenseCatalogueform.page_number = this.tableData.currentPage
      this.licenseCatalogueform.page_size = this.tableData.pageSize
      this.tableData.loading = true
      this.tableData.content = []
      getProofCatalogPage(this.licenseCatalogueform)
        .then(res => {
          console.log('getProofCatalogPage',res)
          this.tableData.loading = false
          if (res.meta.code === '200') {
            res.data.content.forEach(e => {
              e.id = e.code
            })
            this.tableData.content = res.data.content
            this.tableData.total = Number(res.data.total_elements)
            this.numberOfElements = Number(res.data.total_elements)

            // this.tableData1.content = res.content
            // this.tableData1.total = res.numberOfElements
            // this.numberOfElements1 = res.numberOfElements
          }
        })
        .catch(err => {
          this.tableData.loading = false
        })
    },
    getUnionCatalogPage() {
      this.licenseCatalogueform.page_number = this.tableData1.currentPage
      this.licenseCatalogueform.page_size = this.tableData1.pageSize
      this.tableData1.loading = true
      // this.licenseCatalogueform.search_union_catalog_name = this.licenseCatalogueform.search_catalog_name
      // const data = {
      //   search_union_catalog_name: this.licenseCatalogueform.search_catalog_name,
      //   page_number: this.tableData1.currentPage,
      //   page_size: this.tableData1.pageSize
      // }
      getUnionCatalogPage(this.licenseCatalogueform)
        .then(res => {
          this.tableData1.loading = false
          if (res != null) {
            res.content.forEach(e => {
              e.id = e.code
            })
            // this.tableData.content = res.content
            // this.tableData.total = res.numberOfElements
            // this.numberOfElements = res.numberOfElements

            this.tableData1.content = res.content
            this.tableData1.total = res.numberOfElements
            this.numberOfElements1 = res.numberOfElements
          }
        })
        .catch(err => {
          this.tableData1.loading = false
        })
    },
    getIndustryDept() {
      getIndustryDept({ license_item_name: this.licenseForm.keyword }).then(res => {
        if (res.meta.code === '200' && res.data != null) {
          res.data.forEach(e => {
            e.id = e.basic_code
          })
          this.tableData.content = res.data
        }
      })
    },
    proofCatalogPage() {
      this.licenseCatalogueform.page_number = this.tableData.currentPage
      this.licenseCatalogueform.page_size = this.tableData.pageSize
      this.tableData.loading = true
      proofCatalogPage(this.licenseCatalogueform)
        .then(res => {
          this.tableData.loading = false
          if (res.meta.code === '200' && res.data != null) {
            this.tableData.content = res.data.content
            this.tableData.total = res.data.totalElements
            this.numberOfElements = res.data.numberOfElements

            this.tableData1.content = res.data.content
            this.tableData1.total = res.data.totalElements
            this.numberOfElements1 = res.data.numberOfElements
          }
        })
        .catch(err => {
          this.tableData.loading = false
        })
    },
    getElectronicCertificationSlectItem() {
      this.$emit('getElectronicCertificationSlectItem', this.selectItem)
    },
    selectionChange1(data) {
      // this.$refs.table2.radioValue = ''
      //   console.log(data)
      this.selectItem = data
    },
    selectionChange2(data) {
      this.$refs.table1.radioValue = ''
      //   console.log(data)
      this.selectItem = data
    },
    closeDialog() {
      this.$emit('closeDialog')
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
