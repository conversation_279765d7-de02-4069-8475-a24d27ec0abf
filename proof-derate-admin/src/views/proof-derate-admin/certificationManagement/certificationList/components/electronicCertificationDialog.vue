<template>
  <div id="licenseCatalogueDialog">
    <el-dialog title="选择证明" :visible.sync="electronicCertificationDialogShow" width="60%" @open="open" @close="closeDialog">
      <div class="licenseCatalogueDialog">
        <div class="el-tabs">
          <span class="margin-left-10 info-wrap">
            <img :src="arrow" alt />
            <span class="info-title">关联电子证明目录</span>
          </span>
        </div>
        <el-form ref="form1" :model="licenseCatalogueform" label-width="100px" class="el-check-form">
          <el-row :gutter="24" justify="center" type="flex">
            <el-col :span="18">
              <el-form-item label="证明目录名称">
                <el-input v-model="licenseCatalogueform.search_catalog_name" clearable placeholder="请输入证明目录名称" />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-button type="primary" @click="query()">查询</el-button>
            </el-col>
          </el-row>
        </el-form>
        <span style="color: #888">
          共
          <span class="text-red">{{ tableData.total }}</span
          >条符合查询条件
          <span v-if="tableData.content.length != 0">，以下是第1至第{{ tableData.content.length }}项</span>
        </span>
        <custom-table ref="table1" class="custom-table" :is-card-type="false" :table-data="tableData" :table-header="tableHeader" :stripe="false" :table-tools="tableTools" style="margin-top: 10px" @query="query" @selection-change="selectionChange1" @refresh="query(1)"></custom-table>
      </div>
      <div class="el-tabs">
        <span class="margin-left-10 info-wrap">
          <img :src="arrow" alt />
          <span class="info-title">实施配置</span>
        </span>
      </div>
      <div class="implement-configuration">
        <el-form ref="ArtificialForm" :model="ArtificialForm" label-width="160px" @submit.native.prevent>
          <el-row type="flex">
            <el-col :xs="24" :sm="24" :md="24" :lg="24">
              <el-form-item label="审核环节数" prop="examineLevel" :rules="[{ required: true, message: '请选择审核环节数', trigger: 'blur' }]">
                <el-select v-model="ArtificialForm.examineLevel" placeholder="请选择" style="width: 100%" @change="changeExamineLevel">
                  <el-option v-for="item in examineList" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row type="flex">
            <el-col :xs="24" :sm="12" :md="12" :lg="12" v-if="examineLevel1Show">
              <el-form-item label="第一级审核实施区划" prop="audit_org_code1" :rules="[{ required: true, message: '请选择第一级审核实施区划', trigger: 'blur' }]">
                <AdministrativeDivisionCascader ref="AdministrativeDivisionSelect" :divisionCodeLsit="audit_org_code1" :permission-code="'catalog:proof_catalog:proof:list'" @setDivisionCodeAndName="setDivisionCodeAndName1" :optionProps="optionProps" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="12" :lg="12" v-if="examineLevel1Show">
              <el-form-item label="第一级审核部门" prop="audit_divi_code1" :show-message="true" :rules="[{ required: true, message: '请选择第一级审核部门', trigger: 'blur' }]">
                <el-cascader style="width: 100%" v-model="ArtificialForm.audit_divi_code1" :options="organizationLevel1List" :props="examineLevelOptionProps" clearable filterable collapse-tags></el-cascader>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row type="flex">
            <el-col :xs="24" :sm="12" :md="12" :lg="12" v-if="examineLevel2Show">
              <el-form-item label="第二级审核实施区划" prop="audit_org_code2" :rules="[{ required: true, message: '请选择第二级审核实施区划', trigger: 'blur' }]">
                <AdministrativeDivisionCascader :key="audit_org_code2" ref="AdministrativeDivisionSelect" :divisionCodeLsit="audit_org_code2" :permission-code="'catalog:proof_catalog:proof:list'" @setDivisionCodeAndName="setDivisionCodeAndName2" :optionProps="optionProps" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="12" :lg="12" v-if="examineLevel2Show">
              <el-form-item label="第二级审核部门" prop="audit_divi_code2" :show-message="true" :rules="[{ required: true, message: '请选择第二级审核部门', trigger: 'blur' }]">
                <el-cascader style="width: 100%" v-model="ArtificialForm.audit_divi_code2" :options="organizationLevel2List" :props="examineLevelOptionProps" clearable filterable collapse-tags></el-cascader>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row type="flex">
            <el-col :xs="24" :sm="12" :md="12" :lg="12" v-if="examineLevel3Show">
              <el-form-item label="第三级审核实施区划" prop="audit_org_code3" :rules="[{ required: true, message: '请选择第三级审核实施区划', trigger: 'blur' }]">
                <AdministrativeDivisionCascader ref="AdministrativeDivisionSelect" :divisionCodeLsit="audit_org_code3" :permission-code="'catalog:proof_catalog:proof:list'" @setDivisionCodeAndName="setDivisionCodeAndName3" :optionProps="optionProps" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="12" :lg="12" v-if="examineLevel3Show">
              <el-form-item label="第三级审核部门" prop="audit_divi_code3" :show-message="true" :rules="[{ required: true, message: '请选择第三级审核部门', trigger: 'blur' }]">
                <el-cascader style="width: 100%" v-model="ArtificialForm.audit_divi_code3" :options="organizationLevel3List" :props="examineLevelOptionProps" clearable filterable collapse-tags></el-cascader>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row type="flex">
            <el-col :xs="24" :sm="12" :md="12" :lg="12" v-if="examineLevel4Show">
              <el-form-item label="第四级审核实施区划" prop="audit_org_code4" :rules="[{ required: true, message: '请选择第四级审核实施区划', trigger: 'blur' }]">
                <AdministrativeDivisionCascader ref="AdministrativeDivisionSelect" :divisionCodeLsit="audit_org_code4" :permission-code="'catalog:proof_catalog:proof:list'" @setDivisionCodeAndName="setDivisionCodeAndName4" :optionProps="optionProps" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="12" :lg="12" v-if="examineLevel4Show">
              <el-form-item label="第四级审核部门" prop="audit_divi_code4" :show-message="true" :rules="[{ required: true, message: '请选择第四级审核部门', trigger: 'blur' }]">
                <el-cascader style="width: 100%" v-model="ArtificialForm.audit_divi_code4" :options="organizationLevel4List" :props="examineLevelOptionProps" clearable filterable collapse-tags></el-cascader>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row type="flex">
            <el-col :xs="24" :sm="12" :md="12" :lg="12" v-if="examineLevel5Show">
              <el-form-item label="第五级审核实施区划" prop="audit_org_code5" :rules="[{ required: true, message: '请选择第五级审核实施区划', trigger: 'blur' }]">
                <AdministrativeDivisionCascader ref="AdministrativeDivisionSelect" :divisionCodeLsit="audit_org_code5" :permission-code="'catalog:proof_catalog:proof:list'" @setDivisionCodeAndName="setDivisionCodeAndName5" :optionProps="optionProps" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="12" :lg="12" v-if="examineLevel5Show">
              <el-form-item label="第五级审核部门" prop="audit_divi_code5" :show-message="true" :rules="[{ required: true, message: '请选择第五级审核部门', trigger: 'blur' }]">
                <el-cascader style="width: 100%" v-model="ArtificialForm.audit_divi_code5" :options="organizationLevel4List" :props="examineLevelOptionProps" clearable filterable collapse-tags></el-cascader>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :xs="24" :sm="12" :md="12" :lg="24">
              <!-- prop="assist_desc"  :rules="[{ required: true, message: '请填写部门协查说明', trigger: 'blur' }]"-->
              <el-form-item label="部门协查说明">
                <el-input v-model="ArtificialForm.assist_desc" clearable placeholder="请填写部门协查说明" type="textarea" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :xs="24" :sm="12" :md="12" :lg="8">
              <!-- prop="assist_time_hour" :rules="[{ required: true, message: '请填写协查时限', trigger: 'blur' },
              { type: 'number', message: '协查时限必须为数字值' }
              ]"-->
              <el-form-item label="协查时限">
                <el-input v-model.number="ArtificialForm.assist_time_hour" placeholder="请填写协查时限">
                  <template slot="append">小时</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :xs="12" :sm="12" :md="12" :lg="6">
              <!-- :rules="[{ required: true, message: '请填写协查时限', trigger: 'blur' },
              { type: 'number', message: '协查时限必须为数字值' }
              ]" -->
              <el-form-item label label-width="10px" prop="assist_time_minute">
                <el-input v-model.number="ArtificialForm.assist_time_minute" placeholder="请填写协查时限">
                  <template slot="append">分钟</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :xs="10" :sm="10" :md="10" :lg="10">
              <el-form-item label label-width="10px">
                <span>注意：协查时限在法定工作日正常上班时间时间段内予以计算。</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row> </el-row>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="getElectronicCertificationSlectItem()">确定</el-button>
        <el-button @click="closeDialog()">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { proofCatalogPage } from '@/api/assistInvestigate'
import { getIndustryDept } from '@/api/common/license'
// import { getCatalogPage, getUnionCatalogPage } from '@/api/license/index'
import { getProofCatalogPage } from '@/api/license/index'
import CustomTable from '@/components/Element/Table'
import { getOrgTreeList } from '@/api/common/dict'

import { Level } from 'chalk'
export default {
  data() {
    return {
      licenseCatalogueform: {
        // proof_catalog_name: '',
        // page_direction: 'DESC',
        search_catalog_name: '',
        // search_union_catalog_name: '',
        page_number: 1,
        page_size: 10,
      },
      licenseForm: {
        keyword: '',
      },
      cardName: 'first',
      tableData: {
        content: [], // 表格数据

        loading: false, // 控制表格加载效果
        total: 0, // 分页查询时的总数，去掉该属性，不显示分页条
        currentPage: 1, // 当前页码
        pageSize: 10,
        isShowIndex: false,
        maxHeight: '290px',
        multiple: false,
        pageDirection: 'desc',
        isShowSelection: true, // 是否显示多选框，默认false multiple 需为falase
        reserveSelection: false,
      },
      tableHeader: [
        {
          label: '证明目录名称',
          prop: 'name',
          minWidth: '100px',
        },
        // {
        //   label: '证明目录名称',
        //   prop: 'name',
        //   minWidth: '100px'
        // }
        //  { label: '归档', prop: 'investigationStatus',slot: 'investigationStatus', width: '120px', fixed: 'right'}
      ],
      tableTools: [],
      numberOfElements: '',

      tableData1: {
        content: [], // 表格数据

        loading: false, // 控制表格加载效果
        total: 0, // 分页查询时的总数，去掉该属性，不显示分页条
        currentPage: 1, // 当前页码
        pageSize: 10,
        isShowIndex: false,
        maxHeight: '290px',
        multiple: false,
        pageDirection: 'desc',
        isShowSelection: true, // 是否显示多选框，默认false multiple 需为falase
        reserveSelection: false,
      },
      tableHeader1: [
        {
          label: '证明目录名称',
          prop: 'name',
          minWidth: '100px',
        },
        {
          label: '所含证照',
          prop: 'contains_catalog',
          minWidth: '100px',
        },
        //  { label: '归档', prop: 'investigationStatus',slot: 'investigationStatus', width: '120px', fixed: 'right'}
      ],
      tableTools1: [],
      numberOfElements1: '',
      selectItem: '',
      ArtificialForm: {
        id: '',
        investigation_dept_name: '',
        investigation_dept_code: '',
        assist_desc: '',
        division_code: '',
        division_name: '',
        assist_time_hour: '',
        assist_time_minute: '',
        examineLevel: '1',
        audit_org_code1: '',
        audit_org_name1: '',
        audit_divi_code1: [],
        audit_divi_name1: '',
        audit_org_code2: '',
        audit_org_name2: '',
        audit_divi_code2: [],
        audit_divi_name2: '',
        audit_org_code3: '',
        audit_org_name3: '',
        audit_divi_code3: [],
        audit_divi_name3: '',
        audit_org_code4: '',
        audit_org_name4: '',
        audit_divi_code4: [],
        audit_divi_name4: '',
        audit_org_code5: '',
        audit_org_name5: '',
        audit_divi_code5: [],
        audit_divi_name5: '',
        // assist_time_limit: '' //协查时限
      },
      audit_org_code1: [],
      audit_org_code2: [],
      audit_org_code3: [],
      audit_org_code4: [],
      audit_org_code5: [],
      organizationList: [],
      examineList: [
        {
          label: '一级审核',
          value: '1',
        },
        {
          label: '二级审核',
          value: '2',
        },
        {
          label: '三级审核',
          value: '3',
        },
        {
          label: '四级审核',
          value: '4',
        },
        {
          label: '五级审核',
          value: '5',
        },
      ],
      examineLevel1Show: false,
      examineLevel2Show: false,
      examineLevel3Show: false,
      examineLevel4Show: false,
      examineLevel5Show: false,
      organizationLevel1List: [], // 第一审核部门
      organizationLevel2List: [], // 第二审核部门
      organizationLevel3List: [], // 第三审核部门
      organizationLevel4List: [], // 第四审核部门
      organizationLevel5List: [], // 第五审核部门
      optionProps: {
        value: 'value',
        label: 'label',
        children: 'children',
        checkStrictly: true,
        expandTrigger: 'hover',
        emitPath: false,
        multiple: true, // 是否多选
      },
      examineLevelOptionProps: {
        value: 'value',
        label: 'label',
        children: 'sup_list',
        checkStrictly: true,
        expandTrigger: 'hover',
        multiple: true, // 是否多选
      },
      arrow: require('@/assets/proof-derate-admin-images/arrow.png'),
    }
  },
  props: {
    electronicCertificationDialogShow: {
      type: Boolean,
      default: false,
    },
  },
  components: {
    CustomTable,
    AdministrativeDivisionCascader: () => import('@/components/AdministrativeDivisionCascader/index2'),
  },
  mounted() {
    // this.proofCatalogPage()
    // this.getIndustryDept()
    // this.getCatalogPage()
    this.getProofCatalogPage()
    if (this.$route.query.flagCatalog === 'add') {
      this.changeExamineLevel('1')
    }
  },

  methods: {
    open() {},
    editInit(data) {
      console.log('editInit', data)
      let examineLevel = []
      this.audit_org_code1 = []
      this.audit_org_code2 = []
      this.audit_org_code3 = []
      this.audit_org_code4 = []
      this.audit_org_code5 = []
      let audit_org_code1SetData1 = []
      let audit_org_code1SetData2 = []
      let audit_org_code1SetData3 = []
      let audit_org_code1SetData4 = []
      let audit_org_code1SetData5 = []
      data.proof_catalog_license_item_relation[0].audit_r_temp_list.forEach((e) => {
        examineLevel.push(e.audit_level)
        let auditOrgItem = {
          code: e.audit_divi_code,
          name: e.audit_divi_name,
        }
        if (e.audit_level === 1) {
          console.log('e.audit_divi_code', e.audit_divi_code)
          this.audit_org_code1.push(e.audit_divi_code)
          audit_org_code1SetData1.push(auditOrgItem)
          this.ArtificialForm.audit_divi_code1.push(e.audit_org_code)
        }
        if (e.audit_level === 2) {
          this.audit_org_code2.push(e.audit_divi_code)
          audit_org_code1SetData2.push(auditOrgItem)
          this.ArtificialForm.audit_divi_code2.push(e.audit_org_code)
        }
        if (e.audit_level === 3) {
          console.log('e.audit_level', e.audit_level)
          this.audit_org_code3.push(e.audit_divi_code)
          audit_org_code1SetData3.push(auditOrgItem)
          this.ArtificialForm.audit_divi_code3.push(e.audit_org_code)
        }
        if (e.audit_level === 4) {
          this.audit_org_code4.push(e.audit_divi_code)
          audit_org_code1SetData4.push(auditOrgItem)
          this.ArtificialForm.audit_divi_code4.push(e.audit_org_code)
        }
        if (e.audit_level === 5) {
          this.audit_org_code5.push(e.audit_divi_code)
          audit_org_code1SetData5.push(auditOrgItem)
          this.ArtificialForm.audit_divi_code5.push(e.audit_org_code)
        }
      })
      console.log('this.audit_org_code1', this.audit_org_code1)
      console.log('this.audit_org_code2', this.audit_org_code2)
      console.log('this.audit_org_code3', this.audit_org_code3)
      // this.audit_org_code1 = this.audit_org_code1.join(',')
      // this.audit_org_code12 = this.audit_org_code2.join(',')
      if (audit_org_code1SetData1.length != 0) {
        audit_org_code1SetData1 = _.uniqWith(audit_org_code1SetData1, _.isEqual)
        console.log('audit_org_code1SetData1', audit_org_code1SetData1)
        this.setDivisionCodeAndName1(audit_org_code1SetData1)
      }
      if (audit_org_code1SetData2.length != 0) {
        audit_org_code1SetData2 = _.uniqWith(audit_org_code1SetData2, _.isEqual)
        this.setDivisionCodeAndName2(audit_org_code1SetData2)
      }
      if (audit_org_code1SetData3.length != 0) {
        audit_org_code1SetData3 = _.uniqWith(audit_org_code1SetData3, _.isEqual)
        this.setDivisionCodeAndName3(audit_org_code1SetData3)
      }
      if (audit_org_code1SetData4.length != 0) {
        audit_org_code1SetData4 = _.uniqWith(audit_org_code1SetData4, _.isEqual)
        this.setDivisionCodeAndName4(audit_org_code1SetData4)
      }
      if (audit_org_code1SetData5.length != 0) {
        audit_org_code1SetData5 = _.uniqWith(audit_org_code1SetData5, _.isEqual)
        this.setDivisionCodeAndName5(audit_org_code1SetData5)
      }

      this.ArtificialForm.examineLevel = Math.max(...examineLevel) + ''

      this.ArtificialForm.assist_desc = data.proof_catalog_license_item_relation[0].assist_desc
      this.ArtificialForm.assist_time_hour = data.proof_catalog_license_item_relation[0].assist_time_hour
      this.ArtificialForm.assist_time_minute = data.proof_catalog_license_item_relation[0].assist_time_minute

      this.changeExamineLevel(this.ArtificialForm.examineLevel)
      console.log('this.ArtificialForm.examineLevel', this.ArtificialForm.examineLevel)
      this.ArtificialForm.auditList = []
      this.licenseCatalogueform.search_catalog_name = data.proof_catalog_license_item_relation[0].license_name
      this.query()
    },
    setDivisionCodeAndName1(data) {
      console.log('setDivisionCodeAndName', data, this.audit_org_code1)
      this.getOrganizationLevelList('1', data)
      this.ArtificialForm.audit_org_code1 = data
    },
    setDivisionCodeAndName2(data) {
      console.log('setDivisionCodeAndName', data)
      this.getOrganizationLevelList('2', data)
      this.ArtificialForm.audit_org_code2 = data
    },
    setDivisionCodeAndName3(data) {
      console.log('setDivisionCodeAndName', data)
      this.getOrganizationLevelList('3', data)
      this.ArtificialForm.audit_org_code3 = data
    },
    setDivisionCodeAndName4(data) {
      console.log('setDivisionCodeAndName', data)
      this.getOrganizationLevelList('4', data)
      this.ArtificialForm.audit_org_code4 = data
    },
    setDivisionCodeAndName5(data) {
      console.log('setDivisionCodeAndName', data)
      this.getOrganizationLevelList('5', data)
      this.ArtificialForm.audit_org_code5 = data
    },
    getOrganizationLevelList(index, data) {
      let divisionCodeList = []
      data.forEach((e) => {
        divisionCodeList.push(e.code)
      })
      getOrgTreeList({ division_code: divisionCodeList.join(',') }).then((res) => {
        // console.log('getOrgTreeList', res)
        if (res.meta.code === '200' && res.data !== null) {
          this[`organizationLevel` + index + `List`] = res.data
          console.log('this[`organizationLevel`+index+`List`]', this[`organizationLevel` + index + `List`])
        }
      })
    },
    changeExamineLevel(data) {
      console.log('changeExamineLevel', data)
      // this.examineLevel = data
      if (data === '1') {
        this.examineLevel1Show = true
        this.examineLevel2Show = false
        this.examineLevel3Show = false
        this.examineLevel4Show = false
        this.examineLevel5Show = false
      } else if (data === '2') {
        this.examineLevel1Show = true
        this.examineLevel2Show = true
        this.examineLevel3Show = false
        this.examineLevel4Show = false
        this.examineLevel5Show = false
      } else if (data === '3') {
        this.examineLevel1Show = true
        this.examineLevel2Show = true
        this.examineLevel3Show = true
        this.examineLevel4Show = false
        this.examineLevel5Show = false
      } else if (data === '4') {
        this.examineLevel1Show = true
        this.examineLevel2Show = true
        this.examineLevel3Show = true
        this.examineLevel4Show = true
        this.examineLevel5Show = false
      } else if (data === '5') {
        this.examineLevel1Show = true
        this.examineLevel2Show = true
        this.examineLevel3Show = true
        this.examineLevel4Show = true
        this.examineLevel5Show = true
      }
    },
    query() {
      // this.getCatalogPage()
      // this.getUnionCatalogPage()
      this.getProofCatalogPage()
    },

    getProofCatalogPage() {
      this.licenseCatalogueform.page_number = this.tableData.currentPage
      this.licenseCatalogueform.page_size = this.tableData.pageSize
      this.tableData.loading = true
      this.tableData.content = []
      getProofCatalogPage(this.licenseCatalogueform)
        .then((res) => {
          console.log('getProofCatalogPage', res)
          this.tableData.loading = false
          if (res.meta.code === '200') {
            res.data.content.forEach((e) => {
              e.id = e.code
            })
            this.tableData.content = res.data.content
            this.tableData.total = Number(res.data.total_elements)
            this.numberOfElements = Number(res.data.total_elements)

            // this.tableData1.content = res.content
            // this.tableData1.total = res.numberOfElements
            // this.numberOfElements1 = res.numberOfElements
            if (this.$route.query.flagCatalog === 'edit' && this.tableData.content.length !== 0) {
              // console.log('1111', this.tableData.content[0])
              // console.log('this.$refs.table1', this.$refs.table1)
              this.$nextTick(() => {
                this.$refs.table1.radioValue = this.tableData.content[0].id
                this.selectItem = [this.tableData.content[0]]
              })
            }
          }
        })
        .catch((err) => {
          this.tableData.loading = false
        })
    },
    getUnionCatalogPage() {
      this.licenseCatalogueform.page_number = this.tableData1.currentPage
      this.licenseCatalogueform.page_size = this.tableData1.pageSize
      this.tableData1.loading = true
      // this.licenseCatalogueform.search_union_catalog_name = this.licenseCatalogueform.search_catalog_name
      // const data = {
      //   search_union_catalog_name: this.licenseCatalogueform.search_catalog_name,
      //   page_number: this.tableData1.currentPage,
      //   page_size: this.tableData1.pageSize
      // }
      getUnionCatalogPage(this.licenseCatalogueform)
        .then((res) => {
          this.tableData1.loading = false
          if (res != null) {
            res.content.forEach((e) => {
              e.id = e.code
            })
            // this.tableData.content = res.content
            // this.tableData.total = res.numberOfElements
            // this.numberOfElements = res.numberOfElements

            this.tableData1.content = res.content
            this.tableData1.total = res.numberOfElements
            this.numberOfElements1 = res.numberOfElements
          }
        })
        .catch((err) => {
          this.tableData1.loading = false
        })
    },
    getIndustryDept() {
      getIndustryDept({ license_item_name: this.licenseForm.keyword }).then((res) => {
        if (res.meta.code === '200' && res.data != null) {
          res.data.forEach((e) => {
            e.id = e.basic_code
          })
          this.tableData.content = res.data
        }
      })
    },
    proofCatalogPage() {
      this.licenseCatalogueform.page_number = this.tableData.currentPage
      this.licenseCatalogueform.page_size = this.tableData.pageSize
      this.tableData.loading = true
      proofCatalogPage(this.licenseCatalogueform)
        .then((res) => {
          this.tableData.loading = false
          if (res.meta.code === '200' && res.data != null) {
            this.tableData.content = res.data.content
            this.tableData.total = res.data.totalElements
            this.numberOfElements = res.data.numberOfElements

            this.tableData1.content = res.data.content
            this.tableData1.total = res.data.totalElements
            this.numberOfElements1 = res.data.numberOfElements
          }
        })
        .catch((err) => {
          this.tableData.loading = false
        })
    },
    getElectronicCertificationSlectItem() {
      console.log(this.$refs['ArtificialForm'])
      if (this.selectItem !== '') {
        this.$refs['ArtificialForm'].validate((valid) => {
          if (valid) {
            let auditList = []
            const examineLevel = parseInt(this.ArtificialForm.examineLevel)
            console.log('examineLevel', examineLevel)
            for (let level = 1; level <= examineLevel; level++) {
              console.log(level)
              this[`organizationLevel` + level + `List`].forEach((i) => {
                this.ArtificialForm[`audit_divi_code` + level].forEach((e) => {
                  if (e.indexOf(i.value) !== -1) {
                    i.audit_level = level
                    console.log(`organizationLevel` + level + `List`, '---', `audit_divi_code` + level)
                    auditList.push(i)
                  }
                })
              })
            }
            auditList = _.uniqWith(auditList, _.isEqual)
            console.log('auditList', auditList)
            console.log('this.selectItem', this.selectItem)
            this.ArtificialForm.auditList = auditList
            // this.ArtificialForm.auditList = _.uniqBy(auditList, (item) => {
            //   return item.label && item.audit_level
            // }
            // )
            this.$emit('getElectronicCertificationSlectItem', this.selectItem, this.ArtificialForm)
          }
        })
      } else {
        this.$message({
          showClose: true,
          message: '请选择证明目录名称',
          type: 'warning',
        })
      }
    },
    selectionChange1(data) {
      // this.$refs.table2.radioValue = ''
      //   console.log(data)
      this.selectItem = data
    },
    selectionChange2(data) {
      this.$refs.table1.radioValue = ''
      //   console.log(data)
      this.selectItem = data
    },
    closeDialog() {
      this.$emit('closeDialog')
    },
  },
}
</script>

<style lang="scss" scoped>
.implement-configuration {
  margin: 20px 20px 0 0;
}

::v-deep .el-dialog__body {
  height: 580px;
  overflow: auto;
}

.info-wrap {
  display: flex;
  color: #333333;
  font-size: 20px;
  margin-top: 10px;

  margin-bottom: 10px;

  img {
    margin-right: 10px;
    height: 26px;
    width: 26px;
  }
}
</style>