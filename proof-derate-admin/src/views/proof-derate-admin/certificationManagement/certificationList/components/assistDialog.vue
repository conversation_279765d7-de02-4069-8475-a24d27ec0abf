<template>
  <div>
    <el-dialog title="部门协查" :visible.sync="assistDialogShow" width="65%" @open="open" @close="closeDialog">
      <div class="implement-configuration">
        <el-form ref="ArtificialForm" :model="ArtificialForm" label-width="160px" @submit.native.prevent>
          <el-row type="flex">
            <el-col :xs="24" :sm="24" :md="24" :lg="24">
              <el-form-item label="审核环节数" prop="examineLevel" :rules="[{ required: true, message: '请选择审核环节数', trigger: 'change' }]">
                <el-select v-model="ArtificialForm.examineLevel" placeholder="请选择" style="width: 100%" @change="changeExamineLevel">
                  <el-option v-for="item in examineList" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row type="flex">
            <el-col :span="9" v-if="examineLevel1Show">
              <el-form-item label="第一级审核实施区划" prop="audit_org_code1" :rules="[{ required: true, message: '请选择第一级审核实施区划', trigger: 'change' }]">
                <AdministrativeDivisionCascader ref="AdministrativeDivisionSelect" :divisionCodeLsit="audit_org_code1" :permission-code="'catalog:proof_catalog:proof:list'" @setDivisionCodeAndName="setDivisionCodeAndName1" :optionProps="optionProps" />
              </el-form-item>
            </el-col>
            <el-col :span="9" v-if="examineLevel1Show">
              <el-form-item label="第一级审核部门" prop="audit_divi_code1" :show-message="true" :rules="[{ required: true, message: '请选择第一级审核部门', trigger: 'change' }]">
                <el-cascader style="width: 100%" v-model="ArtificialForm.audit_divi_code1" :options="organizationLevel1List" :props="examineLevelOptionProps" clearable filterable collapse-tags></el-cascader>
              </el-form-item>
            </el-col>
            <el-col :span="6" v-if="examineLevel1Show">
              <el-form-item label="第一级名称"  label-width="120px" prop="level_desc1" :show-message="true">
                <el-input v-model="ArtificialForm.level_desc1" placeholder="请填写第一级名称">
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row type="flex">
            <el-col :span="9" v-if="examineLevel2Show">
              <el-form-item label="第二级审核实施区划" prop="audit_org_code2" :rules="[{ required: true, message: '请选择第二级审核实施区划', trigger: 'change' }]">
                <AdministrativeDivisionCascader :key="audit_org_code2" ref="AdministrativeDivisionSelect" :divisionCodeLsit="audit_org_code2" :permission-code="'catalog:proof_catalog:proof:list'" @setDivisionCodeAndName="setDivisionCodeAndName2" :optionProps="optionProps" />
              </el-form-item>
            </el-col>
            <el-col :span="9" v-if="examineLevel2Show">
              <el-form-item label="第二级审核部门" prop="audit_divi_code2" :show-message="true" :rules="[{ required: true, message: '请选择第二级审核部门', trigger: 'change' }]">
                <el-cascader style="width: 100%" v-model="ArtificialForm.audit_divi_code2" :options="organizationLevel2List" :props="examineLevelOptionProps" clearable filterable collapse-tags></el-cascader>
              </el-form-item>
            </el-col>
            <el-col :span="6" v-if="examineLevel2Show">
              <el-form-item label="第二级名称"  label-width="120px" prop="level_desc2" :show-message="true">
                <el-input v-model="ArtificialForm.level_desc2" placeholder="请填写第二级名称">
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row type="flex">
            <el-col :span="9" v-if="examineLevel3Show">
              <el-form-item label="第三级审核实施区划" prop="audit_org_code3" :rules="[{ required: true, message: '请选择第三级审核实施区划', trigger: 'change' }]">
                <AdministrativeDivisionCascader ref="AdministrativeDivisionSelect" :divisionCodeLsit="audit_org_code3" :permission-code="'catalog:proof_catalog:proof:list'" @setDivisionCodeAndName="setDivisionCodeAndName3" :optionProps="optionProps" />
              </el-form-item>
            </el-col>
            <el-col :span="9" v-if="examineLevel3Show">
              <el-form-item label="第三级审核部门" prop="audit_divi_code3" :show-message="true" :rules="[{ required: true, message: '请选择第三级审核部门', trigger: 'change' }]">
                <el-cascader style="width: 100%" v-model="ArtificialForm.audit_divi_code3" :options="organizationLevel3List" :props="examineLevelOptionProps" clearable filterable collapse-tags></el-cascader>
              </el-form-item>
            </el-col>
            <el-col :span="6" v-if="examineLevel3Show">
              <el-form-item label="第三级名称"  label-width="120px" prop="level_desc3" :show-message="true">
                <el-input v-model="ArtificialForm.level_desc3" placeholder="请填写第三级名称">
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row type="flex">
            <el-col :span="9" v-if="examineLevel4Show">
              <el-form-item label="第四级审核实施区划" prop="audit_org_code4" :rules="[{ required: true, message: '请选择第四级审核实施区划', trigger: 'change' }]">
                <AdministrativeDivisionCascader ref="AdministrativeDivisionSelect" :divisionCodeLsit="audit_org_code4" :permission-code="'catalog:proof_catalog:proof:list'" @setDivisionCodeAndName="setDivisionCodeAndName4" :optionProps="optionProps" />
              </el-form-item>
            </el-col>
            <el-col :span="9" v-if="examineLevel4Show">
              <el-form-item label="第四级审核部门" prop="audit_divi_code4" :show-message="true" :rules="[{ required: true, message: '请选择第四级审核部门', trigger: 'change' }]">
                <el-cascader style="width: 100%" v-model="ArtificialForm.audit_divi_code4" :options="organizationLevel4List" :props="examineLevelOptionProps" clearable filterable collapse-tags></el-cascader>
              </el-form-item>
            </el-col>
            <el-col :span="6" v-if="examineLevel4Show">
              <el-form-item label="第四级名称"  label-width="120px" prop="level_desc3" :show-message="true">
                <el-input v-model="ArtificialForm.level_desc4" placeholder="请填写第四级名称">
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row type="flex">
            <el-col :span="9" v-if="examineLevel5Show">
              <el-form-item label="第五级审核实施区划" prop="audit_org_code5" :rules="[{ required: true, message: '请选择第五级审核实施区划', trigger: 'change' }]">
                <AdministrativeDivisionCascader ref="AdministrativeDivisionSelect" :divisionCodeLsit="audit_org_code5" :permission-code="'catalog:proof_catalog:proof:list'" @setDivisionCodeAndName="setDivisionCodeAndName5" :optionProps="optionProps" />
              </el-form-item>
            </el-col>
            <el-col :span="9" v-if="examineLevel5Show">
              <el-form-item label="第五级审核部门" prop="audit_divi_code5" :show-message="true" :rules="[{ required: true, message: '请选择第五级审核部门', trigger: 'change' }]">
                <el-cascader style="width: 100%" v-model="ArtificialForm.audit_divi_code5" :options="organizationLevel4List" :props="examineLevelOptionProps" clearable filterable collapse-tags></el-cascader>
              </el-form-item>
            </el-col>
            <el-col :span="6" v-if="examineLevel5Show">
              <el-form-item label="第五级名称"  label-width="120px" prop="level_desc3" :show-message="true">
                <el-input v-model="ArtificialForm.level_desc5" placeholder="请填写第五级名称">
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :xs="24" :sm="12" :md="12" :lg="24">
              <!-- prop="assist_desc"  :rules="[{ required: true, message: '请填写部门协查说明', trigger: 'change' }]"-->
              <el-form-item label="部门协查说明">
                <el-input v-model="ArtificialForm.assist_desc" clearable placeholder="请填写部门协查说明" type="textarea" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :xs="24" :sm="12" :md="12" :lg="8">
              <!-- prop="assist_time_hour" :rules="[{ required: true, message: '请填写协查时限', trigger: 'change' },
              { type: 'number', message: '协查时限必须为数字值' }
              ]"-->
              <el-form-item label="协查时限">
                <el-input v-model.number="ArtificialForm.assist_time_hour" placeholder="请填写协查时限">
                  <template slot="append">小时</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :xs="12" :sm="12" :md="12" :lg="6">
              <!-- :rules="[{ required: true, message: '请填写协查时限', trigger: 'change' },
              { type: 'number', message: '协查时限必须为数字值' }
              ]" -->
              <el-form-item label label-width="10px" prop="assist_time_minute">
                <el-input v-model.number="ArtificialForm.assist_time_minute" placeholder="请填写协查时限">
                  <template slot="append">分钟</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :xs="10" :sm="10" :md="10" :lg="10">
              <el-form-item label label-width="10px">
                <span>注意：协查时限在法定工作日正常上班时间时间段内予以计算。</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :xs="12" :sm="12" :md="12" :lg="6">
            <el-form-item label="是否开具电子证明" prop="issue_p_license">
              <el-radio-group v-model="ArtificialForm.issue_p_license" @input="radioChange">
                <el-radio
                  v-for="option in electronicProofIssueOptions"
                  :key="option.value"
                  :label="option.value"
                  style="display: block; margin-bottom: 8px;"
                >
                  {{ option.label }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-row>
          <el-row :xs="12" :sm="12" :md="12" :lg="6">
            <el-form-item label="电子证明开具方式" prop="issue_p_license_way" v-if="isIssuePLicense">
              <el-radio-group v-model="ArtificialForm.issue_p_license_way">
                <el-radio label="SYSTEM_GENERATE">系统生成</el-radio>
                <el-radio label="MANUALLY_GENERATED">人工开具</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-row>
          <el-row :xs="12" :sm="12" :md="12" :lg="6" v-if="isIssuePLicense">
            <el-form-item label="电子证明目录配置" prop="license_code" :rules="[{ required: true, message: '请选择电子证明目录配置', trigger: 'change' }]">
              <el-button type="text" @click="relationElectronicProof">{{ ArtificialForm.license_name ? ArtificialForm.license_name : '关联电子证明目录' }}</el-button>
            </el-form-item>
          </el-row>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="getData()">确定</el-button>
        <el-button @click="closeDialog()">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getOrgTreeList } from '@/api/common/dict'
import { electronicProofIssueOptions } from '@/utils/enum'
export default {
  data() {
    return {
      ArtificialForm: {
        id: '',
        investigation_dept_name: '',
        investigation_dept_code: '',
        assist_desc: '',
        division_code: '',
        division_name: '',
        assist_time_hour: '',
        assist_time_minute: '',
        examineLevel: '1',
        audit_org_code1: '',
        audit_org_name1: '',
        audit_divi_code1: [],
        audit_divi_name1: '',
        audit_org_code2: '',
        audit_org_name2: '',
        audit_divi_code2: [],
        audit_divi_name2: '',
        audit_org_code3: '',
        audit_org_name3: '',
        audit_divi_code3: [],
        audit_divi_name3: '',
        audit_org_code4: '',
        audit_org_name4: '',
        audit_divi_code4: [],
        audit_divi_name4: '',
        audit_org_code5: '',
        audit_org_name5: '',
        audit_divi_code5: [],
        audit_divi_name5: '',
        level_desc1:'',
        level_desc2:'',
        level_desc3:'',
        level_desc4:'',
        level_desc5:'',
        issue_p_license: 'ISSUE_UNLIMITED', // 默认为开具
        issue_p_license_way: null,
        license_name: null,
        license_code: null,
        // assist_time_limit: '' //协查时限
      },
      examineLevel1Show: false,
      examineLevel2Show: false,
      examineLevel3Show: false,
      examineLevel4Show: false,
      examineLevel5Show: false,
      organizationLevel1List: [], // 第一审核部门
      organizationLevel2List: [], // 第二审核部门
      organizationLevel3List: [], // 第三审核部门
      organizationLevel4List: [], // 第四审核部门
      organizationLevel5List: [], // 第五审核部门
      audit_org_code1: [],
      audit_org_code2: [],
      audit_org_code3: [],
      audit_org_code4: [],
      audit_org_code5: [],
      optionProps: {
        value: 'value',
        label: 'label',
        children: 'children',
        checkStrictly: true,
        expandTrigger: 'hover',
        emitPath: false,
        multiple: true, // 是否多选
      },
      examineList: [
        {
          label: '一级审核',
          value: '1',
        },
        {
          label: '二级审核',
          value: '2',
        },
        {
          label: '三级审核',
          value: '3',
        },
        {
          label: '四级审核',
          value: '4',
        },
        {
          label: '五级审核',
          value: '5',
        },
      ],
      examineLevelOptionProps: {
        value: 'value',
        label: 'label',
        children: 'sup_list',
        checkStrictly: true,
        expandTrigger: 'hover',
        multiple: true, // 是否多选
      },
      electronicProofTitle: '',
      electronicProofIssueOptions: electronicProofIssueOptions,// 添加选项数据
      isIssuePLicense: true, // 默认显示相关配置
    }
  },
  props: {
    assistDialogShow: {
      type: Boolean,
      default: false,
    },
  },
  components: {
    AdministrativeDivisionCascader: () => import('@/components/AdministrativeDivisionCascader/index2'),
  },
  methods: {
    editInit(data) {
      console.log('editInit', data)
      if (data) {
        let examineLevel = []
        this.audit_org_code1 = []
        this.audit_org_code2 = []
        this.audit_org_code3 = []
        this.audit_org_code4 = []
        this.audit_org_code5 = []
        let audit_org_code1SetData1 = []
        let audit_org_code1SetData2 = []
        let audit_org_code1SetData3 = []
        let audit_org_code1SetData4 = []
        let audit_org_code1SetData5 = []
        data.proof_catalog_artificial_list[0].audit_r_temp_list.forEach((e) => {
          examineLevel.push(e.audit_level)
          let auditOrgItem = {
            code: e.audit_divi_code,
            name: e.audit_divi_name,
          }
          if (e.audit_level === 1) {
            console.log('e.audit_divi_code', e.audit_divi_code)
            this.ArtificialForm.level_desc1 = e.level_desc
            this.audit_org_code1.push(e.audit_divi_code)
            audit_org_code1SetData1.push(auditOrgItem)
            this.ArtificialForm.audit_divi_code1.push(e.audit_org_code)
          }
          if (e.audit_level === 2) {
            this.ArtificialForm.level_desc2 = e.level_desc
            this.audit_org_code2.push(e.audit_divi_code)
            audit_org_code1SetData2.push(auditOrgItem)
            this.ArtificialForm.audit_divi_code2.push(e.audit_org_code)
          }
          if (e.audit_level === 3) {
            console.log('e.audit_level', e.audit_level)
            this.ArtificialForm.level_desc3 = e.level_desc
            this.audit_org_code3.push(e.audit_divi_code)
            audit_org_code1SetData3.push(auditOrgItem)
            this.ArtificialForm.audit_divi_code3.push(e.audit_org_code)
          }
          if (e.audit_level === 4) {
            this.ArtificialForm.level_desc4 = e.level_desc
            this.audit_org_code4.push(e.audit_divi_code)
            audit_org_code1SetData4.push(auditOrgItem)
            this.ArtificialForm.audit_divi_code4.push(e.audit_org_code)
          }
          if (e.audit_level === 5) {
            this.ArtificialForm.level_desc5 = e.level_desc
            this.audit_org_code5.push(e.audit_divi_code)
            audit_org_code1SetData5.push(auditOrgItem)
            this.ArtificialForm.audit_divi_code5.push(e.audit_org_code)
          }
        })
        console.log('this.audit_org_code1', this.audit_org_code1)
        console.log('this.audit_org_code2', this.audit_org_code2)
        console.log('this.audit_org_code3', this.audit_org_code3)
        // this.audit_org_code1 = this.audit_org_code1.join(',')
        // this.audit_org_code12 = this.audit_org_code2.join(',')
        if (audit_org_code1SetData1.length != 0) {
          audit_org_code1SetData1 = _.uniqWith(audit_org_code1SetData1, _.isEqual)
          console.log('audit_org_code1SetData1', audit_org_code1SetData1)
          this.setDivisionCodeAndName1(audit_org_code1SetData1)
        }
        if (audit_org_code1SetData2.length != 0) {
          audit_org_code1SetData2 = _.uniqWith(audit_org_code1SetData2, _.isEqual)
          this.setDivisionCodeAndName2(audit_org_code1SetData2)
        }
        if (audit_org_code1SetData3.length != 0) {
          audit_org_code1SetData3 = _.uniqWith(audit_org_code1SetData3, _.isEqual)
          this.setDivisionCodeAndName3(audit_org_code1SetData3)
        }
        if (audit_org_code1SetData4.length != 0) {
          audit_org_code1SetData4 = _.uniqWith(audit_org_code1SetData4, _.isEqual)
          this.setDivisionCodeAndName4(audit_org_code1SetData4)
        }
        if (audit_org_code1SetData5.length != 0) {
          audit_org_code1SetData5 = _.uniqWith(audit_org_code1SetData5, _.isEqual)
          this.setDivisionCodeAndName5(audit_org_code1SetData5)
        }

        this.ArtificialForm.examineLevel = Math.max(...examineLevel) + ''
        this.ArtificialForm.assist_desc = data.proof_catalog_artificial_list[0].note
        this.ArtificialForm.assist_time_hour = data.proof_catalog_artificial_list[0].assist_time_hour
        this.ArtificialForm.assist_time_minute = data.proof_catalog_artificial_list[0].assist_time_minute
        this.ArtificialForm.license_name = data.proof_catalog_artificial_list[0].license_name
        this.ArtificialForm.license_code = data.proof_catalog_artificial_list[0].license_code

        // 兼容旧的布尔值数据
        const issueValue = data.proof_catalog_artificial_list[0].issue_p_license
        if (typeof issueValue === 'boolean') {
          this.ArtificialForm.issue_p_license = issueValue ? 'ISSUE_UNLIMITED' : 'NOT_ISSUE_ASSIST_ONLY'
        } else {
          this.ArtificialForm.issue_p_license = issueValue || 'NOT_ISSUE_ASSIST_ONLY'
        }

        this.ArtificialForm.issue_p_license_way = data.proof_catalog_artificial_list[0].issue_p_license_way
        this.changeExamineLevel(this.ArtificialForm.examineLevel)
        console.log('this.ArtificialForm.examineLevel', this.ArtificialForm.examineLevel)
        this.ArtificialForm.auditList = []

        // 更新显示状态
        this.isIssuePLicense = this.ArtificialForm.issue_p_license === 'ISSUE_UNLIMITED'
      } else {
        this.changeExamineLevel(this.ArtificialForm.examineLevel)
      }
    },
    getData() {
      this.$refs['ArtificialForm'].validate((valid) => {
        if (valid) {
          let auditList = []
          const examineLevel = parseInt(this.ArtificialForm.examineLevel)
          console.log('examineLevel', examineLevel)
          for (let level = 1; level <= examineLevel; level++) {
            console.log(level)
            this[`organizationLevel` + level + `List`].forEach((i) => {
              this.ArtificialForm[`audit_divi_code` + level].forEach((e) => {
                if (e.indexOf(i.value) !== -1) {
                  i.audit_level = level
                  console.log(`organizationLevel` + level + `List`, '---', `audit_divi_code` + level)
                  auditList.push(i)
                }
              })
            })
          }
          auditList = _.uniqWith(auditList, _.isEqual)
          console.log('auditList', auditList)
          console.log('this.selectItem', this.selectItem)
          auditList.forEach((i,index) => {
            console.log('this.ArtificialForm[`level_desc` + index+1]',`level_desc` + i.audit_level)
            i.level_desc = this.ArtificialForm[`level_desc` + i.audit_level]
            i.audit_org_code = i.value
            i.audit_org_name = i.label
            i.audit_divi_code = i.division_code
            i.audit_divi_name = i.division_name
          })
          this.ArtificialForm.audit_r_temp_list = auditList
          // this.ArtificialForm.auditList = _.uniqBy(auditList, (item) => {
          //   return item.label && item.audit_level
          // }
          // )
          console.log('this.ArtificialForm', this.ArtificialForm)
          this.$emit('getAssistData', this.ArtificialForm)
        }
      })
    },
    closeDialog() {
      this.$emit('closeDialog')
    },
    open() {},
    setDivisionCodeAndName1(data) {
      console.log('setDivisionCodeAndName', data, this.audit_org_code1)
      this.getOrganizationLevelList('1', data)

      this.ArtificialForm.audit_org_code1 = data
      if (this.$refs.ArtificialForm) {
        this.$refs.ArtificialForm.validateField('audit_org_code1')
      }
    },
    setDivisionCodeAndName2(data) {
      console.log('setDivisionCodeAndName', data)
      this.getOrganizationLevelList('2', data)
      this.ArtificialForm.audit_org_code2 = data
      if (this.$refs.ArtificialForm) {
        this.$refs.ArtificialForm.validateField('audit_org_code2')
      }
    },
    setDivisionCodeAndName3(data) {
      console.log('setDivisionCodeAndName', data)
      this.getOrganizationLevelList('3', data)
      this.ArtificialForm.audit_org_code3 = data
      if (this.$refs.ArtificialForm) {
        this.$refs.ArtificialForm.validateField('audit_org_code3')
      }
    },
    setDivisionCodeAndName4(data) {
      console.log('setDivisionCodeAndName', data)
      this.getOrganizationLevelList('4', data)
      this.ArtificialForm.audit_org_code4 = data
      if (this.$refs.ArtificialForm) {
        this.$refs.ArtificialForm.validateField('audit_org_code4')
      }
    },
    setDivisionCodeAndName5(data) {
      console.log('setDivisionCodeAndName', data)
      this.getOrganizationLevelList('5', data)
      this.ArtificialForm.audit_org_code5 = data
      if (this.$refs.ArtificialForm) {
        this.$refs.ArtificialForm.validateField('audit_org_code5')
      }
    },
    changeExamineLevel(data) {
      console.log('changeExamineLevel', data)
      // this.examineLevel = data
      if (data === '1') {
        this.examineLevel1Show = true
        this.examineLevel2Show = false
        this.examineLevel3Show = false
        this.examineLevel4Show = false
        this.examineLevel5Show = false
      } else if (data === '2') {
        this.examineLevel1Show = true
        this.examineLevel2Show = true
        this.examineLevel3Show = false
        this.examineLevel4Show = false
        this.examineLevel5Show = false
      } else if (data === '3') {
        this.examineLevel1Show = true
        this.examineLevel2Show = true
        this.examineLevel3Show = true
        this.examineLevel4Show = false
        this.examineLevel5Show = false
      } else if (data === '4') {
        this.examineLevel1Show = true
        this.examineLevel2Show = true
        this.examineLevel3Show = true
        this.examineLevel4Show = true
        this.examineLevel5Show = false
      } else if (data === '5') {
        this.examineLevel1Show = true
        this.examineLevel2Show = true
        this.examineLevel3Show = true
        this.examineLevel4Show = true
        this.examineLevel5Show = true
      }
    },
    relationElectronicProof() {
      this.$emit('relationElectronicProof')
    },
    getOrganizationLevelList(index, data) {
      let divisionCodeList = []
      data.forEach((e) => {
        divisionCodeList.push(e.code)
      })
      getOrgTreeList({ division_code: divisionCodeList.join(',') }).then((res) => {
        // console.log('getOrgTreeList', res)
        if (res.meta.code === '200' && res.data !== null) {
          this[`organizationLevel` + index + `List`] = res.data
          console.log('this[`organizationLevel`+index+`List`]', this[`organizationLevel` + index + `List`])
        }
      })
    },
    // 回填电子证明目录配置选中值
    setRelationElectronicProof(data) {
      console.log('setRelationElectronicProof', data)
      this.ArtificialForm.license_name = data[0].name
      this.ArtificialForm.license_code = data[0].code
      this.$refs.ArtificialForm.validateField('license_code')
      console.log('this.ArtificialForm.license_code', this.ArtificialForm.license_code)
    },
    radioChange(val) {
      console.log('radioChange', val)
      // 只有选择"开具（不受审核结果限制）"时才显示电子证明开具方式和目录配置
      this.isIssuePLicense = val === 'ISSUE_UNLIMITED'

      if (!this.isIssuePLicense) {
        // 不开具时清空相关配置
        this.ArtificialForm.issue_p_license_way = null
        this.ArtificialForm.license_name = null
        this.ArtificialForm.license_code = null
      } else {
        // 开具时设置默认值
        this.ArtificialForm.issue_p_license_way = 'MANUALLY_GENERATED'
      }
    },
  },
  mounted() {
    this.editInit()
  },
}
</script>

<style scoped>
/* 优化单选框显示效果 */
.el-radio {
  margin-bottom: 8px !important;
  line-height: 1.5;
}

.el-radio__label {
  white-space: normal;
  word-break: break-all;
  line-height: 1.4;
  padding-left: 8px;
}

/* 调整单选框组的布局 */
.el-form-item__content .el-radio-group {
  line-height: 1.5;
}
</style>
