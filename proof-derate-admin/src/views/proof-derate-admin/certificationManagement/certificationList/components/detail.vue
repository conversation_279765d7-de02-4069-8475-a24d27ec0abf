<template>
  <div>
    <!-- <el-divider content-position="left">基础信息</el-divider>
    <el-row>
      <el-col :span="8" :offset="2">证明目录名称: {{ form.name }}</el-col>
      <el-col :span="8" :offset="2">证明目录编码: {{ form.code }}</el-col>
    </el-row>
    <br />
    <el-row>
      <el-col :span="8" :offset="2">证明开具单位类型: {{ form.unit_type_Name }}</el-col>
      <el-col :span="8" :offset="2">创建人名称: {{ form.account_name }}</el-col>
    </el-row>-->
    <el-descriptions class="descriptions" title="基本信息" :column="2" border>
      <el-descriptions-item :label-style="{ width: '140px' }">
        <template slot="label">证明目录名称</template>
        {{ form.name }}
      </el-descriptions-item>
      <el-descriptions-item :label-style="{ width: '140px' }">
        <template slot="label">证明目录编码</template>
        {{ form.code }}
      </el-descriptions-item>
      <el-descriptions-item :label-style="{ width: '140px' }">
        <template slot="label">证明开具单位类型</template>
        {{ form.unit_type_Name }}
      </el-descriptions-item>
      <el-descriptions-item :label-style="{ width: '140px' }">
        <template slot="label">创建人名称</template>
        {{ form.account_name }}
      </el-descriptions-item>
    </el-descriptions>
    <br />
    <!-- <el-divider content-position="left">关联电子证照</el-divider> -->
    <span class="margin-left-10 info-wrap">
      <img :src="arrow" alt />
      <span class="info-title">电子证照</span>
    </span>
    <el-row>
      <el-col :span="24">
        <custom-table ref="table1" :is-card-type="false" :table-data="tableDataElectronic" :table-header="tableHeaderElectronic">
          <template #operate="{ row }">
            <el-button type="text" @click="electronicLook(row)">查看</el-button>
          </template>
        </custom-table>
      </el-col>
    </el-row>
    <!-- <el-divider content-position="left">电子证明</el-divider> -->
    <span class="margin-left-10 info-wrap">
      <img :src="arrow" alt />
      <span class="info-title">电子证明</span>
    </span>
    <el-row>
      <el-col :span="24">
        <custom-table ref="table1" :is-card-type="false" :table-data="tableDataElectronicCertification" :table-header="tableHeaderElectronicCertification">
          <template #operate="{ row }">
            <el-button type="text" @click="electronicCertificationLook(row)">查看</el-button>
          </template>
        </custom-table>
      </el-col>
    </el-row>
    <!-- <el-divider content-position="left">数据共享</el-divider> -->
    <span class="margin-left-10 info-wrap">
      <img :src="arrow" alt />
      <span class="info-title">数据共享</span>
    </span>
    <el-row>
      <el-col :span="24">
        <custom-table ref="table1" :is-card-type="false" :table-data="tableDataData" :table-header="tableHeaderData" />
      </el-col>
    </el-row>
    <!-- <el-divider content-position="left">部门协查</el-divider> -->
    <span class="margin-left-10 info-wrap">
      <img :src="arrow" alt />
      <span class="info-title">部门协查</span>
    </span>
    <el-row>
      <el-col :span="24">
        <custom-table ref="tableDataArtificial" :is-card-type="false" :expand="true" :table-data="tableDataArtificial" :table-header="tableHeaderArtificial">
          <template #examine_level="{ row }">
            {{ numberToChinese(row.examine_level) + '级审核' }}
          </template>
          <template #expand>
            <div v-for="(item, key) in auditRTempList" :key="key" style="margin-left: 440px; color: #c0c4cc">
              <div style="margin: 5px 0px">
                <span style="margin-right: 10px">第{{ numberToChinese(item.audit_level) }}级审核实施区划</span>
                <span style="color: #606266">{{ item.audit_divi_name }}</span>
              </div>
              <div style="margin: 5px 0px">
                <span style="margin-right: 10px">第{{ numberToChinese(item.audit_level) }}级审核实施部门</span>
                <span style="color: #606266">{{ item.audit_org_name }}</span>
              </div>
            </div>
          </template>
        </custom-table>
      </el-col>
    </el-row>
    <!-- <el-divider content-position="left">告知承诺</el-divider> -->
    <span class="margin-left-10 info-wrap">
      <img :src="arrow" alt />
      <span class="info-title">告知承诺</span>
    </span>
    <el-row>
      <el-col :span="24">
        <custom-table ref="table1" :is-card-type="false" :table-data="tableDataPromise" :table-header="tableHeaderPromise">
          <template #commit_attachment_name="{ row }">
            <el-button type="text" @click="goattachment_id(row)">{{ row.commit_attachment_name }}</el-button>
          </template>
        </custom-table>
      </el-col>
    </el-row>
    <!-- <el-divider content-position="left">自行调查</el-divider> -->
    <span class="margin-left-10 info-wrap">
      <img :src="arrow" alt />
      <span class="info-title">自行调查</span>
    </span>
    <el-row>
      <el-col :span="24">
        <custom-table ref="table1" :is-card-type="false" :table-data="tableDataSurvey" :table-header="tableHeaderSurvey" />
      </el-col>
    </el-row>
    <!-- <el-divider content-position="left">其他</el-divider> -->
    <span class="margin-left-10 info-wrap">
      <img :src="arrow" alt />
      <span class="info-title">其他</span>
    </span>
    <el-row>
      <el-col :span="24">
        <custom-table ref="table1" :is-card-type="false" :table-data="tableDataOther" :table-header="tableHeaderOther" />
      </el-col>
    </el-row>
  </div>
</template>
<script>
import CustomTable from '@/components/Element/Table'
import { getGetproofCatalogFind } from '@/api/certificationManagement/certificationList'
import { goLicenseItemView, licenseProofItemView } from '@/api/itemBiz/list'
import Enum from '@/utils/enum'
import { numberToChinese } from '@/utils'
export default {
  name: 'InfoList',
  components: {
    CustomTable,
  },
  data() {
    return {
      unitTypeList: Enum.unitTypeList, // 证明开具单位类型
      arrow: require('@/assets/proof-derate-admin-images/arrow.png'),
      form: {
        name: '',
        code: '',
        unit_type: '',
        unit_type_Name: '',
        account_name: '',
      },
      // 电子证照
      tableDataElectronic: {
        content: [], // 表格数据
      },
      // 表头配置
      tableHeaderElectronic: [
        { label: '电子证照目录名称', prop: 'license_name', minWidth: '200px' }, // 配置slot属性，可支持使用插槽
        { label: '电子证照目录编码', prop: 'license_code', minWidth: '200px' },
        // {
        //   label: '操作',
        //   slot: 'operate',
        //   prop: 'operate',
        //   minWidth: '50px',
        //   fixed: 'right'
        // }
      ],
      // 电子证明
      tableDataElectronicCertification: {
        content: [], // 表格数据
        loading: false, // 控制表格加载效果
        total: 0, // 分页查询时的总数，去掉该属性，不显示分页条
        currentPage: 1, // 当前页码
        pageSize: 10,
        pageDirection: 'desc',
      },
      // 电子证明表头配置
      tableHeaderElectronicCertification: [
        { label: '电子证明名称', prop: 'license_name', minWidth: '200px' }, // 配置slot属性，可支持使用插槽
        { label: '行业部门', prop: 'dept_name', minWidth: '200px' },
        {
          label: '操作',
          slot: 'operate',
          prop: 'operate',
          minWidth: '50px',
          fixed: 'right',
        },
      ],
      // 数据共享
      tableDataData: {
        content: [], // 表格数据
      },
      // 表头配置
      tableHeaderData: [
        {
          label: '系统名称',
          prop: 'system_name',
          minWidth: '200px',
        }, // 配置slot属性，可支持使用插槽
        { label: '数据主题名称', prop: 'data_theme_name', minWidth: '200px' },
        {
          label: '接口访问地址',
          prop: 'system_api_url',
          minWidth: '200px',
        },
      ],
      tableDataArtificial: {
        content: [], // 表格数据
      },
      // 表头配置
      tableHeaderArtificial: [
        { label: '审核环节数', prop: 'examine_level', slot: 'examine_level', minWidth: '200px' },
        {
          label: '协查部门',
          prop: 'investigation_dept_name',
          minWidth: '200px',
          formatter: (row, col, val) => {
            return val === null || val.length === 0 ? '全部' : val
          },
        }, // 配置slot属性，可支持使用插槽
        { label: '部门协查说明', prop: 'note', minWidth: '200px' },
        { label: '电子证明', prop: 'proof', minWidth: '200px' },
      ],
      tableDataPromise: {
        content: [], // 表格数据
      },
      // 表头配置
      tableHeaderPromise: [
        {
          label: '承诺书说明',
          prop: 'commit_book_description',
          minWidth: '200px',
        }, // 配置slot属性，可支持使用插槽
        {
          label: '承诺书模板',
          slot: 'commit_attachment_name',
          prop: 'commit_attachment_name',
          minWidth: '200px',
        },
      ],
      tableDataSurvey: {
        content: [], // 表格数据
      },
      // 表头配置
      tableHeaderSurvey: [
        {
          label: '自行调查说明',
          prop: 'dept_cancel_description',
          minWidth: '200px',
        }, // 配置slot属性，可支持使用插槽
      ],
      tableDataOther: {
        content: [], // 表格数据
      },
      // 表头配置
      tableHeaderOther: [
        {
          label: '其它说明',
          prop: 'other_clear_description',
          minWidth: '200px',
        }, // 配置slot属性，可支持使用插槽
      ],
      auditRTempList: [], // 审核实施部门和区划
    }
  },
  mounted() {
    this.goGetproofCatalogFind(this.$route.query.id)
  },
  methods: {
    numberToChinese,
    goGetproofCatalogFind(findId) {
      getGetproofCatalogFind(findId)
        .then((res) => {
          this.form = res.data
          this.unitTypeList.forEach((item) => {
            if (item.value === this.form.unit_type) {
              this.form.unit_type_Name = item.label
            }
          })
          if (res.data.proof_catalog_license_relation_list != null) {
            this.tableDataElectronic.content = res.data.proof_catalog_license_relation_list // 电子证照
          }
          if (res.data.proof_catalog_data_shared_list != null) {
            this.tableDataData.content = res.data.proof_catalog_data_shared_list // 数据共享
          }
          if (res.data.proof_catalog_artificial_list != null) {
            this.tableDataArtificial.content = res.data.proof_catalog_artificial_list
            if (res.data.proof_catalog_artificial_list.length > 0) {
              this.auditRTempList = _.orderBy(res.data.proof_catalog_artificial_list[0].audit_r_temp_list,'audit_level','asc')
              let data = res.data.proof_catalog_artificial_list[0];
              console.log('data.issue_p_license', data.issue_p_license)
              let proof = data.issue_p_license ? '' + (data.issue_p_license_way == 'SYSTEM_GENERATE' ? '系统生成' : '人工开具') + '，' + data.license_name : '不开具'
              res.data.proof_catalog_artificial_list[0].proof = proof
            }
          }
          if (res.data.proof_catalog_clerk_commitment_list != null) {
            this.tableDataPromise.content = res.data.proof_catalog_clerk_commitment_list
            console.log('proof_catalog_clerk_commitment_list.tableDataPromise.content', this.tableDataPromise.content)
          }
          if (res.data.proof_catalog_dept_survey_list != null) {
            this.tableDataSurvey.content = res.data.proof_catalog_dept_survey_list
          }
          if (res.data.proof_catalog_other_relation_list != null) {
            this.tableDataOther.content = res.data.proof_catalog_other_relation_list
          }
          if (res.data.proof_catalog_license_item_relation != null) {
            this.tableDataElectronicCertification.content = res.data.proof_catalog_license_item_relation
          }
        })
        .catch(() => {})
    },
    // 下载模板
    goattachment_id(row) {
      var raw = window.atob(row.file_data)
      var uInt8Array = new Uint8Array(raw.length)
      for (var i = 0; i < raw.length; i++) {
        uInt8Array[i] = raw.charCodeAt(i)
      }
      const link = document.createElement('a')
      const blob = new Blob([uInt8Array], {
        type: 'application/vnd.ms-excel',
      })
      link.style.display = 'none'
      link.href = URL.createObjectURL(blob)
      link.setAttribute('download', row.commit_attachment_name)
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    },
    // 电子证照查看
    electronicLook(row) {
      goLicenseItemView(row.license_code).then((res) => {
        window.open(res.data.url, '_blank')
      })
    },
    electronicCertificationLook(row) {
      licenseProofItemView(row.license_code).then((res) => {
        window.open(res.data.url, '_blank')
      })
    },
  },
}
</script>
<style scoped lang="scss">
.info-wrap {
  display: flex;
  color: #333333;
  font-size: 20px;
  // margin-bottom: 30px;
  img {
    margin-right: 10px;
  }
}
.descriptions {
  margin-top: 10px;
  padding: 0 10px;
}
</style>
