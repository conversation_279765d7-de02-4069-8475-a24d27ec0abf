<template>
  <div>
    <el-dialog
      title="选择数据主题"
      :visible.sync="dataShareDialogVisible"
      class="dialogwrap"
      width="60%"
      :top="is1366==true?'1vh':'15vh'"
      @close="closeDialog"
    >
      <div class="wrap">
        <el-form :model="themeParam" label-width="120px">
          <!-- <el-row :gutter="24" type="flex">
            <el-col :span="20">
              <el-form-item label="系统名称">
                <el-select v-model="themeParam.system_code" placeholder="请选择" class="select">
                  <el-option v-for="item in sysOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-button type="primary" @click="getThemePageBysearch">查询</el-button>
            </el-col>
          </el-row> -->
          <el-row :gutter="24" type="flex">
            <el-col :span="20">
              <el-form-item label="数据主题名称">
                <el-input v-model="themeParam.data_theme_name" autocomplete="off"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-button type="primary" @click="getThemePageBysearch">查询</el-button>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div class="table">
        <custom-table
          ref="table1"
          :is-card-type="false"
          @selection-change="selectionChange"
          :table-data="dataSubjectData"
          :table-header="dataSubjecHeaderData"
          @query="query"
          @refresh="query(1)"
        ></custom-table>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="closeDialog()">取 消</el-button>
        <el-button type="primary" @click="getThemeData()">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getSystemSelectItem } from '@/api/certificationManagement/certificationList'
import CustomTable from '@/components/Element/Table'
import { getCatalogDataSharedConfigPage, catalogDataSharedConfigDelete } from '@/api/sharedConfig'
import { isPermission } from '@/utils/index.js'

export default {
  props: {
    dataShareDialogVisible: {
      type: Boolean,
      default: false
    }
  },
  components: {
    CustomTable
  },
  data() {
    return {
      dataSubjectData: {
        loading: false, // 控制表格加载效果
        total: 0, // 分页查询时的总数，去掉该属性，不显示分页条
        currentPage: 1, // 当前页码
        pageSize: 10,
        maxHeight: '300px',
        pageDirection: 'desc',
        multiple: false, // 是否多选 数据需要有id 属性值
        isShowSelection: true, // 是否显示多选框，默认false
        content: []
      },
      dataSubjecHeaderData: [
        {
          label: '数据主题名称',
          prop: 'data_theme_name',
          minWidth: '200px'
        },
        // {
        //   label: '表名',
        //   prop: 'data_shared_theme_code',
        //   minWidth: '200px'
        // },
        // {
        //   label: '部门',
        //   prop: 'data_shared_org',
        //   minWidth: '200px'
        // }
        {
          label: '接口访问地址',
          prop: 'system_api_url',
          minWidth: '200px'
        }
      ],
      is1366: false,
      themeParam: {
        system_code: '',
        data_theme_name: '',
        fieldName: '',
        page_number: '1',
        page_size: '10'
      },
      themeSelectData: '',
      sysOptions: [],
      permissionList: JSON.parse(sessionStorage.getItem( 'accountInfo' )).permission
    }
  },

  mounted() {
    this.getSystemSelectItem()
    this.getThemePageBysearch()
    this.screenWidth()
  },

  methods: {
    selectionChange(data) {
      this.themeSelectData = data[0]
    },
    screenWidth() {
      if (screen.width == 1920) {
        this.is1366 = false
      } else if (screen.width == 1366) {
        this.is1366 = true
      } else {
        this.is1366 = false
      }
    },
    query() {
      this.themeParam.page_number = this.dataSubjectData.currentPage
      this.themeParam.page_size = this.dataSubjectData.pageSize

      if ( isPermission( this.permissionList, 'catalog:data_manage:data:list' ) ) {
        this.getCatalogDataSharedConfigPage()
      }
    },
    getThemePageBysearch() {
      this.dataSubjectData.currentPage = 1
      this.dataSubjectData.pageSize = 10
      this.themeParam.page_number = 1
      this.themeParam.page_size = 10
      if ( isPermission( this.permissionList, 'catalog:data_manage:data:list' ) ) {
        this.getCatalogDataSharedConfigPage()
      }
    },
    getCatalogDataSharedConfigPage() {
      getCatalogDataSharedConfigPage(this.themeParam).then(res => {
        if (res.data != null && res.meta.code === '200') {
          this.dataSubjectData.content = res.data.content
          this.dataSubjectData.total = res.data.totalElements
        }
      })
    },
    getThemeData() {
      this.$emit('getDataShare', this.themeSelectData)
    },
    getSystemSelectItem() {
      getSystemSelectItem().then(res => {
        if (res.data != null && res.meta.code === '200') {
          this.sysOptions = res.data
        }
      })
    },
    closeDialog() {
      this.$emit('closeDataShareDialog')
    }
  }
}
</script>

<style lang="scss" scoped>
.select {
  width: 100%;
}
</style>
