<template>
  <div>
    <el-form ref="form" :model="form" label-width="140px">
      <el-row>
        <el-col :xs="24" :sm="12" :md="12" :lg="18">
          <el-form-item label="证明目录名称">
            <el-input v-model="form.proof_catalog_name" clearable placeholder="证明目录名称" />
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12" :md="12" :lg="6">
          <el-form-item>
            <el-button type="primary" @click="search">查询</el-button>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :xs="24" :sm="12" :md="12" :lg="18">
          <el-form-item label="证明开具单位类型">
          <el-select
            v-model="form.unit_type"
            clearable
            filterable
            placeholder="请选择">
              <el-option
                v-for="item in  unitTypeList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>
<script>
import Enum from "@/utils/enum";
import {
  getUnitTypeList,
  getDepartmentList,
  getCodeItemListByCodeId,
  //  getCodeItemList
} from "@/api/common/dict";
export default {
  name: "QueryForm",
  data() {
    return {
      form: {
        proof_catalog_name: "",
        unit_type: ""
      },
      unitTypeList: Enum.unitTypeList
    };
  },
  watch: {},
  mounted: function() {
    this.initData();
  },
  methods: {
    initData: async function() {
      let unitTypeListRes = await getUnitTypeList();
      Enum.unitTypeList = unitTypeListRes.data || [];
      this.unitTypeList = unitTypeListRes.data || [];
    },
    search() {
      this.$emit("click", this.form);
    }
  }
};
</script>

<style scoped>
.el-select {
  width: 100%;
}
</style>
