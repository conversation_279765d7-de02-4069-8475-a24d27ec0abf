<template>
  <div class="content-wrapper dataSharing">
    <section class="content-header">
      <h1>数据共享配置</h1>
      <span class="breadcrumb" align="right">
        <el-button plain type="primary" icon="el-icon-edit" @click="saveSharingData">保存</el-button>
        <el-button type="warning" plain icon="el-icon-back" @click="back">返回</el-button>
      </span>
      <br />
    </section>
    <section class="content" style="margin-top: 10px">
      <el-card class="box-card">
        <el-form ref="sysForm" :model="sysForm" label-width="120px">
          <el-row :gutter="24" type="flex">
            <el-col :span="22">
              <el-form-item label="系统名称:" prop="system_code" :rules="[{ required: true, message: '请输入系统名称', trigger: 'change' }]">
                <el-select v-model="sysForm.system_code" placeholder="请选择" class="select" @change="selectChange">
                  <el-option v-for="item in sysOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24" type="flex">
            <el-col :span="20">
              <el-form-item label="数据主题名称:" prop="data_theme_name" :rules="[{ required: true, message: '请输入数据主题名称', trigger: 'change' }]">
                <el-input v-model="sysForm.data_theme_name" clearable placeholder="数据主题名称" />
              </el-form-item>
            </el-col>
            <el-col :span="2">
              <el-button type="text" @click="dialogDataVisible=true">选择主题>></el-button>
            </el-col>
          </el-row>
          <el-row :gutter="24" type="flex">
            <el-col :span="22">
              <el-form-item label="接口访问地址:" prop="system_api_url" :rules="[{ required: true, message: '请输入接口访问地址', trigger: 'change' }]">
                <el-input v-model="sysForm.system_api_url" clearable placeholder="接口访问地址" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div class="table-title">
          <span>数据项</span>
          <div>
            <el-checkbox :indeterminate="isIndeterminate" v-model="checked" @change="setAllCheck"></el-checkbox>
            <span class="check-text">全选/全不选</span>
          </div>
        </div>
        <custom-table ref="table1" :is-card-type="false" :table-data="tableDataData" :table-header="tableHeaderData">
          <template #display_name="{row}">
            <el-input v-model="row.display_name" placeholder="请输入内容"></el-input>
          </template>
          <template #data_item_name="{row}">
            <el-input v-model="row.data_item_name" placeholder="请输入内容"></el-input>
          </template>
          <template #data_type_name="{row}">
            <el-input v-model="row.data_type_name" disabled placeholder="请输入内容"></el-input>
          </template>
          <template #data_range_mapping="{row}">
            <el-input
              v-model="row.data_range_mapping"
              :disabled="(row.data_type_name==='枚举型'||row.data_type_name==='布尔型')?false:true"
              placeholder="请输入内容"
            ></el-input>
          </template>

          <template #DISPLAY="{row}">
            <el-checkbox v-model="row.DISPLAY" @change="itemCheck"></el-checkbox>
          </template>
        </custom-table>
        <div class="table-title">
          <span>查询条件</span>
          <div>
            <i class="el-icon-circle-plus-outline plusicon" @click="addData"></i>
          </div>
        </div>
        <custom-table ref="table1" :is-card-type="false" :table-data="tableDataData1" :table-header="tableHeaderData1" class>
          <template #data_item_name="{row}">
            <!-- <el-input v-model="row.data_item_name" placeholder="请输入内容"></el-input> -->
            <el-select v-model="row.data_item_name" placeholder="请选择">
              <el-option v-for="item in dataItemLsit" :key="item.value" :label="item.label" :value="item.value" :disabled="item.disabled"></el-option>
            </el-select>
          </template>
          <template #condition_operation_symbol="{row,$index}">
            <el-select v-model="row.condition_operation_symbol" placeholder="请选择" @change="conditionChange(row,$index)">
              <el-option v-for="item in conditionList" :key="item.value" :label="item.label" :value="item.value" :disabled="item.disabled"></el-option>
            </el-select>
          </template>
          <template #condition_key="{row,$index}">
            <el-select v-model="row.condition_key" placeholder="请选择" @change="range1Change(row,$index)">
              <el-option v-for="item in valueList" :key="item.value" :label="item.label" :value="item.value" :disabled="item.disabled"></el-option>
            </el-select>
          </template>
          <template #condition_value="{row,$index}">
            <div>
              <!-- :ref="`range1form+${$index}+item`" -->
              <el-form :ref="`range1form+${$index}`" :model="row" class="rangeform">
                <el-form-item
                  ref="condition_value"
                  prop="condition_value"
                  :rules="[{ required: true, message: '请输入值域1', trigger: 'change' }]"
                >
                  <el-input v-model="row.condition_value" :disabled="row.range1Disabled" placeholder="请输入内容"></el-input>
                </el-form-item>
              </el-form>
            </div>
          </template>
          <template #name4="{row,$index}">
            <el-select v-model="row.name4" placeholder="请选择" @change="range2Change(row,$index)">
              <el-option v-for="item in valueList" :key="item.value" :label="item.label" :value="item.value" :disabled="item.disabled"></el-option>
            </el-select>
          </template>
          <template #condition_value_other="{row,$index}">
            <!-- <el-input v-model="row.name5" :disabled="row.range2Disabled" placeholder="请输入内容"></el-input> -->
            <el-form :ref="`range2form+${$index}`" :model="row" label-width="0px" class="rangeform">
              <el-form-item
                ref="condition_value_other"
                prop="condition_value_other"
                :rules="[{ required: true, message: '请输入值域2', trigger: 'change' }]"
              >
                <el-input v-model="row.condition_value_other" :disabled="row.range2Disabled" placeholder="请输入内容"></el-input>
              </el-form-item>
            </el-form>
          </template>
          <template #operation="{row,$index}">
            <el-button type="text" class="red" @click="delect(row,$index)">删除</el-button>
          </template>
        </custom-table>
      </el-card>
    </section>
    <el-dialog title="选择数据主题" :visible.sync="dialogDataVisible" class="dialogwrap" width="60%" :top="is1366==true?'1vh':'15vh'">
      <div class="wrap">
        <el-form :model="themeParam" label-width="120px">
          <el-row :gutter="24" type="flex">
            <el-col :span="20">
              <el-form-item label="数据主题名称">
                <el-input v-model="themeParam.data_theme_name" autocomplete="off"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-button type="primary" @click="getThemePageBysearch">查询</el-button>
            </el-col>
          </el-row>
          <el-row :gutter="24" type="flex">
            <el-col :span="20">
              <el-form-item label="部门">
                <el-input v-model="themeParam.data_theme_org" autocomplete="off"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24" type="flex">
            <el-col :span="20">
              <el-form-item label="表名">
                <el-input v-model="themeParam.data_shared_theme_code" autocomplete="off"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div class="table">
        <custom-table
          ref="table1"
          :is-card-type="false"
          @selection-change="selectionChange"
          :table-data="dataSubjectData"
          :table-header="dataSubjecHeaderData"
          @query="query"
          @refresh="query(1)"
        ></custom-table>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogDataVisible = false">取 消</el-button>
        <el-button type="primary" @click="getThemeData()">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import CustomTable from '@/components/Element/Table'
import { getThemePage, getSystemSelectItem, getConfigDetail, getConfigDetailByid } from '@/api/certificationManagement/certificationList'
import { getQueryFilterOperationSelectItem, getConditionKeySelectItem } from '@/api/common/dict'
export default {
  components: {
    CustomTable
  },
  data() {
    return {
      sysForm: { system_code: '', data_theme_code: '', data_theme_name: '', system_api_url: '', system_name: '' },
      sysOptions: [],
      checked: true,
      isChekcList: [],
      isIndeterminate: false,
      dialogDataVisible: false,
      dataItemLsit: [],
      dataForm: {
        dataSubject: '',
        department: ''
      },
      themeParam: {
        data_theme_name: '',
        data_theme_org: '',
        data_shared_theme_code: '',
        data_shared_system: '',
        page_number: '1',
        page_size: '10'
      },
      saveData: null,
      themeSelectData: '',
      conditionList: [],
      valueList: [
        {
          value: '1',
          label: '办事人姓名'
        },
        {
          value: '2',
          label: '办事人证件类型'
        },
        {
          value: '3',
          label: '办事人证件号码'
        },
        {
          value: '4',
          label: '办事单位名称'
        },
        {
          value: '5',
          label: '办事单位证件类型'
        },
        {
          value: '6',
          label: '办事单位证件号码'
        },
        {
          value: '7',
          label: '事项编码'
        },
        {
          value: '8',
          label: '事项名称'
        },
        {
          value: '9',
          label: '实施机构'
        },
        {
          value: '10',
          label: '实施机构的统一社会信用代码'
        },
        {
          value: '11',
          label: '实施机构的行政区划代码'
        },
        {
          value: '12',
          label: '自定义'
        }
      ],
      tableDataData: {
        content: [], // 表格数据
        loading: false, // 控制表格加载效果
        total: 0, // 分页查询时的总数，去掉该属性，不显示分页条
        currentPage: 1, // 当前页码
        pageSize: 10,
        pageDirection: 'desc',
        isShowIndex: true
      },
      tableHeaderData: [
        {
          label: '显示名称',
          prop: 'display_name',
          slot: 'display_name',
          minWidth: '200px'
        },
        { label: '数据项名称', prop: 'data_item_name', slot: 'data_item_name', minWidth: '200px' },
        {
          label: '数据类型',
          prop: 'data_type_name',
          minWidth: '200px'
        },
        {
          label: '值域',
          prop: 'data_range_mapping',
          slot: 'data_range_mapping',
          minWidth: '200px'
        },
        {
          label: '是否显示',
          prop: 'DISPLAY',
          slot: 'DISPLAY',
          minWidth: '200px'
        }
      ],
      tableDataData1: {
        content: [
          // {
          //   // data_item_name: '',
          //   // condition_operation_symbol: '',
          //   // condition_key: '',
          //   // condition_value: '',
          //   // condition_value_other: '',
          //   // // name5: '1',
          //   // range1Disabled: true,
          //   // range2Disabled: true
          // }
        ], // 表格数据
        loading: false, // 控制表格加载效果
        total: 0, // 分页查询时的总数，去掉该属性，不显示分页条
        currentPage: 1, // 当前页码
        pageSize: 10,
        pageDirection: 'desc',
        isShowIndex: true
      },
      tableHeaderData1: [
        {
          label: '数据项名称',
          prop: 'data_item_name',
          slot: 'data_item_name',
          minWidth: '200px'
        },
        { label: '条件操作符', prop: 'condition_operation_symbol', slot: 'condition_operation_symbol', minWidth: '200px' },
        {
          label: '值1',
          prop: 'condition_key',
          slot: 'condition_key',
          minWidth: '200px'
        },
        {
          label: '值域1',
          prop: 'condition_value',
          slot: 'condition_value',
          minWidth: '200px'
        },
        // {
        //   label: '值2',
        //   prop: 'name4',
        //   slot: 'name4',
        //   minWidth: '200px'
        // },
        {
          label: '值域2',
          prop: 'condition_value_other',
          slot: 'condition_value_other',
          minWidth: '200px'
        },
        {
          label: '操作',
          prop: 'operation',
          slot: 'operation',
          minWidth: '200px'
        }
      ],
      dataSubjectData: {
        loading: false, // 控制表格加载效果
        total: 0, // 分页查询时的总数，去掉该属性，不显示分页条
        currentPage: 1, // 当前页码
        pageSize: 10,
        maxHeight: '300px',
        pageDirection: 'desc',
        multiple: false, // 是否多选 数据需要有id 属性值
        isShowSelection: true, // 是否显示多选框，默认false
        content: []
      },
      dataSubjecHeaderData: [
        {
          label: '数据主题名称',
          prop: 'data_shared_theme_name',
          minWidth: '200px'
        },
        {
          label: '表名',
          prop: 'data_shared_theme_code',
          minWidth: '200px'
        },
        {
          label: '部门',
          prop: 'data_shared_org',
          minWidth: '200px'
        }
        // {
        //   label: '来源信息系统',
        //   prop: 'source_information_system',
        //   minWidth: '200px'
        // }
      ],
      is1366: false
    }
  },
  beforeRouteLeave(to, from, next) {
    // 设置下一个路由的 meta
    // console.log(to, from)
    // to.meta.keepAlive = false // 缓存
    if (to.name !== 'certification_List_add') {
      localStorage.setItem('dataCache', '')
    }
    next()
  },
  mounted() {
    this.isChekcList = this.tableDataData.content.filter(e => {
      return e.ifshow
    })
    this.screenWidth()
    this.getThemePage()
    this.getSystemSelectItem()
    this.getConditionKeySelectItem()
    this.getQueryFilterOperationSelectItem()
    if (this.$route.query.dataSharType === 'edit') {
      this.getConfigDetailByid()
    }
  },
  // beforeRouteEnter(to, from, next) {
  //   next(vm => {
  //     formRouteName = from.name
  //     console.log(formRouteName)
  //   })
  // },
  methods: {
    setAllCheck(val) {
      this.isIndeterminate = false
      if (val) {
        this.tableDataData.content.forEach(e => {
          e.DISPLAY = true
        })
      } else {
        this.tableDataData.content.forEach(e => {
          e.DISPLAY = false
        })
      }
    },
    itemCheck(val) {
      //   console.log(val)
      this.isChekcList = this.tableDataData.content.filter(e => {
        return e.DISPLAY
      })
      this.isChekcList.length !== this.tableDataData.content.length && this.isChekcList.length > 0 ? (this.isIndeterminate = true) : (this.isIndeterminate = false)
      if (this.isChekcList.length === 0) {
        this.checked = false
        this.isIndeterminate = false
      }
      if (this.isChekcList.length === this.tableDataData.content.length) {
        this.checked = true
        this.isIndeterminate = false
      }
    },
    addData() {
      this.tableDataData1.content.push({
        data_item_name: '',
        condition_operation_symbol: '',
        condition_key: '',
        condition_value: '',
        condition_value_other: '',
        range1Disabled: true,
        range2Disabled: true
      })
    },
    delect(row, index) {
      this.tableDataData1.content.splice(index, 1)
    },
    range1Change(row, index) {
      // 选择项为自定义
      if (row.condition_key === 'CUSTOM' && row.condition_operation_symbol === 'BETWEEN') {
        this.tableDataData1.content[index].range1Disabled = false
        this.tableDataData1.content[index].range2Disabled = false
      } else {
        this.tableDataData1.content[index].range1Disabled = true
        this.tableDataData1.content[index].range2Disabled = true
        // console.log(this.$refs[`range2form+${index}`])
        // console.log(this.$refs[`range1form+${index}`])
        this.$refs[`range1form+${index}`].resetFields()
        this.$refs[`range2form+${index}`].resetFields()
        // this.tableDataData1.content[index].condition_value = ''
        // this.tableDataData1.content[index].condition_value_other = ''
      }
    },
    range2Change(row, index) {
      // 选择项为自定义
      if (row.name4 === '12') {
        this.tableDataData1.content[index].range2Disabled = false
      } else {
        this.tableDataData1.content[index].range2Disabled = true
        this.$refs[`range2form+${index}`].clearValidate()
      }
    },
    conditionChange(row, index) {
      // 条件操作符 为之间,开放值域2
      if (row.condition_operation_symbol === 'BETWEEN' && row.condition_key === 'CUSTOM') {
        this.tableDataData1.content[index].range1Disabled = false
        this.tableDataData1.content[index].range2Disabled = false
      } else {
        this.tableDataData1.content[index].range1Disabled = true
        this.tableDataData1.content[index].range2Disabled = true

        // console.log(this.$refs[`range2form+${index}`])
        // console.log(this.$refs[`range1form+${index}`])
        this.$refs[`range1form+${index}`].resetFields()
        this.$refs[`range2form+${index}`].resetFields()
        // this.tableDataData1.content[index].condition_value = ''
        // this.tableDataData1.content[index].condition_value_other = ''
      }
    },
    saveSharingData() {
      this.$refs[`sysForm`].validate((valid, object) => {
        if (valid) {
          const validateList = []
          if (this.tableDataData1.content.length !== 0) {
            this.tableDataData1.content.forEach((e, index) => {
              if (e.range1Disabled === false) {
                this.$refs[`range1form+${index}`].validate((valid, object) => {
                  validateList.push(valid)
                  if (!valid) {
                    let str = []
                    for (let key in object) {
                      object[key].map(item => {
                        str.push(item.message)
                      })
                      let dom = this.$refs[Object.keys(object)[0]]
                      if (Object.prototype.toString.call(dom) !== '[object Object]') {
                        dom = dom[0]
                        break //结束语句并跳出语句，进行下个语句执行
                      }
                      // 定位代码
                      dom.$el.scrollIntoView({
                        block: 'center',
                        behavior: 'smooth'
                      })
                    }
                  }
                })
              }
              if (e.range2Disabled === false) {
                this.$refs[`range2form+${index}`].validate((valid, object) => {
                  validateList.push(valid)
                  if (!valid) {
                    let str = []
                    for (let key in object) {
                      object[key].map(item => {
                        str.push(item.message)
                      })
                      let dom = this.$refs[Object.keys(object)[0]]
                      if (Object.prototype.toString.call(dom) !== '[object Object]') {
                        dom = dom[0]
                        break //结束语句并跳出语句，进行下个语句执行
                      }
                      // 定位代码
                      dom.$el.scrollIntoView({
                        block: 'center',
                        behavior: 'smooth'
                      })
                    }
                  }
                })
              }
            })
          }
          if (validateList.length === 0 || validateList.indexOf(false) === -1) {
            this.saveData = {}
            Object.assign(this.saveData, this.sysForm)
            this.saveData.config_json = {
              ...this.sysForm,
              data_item_list: '',
              search_condition_list: ''
            }
            this.saveData.config_json.data_item_list = this.tableDataData.content
            this.saveData.config_json.search_condition_list = this.tableDataData1.content.map(e => {
              return {
                data_item_name: e.data_item_name,
                condition_operation_symbol: e.condition_operation_symbol,
                condition_key: e.condition_key,
                condition_value: e.condition_value,
                condition_value_other: e.condition_value_other
              }
            })
            // console.log(this.saveData)
            this.saveData.config_json = JSON.stringify(this.saveData.config_json)
            if (this.$route.query.flagCatalog === 'add') {
              this.$router.push({
                params: { saveData: this.saveData },
                name: 'certification_List_add',
                query: this.$route.query
              })
            } else {
              console.log('this.saveData',this.saveData)
              this.$router.push({
                params: { saveData: this.saveData },
                name: 'certification_List_edit',
                query: this.$route.query
              })
            }
          }
        }
      })
    },
    query() {
      this.themeParam.page_number = this.dataSubjectData.currentPage
      this.themeParam.page_size = this.dataSubjectData.pageSize
      this.getThemePage()
    },
    getThemePageBysearch() {
      this.dataSubjectData.currentPage = 1
      this.dataSubjectData.pageSize = 10
      this.themeParam.page_number = 1
      this.themeParam.page_size = 10
      this.getThemePage()
    },
    getThemePage() {
      getThemePage(this.themeParam).then(res => {
        if (res.data != null && res.meta.code === '200') {
          this.dataSubjectData.content = res.data.content
          this.dataSubjectData.total = res.data.totalElements
          this.dataSubjectData.content.forEach((e, key) => {
            // e.id = key
            e.id = e.data_shared_theme_code
          })
        }
      })
    },
    getSystemSelectItem() {
      getSystemSelectItem().then(res => {
        if (res.data != null && res.meta.code === '200') {
          this.sysOptions = res.data
        }
      })
    },
    getConditionKeySelectItem() {
      getConditionKeySelectItem().then(res => {
        if (res.data != null && res.meta.code === '200') {
          this.valueList = res.data
        }
      })
    },
    getThemeData() {
      if (this.themeSelectData != '') {
        this.dialogDataVisible = false
        this.sysForm.data_theme_name = this.themeSelectData.data_shared_theme_name
        this.sysForm.data_theme_code = this.themeSelectData.data_shared_theme_code
        this.getConfigDetail()
      } else {
        this.$message({
          message: '请选择主题',
          type: 'warning'
        })
      }
    },
    getConfigDetail() {
      if (this.sysForm.system_code != '') {
        getConfigDetail(this.sysForm).then(res => {
          if (res.data != null && res.meta.code === '200') {
            this.tableDataData.content = res.data.data_item_list

            this.dataItemLsit = this.tableDataData.content.map(e => {
              return { value: e.data_item_name, label: e.data_item_name }
            })
            // console.log('this.dataItemLsit',this.dataItemLsit)
            this.sysForm.system_api_url = res.data.system_api_url
          } else {
            this.$message({
              message: res.meta.message,
              type: 'error'
            })
          }
        })
      } else {
        this.$message({
          message: '请先选择系统名称，再选择数据主题名称',
          type: 'warning'
        })
      }
    },
    getConfigDetailByid() {
      getConfigDetailByid(this.$route.query.id).then(res => {
        if (res.data != null && res.meta.code === '200') {
          const sharData = res.data
          this.sysForm = {
            system_code: sharData.system_code,
            data_theme_code: sharData.data_theme_code,
            data_theme_name: sharData.data_theme_name,
            system_api_url: sharData.system_api_url,
            system_name: sharData.system_name
          }
          this.tableDataData.content = sharData.data_item_list
          this.dataItemLsit = this.tableDataData.content.map(e => {
            return { value: e.data_item_name, label: e.data_item_name }
          })
          this.tableDataData1.content = sharData.search_condition_list.map(e => {
            // if (e.condition_key === 'CUSTOM') {
            //   e.range1Disabled = false
            // } else {
            //   e.range1Disabled = true
            // }
            // if (e.condition_operation_symbol === 'BETWEEN') {
            //   e.range2Disabled = false
            // } else {
            //   e.range2Disabled = true
            // }
            if (e.condition_operation_symbol === 'BETWEEN' && e.condition_key === 'CUSTOM') {
              e.range1Disabled = false
              e.range2Disabled = false
            } else {
              e.range1Disabled = true
              e.range2Disabled = true
            }
            return { ...e }
          })
        }
      })
    },
    selectionChange(data) {
      this.themeSelectData = data[0]
    },
    getQueryFilterOperationSelectItem() {
      getQueryFilterOperationSelectItem().then(res => {
        if (res.data != null && res.meta.code === '200') {
          this.conditionList = res.data
        }
      })
    },
    selectChange(data) {
      const selectData = this.sysOptions.filter(e => {
        return e.value == data
      })
      this.sysForm.system_name = selectData[0].label
    },
    back() {
      if (this.$route.query.flagCatalog === 'add') {
        this.$router.push({
          params: { saveData: this.saveData },
          name: 'certification_List_add',
          query: this.$route.query
        })
      } else {
        this.$router.push({
          params: { saveData: this.saveData },
          name: 'certification_List_edit',
          query: this.$route.query
        })
      }
    },
    screenWidth() {
      if (screen.width == 1920) {
        this.is1366 = false
      } else if (screen.width == 1366) {
        this.is1366 = true
      } else {
        this.is1366 = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.select {
  width: 100%;
}
.table-title {
  display: flex;
  justify-content: space-between;
  padding: 0 20px;
}
.check-text {
  margin-left: 10px;
}
.red {
  color: #f56c6c;
}
.plusicon {
  color: #409eff;
  font-size: 20px;
  cursor: pointer;
}
.rangeform ::v-deep .el-form-item {
  margin-top: 20px;
}
.dialogwrap::v-deep .el-dialog__body {
  padding-bottom: 0;
  padding-top: 5px;
}
.dialogwrap::v-deep .table {
  margin-bottom: 0px;
}
.dialogwrap::v-deep .el-card__body {
  padding-top: 0px;
}
</style>