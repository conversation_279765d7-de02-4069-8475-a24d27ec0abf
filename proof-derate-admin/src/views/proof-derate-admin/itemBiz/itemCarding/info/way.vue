<template>
  <div class="content-wrapper padding-10">
    <!-- <papeTitle :title-name="dataConfig.title +':'+ item_vo.item_name" :is-has-back="true" @goToList="backPrev">
      <div>
        <el-button v-show="type === 'confirm'" type="warning" v-permission="'catalog:biz:proof_comb:back'" @click="stillNeedCleanHandel">仍需清理</el-button>
        <el-button :loading="examineApproveLoading" type="primary" v-permission="'catalog:biz:proof_comb:confirm'" @click="examineApproveHandel">提交审批</el-button>
      </div>
    </papeTitle> -->
    <CardTitle :title-name="dataConfig.title +'：'+ item_vo.item_name" :ifback="true" @back="backPrev()">
      <template>
       <el-button v-show="type === 'confirm'" type="warning" v-permission="'catalog:biz:proof_comb:back'" @click="stillNeedCleanHandel">仍需清理</el-button>
        <el-button v-show="type === 'confirm'" :loading="examineApproveLoading" type="primary" v-permission="'catalog:biz:proof_comb:confirm'" @click="examineApproveHandel">提交审批</el-button>
      </template>
    </CardTitle>
      <el-row>
        <el-col :span="24">
          <el-tabs v-model="dataConfig.activeName" tab-position="top" @tab-click="handleClick">
            <el-tab-pane label="详情" name="desc">
              <!-- <el-divider content-position="left">基本信息</el-divider> -->
              <!-- <span class="margin-left-10 info-wrap">
                <img :src="arrow" alt />
                <span class="info-title">基本信息</span>
              </span>-->
              <el-descriptions class="descriptions" title="基本信息" :column="2" border>
                <el-descriptions-item :label-style="{width:'140px'}">
                  <template slot="label">事项名称</template>
                  {{ item_vo.item_name }}
                </el-descriptions-item>
                <el-descriptions-item :label-style="{width:'140px'}">
                  <template slot="label">事项编码</template>
                  {{ item_vo.item_code }}
                </el-descriptions-item>
                <el-descriptions-item :label-style="{width:'140px'}">
                  <template slot="label">实施机构</template>
                  {{ item_vo.impl_org_name }}
                </el-descriptions-item>
                <el-descriptions-item :label-style="{width:'140px'}">
                  <template slot="label">事项类型</template>
                  {{ item_vo.item_type }}
                </el-descriptions-item>
              </el-descriptions>

              <!-- <el-row>
                <el-col :span="8" :offset="2">实施机构: {{ item_vo.impl_org_name }}</el-col>
                <el-col :span="8" :offset="2">事项类型: {{ item_vo.item_type }}</el-col>
              </el-row>-->
              <el-divider content-position="left" />
              <clean-method
                v-for="(data,index) in multipleData"
                :key="index"
                :ref="'formRelevance_'+index"
                :data="data"
                :dict-data="dictData"
                :type="type"
                :is-dis-show-proof-catalog="type==='show'"
                :multiple-data="data"
                :proof-route="proofRoute"
              />
            </el-tab-pane>
            <el-tab-pane v-if="needShowTips" label="过程信息" name="info">
              <item-process-info ref="processInfo" :item-code="itemId" :dict-data="dictData" />
            </el-tab-pane>
          </el-tabs>
        </el-col>
      </el-row>
      <!--梳理确认-->
      <!-- 只能状态为待梳理确认可以修改 -->
      <!-- <div class="fixed-bottom" v-if="type === 'confirm' || type === 'audit'">
        <div class="btn-group">

          <el-button v-show="type === 'confirm'" type="warning" @click="stillNeedCleanHandel">仍需清理</el-button>
          <el-button :loading="examineApproveLoading" type="primary" @click="examineApproveHandel">提交审批</el-button>
        </div>
      </div>-->
  </div>
</template>

<script>
import { mapState } from 'vuex'
import { getProofStatusList, getItemTypeList, getReplaceCancelWay, getUnitTypeList, getProofClearType } from '@/api/common/dict'
import { getProofListFormViewCode, proofListConfirmCreateByCode, proofListConfirmRemainClear, getProofSubmitInfo, getProofConfirmInfo, getProofAuditInfo } from '@/api/itemBiz/list'
import CleanMethod from '@/views/proof-derate-admin/components/CleanMethod'
import ItemProcessInfo from '@/views/proof-derate-admin/components/ItemProcessInfo'
import papeTitle from '@/components/papeTitle'
import CardTitle from '@/components/CardTitle'
export default {
  name: 'ItemCardingInfoWay',
  components: {
    CleanMethod,
    ItemProcessInfo,
    papeTitle,
    CardTitle
  },
  data() {
    return {
      arrow: require('@/assets/proof-derate-admin-images/arrow.png'),
      dataConfig: {
        id: '',
        title: '事项名称',
        activeName: 'desc'
      },
      data: {
        id: '',
        title: '证明档案',
        activeName: 'desc'
      },
      multipleData: [],
      type: '',
      proofRoute: 'item_carding_info_first_draft',
      replaceCancelWayList: [], // 替代取消方式
      proofStatusList: [], // 事项状态
      actualizeList: [], // 行政区划字典
      getItemTypeList: [], // 事项类型
      unitTypeList: [], // 证明开具单位类型:
      proofClearTypeList: [], // 取消方式:
      auditList: [
        { label: '同意', value: 'APPROVED' },
        { label: '不同意', value: 'UNAPPROVED' }
      ],
      item_vo: {},
      proof_data: [],
      dictData: {},
      proofRecordList: Array(3),
      examineApproveLoading: false,
      needShowTips: false,
      itemTips: '',
      titleName: '证明材料清理'
    }
  },
  computed: {
    ...mapState({
      name: state => state.user && state.user.userdata && state.user.userdata.userAccount && state.user.userdata.userAccount.name
    })
  },
  watch: {
    'item_vo.item_type'(val) {
      const getItemTypeList = this.getItemTypeList
      if (getItemTypeList.length > 0 && val !== '') {
        const info = _.find(getItemTypeList, i => i.value === this.item_vo.item_type)
        this.item_vo.item_type = info === undefined ? val : info.label
      }
    }
  },
  created() {
    this.data.id = this.$route.query['id']
    this.itemId = this.data.id
    this.type = this.$route.query['type']
    this.initData()
  },
  methods: {
    getTips(info) {
      this.needShowTips = info.needShowTips
      this.itemTips = info.itemTips
    },
    backPrev() {
      this.$router.go(-1)
    },
    handleClick(tab, event) {
      // console.log(tab, event);
    },
    initData: async function() {
      const proofStatusListRes = await getProofStatusList()
      const itemTypeListRes = await getItemTypeList()
      const replaceCancelWayRes = await getReplaceCancelWay()
      const unitTypeListRes = await getUnitTypeList()
      const proofClearTypeListRes = await getProofClearType()
      this.proofStatusList = proofStatusListRes.data || []
      this.getItemTypeList = itemTypeListRes.data || []
      this.replaceCancelWayList = replaceCancelWayRes.data || []
      this.unitTypeList = unitTypeListRes.data || []
      this.proofClearTypeList = proofClearTypeListRes.data || []

      // 被await阻塞的同步代码
      this.dictData = {
        replaceCancelWayList: this.replaceCancelWayList, // 替代取消方式
        proofStatusList: this.proofStatusList, // 事项状态
        getItemTypeList: this.getItemTypeList, // 事项类型
        unitTypeList: this.unitTypeList, // 证明开具单位类型:
        proofClearTypeList: this.proofClearTypeList // 取消方式:
      }
      // console.log('this.dictData', this.dictData)
      this.needShowTips = true
      this.getProofListFormView()
    },
    getProofListFormView() {
      getProofListFormViewCode(this.data.id, {
        item_clear_status: 'WAIT_FOR_CLEAN'
      }).then(res => {
        // console.log('getProofListFormView', res)
        this.item_vo = res.data.item_vo
        const proof_data = res.data.proof_data
        const item_detail = []
        if (proof_data.length > 0) {
          proof_data.forEach(i => {
            item_detail.push(this.reorganizeData(i))
          })
        }
        this.proof_data = item_detail
        this.getMultipleData()
      })
    },
    getMultipleData() {
      // console.log('this.proof_data', this.proof_data)
      const proof_list_id_List = this.proof_data.map(e => {
        return e.material.material_id
      })
      // proof_list_id_List.indexOf('be2c47e6-4746-4c65-a631-1790ad7d22e8')
      const itemList = this.getfunction(proof_list_id_List)
      const multipleItemList = []
      const unmultipleItemList = []
      // console.log('13123', this.getfunction(proof_list_id_List))
      itemList.forEach(e => {
        const item = this.proof_data.filter(i => i.material.material_id === e)
        multipleItemList.push({ materialData: item })
      })
      this.proof_data.forEach(e => {
        if (itemList.indexOf(e.material.material_id) === -1) {
          unmultipleItemList.push({ materialData: [e] })
        }
      })
      const li = multipleItemList.concat(unmultipleItemList)
      // console.log('multipleItemList', multipleItemList, 'unmultipleItemList', unmultipleItemList, 'li', li)
      this.multipleData = li
      // proof_list_id_List.filter(i=>i==='')
      // console.log('proof_list_id_List',proof_list_id_List,proof_list_id_List.indexOf(''),proof_list_id_List.filter(i=>i===''))
    },
    getfunction(list) {
      var tmp = []
      list
        .concat()
        .sort()
        .sort(function(a, b) {
          if (a == b && tmp.indexOf(a) === -1) tmp.push(a)
        })
      return tmp
    },
    reorganizeData(res) {
      const item_material_vo = res.item_material_vo
      const proof_list_vo = res.proof_list_vo
      let material = {}
      const formRelevance = {
        relevance: ''
      }
      const selectForm = {
        proof_catalog_name: ''
      }
      const selectList = {
        id: ''
      }
      const proof_catalog_vo = res.proof_catalog_vo_list[0] || {}
      // console.log('proof_catalog_vo', proof_catalog_vo)
      if (proof_catalog_vo) {
        formRelevance.relevance = proof_catalog_vo.name
        selectForm.proof_catalog_name = proof_catalog_vo.name
        selectList.id = proof_catalog_vo.id
      }

      material =
        {
          item_proof_status: proof_list_vo.item_proof_status, // 梳理状态
          // 证明材料清理
          material_name: item_material_vo.material_name,
          law_name: item_material_vo.law_name,
          material_id: item_material_vo.id,
          proof_clear_type: proof_list_vo.proof_clear_type, // 取消类型 DIRECTLY_CANCEL|REPLACE_CANCEL|DO_NOT_CLEAN
          proof_clear_type_text: this.proofClearTypeList.filter(i => i.value === proof_list_vo.proof_clear_type)[0].label || proof_list_vo.proof_clear_type, // 取消类型 DIRECTLY_CANCEL|REPLACE_CANCEL|DO_NOT_CLEAN
          direct_description: proof_list_vo.direct_description, // 直接取消说明,
          not_clean_description: proof_list_vo.not_clean_description, // 无需清理说明,
          proof_list_remark: proof_list_vo.proof_list_remark // 备注
        } || {}

      if (proof_list_vo.proof_clear_type === 'REPLACE_CANCEL') {
        const replaceCancelWayList = proof_list_vo.replace_cancel_way.split(',')
        let replaceCancelWayListText = ''
        _.forEach(replaceCancelWayList, i => {
          const way = _.find(this.replaceCancelWayList, j => j.value === i)
          const wayLabel = way ? way.label : proof_list_vo.replace_cancel_way
          replaceCancelWayListText += wayLabel + ','
        })
        material = Object.assign(material, {
          proof_list_id: proof_list_vo.id,
          item_proof_status: proof_list_vo.item_proof_status, // 梳理状态
          replace_cancel_way: proof_list_vo.replace_cancel_way, // 替代取消方式
          replace_cancel_way_text: replaceCancelWayListText.substring(0, replaceCancelWayListText.length - 1), // 替代取消方式的文本
          license_name: proof_list_vo.license_name, // 电子证照名称，逗号隔开
          license_code: proof_list_vo.license_code, // 电子证照code，逗号隔开
          license_description: proof_list_vo.license_description, // 转换为电子证照说明
          license_item_name: proof_list_vo.license_item_name, // 电子证明名称，逗号隔开
          license_item_code: proof_list_vo.license_item_code, // 电子证明code，逗号隔开
          license_item_description: proof_list_vo.license_item_description, // 转换为电子证明说明
          commit_book_description: proof_list_vo.commit_book_description, // 承诺书说明
          commit_attachment_id: proof_list_vo.commit_attachment_id, // 承诺书说明
          commit_attachment_name: proof_list_vo.commit_attachment_name, // 承诺书说明
          industry_dept_name: proof_list_vo.industry_dept_name, // 所属行业部门名称
          industry_dept_code: proof_list_vo.industry_dept_code, // 所属行业部门代码
          // proof_provide_type_investigation:
          //   proof_list_vo.proof_provide_type_investigation !== null
          //     ? this.unitTypeList.find(i => i.value === proof_list_vo.proof_provide_type_investigation).label
          //     : proof_list_vo.proof_provide_type_investigation,
          proof_provide_type_dataShared:
            proof_list_vo.proof_provide_type_dataShared !== null
              ? this.unitTypeList.find(i => i.value === proof_list_vo.proof_provide_type_dataShared).label
              : proof_list_vo.proof_provide_type_dataShared, // 证明开具单位类型

          data_shared_description: proof_list_vo.data_shared_description, // 数据共享说明
          investigation_description: proof_list_vo.investigation_description, // 人工协查说明
          dept_cancel_description: proof_list_vo.dept_cancel_description, // 自行调查说明 （部门自行调查)
          other_clear_description: proof_list_vo.other_clear_description // 其它说明
        })
      }
      const license_name = proof_list_vo.license_name != null ? proof_list_vo.license_name.split(',') : ''
      const license_code = proof_list_vo.license_code != null ? proof_list_vo.license_code.split(',') : ''
      const content = _.map(license_name, function(i, index) {
        return { license_name: i, license_code: license_code[index] }
      })
      return {
        proof_catalog_vo: proof_catalog_vo,
        formRelevance: { relevance: proof_catalog_vo.name },
        selectForm: { proof_catalog_name: proof_catalog_vo.name },
        selectList: { id: proof_catalog_vo.id },
        material: material,
        tableData: { content: content }
      }
    },
    // 仍需清理
    stillNeedCleanHandel() {
      this.$confirm('确定流转至待清理?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
        // type: "warning"
      }).then(() => {
        proofListConfirmRemainClear(this.data.id, {
          tease_account_name: this.name
        }).then(res => {
          const type = res.meta.code === '200' ? 'success' : 'warning'
          this.$message({
            type: type,
            message: res.meta.code === '200' ? '操作成功' : res.meta.message
          })
          if (type === 'success') this.$router.go(-1)
        })
      })
    },
    // 梳理确认
    examineApproveHandel() {
      console.log('this.multipleData', this.multipleData)
      const _this = this
      const examineApproveData = []
      for (let i = 0; i <= this.multipleData.length - 1; i++) {
        if (this.multipleData[i].materialData[0]['material'].hasOwnProperty('proof_list_id')) {
          this.$refs['formRelevance_' + i][0].examineApprove()
          // examineApproveData.push(this.$refs['formRelevance_' + i][0].catalogReplaceInfo)
          examineApproveData.push(this.$refs['formRelevance_' + i][0].catalogReplaceInfoList)
        } else {
          examineApproveData.push(null)
        }
      }

      // 移除非替代取消
      const proof_data = examineApproveData.filter(i => i)
      const proof_data1 = []
      console.log('proof_data', proof_data)
      proof_data.forEach(e => {
        e.forEach(e1 => {
          const item = {
            proof_catalog_id: e1.proof_catalog_id,
            // proof_list_id: e1.proof_list_id
            item_material_id: e1.item_material_id,
            proof_list_remark: e1.proof_list_remark
          }
          proof_data1.push(item)
        })
      })
      // 如果是审核 必填的需校验通过
      if (!proof_data.some(i => i.proof_catalog_id === '')) {
        const sendData = {
          item_code: this.data.id,
          confirm_status: 'CARDING_CONFIRMED', // 待审核
          tease_account_name: '系统管理员',
          proof_data: proof_data1
        }
        console.log('sendData', sendData)
        this.submitData(sendData)
      }
    },
    submitData(sendData) {
      proofListConfirmCreateByCode(sendData).then(res => {
        const type = res.meta.code === '200' ? 'success' : 'warning'
        this.$message({
          type: type,
          message: res.meta.code === '200' ? '操作成功' : res.meta.message
        })
        if (type === 'success') this.$router.go(-1)
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.fixed-bottom {
  width: 100%;
  transform: scale3d(1, 1, 1);
  .btn-group {
    background: rgba(255, 255, 255, 0.95);
    border-top: 1px solid #096dd9;
    padding: 20px 10px 10px 50px;
    // position: absolute;
    bottom: 0;
    right: 0;
    z-index: 100;
    text-align: right;
    height: 80px;
    width: inherit;
  }
}
.content-header {
  position: absolute;
  top: 60px;
  right: 0;
  z-index: 111;
}
.info-wrap {
  display: flex;
  align-items: center;
  img {
    margin-right: 10px;
  }
}
.descriptions {
  margin-top: 10px;
  padding: 0 10px;
}
.margin-24 {
  margin-bottom: 24px;
}
</style>
