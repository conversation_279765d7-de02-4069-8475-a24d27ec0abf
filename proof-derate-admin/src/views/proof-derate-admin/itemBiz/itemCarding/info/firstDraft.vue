<template>
  <div class="content-wrapper">
    <section class="content-header">
      <span class="breadcrumb" align="right">
        <el-button type="warning" plain @click="backPrev" icon="el-icon-back">返回</el-button>
      </span>
      <br />
    </section>
    <section class="content">
        <first-draft-detail :item-code="itemId" />
    </section>
  </div>
</template>

<script>
import FirstDraftDetail from "@/views/proof-derate-admin/components/FirstDraftDetail";
export default {
  name: "ItemCardingFirstDraft",
  components: {
    FirstDraftDetail
  },
  data() {
    return {}
  },
  created() {
    this.itemId = this.$route.query["id"];
  },
  methods: {
    backPrev() {
      this.$router.go(-1);
    }
  }
};
</script>

