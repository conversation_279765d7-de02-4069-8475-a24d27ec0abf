<template>
  <div class="content-wrapper">
    <section class="content-header">
      <span class="breadcrumb" align="right">
        <el-button type="warning" plain @click="backPrev" icon="el-icon-back">返回</el-button>
      </span>
      <br />
    </section>
    <section class="content">
      <clean-method-edit :data=data :type="type" :proofRoute="proofRoute"/>
    </section>
  </div>
</template>

<script>
import CleanMethodEdit from "@/views/proof-derate-admin/components/CleanMethodEdit";
export default {
  name: "ItemCardingEdit",
  components: {
    CleanMethodEdit
  },
  data() {
    return {
      data: {
        id: "",
        title: "证明修改",
        activeName: "desc",
      },
      type: "",
      proofRoute: "item_carding_info_first_draft"
    };
  },
  mounted() {
    this.data.id = this.$route.query["id"];
    this.type = this.$route.query["type"];
  },
  methods: {
    backPrev() {
      this.$router.go(-1);
    }
  }
};
</script>

<style lang="scss" scoped>

</style>
