<template>
  <div class="content-wrapper padding-10">
    <!-- <section class="content-header">
      <h1>证明材料梳理</h1>
    </section>-->
    <!-- <papeTitle :title-name="titleName" :is-has-back="false" /> -->
    <CardTitle :title-name="titleName">
      <template></template>
    </CardTitle>
    <!-- <section class="content"> -->
    <el-card class="box-card" shadow="never">
      <query-form ref="queryForm" style="padding:0 10px" @click="search" />
      <div style="color: #888; padding:20px 10px 0" class="dashed-line">
        共
        <span class="text-red">{{ tableData.total }}</span> 条符合查询条件
      </div>
      <custom-table
        ref="table"
        :is-card-type="false"
        :table-data="tableData"
        :table-header="tableHeader"
        :span-method="objectSpanMethod"
        :stripe="false"
        :table-tools="tableTools"
        style="margin-top: 10px"
        @refresh="query(1)"
        @query="query"
      >
        <template #item_name="{ row }">
          <!-- <div>
              <el-button
                type="text"
                @click="goProofFile(row)"
              >{{ row.handing_item === null || val === undefined ? row.item_name : row.item_name + '【' + row.handing_item + '】' }}</el-button>
          </div>-->
          <!-- <div v-else>{{ row.handing_item === null || val === undefined ? row.item_name : row.item_name + '【' + row.handing_item + '】' }}</div> -->
          <div>{{ row.handing_item === null || val === undefined ? row.item_name : row.item_name + '【' + row.handing_item + '】' }}</div>
        </template>
        >
        <template #operate="{ row }">
          <div v-show="row.item_clear_status === 'CARDING_CONFIRMED'">
            <el-button
              type="text"
              @click="goItemProofFile(row)"
              v-if="isPermission($route.meta.permission,'catalog:biz:proof_comb:info')"
            >查看</el-button>
          </div>
          <div v-show="row.item_clear_status === 'CARDING_UNCONFIRMED'">
            <el-button
              type="text"
              @click="cardimgConfirm(row)"
              v-if="isPermission($route.meta.permission,'catalog:biz:proof_comb:info')"
            >梳理确认</el-button>
          </div>
        </template>
      </custom-table>
    </el-card>
    <!-- </section> -->
  </div>
</template>

<script>
import QueryForm from '@/views/proof-derate-admin/itemBiz/itemCarding/components/QueryForm'
import { getCardingUnconFirmedPage, getCardingUnconFirmedPageCount } from '@/api/itemBiz/clear'
import CustomTable from '@/components/Element/Table'
import Enum from '@/utils/enum'
import CardTitle from '@/components/CardTitle'
import { getProofStatusList, getStandardTypeList, getItemMaterialStandardType } from '@/api/common/dict'
import papeTitle from '@/components/papeTitle'
import { isPermission } from '@/utils/index.js'
export default {
  name: 'MattersCheck',
  components: {
    QueryForm,
    CustomTable,
    papeTitle,
    CardTitle
  },
  data() {
    return {
      proofStatusList: [], // 事项证明状态
      proofStatusAllList: [], // 所有的事项证明状态
      item_source: [], // 事项标准类型
      item_material_source: [], // 证明标准类型
      formData: {
        sxmc: '', // 事项名称
        blxmc: '', // 办理项名称
        zmmc: '', // 证明名称
        zmslzt: '', // 证明梳理状态
        divisionCode: '', // 实施区划
        orgCode: '', // 实施机构
        standardSubject: [], // 事项标准类型
        standard: [] // 证明标准类型
      },

      loading: false,
      tableData: {
        border: false,
        content: [], // 表格数据
        loading: false, // 控制表格加载效果
        total: 0, // 分页查询时的总数，去掉该属性，不显示分页条
        currentPage: 1, // 当前页码
        pageSize: 10,
        pageDirection: 'desc',
        isShowSelection: false, // 是否显示多选框，默认false
        itemClearStatus: 'CARDING_UNCONFIRMED,CARDING_CONFIRMED',
        isShowIndex: true
      },
      tableTools: [],

      tableHeader: [
        {
          label: '事项名称',
          slot: 'item_name',
          prop: 'item_name',
          minWidth: '200px',
          align: 'left',
          formatter: (row, col, val) => {
            return row.handing_item === null || val === undefined ? row.item_name : row.item_name + '【' + row.handing_item + '】'
          }
        },
        { label: '事项编码', prop: 'item_code', minWidth: '180px',align: 'left', },
        { label: '办理项名称', prop: 'handing_item', minWidth: '180px',align: 'left', },
        { label: '实施机构', prop: 'impl_org_name', minWidth: '160px',align: 'left', }, // 配置slot属性，可支持使用插槽

        // { label: "证明名称", prop: "material_name", minWidth: "180px" },
        // { label: "是否关联证明目录", prop: "associated_proof_catalog", minWidth: "180px" },
        {
          label: '事项证明状态',
          prop: 'item_clear_status',
          minWidth: '80px',
          align: 'left',
          formatter: (row, col, val) => {
            // return Enum.proofStatusList.find(i => i.value === val).label;
            if (val) {
              return this.proofStatusAllList.find(i => i.value === val).label
            } else {
              return val
            }
          }
        },
        { label: '操作', slot: 'operate', width: '120px',align: 'left', }
      ],
      detailId: '',
      align: 'center',
      SXCKXQ: false,
      userdata: {}, // 用户角色与权限
      titleName: '证明材料梳理'
    }
  },
  mounted: function () {
    this.initData()
    // const { roles, rolePermissionVos } = this.$store.getters.userdata
    // this.userdata = { roles, rolePermissionVos }
  },
  beforeRouteLeave(to, from, next) {
    from.meta.keepAlive = false
    next()
  },
  methods: {
    isPermission,
    initData: async function () {
      const proofStatusListRes = await getProofStatusList()
      const standardTypeListRes = await getStandardTypeList()
      const itemMaterialStandardTypeRes = await getItemMaterialStandardType()
      this.getProofStatusList(proofStatusListRes)
      this.getStandardTypeList(standardTypeListRes)
      this.getItemMaterialStandardType(itemMaterialStandardTypeRes)
      // 被await阻塞的同步代码
      this.query(1, 'init')
    },
    getProofStatusList(res) {
      let data = res.data || []
      this.proofStatusAllList = data
      data = data.filter(i => {
        return i.value === 'CARDING_UNCONFIRMED' || i.value === 'CARDING_CONFIRMED'
      })
      Enum.proofStatusList.splice(0)
      Enum.proofStatusList.push(...data)
      const proofStatusList = [] // 事项证明状态

      _.forEach(Enum.proofStatusList, item => {
        proofStatusList.push(item.value)
      })
      this.proofStatusList = proofStatusList
    },
    getStandardTypeList(res) {
      const data = res.data || []
      Enum.standardTypeList.splice(0)
      Enum.standardTypeList.push(...data)

      const item_source = [] // 事项标准类型
      _.forEach(Enum.standardTypeList, item => {
        item_source.push(item.value)
      })
      this.item_source = item_source
    },
    getItemMaterialStandardType(res) {
      const data = res.data || []
      Enum.proofStandardTypeList.splice(0)
      Enum.proofStandardTypeList.push(...data)
      const item_material_source = [] // 证明标准类型
      _.forEach(Enum.proofStandardTypeList, item => {
        item_material_source.push(item.value)
      })
      this.item_material_source = item_material_source
    },
    search(data) {
      this.query(1, 'search', data)
    },
    // currentPage: 当前页码
    query(currentPage, type, data) {
      this.tableData.loading = true
      // if (_.isNumber(currentPage)) {
      //   this.tableData.currentPage = currentPage
      // }
      const formData = Object.assign({}, this.$refs['queryForm'].form)
      console.log('formData', formData)
      const proofStatusList = type === 'init' ? this.proofStatusList.join() : formData['item_clear_status'].join()
      const item_source = type === 'init' ? this.item_source.join() : formData['item_source'].join()
      const item_material_source = type === 'init' ? this.item_material_source.join() : formData['item_material_source'].join()
      const sendData = {
        ...formData,
        page_size: this.tableData.pageSize,
        page_number: this.tableData.currentPage,
        page_direction: this.tableData.pageDirection,
        // item_material_source: item_material_source,
        item_material_source: '',
        item_source: item_source,
        item_clear_status: proofStatusList
      }
      console.log('sendData', sendData)
      Object.keys(sendData).forEach(item => {
        if (!sendData[item]) delete sendData[item]
      })
      if (!sendData['item_clear_status']) {
        sendData['item_clear_status'] = 'CARDING_UNCONFIRMED,CARDING_CONFIRMED'
      }
      // sendData['item_status'] = 'WORK'
      sendData['item_code'] = formData['item_code']
      getCardingUnconFirmedPage(sendData)
        .then(res => {
          this.tableData.content = res.data.content || []
          this.tableData.loading = false
          this.tableData.total = Number(res.data.total_elements)
        })
        .catch(() => {
          this.tableData.content = []
          this.tableData.loading = false
        })
      // getCardingUnconFirmedPageCount(sendData)
      //   .then(res => {
      //     // this.tableData.loading = false
      //     this.tableData.total = Number(res.data.total_elements)
      //   })
      //   .catch(() => {
      //     // this.tableData.loading = false
      //   })
      // getOProofListPage(sendData)
      //   .then(res => {
      //     const data = res.data === null ? [] : res.data.content;
      //     this.tableData.content = mergeTableRow(data, [
      //       "impl_org_name",
      //       "item_name"
      //     ])
      //     this.tableData.total = res.data.totalElements;
      //   })
      //   .catch(() => {
      //     console.log("==证明材料查询error==");
      //   });
    },
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      const span = column['property'] + '-span'
      // console.log(span,row)
      if (row[span]) {
        return row[span]
      }
    },
    // 梳理确认
    cardimgConfirm(row) {
      this.$router.push({
        name: 'item_carding_info_way',
        query: {
          id: row.item_code,
          item_clear_status: 'DO_NOT_CLEAN',
          type: 'confirm'
        }
      })
    },
    // 事项证明档案
    goItemProofFile(row) {
      this.$router.push({
        name: 'item_carding_info_way',
        query: {
          id: row.item_code,
          item_clear_status: 'DO_NOT_CLEAN',
          type: 'show'
        }
      })
    },
    // 跳转到证明档案
    goProofFile(row) {
      this.$router.push({
        name: 'item_carding_info_proof',
        query: {
          id: row.item_code,
          item_clear_status: 'CLEAN'
        }
      })
    }
  }
}
</script>

<style scoped>
.content-wrapper {
  /* padding: 10px; */
}
</style>
