<template>
  <div>
    <el-form ref="form" :model="form" label-width="100px">
      <el-row>
        <el-col :xs="24" :sm="12" :md="12" :lg="9">
          <el-form-item label="事项名称">
            <el-input v-model="form.item_name" clearable placeholder="请输入事项名称" />
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12" :md="12" :lg="9">
          <el-form-item label="事项编码">
            <el-input v-model="form.item_code" clearable placeholder="请输入事项编码" />
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12" :md="12" :lg="3">
          <el-form-item>
            <el-button type="primary" @click="search">查询</el-button>
          </el-form-item>
        </el-col>
      </el-row>
      <!-- <el-row>
        <el-col :xs="24" :sm="12" :md="12" :lg="9">
          <el-form-item label="证明名称">
            <el-input v-model="form.proof_name" clearable placeholder="请输入证明名称" />
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12" :md="12" :lg="9">
          <el-form-item label="办理项名称">
            <el-input v-model="form.handing_item" clearable placeholder="请输入办理项名称" />
          </el-form-item>
        </el-col>
      </el-row>-->
      <el-row>
        <el-col :xs="24" :sm="12" :md="12" :lg="9">
          <el-form-item label="实施区划">
            <!-- <division-selector v-model="form.division_code" @change="divisionChange"></division-selector> -->
            <AdministrativeDivisionCascader
              :key="divisionCode"
              ref="AdministrativeDivisionSelect"
              :division-code="divisionCode"
              :permission-code="'catalog:biz:proof_comb:list'"
              @setDivisionCodeAndName="setDivisionCodeAndName"
            />
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12" :md="12" :lg="9">
          <el-form-item label="实施机构">
            <el-select v-model="form.credit_code" filterable placeholder="请选择" style="width:100%" clearable>
              <el-option v-for="item in organizationList" :key="item.credit_code" :label="item.name" :value="item.credit_code" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="9">
          <el-form-item label="事项证明状态">
            <el-checkbox-group v-model="form.item_clear_status">
              <el-checkbox v-for="item in proofStatusList" :key="item.value" :label="item.value">{{ item.label }}</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </el-col>
        <!-- <el-col :xs="24" :sm="12" :md="12" :lg="6"> -->
        <el-col :span="9">
          <el-form-item label="事项标准类型">
            <el-checkbox-group v-model="form.item_source">
              <el-checkbox v-for="item in standardTypeList" :key="item.value" :label="item.value">{{ item.label }}</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </el-col>
        <!-- <el-col :span="24" :xs="24" :sm="12" :md="12" :lg="6">
          <el-form-item label="证明标准类型">
            <el-checkbox-group v-model="form.item_material_source">
              <el-checkbox
                v-for="item in proofStandardTypeList"
                :key="item.value"
                :label="item.value"
              >{{ item.label }}</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </el-col>-->
      </el-row>
    </el-form>
  </div>
</template>

<script>
import Enum from '@/utils/enum'
import { getOrganizationList } from '@/api/commonPack/platManege'
import { getOrgListNoAuth } from '@/api/admin/org.js'
export default {
  name: 'QueryForm',
  components: { AdministrativeDivisionCascader: () => import('@/components/AdministrativeDivisionCascader') },
  data() {
    return {
      form: {
        item_name: '', // 事项名称
        handing_item: '', // 办理项名称
        proof_name: '', // 证明名称
        item_code: '', // 事项编码
        division_code: '', // 实施区划代码
        credit_code: '', // 实时机构代码
        item_clear_status: [], // 事项证明状态
        item_material_source: [], // 证明标准类型
        item_source: [] // 事项标准类型
      },
      standardTypeList: Enum.standardTypeList,
      proofStandardTypeList: Enum.proofStandardTypeList,
      actualizeList: Enum.actualizeList,
      organizationList: [],
      proofStatusList: Enum.proofStatusList,
      currentDivisionCode: '',
      isInitDivisionCode: '',
      divisionCode: '' // 行政区划编码
    }
  },
  computed: {},
  watch: {
    'form.division_code': {
      handler(val) {
        if (val) {
          this.form.credit_code = ''
          // this.getOrganizationList(val)
        } else {
          this.organizationList = []
        }
      },
      immediate: true
    },
    proofStatusList: {
      handler(val) {
        if (val.length > 0) {
          const itemType = []

          Enum.proofStatusList = Enum.proofStatusList.filter(i => {
            return i.value === 'CARDING_UNCONFIRMED' || i.value === 'CARDING_CONFIRMED'
          })
          Enum.proofStatusList.forEach(item => {
            itemType.push(item.value)
          })
          this.form['item_clear_status'] = itemType
        }
      }
    },
    standardTypeList: {
      handler(val) {
        if (val.length > 0) {
          const itemType = []
          Enum.standardTypeList.forEach(item => {
            itemType.push(item.value)
          })
          this.form['item_source'] = itemType
        }
      }
    },
    proofStandardTypeList: {
      handler(val) {
        if (val.length > 0) {
          const itemType = []
          Enum.proofStandardTypeList.forEach(item => {
            itemType.push(item.value)
          })
          this.form['item_material_source'] = itemType
        }
      }
    }
  },
  created() {
    // this.currentDivisionCode = this.$store.getters.userdata.userAccount.divisionCode
    // this.params.divisionCode = this.currentDivisionCode
    this.isInitDivisionCode = sessionStorage.getItem('isInitDivisionCode')
  },
  methods: {
    divisionChange(d) {
      this.form.division_code = d.code || ''
      // this.params.divisionCode = this.params.divisionCode.replace(/(0+)$/g, ""); //末尾去0
      this.organizationList = []
      this.form.credit_code = ''
      if (d.code) {
        getOrgListNoAuth({
          pageSize: 1000,
          pageNumber: 0,
          divisionCode: d.code
        }).then(res => {
          this.organizationList = res.content.map(i => {
            return { label: i.name, value: i.tyshxydm }
          })
        })
      }
    },
    search() {
      this.$emit('click', this.form)
    },
    setDivisionCodeAndName(data) {
      this.form.division_code = data.code || ''
      // this.params.divisionCode = this.params.divisionCode.replace(/(0+)$/g, ""); //末尾去0
      this.organizationList = []
      this.form.credit_code = ''
      this.getOrganizationList(this.form.division_code)
    },
    // 获取实施机构
    getOrganizationList(id) {
      const data = {
        division_code: id,
        permission_code: 'catalog:biz:proof_comb:list',
        scope: true
      }
      getOrganizationList(data).then(res => {
        if (res.meta.code === '200') {
          this.organizationList = res.data
        }
      })
    }
  }
}
</script>

<style scoped>
</style>
