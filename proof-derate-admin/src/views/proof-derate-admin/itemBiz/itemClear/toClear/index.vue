<template>
  <div>
    <el-card class="box-card" :body-style="{'padding-bottom':'0px'}" shadow="never">
      <query-form ref="queryForm" @click="search" />
    </el-card>
    <!-- <br /> -->
    <el-card class="box-card dashed-line" shadow="never">
      <div style="color: #888; padding:20px 10px 0" class="dashed-line">
        共
        <span class="text-red">{{ tableData.total }}</span> 条符合查询条件
      </div>
      <custom-table
        ref="table"
        :is-card-type="false"
        :table-data="tableData"
        :table-header="tableHeader"
        :show-input="false"
        :stripe="false"
        :table-tools="tableTools"
        style="margin-top: 10px"
        @query="query"
      >
        <template #operate="{ row }">
          <div>
            <el-button v-permission="'catalog:biz:proof_clear:info'" type="text" @click="proofClearEdit(row)">清理证明</el-button>
          </div>
        </template>
      </custom-table>
    </el-card>
  </div>
</template>

<script>
import QueryForm from '@/views/proof-derate-admin/itemBiz/itemClear/components/QueryForm'
import CustomTable from '@/components/Element/Table'
import { getWaitForCleanPage, getWaitForCleanPageCount } from '@/api/itemBiz/clear'
import Enum from '@/utils/enum'
import { getProofStatusList } from '@/api/common/dict'

export default {
  name: 'ToClear',
  components: {
    QueryForm,
    CustomTable
  },
  data() {
    return {
      tableData: {
        border: false,
        content: [

        ],
        loading: false,
        total: 0,
        isShowIndex: true,
        currentPage: 1,
        pageSize: 10,
        pageDirection: 'desc',
        itemClearStatus: 'WAIT_FOR_CLEAN,UNAPPROVED',
        itemClearStatusList: []
      },
      tableTools: [],
      tableHeader: [
        {
          label: '事项名称',
          prop: 'item_name',
          minWidth: '220px',
          align: 'left'
        },
        { label: '事项编码', prop: 'item_code', minWidth: '180px', align: 'left' },
        { label: '办理项名称', prop: 'handing_item', minWidth: '160px', align: 'left' },
        { label: '实施机构', prop: 'impl_org_name', minWidth: '180px', align: 'left' },
        // {
        //   label: '事项类型',
        //   prop: 'item_type',
        //   minWidth: '110px',
        //   formatter: (row, col, val) => {
        //     return val === null || val === undefined ? '' : Enum.mattersTypeList.find(i => i.value === val).label
        //   }
        // },
        {
          label: '事项证明状态',
          prop: 'item_clear_status',
          minWidth: '80px',
          align: 'left',
          formatter: (row, col, val) => {
            // return Enum.proofStatusList.find(i => i.value === val).label;
            if (val) {
              return this.itemClearStatusList.find(i => i.value === val).label
            } else {
              return val
            }
          }
        },
        { label: '操作', slot: 'operate', width: '120px', fixed: 'right', align: 'left' }
      ]
    }
  },
  mounted: function() {
    // this.getProofStatusList()
    // this.query(1)
    this.initData()
    // setTimeout(()=>{
    //   this.query(1)
    // },1000)
  },
  methods: {
    initData: async function() {
      await this.getProofStatusList()
      this.query(1)
    },
    search(data) {
      this.query(1, 'search', data)
    },
    query(currentPage, type, data) {
      console.log('currentPage, type, data',currentPage, type, data)
      // if (_.isNumber(currentPage)) {
      //   this.tableData.currentPage = currentPage
      // }
      this.tableData.loading = true
      const sendData = Object.assign({}, type === 'search' ? data : this.$refs['queryForm'].form)
      sendData['item_type'] =
        sendData['item_type'].length > 0
          ? sendData['item_type'].join()
          : 'ADMINISTRATIVE_LICENSE,ADMINISTRATIVE_COLLECTION,ADMINISTRATIVE_SUPPLY,ADMINISTRATIVE_CONFIRMATION,ADMINISTRATIVE_AWARD,ADMINISTRATIVE_ADJUDICATION,OTHER_ADMINISTRATIVE_POWER,PUBLIC_SERVICE'
      sendData['page_size'] = this.tableData.pageSize
      sendData['page_number'] = this.tableData.currentPage
      sendData['page_direction'] = this.tableData.pageDirection
      sendData['item_clear_status'] = this.tableData.itemClearStatus
      sendData['item_source'] = sendData['item_source'].join(',')
      sendData['item_type'] = sendData['item_type'] != '' ? sendData['item_type'] : ''
      // sendData['item_status'] = 'WORK'
      console.log('sendData1', sendData)
      getWaitForCleanPage(sendData)
        .then(res => {
          this.tableData.loading = false
          this.tableData.content = res.data.content
          // this.tableData.content =[          {
          //   id: '638da278-f195-4908-b4ea-d37207ee4283',
          //   created_by: 'ff8080818f5b9f3a018f5c8e097d017e',
          //   created_date: '2024-05-09 17:17:32',
          //   last_modified_by: 'ff8080818f5b9f3a018f5c8e097d017e',
          //   last_modified_date: '2024-05-09 17:17:32',
          //   sync_item_id: null,
          //   item_code: '360900-000401001000-XK-004-01',
          //   item_name: '河道采砂许可',
          //   handing_item: null,
          //   credit_code: '113608007542420572',
          //   impl_org_name: '宜春市行政审批局',
          //   division_code: '360900',
          //   item_type: 'ADMINISTRATIVE_LICENSE',
          //   item_source: 'NOT_STANDARDIZATION',
          //   item_status: 'WORK',
          //   item_clear_status: 'WAIT_FOR_CLEAN',
          //   accept_condition: null,
          //   project_type: 'IMMEDIATE_ITEM',
          //   item_version: null,
          //   version_date: null
          // },
          // {
          //   id: 'c63b345a-389d-48ef-93b6-f8a0a80e3e92',
          //   created_by: 'ff8080818d7397e4018d739e453000bf',
          //   created_date: '2024-04-19 15:28:30',
          //   last_modified_by: 'ff8080818d7397e4018d739e453000bf',
          //   last_modified_date: '2024-04-19 15:28:30',
          //   sync_item_id: null,
          //   item_code: '360900-000201025000-XK-113-01',
          //   item_name: '危险化学品经营许可',
          //   handing_item: null,
          //   credit_code: '11360800733920680B',
          //   impl_org_name: '宜春市应急管理局',
          //   division_code: '360900',
          //   item_type: 'ADMINISTRATIVE_LICENSE',
          //   item_source: 'NOT_STANDARDIZATION',
          //   item_status: 'WORK',
          //   item_clear_status: 'WAIT_FOR_CLEAN',
          //   accept_condition: null,
          //   project_type: 'IMMEDIATE_ITEM',
          //   item_version: null,
          //   version_date: null
          // },
          // {
          //   id: 'f0329c22-9419-434a-8458-6bc0c8fcd6c9',
          //   created_by: null,
          //   created_date: '2024-02-22 16:32:55',
          //   last_modified_by: 'ff8080818d7397e4018d739e453000bf',
          //   last_modified_date: '2024-03-04 09:16:15',
          //   sync_item_id: null,
          //   item_code: '360900-400702015000-XK-125-04-01',
          //   item_name: '社会团体变更住所',
          //   handing_item: '',
          //   credit_code: '360900-40070201500',
          //   impl_org_name: '宜春市行政审批局',
          //   division_code: '360900',
          //   item_type: 'ADMINISTRATIVE_LICENSE',
          //   item_source: 'STANDARDIZATION',
          //   item_status: 'WORK',
          //   item_clear_status: 'WAIT_FOR_CLEAN',
          //   accept_condition: '',
          //   project_type: 'IMMEDIATE_ITEM',
          //   item_version: null,
          //   version_date: null
          // },
          // {
          //   id: 'b83378af-ae0c-4c87-836f-05f297abc24f',
          //   created_by: null,
          //   created_date: '2024-02-22 16:32:55',
          //   last_modified_by: 'ff8080818d7397e4018d739e453000bf',
          //   last_modified_date: '2024-03-04 09:16:06',
          //   sync_item_id: null,
          //   item_code: '360900-300702015000-XK-012-17-01',
          //   item_name: '农民专业合作社设立',
          //   handing_item: '',
          //   credit_code: '360900-30070201500',
          //   impl_org_name: '宜春市行政审批局',
          //   division_code: '360900',
          //   item_type: 'ADMINISTRATIVE_LICENSE',
          //   item_source: 'STANDARDIZATION',
          //   item_status: 'WORK',
          //   item_clear_status: 'WAIT_FOR_CLEAN',
          //   accept_condition: '',
          //   project_type: 'IMMEDIATE_ITEM',
          //   item_version: null,
          //   version_date: null
          // },
          // {
          //   id: '21ff7cd5-b24b-4ee3-8f35-313692db45a3',
          //   created_by: null,
          //   created_date: '2024-02-22 16:32:55',
          //   last_modified_by: 'ff8080818d7397e4018d739e453000bf',
          //   last_modified_date: '2024-03-04 09:15:57',
          //   sync_item_id: null,
          //   item_code: '360900-300702015000-XK-012-14-08',
          //   item_name: '个人独资企业分支机构注销登记',
          //   handing_item: '',
          //   credit_code: '360900-30070201500',
          //   impl_org_name: '宜春市行政审批局',
          //   division_code: '360900',
          //   item_type: 'ADMINISTRATIVE_LICENSE',
          //   item_source: 'STANDARDIZATION',
          //   item_status: 'WORK',
          //   item_clear_status: 'WAIT_FOR_CLEAN',
          //   accept_condition: '',
          //   project_type: 'IMMEDIATE_ITEM',
          //   item_version: null,
          //   version_date: null
          // },
          // {
          //   id: '518483dc-9f51-4bae-96c3-a81acd2090d6',
          //   created_by: null,
          //   created_date: '2024-02-22 16:32:55',
          //   last_modified_by: 'ff8080818d7397e4018d739e453000bf',
          //   last_modified_date: '2024-03-04 09:15:50',
          //   sync_item_id: null,
          //   item_code: '360900-300702015000-XK-012-14-07',
          //   item_name: '个人独资企业分支机构变更登记',
          //   handing_item: '',
          //   credit_code: '360900-30070201500',
          //   impl_org_name: '宜春市行政审批局',
          //   division_code: '360900',
          //   item_type: 'ADMINISTRATIVE_LICENSE',
          //   item_source: 'STANDARDIZATION',
          //   item_status: 'WORK',
          //   item_clear_status: 'WAIT_FOR_CLEAN',
          //   accept_condition: '',
          //   project_type: 'IMMEDIATE_ITEM',
          //   item_version: null,
          //   version_date: null
          // },
          // {
          //   id: '97aadfba-c560-4729-811a-910849f17be7',
          //   created_by: null,
          //   created_date: '2024-02-22 16:32:55',
          //   last_modified_by: 'ff8080818d7397e4018d739e453000bf',
          //   last_modified_date: '2024-03-04 09:15:39',
          //   sync_item_id: null,
          //   item_code: '360900-300702015000-XK-012-14-06',
          //   item_name: '个人独资企业分支机构设立登记',
          //   handing_item: '',
          //   credit_code: '360900-30070201500',
          //   impl_org_name: '宜春市行政审批局',
          //   division_code: '360900',
          //   item_type: 'ADMINISTRATIVE_LICENSE',
          //   item_source: 'STANDARDIZATION',
          //   item_status: 'WORK',
          //   item_clear_status: 'WAIT_FOR_CLEAN',
          //   accept_condition: '',
          //   project_type: 'IMMEDIATE_ITEM',
          //   item_version: null,
          //   version_date: null
          // },
          // {
          //   id: 'd2d9e976-1eab-4ac5-9b3e-8374674ff222',
          //   created_by: null,
          //   created_date: '2024-02-22 16:32:55',
          //   last_modified_by: 'ff8080818d7397e4018d739e453000bf',
          //   last_modified_date: '2024-03-04 09:15:31',
          //   sync_item_id: null,
          //   item_code: '360900-300702015000-XK-012-14-02',
          //   item_name: '个人独资企业设立登记',
          //   handing_item: '',
          //   credit_code: '360900-30070201500',
          //   impl_org_name: '宜春市行政审批局',
          //   division_code: '360900',
          //   item_type: 'ADMINISTRATIVE_LICENSE',
          //   item_source: 'STANDARDIZATION',
          //   item_status: 'WORK',
          //   item_clear_status: 'WAIT_FOR_CLEAN',
          //   accept_condition: '',
          //   project_type: 'IMMEDIATE_ITEM',
          //   item_version: null,
          //   version_date: null
          // },
          // {
          //   id: '14d7ed3a-a925-49a0-8948-9a7f295a0e75',
          //   created_by: null,
          //   created_date: '2024-02-22 16:32:55',
          //   last_modified_by: 'ff8080818d7397e4018d739e453000bf',
          //   last_modified_date: '2024-03-04 09:15:23',
          //   sync_item_id: null,
          //   item_code: '360900-300702015000-XK-012-12-05',
          //   item_name: '合伙企业分支机构设立登记',
          //   handing_item: '',
          //   credit_code: '360900-30070201500',
          //   impl_org_name: '宜春市行政审批局',
          //   division_code: '360900',
          //   item_type: 'ADMINISTRATIVE_LICENSE',
          //   item_source: 'STANDARDIZATION',
          //   item_status: 'WORK',
          //   item_clear_status: 'WAIT_FOR_CLEAN',
          //   accept_condition: '',
          //   project_type: 'IMMEDIATE_ITEM',
          //   item_version: null,
          //   version_date: null
          // },
          // {
          //   id: 'bd7062b9-d3db-495f-a276-202484ddbf01',
          //   created_by: null,
          //   created_date: '2024-02-22 16:32:55',
          //   last_modified_by: 'ff8080818d7397e4018d739e453000bf',
          //   last_modified_date: '2024-03-04 09:15:16',
          //   sync_item_id: null,
          //   item_code: '360900-300702015000-XK-012-07-06',
          //   item_name: '非公司企业法人按《公司法》改制登记',
          //   handing_item: '',
          //   credit_code: '360900-30070201500',
          //   impl_org_name: '宜春市行政审批局',
          //   division_code: '360900',
          //   item_type: 'ADMINISTRATIVE_LICENSE',
          //   item_source: 'STANDARDIZATION',
          //   item_status: 'WORK',
          //   item_clear_status: 'WAIT_FOR_CLEAN',
          //   accept_condition: '',
          //   project_type: 'IMMEDIATE_ITEM',
          //   item_version: null,
          //   version_date: null
          // }]
          // this.$set(this.tableData, 'content', res.data.content)
          console.log('this.tableData.content', this.tableData.content)
          this.tableData.total = Number(res.data.total_elements)
        })
        .catch(() => {
          this.tableData.content = []
          this.tableData.loading = false
        })
      // getWaitForCleanPageCount(sendData)
      //   .then(res => {
      //     // this.tableData.loading = false
      //     this.tableData.total = Number(res.data.total_elements)
      //   })
      //   .catch(() => {
      //     // this.tableData.loading = false
      //   })
    },
    hasDataPermission(value, permission_code) {
      return hasDataPermission(value, permission_code)
    },
    proofClearEdit(row) {
      this.$router.push({
        name: 'item_to_clear_edit',
        query: {
          id: row.item_code,
          item_clear_status: 'WAIT_FOR_CLEAN',
          from_page_type: 'toClear'
        }
      })
    },
    getProofStatusList() {
      return getProofStatusList().then(res => {
        console.log(res)
        if (res.meta.code === '200') {
          this.itemClearStatusList = res.data
          console.log('itemClearStatusList', this.itemClearStatusList)
        }
      })
    }
  }
}
</script>

<style scoped>
.box-card {
  border: 0;
}
</style>
