<template>
  <div class="content-wrapper padding-10">
    <!-- <section class="content-header">
      <span align="right">
        <el-button @click="backPrev">取消</el-button>
        <el-button :loading="examineApproveLoading" type="warning" @click="examineApprove('draft')">暂存</el-button>
        <el-button :loading="examineApproveLoading" type="primary" @click="examineApprove">提交审批</el-button>
      </span>
      <br>
    </section>-->
    <!-- <papeTitle :title-name="title+':'+form.item_name" :is-has-back="true" @goToList="backPrev">
      <div>
        <el-button @click="backPrev">取消</el-button>
        <el-button :loading="examineApproveLoading" type="warning" v-permission="'catalog:biz:proof_clear:hold'" @click="examineApprove('draft')">暂存</el-button>
        <el-button :loading="examineApproveLoading" type="primary" v-permission="'catalog:biz:proof_clear:create'" @click="examineApprove">提交审批</el-button>
      </div>
    </papeTitle>-->
    <CardTitle :title-name="title+'：'+form.item_name" :ifback="true" @back="backPrev()">
      <template>
        <el-button @click="backPrev">取消</el-button>
        <el-button
          :loading="examineApproveLoading"
          type="warning"
          v-permission="'catalog:biz:proof_clear:hold'"
          @click="examineApprove('draft')"
        >暂存</el-button>
        <el-button
          :loading="examineApproveLoading"
          type="primary"
          v-permission="'catalog:biz:proof_clear:create'"
          @click="examineApprove"
        >提交审批</el-button>
      </template>
    </CardTitle>
    <el-tabs v-model="activeName" tab-position="top" @tab-click="handleClick">
      <el-tab-pane label="详情" name="desc">
        <el-alert v-show="(needShowTips&&itemTips!=='')" :title="itemTips" type="warning" :closable="false" show-icon />
        <!-- <el-divider content-position="left">基本信息</el-divider>
              <el-row>
                <el-col :span="8" :offset="2">实施机构: {{ form.impl_org_name }}</el-col>
                <el-col :span="8" :offset="2">事项类型: {{ form.item_type }}</el-col>
        </el-row>-->
        <el-descriptions class="descriptions" title="基本信息" :column="2" border>
          <el-descriptions-item :label-style="{width:'140px'}">
            <template slot="label">事项名称</template>
            {{ form.item_name }}
          </el-descriptions-item>
          <el-descriptions-item :label-style="{width:'140px'}">
            <template slot="label">事项编码</template>
            {{ form.item_code }}
          </el-descriptions-item>
          <el-descriptions-item :label-style="{width:'140px'}">
            <template slot="label">实施机构</template>
            {{ form.impl_org_name }}
          </el-descriptions-item>
          <el-descriptions-item :label-style="{width:'140px'}">
            <template slot="label">事项类型</template>
            {{ form.item_type }}
          </el-descriptions-item>
        </el-descriptions>
        <br />

        <!--证明材料清理-->
        <!-- <el-divider content-position="left">证明材料清理</el-divider> -->
        <span class="margin-left-10">证明材料清理</span>
        <el-row>
          <el-col :span="24">
            <custom-table
              ref="table1"
              :is-card-type="false"
              :table-data="tableData"
              :table-header="tableHeader"
              @select="select"
              @select-all="selectAll"
            >
              <template #license_name="{ row }">
                <el-button type="text" @click="goLicenseItemView(row)">{{ row.license_name }}</el-button>
              </template>
            </custom-table>
          </el-col>
        </el-row>
        <el-divider content-position="left" />
        <!--选中的证明材料-->
        <div v-for="item in tableData.content" :key="item.index">
          <span class="margin-left-10 info-wrap">
            <img :src="arrow" alt />
            <span class="info-title">{{ item.material_name }}</span>
          </span>
          <!-- <el-tabs type="border-card" v-if="item.section"> -->
          <!-- <el-tab-pane :label="item.material_name"> -->
          <edit-clear-info ref="clearInfo" :row="item.clearData" :dict-data="dictData" />
          <!-- </el-tab-pane> -->
          <!-- </el-tabs> -->
          <br />
        </div>
      </el-tab-pane>
      <el-tab-pane v-if="needShowTips" label="过程信息" name="info">
        <item-process-info ref="processInfo" :item-code="itemId" :dict-data="dictData" @getTips="getTips" />
      </el-tab-pane>
    </el-tabs>
    <div class="fixed-bottom">
      <!-- <div class="btn-group">
          <el-button @click="backPrev">取消</el-button>
          <el-button :loading="examineApproveLoading" type="success" @click="examineApprove('draft')">暂存</el-button>
          <el-button :loading="examineApproveLoading" type="primary" @click="examineApprove">提交审批</el-button>
      </div>-->
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import CustomTable from '@/components/Element/Table'
import { getItemView, doNotClean, proofListCreate, proofListCreateDraft, proofListUpdate } from '@/api/itemBiz/clear'
import { goLicenseItemView, getProofListFormViewCode, getProofSubmitInfoByCode, getProofConfirmInfoByCode, getProofAuditInfoByCode } from '@/api/itemBiz/list'
import { getItemTypeList, getProofClearType, getItemMaterialType, getReplaceCancelWay, getUnitTypeList, getProofStatusList } from '@/api/common/dict'
import moment from 'moment'
import EditClearInfo from '@/views/proof-derate-admin/itemBiz/itemClear/components/EditClearInfo'
import ItemProcessInfo from '@/views/proof-derate-admin/components/ItemProcessInfo'
import papeTitle from '@/components/papeTitle'
import CardTitle from '@/components/CardTitle'
export default {
  name: 'ItemtoClearEdit',
  components: {
    CustomTable,
    EditClearInfo,
    ItemProcessInfo,
    papeTitle,
    CardTitle
  },
  data() {
    return {
      title: '事项名称',
      itemId: '',
      needShowTips: false,
      itemTips: '',
      pageType: 'PUBLIC',
      fromPageType: 'toClear',
      form: {
        item_name: '',
        impl_org_name: '',
        item_type: ''
      },
      activeName: 'desc',
      arrow: require('@/assets/proof-derate-admin-images/arrow.png'),
      tempTableData: [],
      titleName: '事项证明档案',
      tableData: {
        content: [], // 表格数据
        isShowSelection: false,
        border: true,
        clearData: {
          relevance: '',
          material_id: '',
          proof_name: '',
          proof_clear_type: '',
          not_clean_description: '',
          direct_description: '',
          replace_cancel_way: '',
          clerk_commitment: {
            commit_book_description: '',
            commit_attachment_id: '',
            // file_data: ["", "", ""],
            commit_attachment_name: ''
          },
          replace_data_shared: {
            industry_dept_name: '',
            industry_dept_code: '',
            proof_provide_type: null,
            data_shared_description: ''
          },
          replace_dept_survey: {
            dept_name: '',
            dept_code: '',
            dept_cancel_description: ''
          },
          replace_investigation: {
            industry_dept_name: '',
            industry_dept_code: '',
            proof_provide_type: null,
            investigation_description: ''
          },
          replace_license: {
            license_description: '',
            license_code: '',
            license_name: ''
          },
          replace_license_item: {
            license_description: '',
            license_code: '',
            license_name: ''
          },
          replace_other: {
            other_clear_description: ''
          },
          user_info: {
            account_dept_code: '',
            account_dept_name: ''
          }
        }
      },
      // 表头配置
      tableHeader: [
        // { label: "序号", prop: "index", minWidth: "80px" },
        { label: '材料名称', prop: 'material_name', minWidth: '200px' }, // 配置slot属性，可支持使用插槽
        {
          label: '材料类型',
          prop: 'material_type',
          minWidth: '200px',
          formatter: (row, col, val) => {
            return val === null || val === undefined ? '' : this.itemMaterialType.find(i => i.value === val).label
          }
        }
        // {
        //   label: '已关联电子证照',
        //   slot: 'license_name',
        //   prop: 'license_name',
        //   minWidth: '200px'
        // }
        // { label: "事项证明状态", prop: "item_proof_status", minWidth: "200px" }
      ],
      examineApproveLoading: false,
      proofRecordList: Array(3),
      itemTypeList: [],
      proofClearType: [],
      itemMaterialType: [],
      replaceCancelWayList: [],
      unitTypeList: [],
      proofStatusList: [],
      dictData: {},
      is_revision: false // 是否从已完成修改为已待清理
    }
  },
  beforeRouteLeave(to, from, next) {
    if (to.name == 'item_clear') {
      to.meta.keepAlive = true
    } else {
      to.meta.keepAlive = false
    }
    next()
  },
  computed: {
    ...mapState({
      account: state => state.user && state.user.userdata && state.user.userdata.userAccount && state.user.userdata.userAccount.account,
      roles: state => state.user && state.user.userdata && state.user.userdata.roles,
      name: state => state.user && state.user.userdata && state.user.userdata.userAccount && state.user.userdata.userAccount.name,
      dept: state => state.user && state.user.userdata && state.user.userdata.userInfo && state.user.userdata.userInfo.orgName
    })
  },
  mounted() {
    const item_code = this.$route.query['id']
    const item_clear_status = this.$route.query['item_clear_status']
    const from_page_type = this.$route.query['from_page_type']
    this.itemId = item_code
    this.pageType = item_clear_status
    this.fromPageType = from_page_type
    this.is_revision = this.$route.query['is_revision']
    this.initData(item_code, item_clear_status)
  },
  methods: {
    initData: async function (item_code, item_clear_status) {
      const itemTypeListRes = await getItemTypeList()
      const proofClearTypeRes = await getProofClearType()
      const itemMaterialTypeRes = await getItemMaterialType()
      const replaceCancelWayRes = await getReplaceCancelWay()
      const unitTypeListRes = await getUnitTypeList()
      const proofStatusListRes = await getProofStatusList()
      this.itemTypeList = itemTypeListRes.data || []
      this.proofClearType = proofClearTypeRes.data || []
      this.itemMaterialType = itemMaterialTypeRes.data || []
      this.replaceCancelWayList = replaceCancelWayRes.data || []
      this.unitTypeList = unitTypeListRes.data || []
      this.proofStatusList = proofStatusListRes.data || []
      console.log('------------', this.proofClearType)
      this.dictData = {
        cancelWayList: this.replaceCancelWayList, // 替代取消方式
        proofClearType: this.proofClearType.reverse(), // 清理类型:
        proofStatusList: this.proofStatusList
      }
      // 被await阻塞的同步代码
      this.getItemView(item_code)
      // 过程信息
      // this.getProofRecordInfo();
    },
    getTips(info) {
      console.log('info', info)
      this.needShowTips = info.needShowTips
      this.itemTips = info.itemTips
    },
    select(selection, row) {
      const section = this.tableData.content[row.index]['section']
      this.tableData.content[row.index]['section'] = !section
    },
    selectAll(selection) {
      this.tableData.content.forEach((i, index) => {
        this.tableData.content[index]['section'] = selection.length !== 0
      })
    },

    getItemView(item_code) {
      getItemView(item_code)
        .then(res => {
          this.form = res.data.item_vo || {}
          if (this.form.item_type) {
            this.form.item_type = this.itemTypeList.find(i => i.value === this.form.item_type).label
          }
          this.tempTableData = _.map(res.data.item_material_vo_list, (item, index) => {
            return {
              index: index,
              item_proof_status: '',
              ...item,
              section: true,
              clearData: {
                ...this.tableData.clearData,
                material_id: item.id,
                proof_name: item.material_name,
                blank_file: '',
                blank_file_id: item.blank_file_id,
                blank_file_name: item.blank_file_name,
                sample_file: '',
                sample_file_id: item.sample_file_id,
                sample_file_name: item.sample_file_name,
                blank_file_list: item.blank_file_list,
                sample_file_list: item.sample_file_list
              },
              dataList: []
            }
          })
          console.log(' this.tempTableData', this.tempTableData)
          this.getProofListFormView()
        })
        .catch(() => {})
    },
    // 获取材料详情
    getProofListFormView() {
      getProofListFormViewCode(this.itemId).then(res => {
        console.log('res', res)
        const that = this
        const proof_data = res.data.proof_data || [] // 清理详情
        const item_vo = res.data.item_vo || []
        const draft_data_list = res.data.draft_data_list || [] // 暂存详情
        this.needShowTips = proof_data.length > 0
        this.currItemType = item_vo.created_date === item_vo.last_modified_date ? '' : 'isStillNeedClear'
        console.log('this.currItemType', this.currItemType)
        console.log('draft_data_list', draft_data_list)
        console.log('proof_data', proof_data)
        console.log('this.tempTableData', this.tempTableData)
        // 当有暂存数据时
        if (draft_data_list.length > 0) {
          // 初稿数据回填
          draft_data_list.forEach(i => {
            const index = this.tempTableData.findIndex(j => j.id === i.material_id)
            console.log('index', index)
            i.proof_list_remark = i.proof_list_remark ? i.proof_list_remark : ''
            const notNUllParam = Object.keys(i).forEach(j => {
              if (i[j] === null || i[j] === 'null' || i[j] === undefined) delete i[j]
            })

            const proof_catalog_vo = i.proof_catalog_vo_list !== undefined ? i.proof_catalog_vo_list[0] : {} || {}

            let relevance = ''
            if (proof_catalog_vo) {
              relevance = proof_catalog_vo.name
            }
            this.tempTableData[index].dataList.push({
              ...this.tableData.clearData,
              ...i,
              relevance
            })
            this.tempTableData[index]['clearData'] = {
              ...this.tableData.clearData,
              ...i,
              blank_file: '',
              blank_file_id: this.tempTableData[index].blank_file_id,
              blank_file_name: this.tempTableData[index].blank_file_name,
              sample_file: '',
              sample_file_id: this.tempTableData[index].sample_file_id,
              sample_file_name: this.tempTableData[index].sample_file_name,

              sample_file_list: this.tempTableData[index].sample_file_list,
              blank_file_list: this.tempTableData[index].blank_file_list
              // relevance
            }
            this.tempTableData[index]['clearData'].dataList = this.tempTableData[index].dataList
          })
          console.log('this.tempTableData1', this.tempTableData)
        } else if (proof_data.length > 0) {
          // 当前状态是仍需清理
          proof_data.forEach(i => {
            // console.log('i.item_material_vo.id',i.item_material_vo,'this.tempTableData',this.tempTableData)
            if (i.item_material_vo.id != null && i.item_material_vo.id != undefined) {
              const index = this.tempTableData.findIndex(j => j.id === i.item_material_vo.id)
              const item_material_vo = i.item_material_vo
              console.log('item_material_vo', item_material_vo)
              const proof_list_vo = i.proof_list_vo
              const proof_catalog_vo = i.proof_catalog_vo_list !== undefined ? i.proof_catalog_vo_list[0] : {} || {}
              const proof_clear_type = proof_list_vo.proof_clear_type
              let clearData = {}
              let relevance = ''

              if (proof_catalog_vo) {
                relevance = proof_catalog_vo.name
              }
              // this.tempTableData[index]["clearData"] = {
              //   ...this.tableData.clearData,
              //   ...i,
              //   relevance
              // };
              if (proof_clear_type === 'DO_NOT_CLEAN') {
                clearData = {
                  not_clean_description: proof_list_vo.not_clean_description
                }
              } else if (proof_clear_type === 'DIRECTLY_CANCEL') {
                clearData = {
                  direct_description: proof_list_vo.direct_description
                }
              } else if (proof_clear_type === 'REPLACE_CANCEL') {
                clearData = {
                  // clerk_commitment: {
                  //   commit_book_description: proof_list_vo.commit_book_description,
                  //   commit_attachment_id: proof_list_vo.commit_attachment_id,
                  //   commit_attachment_name: proof_list_vo.commit_attachment_name
                  // },
                  replace_cancel_way: proof_list_vo.replace_cancel_way.split(','),
                  // replace_data_shared: {
                  //   industry_dept_name: proof_list_vo.industry_dept_name_dataShared,
                  //   industry_dept_code: proof_list_vo.industry_dept_code_dataShared,
                  //   proof_provide_type: null,
                  //   data_shared_description: proof_list_vo.data_shared_description
                  // },
                  // replace_dept_survey: {
                  //   dept_name: proof_list_vo.dept_name,
                  //   dept_code: proof_list_vo.dept_code,
                  //   dept_cancel_description: proof_list_vo.dept_cancel_description
                  // },
                  // replace_investigation: {
                  //   industry_dept_name: proof_list_vo.industry_dept_name_investigation,
                  //   industry_dept_code: proof_list_vo.industry_dept_code_investigation,
                  //   proof_provide_type: null,
                  //   investigation_description: proof_list_vo.investigation_description
                  // },
                  // replace_license: {
                  //   license_description: proof_list_vo.license_description,
                  //   license_code: proof_list_vo.license_code,
                  //   license_name: proof_list_vo.license_name
                  // },
                  // replace_license_item: {
                  //   license_item_description: proof_list_vo.license_description,
                  //   license_item_code: proof_list_vo.license_code,
                  //   license_item_name: proof_list_vo.license_name
                  // },
                  // replace_other: {
                  //   other_clear_description: proof_list_vo.other_clear_description
                  // },
                  proof_list_remark: proof_list_vo.proof_list_remark
                }
              }
              console.log('clearData', clearData)
              console.log('this.tempTableData[index]', index, this.tempTableData)
              this.tempTableData[index].dataList.push({
                ...this.tableData.clearData,
                material_id: proof_list_vo.material_id,
                // proof_catalog_id: "",
                proof_catalog_id: proof_catalog_vo != null ? proof_catalog_vo.id : '',
                proof_list_id: proof_list_vo.proof_list_id,
                proof_name: proof_list_vo.proofName,
                proof_clear_type: proof_list_vo.proof_clear_type,
                blank_file: '',
                blank_file_id: item_material_vo.blank_file_id,
                blank_file_name: item_material_vo.blank_file_name,
                sample_file: '',
                sample_file_id: item_material_vo.sample_file_id,
                sample_file_name: item_material_vo.sample_file_name,

                sample_file_list: this.tempTableData[index].sample_file_list,
                blank_file_list: this.tempTableData[index].blank_file_list,
                relevance,
                ...clearData,
                user_info: {
                  account_name: this.name,
                  account_dept_code: '',
                  account_dept_name: this.dept
                }
              })
              this.tempTableData[index]['clearData'] = {
                ...this.tableData.clearData,
                material_id: proof_list_vo.material_id,
                // proof_catalog_id: "",
                proof_catalog_id: proof_catalog_vo != null ? proof_catalog_vo.id : '',
                proof_list_id: proof_list_vo.proof_list_id,
                proof_name: proof_list_vo.proofName,
                proof_clear_type: proof_list_vo.proof_clear_type,
                blank_file: '',
                blank_file_id: item_material_vo.blank_file_id,
                blank_file_name: item_material_vo.blank_file_name,
                sample_file: '',
                sample_file_id: item_material_vo.sample_file_id,
                sample_file_name: item_material_vo.sample_file_name,

                sample_file_list: this.tempTableData[index].sample_file_list,
                blank_file_list: this.tempTableData[index].blank_file_list,

                ...clearData,
                user_info: {
                  account_name: this.name,
                  account_dept_code: '',
                  account_dept_name: this.dept
                }
              }
              // new Promise(resolvue)
              // setTimeout(()=>{
              // this.tempTableData[index]['clearData'].relevance = relevance

              this.tempTableData[index]['clearData'].dataList = this.tempTableData[index].dataList
              // },1000)

              // this.ref[clearInfo.relevance
            }
          })
        }

        this.tableData.content = this.tempTableData
        // console.log("this.tableData.content",this.tableData.content)
        //  setTimeout(()=>{
        //   console.log(this.$refs.clearInfo,this.$refs.clearInfo)
        //  },500)
      })
    },
    getProofRecordInfo() {
      const sendData = { item_code: this.itemId }
      getProofSubmitInfoByCode(sendData).then(res => {
        this.proofRecordList[0] = res.data != null ? this.formatRecordInfo(res.data) : false
      })
      getProofConfirmInfoByCode(sendData).then(res => {
        this.proofRecordList[1] = res.data != null ? this.formatRecordInfo(res.data) : false
        if (res.data && res.data['confirm_status'] === 'WAIT_FOR_CLEAN') {
          this.needShowTips = true
          this.itemTips = '此事项清理在“梳理环节”被驳回，请完善后重新提交'
        }
      })
      getProofAuditInfoByCode(sendData).then(res => {
        this.proofRecordList[2] = res.data != null ? this.formatRecordInfo(res.data) : false
        if (res.data && res.data['operator_type'] === 'UNAPPROVED') {
          this.needShowTips = true
          this.itemTips = '此事项清理在“审核环节”被驳回，请完善后重新提交'
        }
      })
    },
    formatRecordInfo(data) {
      return {
        ...data,
        confirm_status: data['confirm_status'] ? this.proofStatusList.find(i => i.value === data['confirm_status']).label : data['confirm_status'],
        operator_type: data['operator_type'] ? this.proofStatusList.find(i => i.value === data['operator_type']).label : data['operator_type'],
        operator_type_code: data['operator_type'],
        date: moment(data.created_date).format('MM.DD'),
        minute: moment(data.created_date).format('HH:mm'),
        time: moment(data.created_date).format('YYYY-MM-DD HH:mm')
      }
    },
    handleClick(tab, event) {
      console.log(tab, event)
    },
    backList() {
      this.$router.push({ name: 'item_clear' })
    },
    goLicenseItemView(row) {
      goLicenseItemView(row.license_code).then(res => {
        window.open(res.data.url, '_blank')
      })
    },
    doNotClean() {
      this.$confirm('确定修改为无需清理?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        doNotClean({ item_id: this.form.id }).then(res => {
          const type = res.meta.code === '200' ? 'success' : 'warning'
          this.$message({
            type: type,
            message: res.meta.msg
          })
          if (type === 'success') this.backList()
        })
      })
    },
    backPrev() {
      // this.$router.go(-1);
      this.$router.push({
        name: 'item_clear'
      })
    },

    examineApprove(type) {
      const clearInfoList = this.$refs.clearInfo
      console.log('clearInfoList', clearInfoList)
      const formDataList = []
      const formDom = []
      _.forEach(clearInfoList, item => {
        item.validateForm()
        if (item.getvalidateForm()) {
          formDom.push(item.getvalidateForm())
        }
        // console.log('validateForm()',item.validateForm())
        console.log('item.getFormData()', item.getFormData())
        item.getFormData().forEach(e => {
          formDataList.push(e)
        })
        // formDataList.push(item.getFormData());
      })
      //  console.log('formDom',formDom)
      const sendData = this.filterSubmitData(formDataList)
      if (sendData.length <= 0) {
        this.$message({
          type: 'warning',
          message: '请至少选择一条材料进行清理'
        })
      } else {
        const rowValidater = []
        _.forEach(clearInfoList, item => {
          item.$refs.form.validate(valid => {
            if (valid) {
              rowValidater.push(true)
            } else {
              rowValidater.push(false)
            }
          })
        })
        // 点击跳转到具体校验元素
        if (formDom.length != 0) {
          formDom[0].$el.scrollIntoView({ behavior: 'smooth' })
        }
        console.log('sendData', sendData)
        if (rowValidater.every(i => i === true)) {
          if (type === 'draft') {
            // 提交草稿
            proofListCreateDraft(this.itemId, sendData).then(res => {
              const type = res.meta.code === '200' ? 'success' : 'error'
              this.$message({
                type: type,
                message: '暂存成功'
              })
              if (type !== 'success') {
                this.$message({
                  type: type,
                  message: res.meta.message
                })
              }
            })
          } else {
            if (this.currItemType === 'isStillNeedClear') {
              // 仍需清理之后再次提交
              proofListUpdate(this.itemId, sendData, this.is_revision).then(res => {
                const type = res.meta.code === '200' ? 'success' : 'warning'
                this.$message({
                  type: type,
                  message: res.meta.message
                })
                if (type === 'success') {
                  this.$router.push({
                    name: 'item_clear'
                  })
                } // this.$router.go(-1)
              })
            } else {
              proofListCreate(this.itemId, sendData).then(res => {
                // 首次提交
                const type = res.meta.code === '200' ? 'success' : 'error'
                this.$message({
                  type: type,
                  message: '操作成功'
                })
                if (type === 'success') {
                  // this.$router.go(-1)
                  this.$router.push({
                    name: 'item_clear'
                  })
                } else {
                  this.$message({
                    type: type,
                    message: res.meta.message
                  })
                }
              })
            }
          }
        }
      }
    },
    filterSubmitData(data) {
      console.log(data)
      const handleData = []
      if (data && data.length > 0) {
        data.forEach(i => {
          let item = {}
          const baseInfo = {
            material_id: i.material_id,
            proof_clear_type: i.proof_clear_type,
            proof_name: i.proof_name,
            proof_catalog_id: i.proof_catalog_id,
            proof_list_id: i.proof_list_id,
            proof_list_remark: i.proof_list_remark
          }
          const doNotCleanData = {
            not_clean_description: i.not_clean_description,
            ...baseInfo
          }
          const directlyCancleData = {
            direct_description: i.direct_description,
            ...baseInfo
          }
          const replaceCancelData = {}
          const replaceCancelWay = i.replace_cancel_way
          const replaceLicense = { replace_license: i.replace_license }
          const clerk_commitment = { clerk_commitment: i.clerk_commitment }
          const replaceDataShared = {
            replace_data_shared: i.replace_data_shared
          }
          const replaceInvestigation = {
            replace_investigation: i.replace_investigation
          }
          const replaceDeptSurvey = {
            replace_dept_survey: i.replace_dept_survey
          }
          const replaceOther = { replace_other: i.replace_other }
          const replaceLicenseItem = { replace_license_item: i.replace_license_item }
          const userInfo = {
            user_info: {
              account_name: this.name,
              account_dept_code: '',
              account_dept_name: this.dept
            }
          }

          if (i.proof_clear_type === 'DO_NOT_CLEAN') {
            item = Object.assign(doNotCleanData, userInfo)
          } else if (i.proof_clear_type === 'DIRECTLY_CANCEL') {
            item = Object.assign(directlyCancleData, userInfo)
            // i.proof_clear_type === 'REPLACE_CANCEL' && replaceCancelWay.length > 0
          } else if (i.proof_clear_type === 'REPLACE_CANCEL') {
            console.log('{ replace_cancel_way: replaceCancelWay }, baseInfo, userInfo', { replace_cancel_way: replaceCancelWay }, baseInfo, userInfo)
            item = Object.assign(baseInfo, userInfo)
            // item = Object.assign({ replace_cancel_way: replaceCancelWay }, baseInfo, userInfo)
            // if (replaceCancelWay.indexOf('TURN_LICENSE_OR_OTHER_LICENSE_WAY') != -1) {
            //   // item = Object.assign(item, replaceLicense)
            // }
            // if (replaceCancelWay.indexOf('HANDLE_AFFAIRS_PROMISE') != -1) {
            //   item = Object.assign(item, clerk_commitment)
            // }
            // if (replaceCancelWay.indexOf('DATA_SHARING') != -1) {
            //   item = Object.assign(item, replaceDataShared)
            // }
            // if (replaceCancelWay.indexOf('ARTIFICIAL_INVESTIGATION') != -1) {
            //   item = Object.assign(item, replaceInvestigation)
            // }
            // if (replaceCancelWay.indexOf('DEPARTMENT_INVESTIGATION') != -1) {
            //   item = Object.assign(item, replaceDeptSurvey)
            // }
            // if (replaceCancelWay.indexOf('OTHER_WAY') != -1) {
            //   item = Object.assign(item, replaceOther)
            // }
            // if (replaceCancelWay.indexOf('TURN_LICENSE_ITEM') != -1) {
            //   item = Object.assign(item, replaceLicenseItem)
            // }
          }
          handleData.push(item)
        })
      }
      return handleData
    },
    preliminaryDrafts(proof_list_id) {
      const route = this.$router.resolve({
        name: this.proofRoute,
        query: {
          id: proof_list_id
        }
      })
      window.open(route.href, '_blank')
    }
  }
}
</script>

<style lang="scss" scoped>
.time {
  color: #919191;
}
.status {
  color: #2d76ce;
  font-size: 19px;
  font-weight: 900;
}
.fixed-bottom {
  width: 100%;
  transform: scale3d(1, 1, 1);
  .btn-group {
    background: rgba(255, 255, 255, 0.95);
    border-top: 1px solid #096dd9;
    padding: 20px 18px 10px 50px;
    // position: absolute;
    bottom: 0;
    right: 0;
    z-index: 100;
    text-align: right;
    height: 80px;
    width: inherit;
  }
}
.content {
  .el-tabs--border-card {
    padding-bottom: 20px;
  }
}
.content-header {
  position: absolute;
  top: 60px;
  right: 0;
  z-index: 111;
}
.content-title {
  margin-left: 20px;
}
.margin-bottom-10 {
  margin-bottom: 24px;
  i {
    cursor: pointer;
  }
}
.descriptions {
  margin-top: 10px;
  padding: 0 10px;
}
.margin-left-10 {
  margin-left: 10px;
}
.info-title {
  font-size: 20px;
  color: #333333;
}
.info-wrap {
  display: flex;
  align-items: center;
  img {
    margin-right: 10px;
    width: 35px;
    height: 35px;
  }
}
</style>
