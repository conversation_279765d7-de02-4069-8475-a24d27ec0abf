<template>
  <div class="content-wrapper padding-10">
    <!-- <papeTitle :title-name="titleName" :is-has-back="false">
      <div>
        <el-button type="primary" v-permission="'catalog:user:manage:import'" @click="importData()">导入清单结果</el-button>
      </div>
    </papeTitle>-->
    <CardTitle :title-name="titleName">
      <template>
        <div>
          <el-button type="primary" v-permission="'catalog:biz:proof_clear:import'" @click="importData()">导入清单结果</el-button>
        </div>
      </template>
    </CardTitle>
    <!-- <section class="content">  type="border-card"-->
    <el-tabs v-model="activeName" tab-position="top">
      <el-tab-pane label="待清理" name="todo">
        <to-clear v-if="activeName==='todo'" ref="toclear" />
      </el-tab-pane>
      <el-tab-pane label="已清理" name="cleared">
        <cleared v-if="activeName==='cleared'" />
      </el-tab-pane>
      <!-- <el-tab-pane label="无需清理" name="noClear">
        <no-clear v-if="activeName==='noClear'" />
      </el-tab-pane>-->
    </el-tabs>
    <!-- </section> -->
    <el-dialog title="清理结果导入" :visible.sync="dialogVisible" width="40%" center :before-close="cancle">
      <div class>
        <p>请选择您要导入的数据</p>
        <el-row :gutter="24" justify="center" align="middle" type="flex">
          <el-col :span="20">
            <el-input v-model="fileForm.fileName" placeholder readonly />
          </el-col>
          <el-col :span="4">
            <el-upload
              :auto-upload="false"
              :show-file-list="false"
              class="upload-demo"
              action
              :on-change="handleChange"
              :file-list="fileList"
              accept=".xls, .xlsx"
            >
              <el-button type="primary">浏览</el-button>
            </el-upload>
          </el-col>
        </el-row>
        <p class="tip">
          <i class="el-icon-info" />
          温馨提示：请选择以.xls/.xlsx为后缀名的文件且上传文件不超过1M！
          <el-button type="text" @click="getUserTemplate">下载导入模板</el-button>
          <el-button type="text" @click="getRangeTemplate">范围模板</el-button>
        </p>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="cancle()">取 消</el-button>
        <el-button type="primary" :loading="fileLoading" @click="importFile()">导入</el-button>
      </span>
    </el-dialog>
    <el-dialog title="选择数据下载范围" :visible.sync="dialogVisible1" width="40%" center :before-close="cancle1">
      <el-form ref="rangeForm" :model="rangeForm" label-width="120px">
        <el-row :gutter="24" justify="center" align="middle" type="flex">
          <el-col :span="24">
            <el-form-item label="实施区划">
              <division-selector v-model="rangeForm.division_code" @change="divisionChange" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24" justify="center" align="middle" type="flex">
          <el-col :span="24">
            <el-form-item label="实施机构">
              <el-select v-model="rangeForm.credit_code" filterable placeholder="请选择" style="width:100%" clearable>
                <el-option v-for="item in organizationList" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24" justify="center" align="middle" type="flex">
          <el-col :span="24">
            <el-form-item label="事件标准类型">
              <el-checkbox-group v-model="rangeForm.item_source_list">
                <el-checkbox v-for="(i, key) in standardTypeList" :key="key" :label="i.value">{{ i.label }}</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24" justify="center" align="middle" type="flex">
          <el-col :span="24">
            <el-form-item label="事件类型">
              <el-checkbox-group v-model="rangeForm.item_type_list">
                <el-checkbox v-for="(i, key) in mattersTypeList" :key="key" :label="i.value">{{ i.label }}</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="cancle1()">取 消</el-button>
        <el-button type="primary" :loading="rangeTemplateLoading" @click="downFile()">下载</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import ToClear from '@/views/proof-derate-admin/itemBiz/itemClear/toClear'
import Cleared from '@/views/proof-derate-admin/itemBiz/itemClear/cleared'
import NoClear from '@/views/proof-derate-admin/itemBiz/itemClear/noClear'
import Enum from '@/utils/enum'
import { getMattersTypeList, getStandardTypeList } from '@/api/common/dict'
import { downloadCleanResultTemplate, importCleanResultByExcel } from '@/api/certificationManagement/certificationList'
import { getOrgListNoAuth } from '@/api/admin/org.js'
import divisionSelector from '@/components/DivisionSelector'
import { dataURLtoDownload, getIsWhitelist, getFileType } from '@/utils/index'
import { validPrefix } from '@/utils/validate'
import papeTitle from '@/components/papeTitle'
import CardTitle from '@/components/CardTitle'
export default {
  name: 'MattersClear',
  components: {
    ToClear,
    Cleared,
    NoClear,
    divisionSelector,
    papeTitle,
    CardTitle
  },
  data() {
    return {
      activeName: 'todo',
      dialogVisible: false,
      dialogVisible1: false,
      fileForm: {
        fileName: '',
        file: ''
      },
      rangeForm: {
        division_code: '',
        credit_code: '',
        item_source_list: [], // 事项标准类型
        item_type_list: [] // 事项类型
      },
      fileList: [],
      fileLoading: false,
      checkList: [],
      checkList1: [],
      mattersTypeList: [],
      standardTypeList: [],
      organizationList: [],
      rangeTemplateLoading: false,
      whitelist: ['xlsx', 'xls'],
      titleName: '证明材料清理'
    }
  },
  beforeRouteLeave(to, from, next) {
    from.meta.keepAlive = false
    next()
  },
  computed: {},
  created() {
    this.getMattersTypeList()
    this.getStandardTypeList()
  },
  methods: {
    getMattersTypeList() {
      getMattersTypeList()
        .then(res => {
          const data = res.data || []
          Enum.mattersTypeList.splice(0)
          Enum.mattersTypeList.push(...data)
          this.mattersTypeList.push(...data)
          this.mattersTypeList.forEach(e => {
            this.rangeForm.item_type_list.push(e.value)
          })
        })
        .catch(() => {
          Enum.mattersTypeList.splice(0)
        })
    },
    getStandardTypeList() {
      getStandardTypeList().then(res => {
        const data = res.data || []
        this.standardTypeList.push(...data)
        this.standardTypeList.forEach(e => {
          this.rangeForm.item_source_list.push(e.value)
        })
      })
    },
    importData() {
      this.dialogVisible = true
    },
    getUserTemplate() {
      this.downloadCleanResultTemplate()
    },
    getRangeTemplate() {
      this.dialogVisible1 = true
    },
    handleChange(file, fileList) {
      const fileName = getFileType(file.name).fileName
      const isLt10m = file.size / (1024 * 1024) < 1
      if (!isLt10m) {
        this.$message.error('文件大小不能超过1m！')
      } else {
        if (fileName.indexOf('http') != -1 || validPrefix(fileName)) {
          this.$message.error('文件名开头包含特殊符号！')
        } else {
          if (!getIsWhitelist(file.name, this.whitelist)) {
            this.$message.error(`请重新选择以${this.whitelist.join(',')}为后缀名的文件！`)
          } else {
            this.fileForm.fileName = file.name
            this.fileForm.file = file.raw
          }
        }
      }
    },
    divisionChange(d) {
      this.rangeForm.division_code = d.code || ''
      // this.params.divisionCode = this.params.divisionCode.replace(/(0+)$/g, ""); //末尾去0
      this.organizationList = []
      this.rangeForm.credit_code = ''
      if (d.code) {
        getOrgListNoAuth({
          pageSize: 1000,
          divisionCode: d.code
        }).then(res => {
          this.organizationList = res.content.map(i => {
            return { label: i.name, value: i.tyshxydm }
          })
        })
      }
    },
    getFileType(filePath) {
      const startIndex = filePath.lastIndexOf('.')
      if (startIndex != -1) {
        return filePath.substring(startIndex + 1, filePath.length).toLowerCase()
      } else {
        return ''
      }
    },
    downloadCleanResultTemplate(params) {
      return downloadCleanResultTemplate(params).then(res => {
        if (res.data != null && res.meta.code === '200') {
          dataURLtoDownload(res.data.fileData, res.data.file_name)
        }
      })
    },
    importCleanResultByExcel(data) {
      this.fileLoading = true
      importCleanResultByExcel(data)
        .then(res => {
          // console.log(res)
          this.fileLoading = false
          this.cancle()
          if (res.data != null && res.meta.code === '200') {
            const tipArray = res.data.split('，')
            // console.log(tipArray)
            if (tipArray.length > 1) {
              let newStr = tipArray[2].replace(/\//g, '<br/>')
              newStr = '<span class="tiptitle">' + this.insertStr(newStr, 6, '：</span>')
              this.$message({
                // duration: 0,
                iconClass: 'el-icon-warning-outline',
                customClass: 'tipmessage',
                dangerouslyUseHTMLString: true,
                message: '<span class="tiptitle">' + tipArray[0] + ',' + tipArray[1] + ',' + '<span class="tipitem">' + newStr + '</span>' + '</span>',
                // message: '<strong>这是 <i>HTML</i> 片段</strong>',
                type: 'warning'
              })
            } else {
              this.$message({
                iconClass: 'el-icon-circle-check',
                // duration: 0,
                message: tipArray[0],
                type: 'success',
                customClass: 'tipmessage1'
              })
            }
          } else {
            this.$message({
              message: res.meta.message,
              type: 'error'
            })
          }
        })
        .catch(() => {
          this.fileLoading = false
        })
    },
    downFile() {
      const params = {
        division_code: this.rangeForm.division_code,
        credit_code: this.rangeForm.credit_code,
        item_source_list: this.rangeForm.item_source_list.join(','),
        item_type_list: this.rangeForm.item_type_list.join(',')
      }
      this.rangeTemplateLoading = true
      // this.rangeForm.item_source_list = this.rangeForm.item_source_list.join(',')
      // this.rangeForm.item_type_list = this.rangeForm.item_type_list.join(',')
      this.downloadCleanResultTemplate(params)
        .then(() => {
          this.rangeTemplateLoading = false
        })
        .catch(() => {
          this.rangeTemplateLoading = false
        })
    },
    importFile() {
      const formData = new FormData()
      formData.append('file', this.fileForm.file)
      this.importCleanResultByExcel(formData)
    },
    cancle() {
      this.dialogVisible = false
      this.fileForm.fileName = ''
      this.fileForm.file = ''
      this.fileList = []
    },
    cancle1() {
      this.dialogVisible1 = false
    },
    /**
     * 字符串指定位置添加元素
     * @param str1:原字符串
     * @param n:插入位置
     * @param str2:插入元素
     * @return  拼接后的字符串
     */

    insertStr(str1, n, str2) {
      let s1 = ''
      let s2 = ''
      if (str1.length < n) {
        return str1 + str2
      } else {
        s1 = str1.substring(0, n)
        s2 = str1.substring(n, str1.length)
        return s1 + str2 + s2
      }
    }
  }
}
</script>
<style lang="scss">
// @import '@/styles/element-ui.scss';
</style>

<style scoped>
.top {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.top h1 {
  font-size: 24px;
  margin: 0;
}
.tip i {
  color: #e6a23c;
}
.content-wrapper /deep/.el-upload__input {
  display: none;
}
.content-wrapper /deep/.el-tabs__nav-scroll {
  background: #fff;
}
.content-wrapper /deep/.el-tabs--top .el-tabs__item.is-top {
  padding-right: 20px !important;
  width: 150px;
  text-align: center;
  font-size: 16px;
}
.content-wrapper /deep/.el-tabs__header {
  margin: 0 0 0px;
}
</style>

<style>
.tiptitle {
  color: #444;
}
.tipitem {
  color: rgba(0, 0, 0, 0.424);
}
.tipmessage {
  background-color: #fdf6ec;
  border-color: #faecd8;
  align-items: start;
  line-height: 30px;
}
.tipmessage i {
  margin-top: 5px;
  margin-right: 15px;
  color: #e6a23c;
  font-size: 20px;
}
.tipmessage .el-message__content {
  line-height: 25px;
}

.tipmessage1 {
  background-color: #f0f9eb;
  border-color: #57ec0d;
  align-items: start;
  color: #444;
  /* line-height: 30px; */
}
.tipmessage1 i {
  color: #67c23a;
  /* margin-top: 5px; */
  margin-right: 15px;
  font-size: 20px;
}
.tipmessage1 .el-message__content {
  line-height: 18px;
}
</style>
