<!-- 转化为电子证照 / 其他证件 -->
<template>
  <div>
    <el-col :span="24">
      <el-form-item
        prop="license"
        label="关联电子证照："
        :inline-message="true"
        :rules="[{required: true, validator: validateLicense, trigger: ['blur', 'change']}]"
      >
        <el-button v-if="operateType !== 'detail'" type="text" @click="openLicenseDialog">选择证照</el-button>
        <custom-table
          v-if="form.license && form.license.length > 0"
          ref="table"
          :is-card-type="false"
          :table-data="{
            content: form.license
          }"
          :table-header="tableHeader"
          :show-input="false"
          :stripe="false"
          @show="show"
        />
      </el-form-item>
    </el-col>
    <el-col :span="24">
      <el-form-item label="转化证照说明：">
        <el-input
          v-model="form.licenseExplain"
          type="textarea"
          :rows="3"
          :readonly="operateType === 'detail'"
          placeholder="您可填写要转化证照的补充说明"
        />
      </el-form-item>
    </el-col>
    <el-dialog :visible.sync="licenseDialogVisible" title="选择证照目录" width="900px">
      <el-form ref="licenseForm" :model="licenseForm" label-width="105px" @submit.native.prevent>
        <el-form-item
          prop="keyword"
          label="证照目录名称"
          :show-message="false"
          :rules="[{required: true, message: '请选择', trigger: ['blur']}]"
        >
          <el-input
            v-model="licenseForm.keyword"
            placeholder="请输入证照目录名称"
            style="width: 500px;margin-right: 10px"
            clearable
          />
          <el-button type="primary" :loading="transferLoading" @click="queryLicense">查询</el-button>
        </el-form-item>
      </el-form>
      <custom-transfer
        v-model="selectedLicenseList"
        v-loading="transferLoading"
        :props="{
          key: 'value',
          label: 'label'
        }"
        filterable
        :titles="['待选项', '已选项']"
        :data="licenseOptions"
        style="margin-top: 20px"
      />
      <div slot="footer">
        <el-button type="primary" @click="updateLicenseList">确定</el-button>
      </div>
    </el-dialog>
    <licenseCatalogueDialog :license-catalogue-dialog-show="licenseCatalogueDialog" @closeDialog="closeDialog" @getSlectItem="getSlectItem" />
  </div>
</template>

<script>
import CustomTable from '@/components/Element/Table'
import CustomTransfer from '@/views/proof-derate-admin/itemBiz/itemClear/components/CustomTransfer'
import { getLicenseItemName, goLicenseItemView } from '@/api/common/license'
import licenseCatalogueDialog from '@/views/proof-derate-admin/ItemManagement/itemProve/components/licenseCatalogueDialog.vue'
export default {
  name: 'ElectronicLicense',
  components: {
    CustomTable,
    CustomTransfer,
    licenseCatalogueDialog
  },
  props: {
    data: {
      type: Object,
      default: () => {
        return {}
      }
    },
    formRefs: {
      type: Object,
      default: () => {
        return {}
      }
    },
    operateType: {
      type: String,
      default: 'add'
    }
  },
  data() {
    return {
      form: {
        license: [],
        licenseExplain: ''
      },
      licenseDialogVisible: false,
      licenseCatalogueDialog: false,
      tableHeader: [
        { label: '关联电子证照名称', prop: 'label', minWidth: '250px' },
        {
          label: '操作',
          prop: 'operateColumn',
          width: '90px',
          fixed: 'right',
          actions: [
            {
              label: '查看',
              action: 'show'
            }
          ]
        }
      ],
      licenseForm: {
        keyword: ''
      },
      transferLoading: false,
      licenseOptions: [],
      selectedLicenseList: [],
      validateLicense: (rule, value, callback) => {
        if (_.isEmpty(this.form.license)) {
          callback(new Error('请选择关联的电子证照'))
        } else {
          callback()
        }
      }
    }
  },
  watch: {
    data: {
      handler(val) {
        if (!_.isEmpty(val)) {
          Object.keys(this.form).forEach(key => {
            if (key === 'licenseExplain') {
              this.form[key] = val['license_description']
            } else if (key === 'license') {
              if (
                typeof val['license_name'] === 'string' &&
                val['license_name'].length > 0
              ) {
                const license_name =
                  val['license_name'].length > 0 ? val['license_name'].split(',') : []
                const license_code =
                  val['license_code'].length > 0 ? val['license_code'].split(',') : []
                const license = []
                license_name.forEach((item, index) => {
                  license.push({ label: item, value: license_code[index] })
                })
                this.form[key] = license
              }
            }
          })
        }
      },
      deep: true,
      immediate: true
    },
    form: {
      handler(val) {
        this.$emit('change', val)
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    openLicenseDialog() {
      // this.licenseDialogVisible = true;
      this.licenseCatalogueDialog = true
      this.selectedLicenseList = _.cloneDeep(this.form.license)
    },
    queryLicense() {
      // this.$refs.licenseForm.validate(valid => {
      // if (valid) {
      this.transferLoading = true
      // todo 查询参数
      getLicenseItemName({ license_item_name: this.licenseForm.keyword })
        .then(res => {
          this.licenseOptions = res.data || []
          this.transferLoading = false
        })
        .catch(e => {
          this.licenseOptions = []
          this.transferLoading = false
        })
      // }
      // });
    },
    updateLicenseList() {
      this.licenseDialogVisible = false
      this.form.license = this.selectedLicenseList
      if (!_.isEmpty(this.form.license)) {
        this.formRefs.validateField('license')
      }
    },
    show(row) {
      goLicenseItemView(row.value).then(res => {
        window.open(res.data.url, '_blank')
      })
    },
    closeDialog() {
      this.licenseCatalogueDialog = false
    },
    getSlectItem(data) {
      console.log(data)
      this.selectedLicenseList = []
      const item = {
        label: '',
        value: ''
      }
      data.forEach(e => {
        item.label = e.name
        item.value = e.code
        this.selectedLicenseList.push(item)
      })
      // label: "中华人民共和国结婚证（自定义测试专属请勿操作）"
      // value: "100213101"

      this.licenseCatalogueDialog = false
      this.form.license = this.selectedLicenseList
      console.log('this.selectedLicenseList', this.selectedLicenseList)
      if (!_.isEmpty(this.form.license)) {
        this.formRefs.validateField('license')
      }
    }
  }
}
</script>

<style scoped>
</style>
