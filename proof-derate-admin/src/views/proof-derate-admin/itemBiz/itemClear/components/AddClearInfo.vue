<template>
  <div>
    <el-form ref="form" :model="form" label-width="150px">
      <el-row>
        <el-col :span="24">
          <el-form-item label="材料提供依据：">{{ form.evidence || "无" }}</el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="所属证明目录：">{{ form.catalogue || "无" }}</el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item
            prop="proof_clear_type"
            label="证明清理类型："
            :rules="[{required: true, message: '请选择证明清理类型', trigger: ['blur', 'change']}]"
          >
            <el-radio-group v-if="operateType !== 'detail'" v-model="form.proof_clear_type">
              <el-radio
                v-for="item in proofClearType"
                :key="item.value"
                :label="item.value"
              >{{ item.label }}</el-radio>
            </el-radio-group>
            <span v-else>{{ form.proof_clear_type | getClearTypeLabel(proofClearType) }}</span>
          </el-form-item>
        </el-col>
        <!--    证明清理类型：直接取消    -->
        <el-col v-if="form.proof_clear_type === 'DIRECTLY_CANCEL'" :span="24">
          <el-form-item
            prop="direct_description"
            label="直接取消说明："
            :show-message="false"
            :rules="[{required: true, message: '请输入', trigger: ['blur', 'change']}]"
          >
            <el-input
              v-model="form.direct_description"
              type="textarea"
              :rows="3"
              placeholder="您可填写直接取消原因说明"
              :readonly="operateType === 'detail'"
            />
          </el-form-item>
        </el-col>
        <!--    证明清理类型：代替取消    -->
        <template v-else-if="form.proof_clear_type === 'REPLACE_CANCEL'">
          <el-col :span="24">
            <el-form-item
              prop="replace_cancel_way"
              label="替代取消方式"
              :rules="[{required: true, message: '请选择替代取消方式', trigger: ['blur', 'change']}]"
            >
              <el-checkbox-group
                v-if="operateType !== 'detail'"
                v-model="form.replace_cancel_way"
                @change="wayChange"
              >
                <el-checkbox
                  v-for="item in cancelWayList"
                  :key="item.value"
                  :label="item.value"
                >{{ item.label }}</el-checkbox>
              </el-checkbox-group>
              <span v-else>{{ form.replace_cancel_way | getWayLabel(cancelWayList) }}</span>
            </el-form-item>
          </el-col>
          <!--    转化为电子证照 / 其他证件    -->
          <template
            v-if="form.replace_cancel_way && form.replace_cancel_way.includes('TURN_LICENSE_OR_OTHER_LICENSE_WAY')"
          >
            <electronic-license
              ref="customComponent1"
              :data="form.replace_license"
              :operate-type="operateType"
              :form-refs="formRefs"
            />
          </template>
          <!--    办事人承诺    -->
          <template
            v-if="form.replace_cancel_way && form.replace_cancel_way.includes('HANDLE_AFFAIRS_PROMISE')"
          >
            <el-col :span="24">
              <el-form-item
                prop="clerk_commitment.commit_attachment_id"
                label="承诺书模板："
                :rules="[{required: true, validator: validateMould, trigger: ['blur', 'change']}]"
              >
                <el-upload
                  ref="upload"
                  action="action"
                  :limit="1"
                  :auto-upload="true"
                  :disabled="operateType === 'detail'"
                  :file-list="fileList"
                  :multiple="false"
                  :on-change="onChange"
                  :on-exceed="onExceed"
                  :http-request="uploadFile"
                  :on-preview="onPreview"
                  :on-remove="onRemove"
                  :before-remove="beforeRemove"
                  class="upload-inline"
                >
                  <template v-if="fileList && fileList.length === 0">
                    <template v-if="!form.clerk_commitment.commit_attachment_id">
                      <el-button type="primary" size="mini">上传附件</el-button>
                      <div slot="tip" class="el-upload__tip">
                        上传的附件不能超过10MB,如文件过大，请先进行压缩后再上传；
                        <br>
                        禁止上传以下类型的附件：{{ blacklist.join(',') }}。
                      </div>
                    </template>
                  </template>
                </el-upload>
                <template v-if="fileList.length === 0 && form.clerk_commitment.commit_attachment_id">
                  <el-tag type="info" closable @close.stop="onRemove">{{ form.clerk_commitment.commit_attachment_name }}</el-tag>
                </template>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="承诺书说明：">
                <el-input
                  v-model="form.clerk_commitment.commit_book_description"
                  type="textarea"
                  :rows="3"
                  :placeholder="operateType !== 'detail' ? '您可填写该承诺书模板可避免的风险' : ''"
                  :readonly="operateType === 'detail'"
                />
              </el-form-item>
            </el-col>
            <!-- <clerk-promise
              ref="customComponent"
              :data="form"
              :operate-type="operateType"
              :form-refs="formRefs"
            />-->
          </template>
          <!--     ‘ 数据共享(证明开具部门)’和‘人工协查(证明开具部门)’共有部分     -->
          <!-- <template
            v-if="form.replace_cancel_way && (form.replace_cancel_way.includes('DATA_SHARING') || form.replace_cancel_way.includes('ARTIFICIAL_INVESTIGATION'))"
          >
            <el-col :xs="24" :sm="12" :md="12" :lg="12">
              <el-form-item
                prop="replace_data_shared.proof_provide_type"
                label="证明开具单位类型："
                :rules="[{required: true, message: '请选择证明开具单位类型', trigger: ['blur', 'change']}]"
                :show-message="false"
              >
                <el-select
                  v-if="operateType !== 'detail'"
                  v-model="form.replace_data_shared.proof_provide_type"
                  style="max-width: 300px"
                  @change="unitTypeChange"
                >
                  <el-option
                    v-for="item in unitTypeList"
                    :key="item.value"
                    :value="item.value"
                    :label="item.label"
                  />
                </el-select>
                <span v-else>{{ form.unitType | getUnitTypeLabel(unitTypeList) }}</span>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="12" :lg="12">
              <el-form-item
                prop="replace_data_shared.industry_dept_code"
                label="所属行业部门："
                :rules="[{required: true, message: '请选择所属行业部门', trigger: ['blur', 'change']}]"
                :show-message="false"
              >
                <el-select
                  v-if="operateType !== 'detail'"
                  v-model="form.replace_data_shared.industry_dept_code"
                  filterable
                  style="max-width: 300px"
                  @change="departmentChange"
                >
                  <el-option
                    v-for="item in departmentList"
                    :key="item.value"
                    :value="item.value"
                    :label="item.label"
                  />
                </el-select>
                <span v-else>{{ form.department | getDepartmentLabel(departmentList) }}</span>
              </el-form-item>
            </el-col>
          </template> -->
          <!--    数据共享(证明开具部门)    -->
          <template
            v-if="form.replace_cancel_way && form.replace_cancel_way.includes('DATA_SHARING')"
          >
            <el-col :span="24">
              <el-form-item
                prop="replace_data_shared.data_shared_description"
                label="数据共享说明："
                :show-message="false"
                :rules="[{required: true, message: '请输入数据共享说明', trigger: ['blur', 'change']}]"
              >
                <el-input
                  v-model="form.replace_data_shared.data_shared_description"
                  type="textarea"
                  :rows="3"
                  placeholder="您可填写需证明开具部门提供的协查数据范围或内容"
                />
              </el-form-item>
            </el-col>
            <!-- <data-sharing
              ref="customComponent"
              :data="form"
              :operate-type="operateType"
              :form-refs="formRefs"
            />-->
          </template>
          <!--    人工协查(证明开具部门)   -->
          <template
            v-if="form.replace_cancel_way && form.replace_cancel_way.includes('ARTIFICIAL_INVESTIGATION')"
          >
            <el-form-item
              prop="replace_investigation.investigation_description"
              label="人工协查说明："
              :show-message="false"
              :rules="[{required: true, message: '请输入人工协查说明', trigger: ['blur', 'change']}]"
            >
              <el-input
                v-model="form.replace_investigation.investigation_description"
                type="textarea"
                :rows="3"
                placeholder="您可填写需证明开具部门提供的人工协查内容或要求"
                :readonly="operateType === 'detail'"
              />
            </el-form-item>
            <!-- <manual-collaboration
              ref="customComponent"
              :data="form"
              :operate-type="operateType"
              :form-refs="formRefs"
            />-->
          </template>
          <!--    部门自行调查   -->
          <template
            v-if="form.replace_cancel_way && form.replace_cancel_way.includes('DEPARTMENT_INVESTIGATION')"
          >
            <!-- <el-col :span="24">
              <el-form-item
                prop="replace_dept_survey.dept_code"
                label="行业部门："
                :rules="[{required: true, message: '请选择行业部门', trigger: ['blur', 'change']}]"
                :show-message="false"
              >
                <el-select
                  v-if="operateType !== 'detail'"
                  v-model="form.replace_dept_survey.dept_code"
                  filterable
                  style="max-width: 300px"
                  @change="surveyDepartmentChange"
                >
                  <el-option
                    v-for="item in departmentList"
                    :key="item.value"
                    :value="item.value"
                    :label="item.label"
                  />
                </el-select>
                <span v-else>{{ form.department | getDepartmentLabel(departmentList) }}</span>
              </el-form-item>
            </el-col>-->
            <el-form-item
              prop="replace_dept_survey.dept_cancel_description"
              label="自行调查说明："
              :show-message="false"
              :rules="[{required: true, message: '请输入自行调查说明', trigger: ['blur', 'change']}]"
            >
              <el-input
                v-model="form.replace_dept_survey.dept_cancel_description"
                type="textarea"
                :rows="3"
                placeholder="您可填写具体的自行调查方式"
                :readonly="operateType === 'detail'"
              />
            </el-form-item>
            <!-- <self-investigate
              ref="customComponent"
              :data="form"
              :operate-type="operateType"
              :form-refs="formRefs"
            />-->
          </template>
          <!--    其他   -->
          <template v-if="form.replace_cancel_way && form.replace_cancel_way.includes('OTHER_WAY')">
            <el-form-item
              prop="replace_other.other_clear_description"
              label="其它说明："
              :show-message="false"
              :rules="[{required: true, message: '请输入其他说明', trigger: ['blur', 'change']}]"
            >
              <el-input
                v-model="form.replace_other.other_clear_description"
                type="textarea"
                :rows="3"
                placeholder="您可填写其他替代取消方式的说明"
                :readonly="operateType === 'detail'"
              />
            </el-form-item>
            <!-- <other
              ref="customComponent"
              :data="form"
              :operate-type="operateType"
              :form-refs="formRefs"
            />-->
          </template>
        </template>
      </el-row>
    </el-form>
  </div>
</template>

<script>
import Enum from '@/utils/enum'
import {
  getProofClearType,
  getReplaceCancelWay,
  getUnitTypeList,
  getDepartmentList,
  getCodeItemList,
  getCodeItemListByCodeId
} from '@/api/common/dict'
import { proofListCommitAttachmentt } from '@/api/itemBiz/clear'

export default {
  name: 'AddClearInfo',
  components: {
    ElectronicLicense: () =>
      import(
        '@/views/proof-derate-admin/itemBiz/itemClear/components/ElectronicLicense'
      )

  },
  filters: {
    getClearTypeLabel(type, typeList) {
      if (type) {
        const obj = _.find(typeList, { value: type }) || { label: type }
        return obj.label
      }
      return ''
    },
    getWayLabel(way, cancelWayList) {
      if (way) {
        const obj = _.find(cancelWayList, { value: way }) || { label: way }
        return obj.label
      }
      return ''
    },
    getUnitTypeLabel(unitType, unitTypeList) {
      if (unitType) {
        const obj = _.find(unitTypeList, { value: unitType }) || {
          label: unitType
        }
        return obj.label
      }
      return ''
    },
    getDepartmentLabel(department, departmentList) {
      if (department) {
        const obj = _.find(departmentList, { value: department }) || {
          label: department
        }
        return obj.label
      }
      return ''
    }
  },
  props: {
    row: {
      type: Object,
      default: () => {
        return {}
      }
    },
    operateType: {
      type: String,
      default: 'add'
    }
  },
  data() {
    return {
      // 承诺人提交接口
      action:
        process.env.VUE_APP_PROOF_DERATE_API_URL +
        'proof_list/commit_attachment',
      validateMould: (rule, value, callback) => {
        if (_.isEmpty(this.form.clerk_commitment.commit_attachment_id)) {
          callback(new Error('请上传承诺书'))
        } else {
          callback()
        }
      },
      blacklist: ['.exe', '.sh', '.bat', '.com', '.dll', 'jsp'],
      fileList: [],
      form: {
        evidence: '无',
        catalogue: '无',
        proof_clear_type: '',
        direct_description: '',
        replace_cancel_way: '',
        unitType: '',
        department: '',
        dataSharingExplain: '',
        license: [],
        licenseExplain: ''
      },
      cancelWayList: [
        /* {
          label: "转化为电子证照/其他证件",
          value: "TURN_LICENSE_OR_OTHER_LICENSE_WAY"
        },
        { label: "办事人承诺", value: "HANDLE_AFFAIRS_PROMISE" },
        { label: "数据共享（证明开具部门）", value: "DATA_SHARING" },
        {
          label: "人工协查（证明开具部门）",
          value: "ARTIFICIAL_INVESTIGATION"
        },
        { label: "部门自行调查", value: "DEPARTMENT_INVESTIGATION" },
        { label: "其它", value: "OTHER_WAY" }*/
      ],
      proofClearType: [],
      formRefs: null,
      unitTypeList: [],
      departmentList: []
    }
  },
  watch: {
    row: {
      handler(val) {
        this.form = _.cloneDeep(val)
        if (!this.form.replace_cancel_way) {
          this.$set(this.form, 'replace_cancel_way', [])
        }
      },
      deep: true,
      immediate: true
    }
  },
  created() {
    this.initDict()
  },
  mounted() {
    this.formRefs = this.$refs.form
  },
  methods: {
    initDict: async function() {
      const proofClearTypeRes = await getProofClearType()
      const cancelWayListRes = await getReplaceCancelWay()
      const unitTypeListRes = await getUnitTypeList()
      // let departmentListRes = await getDepartmentList();
      const codeInfo = await getCodeItemList()
      const codeId = codeInfo.content[0].id || ''
      const departmentListRes = await getCodeItemListByCodeId(
        JSON.stringify({ codeId: codeId, pageNumber: 0, pageSize: 1000 })
      )
      const departmentList = departmentListRes.content.map(i => {
        return { label: i.name, value: i.value }
      })
      this.proofClearType = proofClearTypeRes.data.reverse() || []
      this.cancelWayList = cancelWayListRes.data || []
      this.unitTypeList = unitTypeListRes.data || []
      this.departmentList = departmentList || []
    },
    validateForm() {
      let bo
      this.$refs.form.validate(valid => {
        bo = valid
      })
      return bo
    },
    getFormData() {
      const customComponents = this.$refs.customComponent
      _.forEach(customComponents, item => {
        const formData = item.form || {}
        Object.keys(formData).forEach(key => {
          this.$set(this.form, key, formData[key])
        })
      })

      const customComponents1 = this.$refs.customComponent1
      if (customComponents1 != undefined) {
        const replace_license = customComponents1.form
        const license_description = replace_license.licenseExplain
        let license_code = []
        let license_name = []
        replace_license.license.forEach(i => {
          license_code.push(i.value)
          license_name.push(i.label)
        })
        license_code = license_code.join(',')
        license_name = license_name.join(',')
        const form = { license_description, license_code, license_name }
        this.form.replace_license = form
      }
      return _.cloneDeep(this.form)
    },
    unitTypeChange(val) {
      this.form.replace_data_shared.proof_provide_type = val
      this.form.replace_investigation.proof_provide_type = val
    },
    departmentChange(val) {
      const obj = _.find(this.departmentList, { value: val }) || {
        label: val
      }
      this.form.replace_data_shared.industry_dept_code = val
      this.form.replace_investigation.industry_dept_code = val
      this.form.replace_data_shared.industry_dept_name = obj.label
      this.form.replace_investigation.industry_dept_name = obj.label
    },
    surveyDepartmentChange(val) {
      const obj = _.find(this.departmentList, { value: val }) || {
        label: val
      }
      this.form.replace_dept_survey.dept_name = obj.label
    },
    wayChange(val) {
      // if (!val.includes(3) && !val.includes(4)) {
      //   // todo 待确认枚举值
      //   this.form.department = "";
      //   this.form.unitType = "";
      // }
    },
    dataSharingExplainChange() {
      this.formRefs.validateField('dataSharingExplain')
    },

    // 上传承诺书接口
    onRemove(file, fileList) {
      this.fileList = fileList || []
      this.form.clerk_commitment.commit_attachment_id = ''
      this.form.clerk_commitment.commit_attachment_name = ''
    },
    beforeRemove(file, fileList) {
      return new Promise((resolve, reject) => {
        this.$confirm('确定移除该附件?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            resolve()
          })
          .catch(() => {
            reject()
          })
      })
    },
    onChange(file, fileList) {
      const fileSuffix = file.name.substring(file.name.lastIndexOf('.') + 1)
      const isBlacklist = this.blacklist.includes(
        fileSuffix.toLocaleLowerCase()
      )
      const isLt10m = file.size / (1024 * 1024) < 10
      if (isBlacklist) {
        this.$message.warning(
          `不能上传类型为${this.blacklist.join(',')}的文件！`
        )
        fileList.pop()
      } else if (!isLt10m) {
        this.$message.warning(`文件不能超过10MB`)
        fileList.pop()
      }
      this.fileList = fileList
      this.form.clerk_commitment.commit_attachment_id = ''
      this.form.clerk_commitment.commit_attachment_name = ''
      // if (this.form.clerk_commitment.commit_attachment_name) {
      //   this.formRefs.validateField("mould");
      // }
    },
    onExceed(files, fileList) {
      this.$message.warning('只能上传一个文件！')
    },
    uploadFile(data) {
      if (!data) {
        return
      }
      const name = data.file.name
      const file = data.file
      const fd = new FormData()
      fd.append('name', name)
      fd.append('file', file)
      proofListCommitAttachmentt(fd).then(res => {
        const type = res.meta.code === '200' ? 'success' : 'warning'
        this.$message({
          type: type,
          message: res.meta.msg
        })
        if (type === 'success') {
          this.form.clerk_commitment.commit_attachment_id = res.data.sessionId
          this.form.clerk_commitment.commit_attachment_name = name
        }
      })
    },
    onPreview(file) {
      const data = file.raw || file
      if (window.navigator.msSaveBlob) {
        // IE以及IE内核的浏览器
        window.navigator.msSaveBlob(data, data.name)
        return
      }
      const url = window.URL.createObjectURL(new Blob([data]))
      const link = document.createElement('a')
      link.style.display = 'none'
      link.href = url
      link.setAttribute('download', data.name)
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-upload__input {
  display: none;
}
::v-deep .el-form-item__content {
  position: inherit;
}
.upload-inline {
  display: flex;
  align-items: center;
  .el-upload-list {
    display: inline-block;
    margin-left: 10px;
    .el-upload-list__item:first-child {
      margin-top: 0;
    }
  }
  .el-upload__tip {
    color: red;
    line-height: 15px;
    margin: 0 5px 0 10px;
  }
}
</style>
