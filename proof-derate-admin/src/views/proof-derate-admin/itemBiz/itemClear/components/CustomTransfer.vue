<template>
  <div class="div-content">
    <div class="common-class">
      <div class="div-title">
        <el-checkbox
          v-model="checkAllLeft"
          class="all-checkbox-class"
          :indeterminate="isIndeterminateLeft"
          @change="handleCheckAllChangeLeft"
        >{{ titles[0] }}</el-checkbox>
        <span>{{ leftCheckedData.length + '/' + leftOptions.length }}</span>
      </div>
      <div class="check-content">
        <el-checkbox-group v-model="leftCheckedData">
          <el-checkbox
            v-for="(item, index) in leftOptions"
            :key="item[key] || index"
            :label="item[key]"
          >
          <template>{{formatContent ? formatContent(item) : item[props.label]}}</template>
          </el-checkbox>
        </el-checkbox-group>
      </div>
    </div>
    <div class="div-center">
      <el-button :disabled="leftCheckedData.length === 0" type="primary" icon="el-icon-arrow-right" @click="rightShift " />
      <el-button :disabled="rightCheckedData.length === 0" type="primary" icon="el-icon-arrow-left" @click="leftShift" />
    </div>
    <div class="common-class">
      <div class="div-title">
        <el-checkbox
          v-model="checkAllRight"
          class="all-checkbox-class"
          :indeterminate="isIndeterminateRight"
          @change="handleCheckAllChangeRight"
        >{{ titles[1] }}</el-checkbox>
        <span>{{ rightCheckedData.length + '/' + rightOptions.length }}</span>
      </div>
      <div class="check-content">
        <el-checkbox-group v-model="rightCheckedData">
          <el-checkbox
            v-for="(item, index) in rightOptions"
            :key="item[key] || index"
            :label="item[key]"
          >
          <template>{{formatContent ? formatContent(item) : item[props.label]}}</template>
          </el-checkbox>
        </el-checkbox-group>
      </div>
    </div>
  </div>
</template>

<script>

export default {
  name: 'CustomTransfer',
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    value: {
      type: Array,
      default: () => { return [] }
    },
    data: {
      type: Array,
      default: () => { return [] }
    },
    titles: {
      type: Array,
      default: () => { return ['待选项', '已选项'] }
    },
    props: {
      type: Object,
      default: () => { return { key: 'id', label: 'name' } }
    },
    formatContent: {
      type: Function,
      default: null
    }
  },
  data() {
    return {
      checkAllLeft: false,
      isIndeterminateLeft: false,
      leftCheckedData: [],
      leftOptions: [],
      checkAllRight: false,
      isIndeterminateRight: false,
      rightCheckedData: [],
      rightOptions: []
    }
  },
  computed: {
    key() {
      return this.props.key || 'value'
    }
  },
  watch: {
    data: {
      handler(val) {
        this.leftOptions = _.cloneDeep(val)
      },
      deep: true,
      immediate: true
    },
    value: {
      handler(val) {
        this.rightOptions = val
        if (!_.isEmpty(val)) {
          this.$nextTick(() => {
            this.leftOptions = _.filter(this.leftOptions, item => {
              return !_.some(val, { [this.key]: item[this.key] })
            })
          })
        }
      },
      deep: true,
      immediate: true
    },
    leftCheckedData: {
      handler(value) {
        const checkedCount = value.length
        this.checkAllLeft = checkedCount === this.leftOptions.length && this.leftOptions.length !== 0
        this.isIndeterminateLeft = checkedCount > 0 && checkedCount < this.leftOptions.length
      },
      deep: true,
      immediate: true
    },
    rightCheckedData: {
      handler(value) {
        const checkedCount = value.length
        this.checkAllRight = checkedCount === this.rightOptions.length && this.rightOptions.length !== 0
        this.isIndeterminateRight = checkedCount > 0 && checkedCount < this.rightOptions.length
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    handleCheckAllChangeLeft(val) {
      this.leftCheckedData = val ? _.map(this.leftOptions, item => item[this.key]) : []
      this.isIndeterminateLeft = false
    },
    handleCheckAllChangeRight(val) {
      this.rightCheckedData = val ? _.map(this.rightOptions, item => item[this.key]) : []
      this.isIndeterminateRight = false
    },
    rightShift() {
      const list = _.filter(this.leftOptions, item => this.leftCheckedData.includes(item[this.key]))
      this.rightOptions.push(...list)
      this.leftOptions = _.filter(this.leftOptions, item => !this.leftCheckedData.includes(item[this.key]))
      this.leftCheckedData = []
      this.$emit('change', this.rightOptions)
    },
    leftShift() {
      const list = _.filter(this.rightOptions, item => this.rightCheckedData.includes(item[this.key]))
      this.leftOptions.push(...list)
      this.rightOptions = _.filter(this.rightOptions, item => !this.rightCheckedData.includes(item[this.key]))
      this.rightCheckedData = []
      this.$emit('change', this.rightOptions)
    }
  }
}
</script>

<style lang="scss" scoped>
  .div-content{
    height: 400px;
    width: 100%;
    display: flex;
    text-align: center;
  }
  .common-class{
    height: 100%;
    min-width: 200px;
    overflow: hidden !important;
    border: #495060 1px solid;
    border-radius: 4px;
    overflow-y: auto;
    border: #EBEEF5 1px solid;
    flex: 0.5;
    .div-title{
      border: #EBEEF5 1px solid;
      height: 50px;
      background: #F5F7FA;
      display: flex;
      width: 100%;
      align-items: center;
      padding: 0 10px;
      overflow: hidden;
      .all-checkbox-class{
        margin-right: auto;
      }
    }
    .check-content{
      text-align: left;
      padding: 6px 11px;
      width: 100%;
      height: calc(100% - 50px);
      overflow-y: auto;
      overflow-x: hidden;
      .el-checkbox{
        display: flex;
        align-items: center;
        margin-right: 3px;
        margin-bottom: 5px;
        .el-checkbox__label{
          width: 100%;
        }
      }
    }
  }
  .div-center{
    width: 100px;
    height: 100%;
    margin: 0 5px;
    display: inline-flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    .el-button + .el-button{
      margin-left: 0px !important;
      margin-top: 8px !important;
    }
  }
</style>
