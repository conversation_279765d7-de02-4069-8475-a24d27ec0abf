<template>
  <div>
    <el-form ref="form" :model="form" label-width="100px">
      <el-row>
        <el-col :xs="24" :sm="24" :md="24" :lg="10">
          <el-form-item label="事项名称">
            <el-input v-model="form.item_name" clearable placeholder="请输入事项名称" />
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12" :md="12" :lg="11">
          <el-form-item label="事项编码">
            <el-input v-model="form.item_code" clearable placeholder="请输入事项编码" />
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12" :md="12" :lg="3">
          <el-form-item>
            <el-button type="primary" @click="search">查询</el-button>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :xs="24" :sm="12" :md="12" :lg="10">
          <el-form-item label="实施区划">
            <!-- <division-selector v-model="form.division_code" @change="divisionChange"></division-selector> -->
            <AdministrativeDivisionCascader
              :key="divisionCode"
              ref="AdministrativeDivisionSelect"
              :division-code="divisionCode"
              :permission-code="'catalog:biz:proof_clear:list'"
              @setDivisionCodeAndName="setDivisionCodeAndName"
            />
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12" :md="12" :lg="11">
          <el-form-item label="实施机构">
            <el-select v-model="form.credit_code" filterable placeholder="请选择" style="width:100%" clearable>
              <el-option v-for="item in organizationList" :key="item.credit_code" :label="item.name" :value="item.credit_code" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="事项标准类型">
            <!-- <el-radio-group v-model="form.item_source">
              <el-radio
                v-for="item in standardTypeList"
                :key="item.value"
                :label="item.value"
              >{{ item.label }}
              </el-radio>
            </el-radio-group>-->
            <el-checkbox-group v-model="form.item_source">
              <el-checkbox v-for="item in standardTypeList" :key="item.value" :label="item.value">{{ item.label }}</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="事项类型">
            <el-checkbox-group v-model="form.item_type">
              <el-checkbox v-for="item in mattersTypeList" :key="item.value" :label="item.value">{{ item.label }}</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>
import Enum from '@/utils/enum'
import { getMattersTypeList, getStandardTypeList } from '@/api/common/dict'
import { getOrgListNoAuth } from '@/api/admin/org.js'
import { getOrganizationList } from '@/api/commonPack/platManege'
import { getDivision } from '@/api/admin/baseobject.js'
export default {
  name: 'QueryForm',
  components: { AdministrativeDivisionCascader: () => import('@/components/AdministrativeDivisionCascader') },
  data() {
    return {
      form: {
        item_name: '', // 事项名称
        handing_item: '', // 办理项名称
        item_code: '', //
        division_code: '', // 实施区划代码
        credit_code: '', // 实时机构代码
        item_type: [], // 事项类型
        item_source: ['NOT_STANDARDIZATION', 'STANDARDIZATION'] // 事项来源：0代表非标准事项，1代表服务事项
      },
      standardTypeList: Enum.standardTypeList,
      mattersTypeList: Enum.mattersTypeList,
      actualizeList: Enum.actualizeList,
      organizationList: [],
      currentDivisionCode: '',
      isInitDivisionCode: '',
      divisionCode: ''
    }
  },
  watch: {
    mattersTypeList: {
      handler(val) {
        if (val.length > 0) {
          const itemType = []
          Enum.mattersTypeList.forEach(item => {
            itemType.push(item.value)
          })
          this.form['item_type'] = itemType
        }
      }
    }
  },
  created() {
    // this.currentDivisionCode = this.$store.getters.userdata.userAccount.divisionCode
    // this.params.divisionCode = this.currentDivisionCode
    this.isInitDivisionCode = sessionStorage.getItem('isInitDivisionCode')
    this.getMattersTypeList()
    this.getStandardTypeList()
  },
  methods: {
    divisionChange(d) {
      console.log('d', d)
      this.form.division_code = d.code || ''
      // this.params.divisionCode = this.params.divisionCode.replace(/(0+)$/g, ""); //末尾去0
      this.organizationList = []
      this.form.credit_code = ''
      if (d.code) {
        getOrgListNoAuth({
          pageSize: 1000,
          divisionCode: d.code
        }).then(res => {
          this.organizationList = res.content.map(i => {
            return { label: i.name, value: i.tyshxydm }
          })
        })

        // getDivision(d.code).then(res => {
        //   // console.log('res', res)
        //   this.organizationList = res.map(i => {
        //     return { label: i.name, value: i.value }
        //   })
        // })
      }
    },
    getMattersTypeList() {
      getMattersTypeList()
        .then(res => {
          const data = res.data || []
          Enum.mattersTypeList.splice(0)
          Enum.mattersTypeList.push(...data)
        })
        .catch(() => {
          Enum.mattersTypeList.splice(0)
        })
    },
    getStandardTypeList() {
      getStandardTypeList()
        .then(res => {
          const data = res.data || []
          Enum.standardTypeList.splice(0)
          Enum.standardTypeList.push(...data)
        })
        .catch(() => {
          Enum.standardTypeList.splice(0)
        })
    },
    search() {
      this.$emit('click', this.form)
    },
    setDivisionCodeAndName(data) {
      this.form.division_code = data.code || ''
      // this.params.divisionCode = this.params.divisionCode.replace(/(0+)$/g, ""); //末尾去0
      this.organizationList = []
      this.form.credit_code = ''
      this.getOrganizationList(this.form.division_code)
    },
    // 获取实施机构
    getOrganizationList(id) {
      const data = {
        division_code: id,
        permission_code: 'catalog:biz:proof_clear:list',
        scope: true
      }
      getOrganizationList(data).then(res => {
        if (res.meta.code === '200') {
          this.organizationList = res.data
        }
      })
    }
  }
}
</script>

<style scoped>
</style>
