<template>
  <div>
    <el-form ref="form" :model="form" label-width="150px">
      <el-row>
        <!-- <el-col :span="24">
          <el-form-item label="材料提供依据：">{{ form.evidence || "无" }}</el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="样例：">
            <span v-if="form.sample_file_id===null">
              <div v-if="form.sample_file_list">
                <a v-if="form.sample_file_list.length!=0" class="download" @click="downloadSampleFileList(form.sample_file_list)">
                  示例样表
                  <i class="el-icon-download"></i>
                </a>
                <span v-else>无</span>
              </div>

              <span v-else>无</span>
            </span>

            <a v-else class="download" @click="downloadFile('sample')">
              示例样表
              <i class="el-icon-download"></i>
            </a>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="空白表格：">

            <span v-if="form.blank_file_id===null">
              <div v-if="form.blank_file_list">
                <a v-if="form.blank_file_list.length!=0" class="download" @click="downloadblankFileList(form.blank_file_list)">
                  空白表格
                  <i class="el-icon-download"></i>
                </a>
                <span v-else>无</span>
              </div>

              <span v-else>无</span>
            </span>

            <a v-else-if="form.blank_file_id!==null" class="download" @click="downloadFile('blank')">
              空白表格
              <i class="el-icon-download"></i>
            </a>
          </el-form-item>
        </el-col>-->
        <el-col :span="24">
          <el-descriptions class="descriptions" title :column="2" border>
            <el-descriptions-item class="descriptions-item" :label-style="{ width: '140px' }">
              <template slot="label">材料提供依据</template>
              {{ form.evidence || "无" }}
            </el-descriptions-item>
            <el-descriptions-item class="descriptions-item" :label-style="{ width: '140px' }">
              <template slot="label">样例</template>
              <span v-if="form.sample_file_id === null">
                <div v-if="form.sample_file_list">
                  <a v-if="form.sample_file_list.length != 0" class="download"
                    @click="downloadSampleFileList(form.sample_file_list)">
                    示例样表
                    <i class="el-icon-download" />
                  </a>
                  <span v-else>无</span>
                </div>

                <span v-else>无</span>
              </span>
              <!-- <a v-else class="download" @click="downloadFile('sample')">{{ form.sample_file_name }}</a> -->
              <a v-else class="download" @click="downloadFile('sample')">
                示例样表
                <i class="el-icon-download" />
              </a>
            </el-descriptions-item>
            <el-descriptions-item :label-style="{ width: '140px' }">
              <template slot="label">空白表格</template>
              <span v-if="form.blank_file_id === null">
                <div v-if="form.blank_file_list">
                  <a v-if="form.blank_file_list.length != 0" class="download"
                    @click="downloadblankFileList(form.blank_file_list)">
                    空白表格
                    <i class="el-icon-download" />
                  </a>
                  <span v-else>无</span>
                </div>

                <span v-else>无</span>
              </span>
              <!-- <a v-else class="download" @click="downloadFile('blank')">{{ form.blank_file_name }}</a> -->
              <a v-else-if="form.blank_file_id !== null" class="download" @click="downloadFile('blank')">
                空白表格
                <i class="el-icon-download" />
              </a>
            </el-descriptions-item>
          </el-descriptions>
        </el-col>
        <el-col :span="24">
          <el-form-item prop="proof_clear_type" label="证明清理类型：" :inline-message="true"
            :rules="[{ required: true, message: '请选择证明清理类型', trigger: ['blur', 'change'] }]">
            <el-radio-group v-if="operateType !== 'detail'" v-model="form.proof_clear_type" @change="radioChange">
              <el-radio v-for="item in proofClearType" :key="item.value" :label="item.value">{{ item.label }}</el-radio>
            </el-radio-group>
            <span v-else>{{ form.proof_clear_type | getClearTypeLabel(proofClearType) }}</span>
          </el-form-item>
        </el-col>
        <!--    证明清理类型：无需清理    -->
        <el-col v-if="form.proof_clear_type === 'DO_NOT_CLEAN'" :span="24">
          <el-form-item prop="not_clean_description" label="无需清理说明：" :inline-message="true"
            :rules="[{ required: true, message: '请输入', trigger: ['blur', 'change'] }]">
            <el-input v-model="form.not_clean_description" type="textarea" :rows="3" placeholder="您可填写无需清理原因说明"
              :readonly="operateType === 'detail'" />
          </el-form-item>
        </el-col>
        <!--    证明清理类型：直接取消    -->
        <el-col v-else-if="form.proof_clear_type === 'DIRECTLY_CANCEL'" :span="24">
          <el-form-item prop="direct_description" label="直接取消说明：" :inline-message="true"
            :rules="[{ required: true, message: '请输入', trigger: ['blur'] }]">
            <el-input v-model="form.direct_description" type="textarea" :rows="3" placeholder="您可填写直接取消原因说明"
              :readonly="operateType === 'detail'" />
          </el-form-item>
        </el-col>
        <!--    证明清理类型：代替取消    -->
        <template v-else-if="form.proof_clear_type === 'REPLACE_CANCEL'">
          <el-col :span="24">
            <el-form-item label="证明目录" prop="relevance" :inline-message="true"
              :rules="[{ required: true, message: '请输入', trigger: ['blur'] }]">
              <el-input v-model="form.relevance" placeholder="请输入内容" disabled>
                <template slot="append">
                  <span class="colorText" @click="selectEvent">选择</span>
                </template>
              </el-input>
            </el-form-item>
            <!-- </el-form> -->
          </el-col>

          <template>
            <el-col :span="24">
              <el-form-item label="替换方式">
                <div>
                  <custom-table ref="table1" :is-card-type="false" :table-data="tableDirectoryData"
                    :table-header="tableDirectoryHeader" :span-method="objectSpanMethod" class="customTable">
                    <template #license_name="{ row }">
                      <el-button v-if="row.alternativeMethods != '电子证明'" type="text" @click="goLicenseItemView(row)">{{
                        row.license_name }}</el-button>
                    </template>
                  </custom-table>
                </div>
              </el-form-item>
            </el-col>
          </template>
          <el-col :span="24">
            <el-form-item label="备注：" :inline-message="true">
              <el-input v-model="proof_list_remark" type="textarea" :rows="3" maxlength="500" placeholder="输入备注"
                :readonly="operateType === 'detail'" />
            </el-form-item>
          </el-col>
        </template>
      </el-row>
    </el-form>
    <!-- 关联证明目录弹框 -->
    <el-dialog title="关联目录" :width="is1366 == true ? '55%' : '40%'" :visible.sync="selectDialog" class="dialog">
      <el-transfer ref="transfer" v-model="value" :filterable="true" :titles="['待选', '已选']"
        filter-placeholder="请输入证明目录名称" :data="generateData" class="transfer" @change="selectionChange">
        <div slot="left-footer" class="transfer-input">
          <el-input v-model="selectForm.proof_catalog_name" placeholder="请输入内容" />
          <el-button class="transfer-footer" size="small" @click="searchTransfer">查询</el-button>
        </div>
      </el-transfer>
      <div slot="footer" class="dialog-footer">
        <!-- <el-button type="primary" @click="selectFirm()">确 定</el-button> -->
        <el-button type="primary" @click="selectFirmDouble()">确 定</el-button>

        <el-button @click="selectDialog = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Enum from '@/utils/enum'
import CustomTable from '@/components/Element/Table'
import { getProofClearType, getReplaceCancelWay, getUnitTypeList, getDepartmentList, getCodeItemList, getCodeItemListByCodeId } from '@/api/common/dict'
import { proofListCommitAttachmentt } from '@/api/itemBiz/clear'
import { getGetproofCatalogPage, getGetproofCatalogFind } from '@/api/certificationManagement/certificationList'
import { downloadSampleFile, downloadBlankFile } from '@/api/common/download'
import { dataURLtoDownload, getIsWhitelist, getFileType } from '@/utils/index'
import { validPrefix } from '@/utils/validate'
import { goLicenseItemView } from '@/api/itemBiz/list'
export default {
  name: 'EditClearInfo',
  components: {
    CustomTable,
    ElectronicLicense: () => import('@/views/proof-derate-admin/itemBiz/itemClear/components/ElectronicLicense')
  },
  filters: {
    getClearTypeLabel(type, typeList) {
      if (type) {
        const obj = _.find(typeList, { value: type }) || { label: type }
        return obj.label
      }
      return ''
    },
    getWayLabel(way, cancelWayList) {
      if (way) {
        const obj = _.find(cancelWayList, { value: way }) || { label: way }
        return obj.label
      }
      return ''
    },
    getUnitTypeLabel(unitType, unitTypeList) {
      if (unitType) {
        const obj = _.find(unitTypeList, { value: unitType }) || {
          label: unitType
        }
        return obj.label
      }
      return ''
    },
    getDepartmentLabel(department, departmentList) {
      if (department) {
        const obj = _.find(departmentList, { value: department }) || {
          label: department
        }
        return obj.label
      }
      return ''
    }
  },
  props: {
    row: {
      type: Object,
      default: () => {
        return {}
      }
    },
    dictData: {
      Object,
      default: function () {
        return {}
      }
    },
    operateType: {
      type: String,
      default: 'add'
    }
  },
  data() {
    return {
      CS: {
        'text-align': 'center', // 文本居中
        'min-width': '250px', // 最小宽度
        'word-break': 'break-all' // 过长时自动换行
      },
      LS: {
        color: '#000',
        'text-align': 'center',
        'font-weight': '600',
        height: '40px',
        'background-color': 'rgba(255, 97, 2, 0.1)',
        'min-width': '110px',
        'word-break': 'keep-all'
      },

      // 承诺人提交接口
      action: process.env.VUE_APP_PROOF_DERATE_API_URL + 'proof_list/commit_attachment',
      validateMould: (rule, value, callback) => {
        if (_.isEmpty(this.form.clerk_commitment.commit_attachment_id)) {
          callback(new Error('请上传承诺书'))
        } else {
          callback()
        }
      },
      blacklist: ['.exe', '.sh', '.bat', '.com', '.dll', 'jsp'],
      fileList: [],
      formList: [],
      form: {
        evidence: '无',
        sample_file_id: '',
        sample_file_name: '',
        blank_file_id: '',
        blank_file_name: '',
        proof_clear_type: '',
        // proof_list_remark:'',
        not_clean_description: '',
        remarks: '',
        direct_description: '',
        replace_cancel_way: '',
        unitType: '',
        department: '',
        dataSharingExplain: '',
        license: [],
        licenseExplain: '',
        relevance: '',
        proof_catalog_id: '',
        blank_file_list: [],
        sample_file_list: []
      },
      cancelWayList: [
        /* {
          label: "转化为电子证照/其他证件",
          value: "TURN_LICENSE_OR_OTHER_LICENSE_WAY"
        },
        { label: "办事人承诺", value: "HANDLE_AFFAIRS_PROMISE" },
        { label: "数据共享（证明开具部门）", value: "DATA_SHARING" },
        {
          label: "人工协查（证明开具部门）",
          value: "ARTIFICIAL_INVESTIGATION"
        },
        { label: "自行调查", value: "DEPARTMENT_INVESTIGATION" },
        { label: "其它", value: "OTHER_WAY" }*/
      ],
      proofClearType: [],
      whitelist: ['doc', 'docx', 'pdf'],
      formRefs: null,
      unitTypeList: [],
      departmentList: [],
      // 关联电子证照
      selectDialog: false,
      selectLoading: false,
      // formRelevance: {
      //   relevance: "",
      //   item_code: ""
      // },
      selectForm: {
        proof_catalog_name: ''
      },
      tableDataSelect: {
        content: [], // 表格数据
        loading: false, // 控制表格加载效果
        total: 0, // 分页查询时的总数，去掉该属性，不显示分页条
        currentPage: 1, // 当前页码
        pageSize: 10,
        pageDirection: 'desc',
        multiple: true, // 是否多选 数据需要有id 属性值
        isShowSelection: true // 是否显示多选框，默认false
      },
      tableDirectoryData: {
        border: true,
        content: [], // 表格数据
        loading: false, // 控制表格加载效果
        total: 0, // 分页查询时的总数，去掉该属性，不显示分页条
        currentPage: 1, // 当前页码
        pageSize: 10,
        pageDirection: 'desc'
      },
      tableHeaderSelect: [
        {
          label: '',
          slot: 'select',
          prop: 'select',
          minWidth: '50px',
          fixed: 'right'
        },
        {
          label: '证明目录名称',
          prop: 'name',
          minWidth: '200px'
        },
        {
          label: '操作',
          slot: 'operate',
          prop: 'operate',
          minWidth: '50px',
          fixed: 'right'
        }
      ],
      tableDirectoryHeader: [
        {
          label: '证明目录',
          prop: 'name',
          minWidth: '200px'
        },
        {
          label: '替代方式',
          prop: 'alternativeMethods',
          minWidth: '200px'
        }
        // {
        //   label: '电子证照',
        //   prop: 'license_name',
        //   slot: 'license_name',
        //   minWidth: '200px'
        // }
      ],
      radio: '',
      selectList: {},
      replace_cancel_way_status: 0, // 是否选择过证明目录，0未选择，1选择过1次，大于1，替代取消的值修改过
      value: [],
      generateData: [],
      generateDataIndex: [],
      is1366: false,
      generateDataSelect: [],
      proof_list_remark: ''
    }
  },
  watch: {
    form: {
      handler(val) {
        if (this.replace_cancel_way_status === 1) {
          this.replace_cancel_way_status++
        } else {
          // console.log('this.form', this.row, this.replace_cancel_way_status, this.row.proof_catalog_id)
          // 替换目录任何form值改动的话，清空所选证明目录
          this.form.relevance = this.replace_cancel_way_status === 0 ? this.row.relevance : ''
          this.form.proof_catalog_id = this.replace_cancel_way_status === 0 ? this.row.proof_catalog_id : ''
          this.replace_cancel_way_status = 1
        }
      },
      deep: true,
      immediate: true
    },
    selectDialog: {
      handler(val) {
        // console.log('selectDialog', val)
        if (val) {
          // console.log(val)
          setTimeout(() => {
            this.watchLeftAllChecked()
            this.watchRightAllChecked()
            // const dom = $('div .is-filterable')[0]
            const dom = this.$refs.transfer.$el.firstChild.childNodes[1].childNodes[1]
            // console.log('this.$refs.transfer', this.$refs.transfer)
            // console.log('dom', this.$refs.transfer.$el.firstChild.childNodes[1].childNodes[1])
            dom.addEventListener('scroll', () => {
              if (dom.scrollTop + dom.clientHeight === dom.scrollHeight) {
                // console.log('到底了')
                this.tableDataSelect.pageSize = 10
                this.tableDataSelect.currentPage++
                this.query(this.tableDataSelect.currentPage)
              }
            })
          }, 0)
        }
      }
    }
    // row: {
    //   handler(val) {
    //     console.log('val',val)
    //     this.form = _.cloneDeep(val);
    //     if (!this.form.replace_cancel_way) {
    //       this.$set(this.form, "replace_cancel_way", []);
    //     }
    //   },
    //   deep: true,
    //   immediate: true
    // }
  },
  created() {
    this.initDict()
    this.selectList = this.row.dataList
    // this.selectList.id = this.row.proof_catalog_id
    // this.selectList.name = this.row.relevance
    console.log('this.selectList', this.selectList)
    // this.generateDataSelect=JSON.parse(JSON.stringify(this.selectList))
    this.selectList.forEach(i => {
      if (i.proof_catalog_id != null && i.proof_catalog_id !== '') {
        this.value.push(i.proof_catalog_id)
        this.generateDataSelect.push({ label: i.relevance, key: i.proof_catalog_id, pinyin: i.relevance, proof_catalog_id: i.proof_catalog_id })
      }
    })
    if (this.value.length > 2) {
      this.tableDataSelect.pageSize = this.value.length + 10
    }
    console.log('this.value', this.value)
    // console.log('this.row.licenseId', this.row.proof_catalog_id)
    // console.log('this.row.relevance', this.row.relevance)
    // this.selectFirm()
    this.selectFirmDouble()
  },
  mounted() {
    this.screenWidth()
    this.formRefs = this.$refs.form
    console.log('this.row', this.row)
    this.form = _.cloneDeep(this.row)
    this.proof_list_remark = this.row.proof_list_remark
    if (this.form.not_clean_description === '') {
      this.form.not_clean_description = '该材料暂无优化'
    }
    if (this.form.proof_clear_type === '' || this.form.proof_clear_type === null) {
      this.form.proof_clear_type = 'DO_NOT_CLEAN'
    }
    // console.log('this.row', this.row, this.row.proof_clear_type)
    if (!this.form.replace_cancel_way) {
      this.$set(this.form, 'replace_cancel_way', [])
    }
    console.log('this.form', this.form, this.form.sample_file_list)
  },
  methods: {
    // 监听左侧全选是否选中
    watchLeftAllChecked() {
      this.leftscrollAllCheckedWatch && this.leftscrollAllCheckedWatch()
      const transferVm = this.$refs.transfer
      transferVm.$refs.leftPanel.handleAllCheckedChange = () => {
        return false
      }
    },
    // 监听右侧全选是否选中
    watchRightAllChecked() {
      this.rightscrollAllCheckedWatch && this.rightscrollAllCheckedWatch()
      const transferVm = this.$refs.transfer
      transferVm.$refs.rightPanel.handleAllCheckedChange = () => {
        return false
      }
    },
    searchTransfer() {
      this.generateData = []
      this.query(1)
    },
    filterMethod(query, item) {
      return true
    },
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      // console.log(row, column)
      if (columnIndex == 0) {
        // console.log(row.rowspan)
        if (row.rowspan !== undefined) {
          return {
            rowspan: row.rowspan,
            colspan: 1
          }
        } else {
          return {
            rowspan: 0,
            colspan: 0
          }
        }
      }
    },
    initDict: async function () {
      //  console.log('this.dictData.proofClearType',this.dictData.proofClearType)
      // this.proofClearType = this.dictData.proofClearType.reverse() || []
      this.proofClearType = this.dictData.proofClearType // 事项证明档案: K的事项0314001
      //  console.log('this.proofClearType',this.proofClearType)
      this.cancelWayList = this.dictData.cancelWayList || []
    },
    validateForm() {
      let bo
      this.$refs.form.validate(valid => {
        bo = valid
      })
      return bo
    },
    getvalidateForm() {
      let bo
      this.$refs.form.validate(valid => {
        bo = valid
      })
      if (!bo) {
        return this.$refs.form
      } else {
        return false
      }
    },
    // getFormData() {
    //   const customComponents = this.$refs.customComponent;
    //   _.forEach(customComponents, item => {
    //     const formData = item.form || {};
    //     Object.keys(formData).forEach(key => {
    //       console.log('key',key)
    //       this.$set(this.form, key, formData[key]);
    //     });
    //   });

    //   const customComponents1 = this.$refs.customComponent1;
    //   if (customComponents1 != undefined) {
    //     let replace_license = customComponents1.form;
    //     let license_description = replace_license.licenseExplain;
    //     let license_code = [];
    //     let license_name = [];
    //     replace_license.license.forEach(i => {
    //       license_code.push(i.value);
    //       license_name.push(i.label);
    //     });
    //     license_code = license_code.join(",");
    //     license_name = license_name.join(",");
    //     let form = { license_description, license_code, license_name };
    //     this.form.replace_license = form;
    //   }
    //   return _.cloneDeep(this.form);
    // },
    getFormData() {
      const customComponents = this.$refs.customComponent
      // console.log('this.form', this.form.proof_clear_type)

      const formList = _.cloneDeep(this.formList)
      const form1 = _.cloneDeep(this.form)
      console.log('form1', form1)
      _.forEach(customComponents, item => {
        const formData = item.form || {}

        Object.keys(formData).forEach(key => {
          // console.log('key', key)
          // this.$set(this.form, key, formData[key]);
          this.$set(form1, key, formData[key])
        })
      })

      const customComponents1 = this.$refs.customComponent1
      if (customComponents1 != undefined) {
        const replace_license = customComponents1.form
        const license_description = replace_license.licenseExplain
        let license_code = []
        let license_name = []
        replace_license.license.forEach(i => {
          license_code.push(i.value)
          license_name.push(i.label)
        })
        license_code = license_code.join(',')
        license_name = license_name.join(',')
        const form = { license_description, license_code, license_name }
        // this.form.replace_license = form;
        form1.replace_license = form
      }
      // return _.cloneDeep(this.form);
      // console.log('form1', _.cloneDeep(form1))
      // return _.cloneDeep(form1)
      if (this.form.proof_clear_type === 'DIRECTLY_CANCEL' || this.form.proof_clear_type === 'DO_NOT_CLEAN') {
        return [form1]
      } else {
        formList.forEach(e => {
          e.proof_list_remark = this.proof_list_remark
        })
        return formList
      }
    },
    replaceLicenseChange(val) {
      if (this.replace_cancel_way_status === 2) {
        this.replace_cancel_way_status++
      } else {
        this.form.relevance = ''
        this.form.proof_catalog_id = ''
      }
    },
    unitTypeChange(val) {
      this.form.replace_data_shared.proof_provide_type = val
      this.form.replace_investigation.proof_provide_type = val
    },
    departmentChange(val) {
      const obj = _.find(this.departmentList, { value: val }) || {
        label: val
      }
      this.form.replace_data_shared.industry_dept_code = val
      this.form.replace_investigation.industry_dept_code = val
      this.form.replace_data_shared.industry_dept_name = obj.label
      this.form.replace_investigation.industry_dept_name = obj.label
    },
    surveyDepartmentChange(val) {
      const obj = _.find(this.departmentList, { value: val }) || {
        label: val
      }
      this.form.replace_dept_survey.dept_name = obj.label
    },
    wayChange(val) {
      // console.log('val', val)
      // if (!val.includes(3) && !val.includes(4)) {
      //   // todo 待确认枚举值
      //   this.form.department = "";
      //   this.form.unitType = "";
      // }
    },
    dataSharingExplainChange() {
      this.formRefs.validateField('dataSharingExplain')
    },

    // 上传承诺书接口
    onRemove(file, fileList) {
      this.fileList = fileList || []
      this.form.clerk_commitment.commit_attachment_id = ''
      this.form.clerk_commitment.commit_attachment_name = ''
    },
    beforeRemove(file, fileList) {
      return new Promise((resolve, reject) => {
        this.$confirm('确定移除该附件?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            resolve()
          })
          .catch(() => {
            reject()
          })
      })
    },
    onChange(file, fileList) {
      // const fileSuffix = file.name.substring(file.name.lastIndexOf(".") + 1);
      // const isBlacklist = this.blacklist.includes(
      //   fileSuffix.toLocaleLowerCase()
      // );
      // const isLt10m = file.size / (1024 * 1024) < 10;
      // if (isBlacklist) {
      //   this.$message.warning(
      //     `不能上传类型为${this.blacklist.join(",")}的文件！`
      //   );
      //   fileList.pop();
      // } else if (!isLt10m) {
      //   this.$message.warning(`文件不能超过10MB`);
      //   fileList.pop();
      // }
      // this.fileList = fileList;
      // this.form.clerk_commitment.commit_attachment_id = "";
      // this.form.clerk_commitment.commit_attachment_name = "";

      // console.log('this.fileList',this.fileList)

      this.fileList = fileList
      this.form.clerk_commitment.commit_attachment_id = ''
      this.form.clerk_commitment.commit_attachment_name = ''
      const fileName = getFileType(file.name).fileName
      const isLt10m = file.size / (1024 * 1024) < 1
      if (!isLt10m) {
        this.$message.error('文件大小不能超过1m！')
        fileList.pop()
      } else {
        if (fileName.indexOf('http') != -1 || validPrefix(fileName)) {
          this.$message.error('文件名开头包含特殊符号！')
          fileList.pop()
        } else {
          if (!getIsWhitelist(file.name, this.whitelist)) {
            this.$message.error(`请重新选择以${this.whitelist.join(',')}为后缀名的文件！`)
            fileList.pop()
          } else {
            this.uploadFile(file)
          }
        }
      }
      // this.fileList = fileList;
      // console.log('this.fileList',this.fileList)
      // this.form.clerk_commitment.commit_attachment_id = "";
      // this.form.clerk_commitment.commit_attachment_name = "";

      // if (this.form.clerk_commitment.commit_attachment_name) {
      //   this.formRefs.validateField("mould");
      // }
    },
    onExceed(files, fileList) {
      this.$message.warning('只能上传一个文件！')
    },
    httpRequest() { },
    uploadFile(data) {
      if (!data) {
        return
      }
      // let name = data.file.name;
      // let file = data.file;
      const name = data.name
      const file = data.raw
      const fd = new FormData()
      fd.append('name', name)
      fd.append('file', file)
      proofListCommitAttachmentt(fd).then(res => {
        const type = res.meta.code === '200' ? 'success' : 'warning'
        this.$message({
          type: type,
          message: type === 'success' ? '上传成功' : res.meta.message
        })
        if (type === 'success') {
          this.form.clerk_commitment.commit_attachment_id = res.data.sessionId
          this.form.clerk_commitment.commit_attachment_name = name
        } else {
          this.form.clerk_commitment.commit_attachment_id = ''
          this.form.clerk_commitment.commit_attachment_name = ''
          this.fileList = []
        }
      })
    },
    onPreview(file) {
      const data = file.raw || file
      if (window.navigator.msSaveBlob) {
        // IE以及IE内核的浏览器
        window.navigator.msSaveBlob(data, data.name)
        return
      }
      const url = window.URL.createObjectURL(new Blob([data]))
      const link = document.createElement('a')
      link.style.display = 'none'
      link.href = url
      link.setAttribute('download', data.name)
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    },
    // 关联电子证照
    selectQuery(data) {
      this.$refs['selectForm'].validate(valid => {
        if (valid) {
          this.query(1, 'search', data)
        } else {
          return false
        }
      })
    },
    query(currentPage, type, data) {
      if (_.isNumber(currentPage)) {
        this.tableDataSelect.currentPage = currentPage
      }
      const formData = Object.assign({}, this.selectForm)
      const sendData = {
        ...formData,
        page_size: this.tableDataSelect.pageSize,
        page_number: this.tableDataSelect.currentPage,
        page_direction: this.tableDataSelect.pageDirection
      }
      Object.keys(sendData).forEach(item => {
        if (!sendData[item]) delete sendData[item]
      })
      getGetproofCatalogPage(sendData)
        .then(res => {
          const data = res.data === null ? [] : res.data.content
          this.tableDataSelect.content = res.data.content
          this.tableDataSelect.total = res.data.totalElements
          // console.log('this.tableDataSelect.content', this.tableDataSelect.content)
          // console.log('this.selectList', this.selectList)
          // if (this.selectForm.proof_catalog_name == '') {

          // }else{
          //   this.tableDataSelect.content.forEach(e => {
          //     this.generateData.push({ label: e.name, key: e.id, pinyin: e.name, proof_catalog_id: e.id })
          //   })

          // }
          let selectListArray = []
          // this.selectList = undefined
          if (this.generateDataSelect != undefined) {
            selectListArray = this.generateDataSelect
          }
          console.log('selectListArray', selectListArray)
          this.tableDataSelect.content.forEach(e => {
            this.generateData.push({ label: e.name, key: e.id, pinyin: e.name, proof_catalog_id: e.id })
          })
          selectListArray.forEach(e0 => {
            this.generateData.push(e0)
          })
          this.generateData = _.uniqBy(this.generateData, 'key')
          console.log('this.generateData', this.generateData, 'generateDataIndex', this.generateDataIndex)
        })
        .catch(() => { })
    },
    selectEvent() {
      this.query(1)
      this.selectDialog = true
      console.log('generateData', this.generateData)
    },
    selectionChange(val1, val2, val3) {
      this.generateDataSelect = []
      // console.log('selectionChange', val1, this.generateData)
      // console.log('this.value', this.value)
      this.selectList = []
      val1.forEach(i => {
        // console.log(this.generateData.filter(item => item.key === i))
        const item = {
          proof_catalog_id: i
        }
        this.selectList.push(item)
        this.generateDataSelect.push(this.generateData.filter(item => item.key === i)[0])
        // i.relevance = i.name
      })
      // console.log('this.generateDataSelect', this.generateDataSelect)
    },

    selectFirmDouble() {
      // console.log('111111', this.selectList)
      this.formList = []
      this.tableDirectoryData.content = []
      this.form.relevance = ''
      // this.form.replace_cancel_way = []
      // this.form.replace_cancel_way.push('TURN_LICENSE_OR_OTHER_LICENSE_WAY')
      // console.log('value', this.value)

      if (JSON.stringify(this.selectList) != '{}') {
        this.selectList.forEach((e, index) => {
          if (e.proof_catalog_id !== '' && e.proof_catalog_id !== null) {
            getGetproofCatalogFind(e.proof_catalog_id)
              .then(res => {
                console.log('getGetproofCatalogFind', res)
                const {
                  proof_catalog_license_relation_list,
                  proof_catalog_clerk_commitment_list,
                  proof_catalog_data_shared_list,
                  proof_catalog_artificial_list,
                  proof_catalog_dept_survey_list,
                  proof_catalog_other_relation_list,
                  proof_catalog_license_item_relation
                } = res.data
                let replace_cancel_way = []
                // this.form.proof_list_remark = this.proof_list_remark
                let rowspan = 0
                if (proof_catalog_license_relation_list != null) {
                  rowspan = rowspan + proof_catalog_license_relation_list.length
                }
                if (proof_catalog_clerk_commitment_list != null) {
                  rowspan = rowspan + proof_catalog_clerk_commitment_list.length
                }
                if (proof_catalog_data_shared_list != null) {
                  rowspan = rowspan + proof_catalog_data_shared_list.length
                }
                if (proof_catalog_artificial_list != null) {
                  rowspan = rowspan + proof_catalog_artificial_list.length
                }
                if (proof_catalog_dept_survey_list != null) {
                  rowspan = rowspan + proof_catalog_dept_survey_list.length
                }
                if (proof_catalog_other_relation_list != null) {
                  rowspan = rowspan + proof_catalog_other_relation_list.length
                }
                if (proof_catalog_license_item_relation != null) {
                  rowspan = rowspan + 1
                }
                console.log('rowspan', rowspan)
                // console.log('proof_catalog_clerk_commitment_list', proof_catalog_clerk_commitment_list, 'proof_catalog_artificial_list', proof_catalog_artificial_list, res.data)
                // console.log('this.$refs.customComponent1', this.$refs.customComponent1)
                if (proof_catalog_license_relation_list != null && proof_catalog_license_relation_list.length != 0) {
                  proof_catalog_license_relation_list[0].name = res.data.name

                  proof_catalog_license_relation_list[0].rowspan = rowspan
                  proof_catalog_license_relation_list.forEach(e => {
                    e.alternativeMethods = '电子证照'
                    this.tableDirectoryData.content.push(e)
                  })

                  // console.log('进来了电子证照', proof_catalog_license_relation_list, this.$refs.customComponent1)
                  // 电子证照
                  let license_description = ''
                  let license_code = []
                  let license_name = []
                  let data = []
                  proof_catalog_license_relation_list.forEach(i => {
                    license_code.push(i.license_code)
                    license_name.push(i.license_name)
                  })
                  license_code = license_code.join(',')
                  license_name = license_name.join(',')
                  license_description = ''
                  data = proof_catalog_license_relation_list.map(i => {
                    return { label: i.license_name, value: i.license_code }
                  })
                  this.form.replace_license = { license_description, license_code, license_name }
                  replace_cancel_way.push('TURN_LICENSE_OR_OTHER_LICENSE_WAY')
                } else {
                  // this.$refs.customComponent1.form.license = [];
                  // 特殊处理该组件
                  replace_cancel_way = replace_cancel_way.filter(i => i != 'TURN_LICENSE_OR_OTHER_LICENSE_WAY')
                }

                if (proof_catalog_license_item_relation != null && proof_catalog_license_item_relation.length != 0) {
                  proof_catalog_license_item_relation.name = res.data.name

                  proof_catalog_license_item_relation.alternativeMethods = '电子证明'
                  // 保证第一条数据带上rowspan属性 实现表格合并
                  if (proof_catalog_license_relation_list == null || proof_catalog_license_relation_list.length == 0) {
                    proof_catalog_license_item_relation.rowspan = rowspan
                  }
                  this.tableDirectoryData.content.push(proof_catalog_license_item_relation)
                  // console.log('进来了电子证明', proof_catalog_license_item_relation, this.$refs.customComponent1)
                  // 电子证照
                  let license_description = ''
                  let license_code = ''
                  let license_name = ''
                  license_code = proof_catalog_license_item_relation.license_code
                  license_name = proof_catalog_license_item_relation.license_name
                  license_description = proof_catalog_license_item_relation.license_description
                  this.form.replace_license_item = { license_description, license_code, license_name }
                  replace_cancel_way.push('TURN_LICENSE_ITEM')
                  // }
                } else {
                  // 特殊处理该组件
                  replace_cancel_way = replace_cancel_way.filter(i => i != 'TURN_LICENSE_ITEM')
                }

                if (proof_catalog_clerk_commitment_list != null && proof_catalog_clerk_commitment_list.length != 0) {
                  proof_catalog_clerk_commitment_list[0].name = res.data.name
                  proof_catalog_clerk_commitment_list[0].alternativeMethods = '承诺书'
                  if ((proof_catalog_license_relation_list == null || proof_catalog_license_relation_list.length == 0) && (proof_catalog_license_item_relation == null || proof_catalog_license_item_relation.length == 0)) {
                    proof_catalog_clerk_commitment_list[0].rowspan = rowspan
                  }

                  this.tableDirectoryData.content.push(proof_catalog_clerk_commitment_list[0])
                  // console.log('进来了承诺书', proof_catalog_clerk_commitment_list)
                  replace_cancel_way.push('HANDLE_AFFAIRS_PROMISE')
                  this.form.clerk_commitment.commit_attachment_id = proof_catalog_clerk_commitment_list[0].commit_attachment_id
                  this.form.clerk_commitment.commit_attachment_name = proof_catalog_clerk_commitment_list[0].commit_attachment_name
                  this.form.clerk_commitment.commit_book_description = proof_catalog_clerk_commitment_list[0].commit_book_description
                } else {
                  replace_cancel_way = replace_cancel_way.filter(i => i != 'HANDLE_AFFAIRS_PROMISE')
                  this.form.clerk_commitment.commit_attachment_id = ''
                  this.form.clerk_commitment.commit_attachment_name = ''
                  this.form.clerk_commitment.commit_book_description = ''
                }

                if (proof_catalog_data_shared_list != null && proof_catalog_data_shared_list.length != 0) {
                  proof_catalog_data_shared_list[0].name = res.data.name
                  // proof_catalog_data_shared_list[0].alternativeMethods = '自行调查'
                  proof_catalog_data_shared_list[0].alternativeMethods = '数据共享'
                  if ((proof_catalog_clerk_commitment_list == null || proof_catalog_clerk_commitment_list.length == 0) && (proof_catalog_license_relation_list == null || proof_catalog_license_relation_list.length == 0) && (proof_catalog_license_item_relation == null || proof_catalog_license_item_relation.length == 0)) {
                    proof_catalog_data_shared_list[0].rowspan = rowspan
                  }
                  this.tableDirectoryData.content.push(proof_catalog_data_shared_list[0])
                  // console.log('进来了部门自行调查', proof_catalog_clerk_commitment_list)
                  replace_cancel_way.push('DATA_SHARING')
                  this.form.replace_data_shared.data_shared_description = proof_catalog_data_shared_list[0].note
                } else {
                  replace_cancel_way = replace_cancel_way.filter(i => i != 'DATA_SHARING')
                  this.form.replace_data_shared.data_shared_description = ''
                }
                if (proof_catalog_artificial_list != null && proof_catalog_artificial_list.length != 0) {

                  proof_catalog_artificial_list[0].name = res.data.name
                  proof_catalog_artificial_list[0].alternativeMethods = '人工协查'
                  if (
                    (proof_catalog_data_shared_list == null || proof_catalog_data_shared_list.length == 0) &&
                    (proof_catalog_clerk_commitment_list == null || proof_catalog_clerk_commitment_list.length == 0) &&
                    (proof_catalog_license_relation_list == null || proof_catalog_clerk_commitment_list.length == 0) &&
                    (proof_catalog_license_item_relation == null || proof_catalog_license_item_relation.length == 0)
                  ) {
                    proof_catalog_artificial_list[0].rowspan = rowspan
                  }

                  this.tableDirectoryData.content.push(proof_catalog_artificial_list[0])
                  console.log('进来了人工协查', proof_catalog_artificial_list)
                  replace_cancel_way.push('ARTIFICIAL_INVESTIGATION')
                  this.form.replace_investigation.investigation_description = proof_catalog_artificial_list[0].note
                } else {
                  replace_cancel_way = replace_cancel_way.filter(i => i != 'ARTIFICIAL_INVESTIGATION')
                  this.form.replace_investigation.investigation_description = ''
                }

                if (proof_catalog_dept_survey_list != null && proof_catalog_dept_survey_list.length != 0) {
                  proof_catalog_dept_survey_list[0].name = res.data.name
                  proof_catalog_dept_survey_list[0].alternativeMethods = '自行调查'
                  if (
                    (proof_catalog_artificial_list == null || proof_catalog_artificial_list.length == 0) &&
                    (proof_catalog_data_shared_list == null || proof_catalog_data_shared_list.length == 0) &&
                    (proof_catalog_clerk_commitment_list == null || proof_catalog_clerk_commitment_list.length == 0) &&
                    (proof_catalog_license_relation_list == null || proof_catalog_license_relation_list.length == 0) &&
                    (proof_catalog_license_item_relation == null || proof_catalog_license_item_relation.length == 0)
                  ) {
                    proof_catalog_dept_survey_list[0].rowspan = rowspan
                  }
                  this.tableDirectoryData.content.push(proof_catalog_dept_survey_list[0])
                  // console.log('进来了部门自行调查', proof_catalog_dept_survey_list)
                  replace_cancel_way.push('DEPARTMENT_INVESTIGATION')
                  this.form.replace_dept_survey.dept_cancel_description = proof_catalog_dept_survey_list[0].dept_cancel_description
                } else {
                  replace_cancel_way = replace_cancel_way.filter(i => i != 'DEPARTMENT_INVESTIGATION')
                  this.form.replace_dept_survey.dept_cancel_description = ''
                }

                if (proof_catalog_other_relation_list != null && proof_catalog_other_relation_list.length != 0) {
                  proof_catalog_other_relation_list[0].name = res.data.name
                  proof_catalog_other_relation_list[0].alternativeMethods = '其他'
                  if (
                    (proof_catalog_dept_survey_list == null || proof_catalog_dept_survey_list.length == 0) &&
                    (proof_catalog_artificial_list == null || proof_catalog_artificial_list.length == 0) &&
                    (proof_catalog_data_shared_list == null || proof_catalog_data_shared_list.length == 0) &&
                    (proof_catalog_clerk_commitment_list == null || proof_catalog_clerk_commitment_list.length == 0) &&
                    (proof_catalog_license_relation_list == null || proof_catalog_license_relation_list.length == 0) &&
                    (proof_catalog_license_item_relation == null || proof_catalog_license_item_relation.length == 0)
                  ) {
                    proof_catalog_other_relation_list[0].rowspan = rowspan
                  }
                  this.tableDirectoryData.content.push(proof_catalog_other_relation_list[0])
                  // console.log('进来了其他', proof_catalog_other_relation_list)
                  replace_cancel_way.push('OTHER_WAY')
                  this.form.replace_other.other_clear_description = proof_catalog_other_relation_list[0].other_clear_description
                } else {
                  replace_cancel_way = replace_cancel_way.filter(i => i != 'OTHER_WAY')
                  this.form.replace_other.other_clear_description = ''
                }

                this.form.relevance = res.data.name + ',' + this.form.relevance
                // this.form.relevance = this.form.relevance.slice(0, this.form.relevance.length - 1)
                this.form.proof_catalog_id = e.proof_catalog_id
                this.replace_cancel_way_status = 1
                this.selectDialog = false
                // this.value = []
                this.generateData = []
                // console.log('this.form1111', 'this.form.relevance',this.form.relevance)
                this.form.replace_cancel_way = replace_cancel_way

                this.formList.push(_.cloneDeep(this.form))
                console.log('this.tableDirectoryData.content', 'index', index, this.tableDirectoryData.content)
              })
              .then(() => {
                // console.log('this.tableDirectoryData.content1111', this.tableDirectoryData.content)
              })
              .catch(err => {
                console.log(err)
              })
          }
        })
        this.selectDialog = false
      } else {
        this.$message({
          message: '请选择一条证明目录',
          type: 'warning'
        })
      }
    },
    selectFirm() {
      // console.log('111111', this.selectList)
      this.tableDirectoryData.content = []
      this.form.replace_cancel_way = []
      this.form.replace_cancel_way.push('TURN_LICENSE_OR_OTHER_LICENSE_WAY')
      if (JSON.stringify(this.selectList) != '{}') {
        getGetproofCatalogFind(this.selectList.id)
          .then(res => {
            const {
              proof_catalog_license_relation_list,
              proof_catalog_clerk_commitment_list,
              proof_catalog_data_shared_list,
              proof_catalog_artificial_list,
              proof_catalog_dept_survey_list,
              proof_catalog_other_relation_list
            } = res.data
            const replace_cancel_way = []
            console.log('proof_catalog_clerk_commitment_list', proof_catalog_clerk_commitment_list, 'proof_catalog_artificial_list', proof_catalog_artificial_list, res.data)
            console.log('this.$refs.customComponent1', this.$refs.customComponent1)
            if (proof_catalog_license_relation_list != null) {
              proof_catalog_license_relation_list[0].name = this.selectList.name
              proof_catalog_license_relation_list[0].alternativeMethods = '电子证照'
              this.tableDirectoryData.content.push(proof_catalog_license_relation_list[0])
              console.log('进来了电子证照', proof_catalog_license_relation_list, this.$refs.customComponent1)
              // 电子证照
              let license_description = ''
              let license_code = []
              let license_name = []
              let data = []
              // if (this.$refs.customComponent1 != undefined) {
              proof_catalog_license_relation_list.forEach(i => {
                license_code.push(i.license_code)
                license_name.push(i.license_name)
              })
              license_code = license_code.join(',')
              license_name = license_name.join(',')
              license_description = ''
              data = proof_catalog_license_relation_list.map(i => {
                return { label: i.license_name, value: i.license_code }
              })
              this.form.replace_license = { license_description, license_code, license_name }
              // this.$refs.customComponent1.form.license = data
              // } else {
              //   this.form.replace_license = {
              //     license_description: '',
              //     license_code: '',
              //     license_name: ''
              //   }
              // }
            } else {
              // this.$refs.customComponent1.form.license = [];
              // 特殊处理该组件
              this.form.replace_cancel_way = this.form.replace_cancel_way.filter(i => i != 'TURN_LICENSE_OR_OTHER_LICENSE_WAY')
            }

            if (proof_catalog_clerk_commitment_list != null) {
              proof_catalog_clerk_commitment_list[0].name = this.selectList.name
              proof_catalog_clerk_commitment_list[0].alternativeMethods = '承诺书'
              this.tableDirectoryData.content.push(proof_catalog_clerk_commitment_list[0])
              console.log('进来了承诺书', proof_catalog_clerk_commitment_list)
              this.form.replace_cancel_way.push('HANDLE_AFFAIRS_PROMISE')
              this.form.clerk_commitment.commit_attachment_id = proof_catalog_clerk_commitment_list[0].commit_attachment_id
              this.form.clerk_commitment.commit_attachment_name = proof_catalog_clerk_commitment_list[0].commit_attachment_name
              this.form.clerk_commitment.commit_book_description = proof_catalog_clerk_commitment_list[0].commit_book_description
            } else {
              this.form.replace_cancel_way = this.form.replace_cancel_way.filter(i => i != 'HANDLE_AFFAIRS_PROMISE')
              this.form.clerk_commitment.commit_attachment_id = ''
              this.form.clerk_commitment.commit_attachment_name = ''
              this.form.clerk_commitment.commit_book_description = ''
            }

            if (proof_catalog_data_shared_list != null) {
              proof_catalog_data_shared_list[0].name = this.selectList.name
              proof_catalog_data_shared_list[0].alternativeMethods = '自行调查'
              this.tableDirectoryData.content.push(proof_catalog_data_shared_list[0])
              console.log('进来了部门自行调查', proof_catalog_clerk_commitment_list)
              this.form.replace_cancel_way.push('DATA_SHARING')
              this.form.replace_data_shared.data_shared_description = proof_catalog_data_shared_list[0].note
            } else {
              this.form.replace_cancel_way = this.form.replace_cancel_way.filter(i => i != 'DATA_SHARING')
              this.form.replace_data_shared.data_shared_description = ''
            }
            if (proof_catalog_artificial_list != null) {
              proof_catalog_artificial_list[0].name = this.selectList.name
              proof_catalog_artificial_list[0].alternativeMethods = '人工协查'
              this.tableDirectoryData.content.push(proof_catalog_artificial_list[0])
              console.log('进来了人工协查', proof_catalog_artificial_list)
              this.form.replace_cancel_way.push('ARTIFICIAL_INVESTIGATION')
              this.form.replace_investigation.investigation_description = proof_catalog_artificial_list[0].note
            } else {
              this.form.replace_cancel_way = this.form.replace_cancel_way.filter(i => i != 'ARTIFICIAL_INVESTIGATION')
              this.form.replace_investigation.investigation_description = ''
            }

            if (proof_catalog_dept_survey_list != null) {
              proof_catalog_dept_survey_list[0].name = this.selectList.name
              proof_catalog_dept_survey_list[0].alternativeMethods = '自行调查'
              this.tableDirectoryData.content.push(proof_catalog_dept_survey_list[0])
              console.log('进来了部门自行调查', proof_catalog_dept_survey_list)
              this.form.replace_cancel_way.push('DEPARTMENT_INVESTIGATION')
              this.form.replace_dept_survey.dept_cancel_description = proof_catalog_dept_survey_list[0].dept_cancel_description
            } else {
              this.form.replace_cancel_way = this.form.replace_cancel_way.filter(i => i != 'DEPARTMENT_INVESTIGATION')
              this.form.replace_dept_survey.dept_cancel_description = ''
            }

            if (proof_catalog_other_relation_list != null) {
              proof_catalog_other_relation_list[0].name = this.selectList.name
              proof_catalog_other_relation_list[0].alternativeMethods = '其他'
              this.tableDirectoryData.content.push(proof_catalog_other_relation_list[0])
              console.log('进来了其他', proof_catalog_other_relation_list)
              this.form.replace_cancel_way.push('OTHER_WAY')
              this.form.replace_other.other_clear_description = proof_catalog_other_relation_list[0].other_clear_description
            } else {
              this.form.replace_cancel_way = this.form.replace_cancel_way.filter(i => i != 'OTHER_WAY')
              this.form.replace_other.other_clear_description = ''
            }

            this.form.relevance = this.selectList.name
            this.form.proof_catalog_id = this.selectList.id
            this.replace_cancel_way_status = 1
            this.selectDialog = false
            console.log('this.tableDirectoryData.content', this.tableDirectoryData.content)
          })
          .catch(err => {
            console.log(err)
          })
      } else {
        this.$message({
          message: '请选择一条证明目录',
          type: 'warning'
        })
      }
    },
    // 查看
    selectLook(row) {
      this.$router.push({
        name: 'certification_List_info',
        query: {
          id: row.id
        }
      })
    },
    downloadFile(type) {
      if (type === 'sample') {
        downloadSampleFile({ attachment_id: this.form.sample_file_id }).then(res => {
          if (res.meta.code === '200') {
            dataURLtoDownload(res.data, this.form.sample_file_name)
          }
        })
      } else {
        downloadBlankFile({ attachment_id: this.form.blank_file_id }).then(res => {
          if (res.meta.code === '200') {
            dataURLtoDownload(res.data, this.form.blank_file_name)
          }
        })
      }
    },
    downloadSampleFileList(data) {
      console.log(data)
      this.downSampleFile(data)
    },
    downloadblankFileList(data) {
      console.log(data)
      this.downblankFile(data)
    },
    downSampleFile(row) {
      if (row.length != 0) {
        console.log(row)
        const itemList = row
        itemList.forEach((e, index) => {
          // this.downURLfile(e.intranet_path, e.attachment_name, index)
          this.createIFrame(e.intranet_path, 1000, 1000)
        })
      } else {
        this.$message({
          showClose: true,
          message: '暂无文件下载',
          type: 'warning'
        })
      }
    },
    downblankFile(row) {
      console.log(row)
      if (row != 0) {
        // console.log(row.blank_file_list)
        const itemList = row
        // intranet_path attachment_name
        itemList.forEach(e => {
          // this.downURLfile(e.intranet_path, e.attachment_name)
          this.createIFrame(e.intranet_path, 1000, 1000)
        })
      } else {
        this.$message({
          showClose: true,
          message: '暂无文件下载',
          type: 'warning'
        })
      }
    },
    createIFrame(url, triggerDelay, removeDelay) {
      console.log(url, triggerDelay, removeDelay)
      setTimeout(function () {
        // 动态添加iframe,设置src,然后删除
        const frame = document.createElement('iframe') // 创建a对象
        frame.setAttribute('style', 'display: none')
        frame.setAttribute('src', url)
        frame.setAttribute('id', 'iframeName')
        frame.setAttribute('referrerpolicy', 'no-referrer')
        document.body.appendChild(frame)
        setTimeout(function () {
          const node = document.getElementById('iframeName')
          node.parentNode.removeChild(node)
        }, removeDelay)
      }, triggerDelay)
    },
    radioChange() {
      // this.$refs['form'].clearValidate();
    },
    goLicenseItemView(row) {
      goLicenseItemView(row.license_code).then(res => {
        window.open(res.data.url, '_blank')
      })
    },
    screenWidth() {
      if (screen.width == 1920) {
        this.is1366 = false
        // console.log('1920*1080')
      } else if (screen.width == 1366) {
        // console.log('1366*768')
        this.is1366 = true
      } else {
        this.is1366 = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.customTable ::v-deep .el-card__body {
  padding-top: 10px;
  padding-left: 0;
}

::v-deep .el-upload__input {
  display: none;
}

::v-deep .el-form-item__content {
  position: inherit;
}

::v-deep .el-input-group__append,
.el-input-group__prepend {
  background: none;
  // color: #4a6eff;
  border: 0;
  cursor: pointer;
}

.upload-inline {
  display: flex;
  align-items: center;

  .el-upload-list {
    display: inline-block;
    margin-left: 10px;

    .el-upload-list__item:first-child {
      margin-top: 0;
    }
  }

  .el-upload__tip {
    color: red;
    line-height: 15px;
    margin: 0 5px 0 10px;
  }
}

a {
  cursor: pointer;
  color: #409eff;
}

.formRelevance {
  position: relative;
}

.selectForm {
  position: relative;

  .catalogBtn {
    position: absolute;
    top: -1%;
  }
}

.dialog-footer {
  text-align: center;
}

.transfer ::v-deep .el-button--primary.is-disabled {
  display: block;
}

.transfer ::v-deep .el-button+.el-button {
  margin-left: 0;
}

.transfer ::v-deep .el-transfer-panel__header .el-checkbox__input {
  display: none;
}

.transfer ::v-deep .el-transfer-panel {
  width: 278px;
}

.transfer ::v-deep .el-transfer-panel__body {
  width: 264px;
  // height: 214px;
}

.transfer ::v-deep .el-transfer-panel .el-transfer-panel__footer {
  top: 50px;
  bottom: 0;
  width: 0%;
  left: initial;
  right: 251px;
  display: inline-block;
}

.transfer-input {
  display: flex;
  width: 230px;

  .transfer-footer {
    margin-left: 5px;
  }
}

.transfer ::v-deep .el-transfer-panel:first-child .el-transfer-panel__filter {
  width: 75%;
  height: 30px;
}

.transfer ::v-deep .el-transfer-panel:first-child .el-transfer-panel__filter input {
  display: none;
}

.transfer ::v-deep .el-transfer-panel__filter .el-input__icon {
  display: none;
}

.transfer ::v-deep .el-transfer-panel .el-transfer-panel__header .el-checkbox .el-checkbox__label span {
  display: none;
}

.transfer ::v-deep .el-transfer-panel__list {
  overflow-y: scroll;
}

.descriptions {
  padding: 0 20px;
  margin-top: 24px;
  margin-bottom: 30px;

  .descriptions-item {
    width: 140px;
  }
}
</style>
