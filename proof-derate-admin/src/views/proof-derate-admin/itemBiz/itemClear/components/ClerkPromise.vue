<!--  办事人承诺  -->
<template>
  <div>
    <el-col :span="24">
      <el-form-item
        prop="mould"
        label="承诺书模板："
        :rules="[{required: true, validator: validateMould, trigger: ['blur', 'change']}]"
      >
        <el-upload
          ref="upload"
          action="action"
          :limit="1"
          :auto-upload="true"
          :disabled="operateType === 'detail'"
          :file-list="fileList"
          :multiple="false"
          :on-change="onChange"
          :on-exceed="onExceed"
          :http-request="uploadFile"
          :on-preview="onPreview"
          :on-remove="onRemove"
          :before-remove="beforeRemove"
          class="upload-inline"
        >
          <template v-if="fileList && fileList.length === 0">
            <el-button type="primary" size="mini">上传附件</el-button>
            <div slot="tip" class="el-upload__tip">
              上传的附件不能超过10MB,如文件过大，请先进行压缩后再上传；
              <br />
              禁止上传以下类型的附件：{{ blacklist.join(',') }}。
            </div>
          </template>
        </el-upload>
      </el-form-item>
    </el-col>
    <el-col :span="24">
      <el-form-item label="承诺书说明：">
        <el-input
          v-model="form.clerk_commitment.commit_book_description"
          type="textarea"
          :rows="3"
          :placeholder="operateType !== 'detail' ? '您可填写该承诺书模板可避免的风险' : ''"
          :readonly="operateType === 'detail'"
        />
      </el-form-item>
    </el-col>
  </div>
</template>

<script>
import { proofListCommitAttachmentt } from "@/api/itemBiz/clear";

export default {
  name: "ClerkPromise",
  components: {},
  props: {
    data: {
      type: Object,
      default: () => {
        return {};
      }
    },
    formRefs: {
      type: Object,
      default: () => {
        return {};
      }
    },
    operateType: {
      type: String,
      default: "add"
    }
  },
  data() {
    return {
      action:
        process.env.VUE_APP_PROOF_DERATE_API_URL +
        "proof_list/commit_attachment",
      form: {
        mould: null,
        clerk_commitment:{
          commit_book_description: "",
          commit_attachment_id: "",
          commit_attachment_name: ""
        }
      },
      validateMould: (rule, value, callback) => {
        if (_.isEmpty(this.form.mould)) {
          callback(new Error("请选择"));
        } else {
          callback();
        }
      },
      blacklist: [".exe", ".sh", ".bat", ".com", ".dll", "jsp"],
      fileList: []
    };
  },
  watch: {
    data: {
      handler(val) {
        console.log("上传承诺书",val,Object.keys(this.form))
        if (!_.isEmpty(val,Object.keys(this.form))) {
          Object.keys(this.form).forEach(key => {
            this.form[key] = val[key];
          });
          this.fileList = this.form.mould ? new Array(this.form.mould) : [];
        }
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    onRemove(file, fileList) {
      this.fileList = fileList;
      this.form.mould = fileList[0] ? fileList[0].raw : null;
    },
    beforeRemove(file, fileList) {
      return new Promise((resolve, reject) => {
        this.$confirm("确定移除该附件?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            resolve();
          })
          .catch(() => {
            reject();
          });
      });
    },
    onChange(file, fileList) {
      const fileSuffix = file.name.substring(file.name.lastIndexOf(".") + 1);
      const isBlacklist = this.blacklist.includes(
        fileSuffix.toLocaleLowerCase()
      );
      const isLt10m = file.size / (1024 * 1024) < 10;
      if (isBlacklist) {
        this.$message.warning(
          `不能上传类型为${this.blacklist.join(",")}的文件！`
        );
        fileList.pop();
      } else if (!isLt10m) {
        this.$message.warning(`文件不能超过10MB`);
        fileList.pop();
      }
      this.fileList = fileList;
      this.form.mould = fileList[0] ? fileList[0].raw : null;
      if (this.form.mould) {
        this.formRefs.validateField("mould");
      }
    },
    onExceed(files, fileList) {
      this.$message.warning("只能上传一个文件！");
    },
    uploadFile(data) {
      if (!data) {
        return;
      }
      let name = data.file.name;
      let file = data.file;
      let fd = new FormData();
      fd.append("name", name);
      fd.append("file", file);
      console.log(data, fd);
      proofListCommitAttachmentt(fd).then(res => {
        let type = res.meta.code === "200" ? "success" : "warning";
          this.$message({
            type: type,
            message: res.meta.msg
          });
          if (type === "success"){
            this.form.clerk_commitment.commit_attachment_id = res.data.sessionId;
            this.form.clerk_commitment.commit_attachment_name = name;
          }
     
      });
    },
    onPreview(file) {
      const data = file.raw || file;
      if (window.navigator.msSaveBlob) {
        // IE以及IE内核的浏览器
        window.navigator.msSaveBlob(data, data.name);
        return;
      }
      const url = window.URL.createObjectURL(new Blob([data]));
      const link = document.createElement("a");
      link.style.display = "none";
      link.href = url;
      link.setAttribute("download", data.name);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  }
};
</script>

<style lang="scss" scoped>
.upload-inline {
  display: flex;
  align-items: center;
  .el-upload-list {
    display: inline-block;
    margin-left: 10px;
    .el-upload-list__item:first-child {
      margin-top: 0;
    }
  }
  .el-upload__tip {
    color: red;
    line-height: 15px;
    margin: 0 5px 0 10px;
  }
}
</style>
