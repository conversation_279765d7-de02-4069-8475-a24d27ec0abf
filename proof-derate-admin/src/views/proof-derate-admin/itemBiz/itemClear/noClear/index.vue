<template>
  <div>
    <el-card class="box-card">
      <query-form ref="queryForm" @click="search" />
    </el-card>
    <br>
    <el-card class="box-card">
      <span style="color: #888">
        共
        <span class="text-red">{{ tableData.total }}</span> 条符合查询条件
      </span>
      <custom-table
        ref="table"
        :is-card-type="false"
        :table-data="tableData"
        :table-header="tableHeader"
        :show-input="false"
        :stripe="false"
        :table-tools="tableTools"
        style="margin-top: 10px"
        @query="query"
      >
        <template #operate="{ row }">
          <div v-show="hasDataPermission('ZMGL:YWBL:SXZMQL:QLZM', row.permission_code)">
            <el-button
              v-hasRoleOrPermission="'ZMGL:YWBL:SXZMQL:QLZM'"
              type="text"
              @click="proofClearEdit(row)"
            >清理证明</el-button>
          </div>
        </template>
      </custom-table>
    </el-card>
  </div>
</template>

<script>
import QueryForm from '@/views/proof-derate-admin/itemBiz/itemClear/components/QueryForm'
import CustomTable from '@/components/Element/Table'
import { getItemList } from '@/api/itemBiz/clear'
import Enum from '@/utils/enum'
import { hasDataPermission } from '@/utils/index'

export default {
  name: 'NoClear',
  components: {
    QueryForm,
    CustomTable
  },
  data() {
    return {
      tableData: {
        content: [{}],
        loading: false,
        total: 0,
        currentPage: 1,
        pageSize: 10,
        pageDirection: 'desc',
        itemClearStatus: 'DO_NOT_CLEAN'
      },
      tableTools: [],
      tableHeader: [
        {
          label: '事项名称【办理项】',
          prop: 'item_name',
          minWidth: '250px',
          formatter: (row, col, val) => {
            return row.handing_item === null || val === undefined ? row.item_name : row.item_name + '【' + row.handing_item + '】'
          }
        },
        { label: '实施机构', prop: 'impl_org_name', minWidth: '180px' },
        { label: '事项类型', prop: 'item_type', minWidth: '120px',
          formatter: (row, col, val) => {
            return val === null || val === undefined ? '' : Enum.mattersTypeList.find(i => i.value === val).label
          }
        },
        { label: '操作', slot: 'operate', width: '120px', fixed: 'right' }
      ]
    }
  },
  mounted: function() {
    this.query(1)
  },
  methods: {
    search(data) {
      this.query(1, 'search', data)
    },
    query(currentPage, type, data) {
      // if (_.isNumber(currentPage)) {
      //   this.tableData.currentPage = currentPage
      // }
      const sendData = Object.assign({}, type === 'search' ? data : this.$refs['queryForm'].form)
      sendData['item_type'] =
        sendData['item_type'].length > 0 ? sendData['item_type'].join() : ''
      sendData['page_size'] = this.tableData.pageSize
      sendData['page_number'] = this.tableData.currentPage
      sendData['page_direction'] = this.tableData.pageDirection
      sendData['item_clear_status'] = this.tableData.itemClearStatus
      sendData['item_type'] =
        sendData['item_type'] != ''
          ? sendData['item_type']
          : ''

      getItemList(sendData)
        .then(res => {
          this.tableData.content = res.data.content || []
          this.tableData.total = res.data.totalElements
        })
        .catch(() => {
          this.tableData.content = []
        })
    },
    hasDataPermission(value, permission_code) {
      return hasDataPermission(value, permission_code)
    },
    proofClearEdit(row) {
      this.$router.push({
        name: 'item_to_clear_edit',
        query: {
          id: row.item_code,
          item_clear_status: 'WAIT_FOR_CLEAN',
          from_page_type: 'noClear'
        }
      })
    }
  }
}
</script>

<style scoped>
</style>
