<template>
  <div class="content-wrapper">
    <section class="content-header">
      <span class="breadcrumb" align="right">
        <!-- <el-button
          v-if="!opearateDisplay"
          type="primary"
          plain
          @click="doNotClean"
          icon="el-icon-edit"
        >无需清理</el-button> -->
        <el-button type="warning" plain icon="el-icon-back" @click="backPrev">返回</el-button>
      </span>
      <br>
    </section>
    <section class="content">
      <el-row>
        <el-col :span="21">
          <h3>{{ title }}: {{ form.item_name }}</h3>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-tabs v-model="activeName" type="border-card" @tab-click="handleClick">
            <el-tab-pane label="详情" name="desc">
              <el-divider content-position="left">基本信息</el-divider>
              <el-row>
                <el-col :span="8" :offset="2">实施机构: {{ form.impl_org_name }}</el-col>
                <el-col :span="8" :offset="2">事项类型: {{ form.item_type }}</el-col>
              </el-row>
              <br>

              <!--证明材料清理-->
              <el-divider content-position="left">证明材料清理</el-divider>
              <el-row>
                <el-col :span="24">
                  <custom-table
                    ref="multipleTable"
                    :is-card-type="false"
                    :table-data="tableData"
                    :table-header="tableHeader"
                    :row-key="tableData.rowKey"
                    @select-all="selectAll"
                    @select="select"
                    @show="show"
                  >
                    <template #license_name="{ row }">
                      <div v-for="(item,index) in row.license_name" :key="index">
                        <el-button
                          type="text"
                          @click="goLicenseItemView(row.license_code[index])"
                        >{{ item }}</el-button>
                        <br>
                      </div>
                    </template>
                  </custom-table>
                </el-col>
              </el-row>
              <!--选中的证明材料-->
              <div v-for="item in tableData.content" :key="item.index">
                <el-tabs v-if="item.section && item.proof_list_id == null" type="border-card">
                  <el-tab-pane :label="item.material_name">
                    <edit-clear-info ref="clearInfo" :row="item.clearData" />
                  </el-tab-pane>
                </el-tabs>
                <br>
              </div>
            </el-tab-pane>
          </el-tabs>
        </el-col>
      </el-row>
      <div v-if="!opearateDisplay" class="fixed-bottom">
        <div class="btn-group">
          <el-button @click="backPrev">取消</el-button>
          <el-button :loading="examineApproveLoading" type="primary" @click="examineApprove">提交审批</el-button>
        </div>
      </div>
    </section>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import CustomTable from '@/components/Element/Table'
import { getItemView, doNotClean, proofListCreate } from '@/api/itemBiz/clear'
import { goLicenseItemView } from '@/api/itemBiz/list'
import {
  getItemTypeList,
  getProofClearType,
  getItemMaterialType,
  getReplaceCancelWay,
  getUnitTypeList,
  getProofStatusList
} from '@/api/common/dict'
import EditClearInfo from '@/views/proof-derate-admin/itemBiz/itemClear/components/EditClearInfo'

export default {
  name: 'ItemClearedAdd',
  components: {
    CustomTable,
    EditClearInfo
  },
  data() {
    return {
      title: '证明档案编辑',
      itemId: '',
      pageType: 'PUBLIC',
      form: {
        item_name: '',
        impl_org_name: '',
        item_type: ''
      },
      activeName: 'desc',
      tableData: {
        content: [], // 表格数据
        isShowSelection: true,
        disabledcCondition: [
          { field: 'proof_list_id', condition: 'neq', value: 'null' },
          { field: 'section', condition: 'eq', value: 'true' }
        ],
        rowKey: 'index',
        clearData: {
          material_id: '',
          proof_name: '',
          proof_clear_type: '',
          direct_description: '',
          replace_cancel_way: '',
          clerk_commitment: {
            commit_book_description: '',
            commit_attachment_id: '',
            // file_data: ["", "", ""],
            commit_attachment_name: ''
          },
          replace_data_shared: {
            industry_dept_name: '',
            industry_dept_code: '',
            proof_provide_type: null,
            data_shared_description: ''
          },
          replace_dept_survey: {
            dept_name: '',
            dept_code: '',
            dept_cancel_description: ''
          },
          replace_investigation: {
            industry_dept_name: '',
            industry_dept_code: '',
            proof_provide_type: null,
            investigation_description: ''
          },
          replace_license: {
            license_description: '',
            license_code: '',
            license_name: ''
          },
          replace_other: {
            other_clear_description: ''
          },
          user_info: {
            account_dept_code: '',
            account_dept_name: ''
          }
        }
      },
      // 表头配置
      tableHeader: [
        { label: '材料名称', prop: 'material_name', minWidth: '200px' }, // 配置slot属性，可支持使用插槽
        {
          label: '材料类型',
          prop: 'material_type',
          minWidth: '200px',
          formatter: (row, col, val) => {
            return val === null || val === undefined
              ? ''
              : this.itemMaterialType.find(i => i.value === val).label
          }
        },
        {
          label: '已关联电子证照',
          slot: 'license_name',
          prop: 'license_name',
          minWidth: '200px',
          formatter: (row, col, val) => {
            const licenseNameStr = ''
            console.log('licenseNameStr', val)
            // if(val === null || val === undefined){
            //   val.split(",").forEach(i=>{
            //     licenseNameStr += val+"<br />";
            //   })
            // }

            return val
          }
        },
        {
          label: '事项证明状态',
          prop: 'item_proof_status',
          minWidth: '200px',
          formatter: (row, col, val) => {
            return val === null || val === undefined
              ? ''
              : this.proofStatusList.find(i => i.value === val).label
          }
        },
        {
          label: '操作',
          prop: 'operateColumn', // prop为“operateColumn”时，可配置actions按钮列表
          minWidth: '50px',
          fixed: 'right',
          actions: [
            {
              type: 'text',
              label: '查看',
              action: 'show', // 按钮该按钮时，派发事件的名称
              isShowFunc: (row, index) => {
                return row.section && row.proof_list_id != null // 已梳理确认
              }
            }
          ]
        }
      ],
      examineApproveLoading: false,
      itemTypeList: [],
      proofClearType: [],
      itemMaterialType: [],
      replaceCancelWayList: [],
      unitTypeList: [],
      proofStatusList: [],
      opearateDisplay: false
    }
  },
  beforeRouteLeave(to, from, next) {
    if (to.name == 'item_clear') {
      to.meta.keepAlive = true
    } else {
      to.meta.keepAlive = false
    }
    next()
  },

  computed: {
    ...mapState({
      account: state =>
        state.user &&
				state.user.userdata &&
				state.user.userdata.userAccount &&
				state.user.userdata.userAccount.account,
      roles: state =>
        state.user && state.user.userdata && state.user.userdata.roles,
      name: state =>
        state.user &&
				state.user.userdata &&
				state.user.userdata.userAccount &&
				state.user.userdata.userAccount.name,
      dept: state =>
        state.user &&
				state.user.userdata &&
				state.user.userdata.userInfo &&
				state.user.userdata.userInfo.orgName
    })
  },
  created() {
    const item_code = this.$route.query['id']
    const item_clear_status = this.$route.query['item_clear_status']
    const is_new_proof_list = true
    this.itemId = item_code
    this.pageType = item_clear_status
    this.initData(item_code, { item_clear_status, is_new_proof_list })
  },
  methods: {
    initData: async function(item_code, param) {
      const itemTypeListRes = await getItemTypeList()
      const proofClearTypeRes = await getProofClearType()
      const itemMaterialTypeRes = await getItemMaterialType()
      const replaceCancelWayRes = await getReplaceCancelWay()
      const unitTypeListRes = await getUnitTypeList()
      const proofStatusListRes = await getProofStatusList()
      this.itemTypeList = itemTypeListRes.data || []
      this.proofClearType = proofClearTypeRes.data || []
      this.itemMaterialType = itemMaterialTypeRes.data || []
      this.replaceCancelWayList = replaceCancelWayRes.data || []
      this.unitTypeList = unitTypeListRes.data || []
      this.proofStatusList = proofStatusListRes.data || []
      // 被await阻塞的同步代码
      this.getItemView(item_code, param)
    },
    select(selection, row) {
      const section = this.tableData.content[row.index]['section']
      this.tableData.content[row.index]['section'] = !section
    },
    selectAll(selection) {
      this.tableData.content.forEach((i, index) => {
        this.tableData.content[index]['section'] =
          selection.length !== 0
      })
    },
    toggleSelection(rows) {
      const that = this
      if (rows) {
        that.$nextTick(function() {
          rows.map(row => {
            that.$refs['multipleTable'].defaultTickRow(row, true)
          })
        })
      } else {
        that.$refs['multipleTable'].clearSelection()
      }
    },
    getItemView(item_code, param) {
      getItemView(item_code, param)
        .then(res => {
          this.form = res.data.item_vo || {}
          if (this.form.item_type) {
            this.form.item_type = this.itemTypeList.find(
              i => i.value === this.form.item_type
            ).label
          }
          this.tableData.content = _.map(
            res.data.item_material_vo_list,
            (item, index) => {
              const licenseNameArr =
                item.license_name != null ? item.license_name.split(',') : []
              const licenseCodeArr =
                item.license_code != null ? item.license_code.split(',') : []
              return {
                index: index,
                item_proof_status: '',
                ...item,
                section: false,
                license_name: licenseNameArr,
                license_code: licenseCodeArr,
                clearData: {
                  ...this.tableData.clearData,
                  material_id: item.id,
                  proof_name: item.material_name
                }
              }
            }
          )
          this.opearateDisplay = this.tableData.content.every(i => {
            return i.section && i.proof_list_id != null
          })
          // console.log("opearateDisplay",opearateDisplay);
          this.toggleSelection(
            this.tableData.content.filter(i => i.section == true)
          ) // 数据回选
        })
        .catch(() => {})
    },
    handleClick(tab, event) {
      console.log(tab, event)
    },
    show(row) {
      this.$router.push({
        name: 'item_cleared_info_way',
        query: {
          id: row.proof_list_id
          // proofRoute: "item_cleared_info_way",
        }
      })
    },
    backList() {
      this.$router.push({ name: 'item_clear' })
    },
    goLicenseItemView(license_code) {
      goLicenseItemView(license_code).then(res => {
        window.open(res.data.url, '_blank')
      })
    },
    doNotClean() {
      this.$confirm('确定修改为无需清理?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        doNotClean({ item_id: this.form.id }).then(res => {
          const type = res.meta.code === '200' ? 'success' : 'warning'
          this.$message({
            type: type,
            message: res.meta.msg
          })
          if (type === 'success') this.backList()
        })
      })
    },
    backPrev() {
      this.$router.go(-1)
    },
    examineApprove() {
      const clearInfoList = this.$refs.clearInfo
      const formDataList = []
      _.forEach(clearInfoList, item => {
        item.validateForm()
        formDataList.push(item.getFormData())
      })
      const sendData = this.filterSubmitData(formDataList)
      // console.log("==formDataList==", formDataList);
      // console.log("==最后提交sendData==", sendData);
      if (sendData.length <= 0) {
        this.$message({
          type: 'warning',
          message: '请至少选择一条材料进行清理'
        })
      } else {
        const rowValidater = []
        _.forEach(clearInfoList, item => {
          item.$refs.form.validate(valid => {
            if (valid) {
              rowValidater.push(true)
            } else {
              rowValidater.push(false)
            }
          })
        })
        if (rowValidater.every(i => i === true)) {
          proofListCreate(this.itemId, sendData).then(res => {
            const type = res.meta.code === '200' ? 'success' : 'warning'
            this.$message({
              type: type,
              message: res.meta.msg
            })
            if (type === 'success') this.$router.go(-1)
          })
        }
      }
    },
    filterSubmitData(data) {
      const handleData = []
      if (data && data.length > 0) {
        data.forEach(i => {
          let item = {}
          const directlyCancleData = {
            direct_description: i.direct_description,
            material_id: i.material_id,
            proof_clear_type: i.proof_clear_type,
            proof_name: i.proof_name
          }
          const replaceCancelData = {}
          const replaceCancelWay = i.replace_cancel_way
          const replaceLicense = { replace_license: i.replace_license }
          const clerk_commitment = { clerk_commitment: i.clerk_commitment }
          const replaceDataShared = {
            replace_data_shared: i.replace_data_shared
          }
          const replaceInvestigation = {
            replace_investigation: i.replace_investigation
          }
          const replaceDeptSurvey = {
            replace_dept_survey: i.replace_dept_survey
          }
          const replaceOther = { replace_other: i.replace_other }
          const userInfo = { user_info: {
            account_name: this.name,
            account_dept_code: '',
            account_dept_name: this.dept
          }}
          if (i.proof_clear_type === 'DIRECTLY_CANCEL') {
            item = Object.assign(directlyCancleData, userInfo)
          } else if (
            i.proof_clear_type === 'REPLACE_CANCEL' &&
            replaceCancelWay.length > 0
          ) {
            item = Object.assign(
              { replace_cancel_way: replaceCancelWay },
              directlyCancleData,
              userInfo
            )
            if (
              replaceCancelWay.indexOf('TURN_LICENSE_OR_OTHER_LICENSE_WAY') !=
              -1
            ) {
              item = Object.assign(item, replaceLicense)
            }
            if (replaceCancelWay.indexOf('HANDLE_AFFAIRS_PROMISE') != -1) {
              item = Object.assign(item, clerk_commitment)
            }
            if (replaceCancelWay.indexOf('DATA_SHARING') != -1) {
              item = Object.assign(item, replaceDataShared)
            }
            if (replaceCancelWay.indexOf('ARTIFICIAL_INVESTIGATION') != -1) {
              item = Object.assign(item, replaceInvestigation)
            }
            if (replaceCancelWay.indexOf('DEPARTMENT_INVESTIGATION') != -1) {
              item = Object.assign(item, replaceDeptSurvey)
            }
            if (replaceCancelWay.indexOf('OTHER_WAY') != -1) {
              item = Object.assign(item, replaceOther)
            }
          }
          handleData.push(item)
        })
      }
      return handleData
    }
  }
}
</script>

<style lang="scss" scoped>
.fixed-bottom {
  width: 100%;
  transform: scale3d(1, 1, 1);
  .btn-group {
    background: rgba(255, 255, 255, 0.95);
    border-top: 1px solid #096dd9;
    padding: 20px 18px 10px 50px;
    // position: absolute;
    bottom: 0;
    right: 0;
    z-index: 100;
    text-align: right;
    height: 80px;
    width: inherit;
  }
}
</style>
