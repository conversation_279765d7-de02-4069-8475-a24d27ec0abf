<template>
  <div>
    <el-card class="box-card" shadow="never" :body-style="{'padding-bottom':'0px'}">
      <query-form ref="queryForm" @click="search" />
    </el-card>
    <!-- <br /> -->
    <el-card class="box-card dashed-line" shadow="never">
      <div style="color: #888; padding:20px 10px 0" class="dashed-line">
        共
        <span class="text-red">{{ tableData.total }}</span> 条符合查询条件
      </div>
      <custom-table
        ref="table"
        :is-card-type="false"
        :table-data="tableData"
        :table-header="tableHeader"
        :show-input="false"
        :stripe="false"
        :table-tools="tableTools"
        style="margin-top: 10px"
        @query="query"
      >
        <template #operate="{ row }">
          <div>
            <el-button type="text" @click="goItemProofFile(row)">查看</el-button>
          </div>
        </template>
      </custom-table>
    </el-card>
  </div>
</template>

<script>
import QueryForm from '@/views/proof-derate-admin/itemBiz/itemClear/components/QueryForm'
import CustomTable from '@/components/Element/Table'
import { getWaitForCleanPage, getWaitForCleanPageCount } from '@/api/itemBiz/clear'
import Enum from '@/utils/enum'
import { hasDataPermission } from '@/utils/index'
import { getProofStatusList } from '@/api/common/dict'
export default {
  name: 'Cleared',
  components: {
    QueryForm,
    CustomTable
  },
  data() {
    return {
      proofStatusList: [], // 事项证明状态
      tableData: {
        border: false,
        content: [{}],
        loading: false,
        total: 0,
        currentPage: 1,
        isShowIndex: true,
        pageSize: 10,
        pageDirection: 'desc',
        itemClearStatus: 'CARDING_UNCONFIRMED,CARDING_CONFIRMED,APPROVED'
        // itemClearStatus: "CLEAN"
      },
      tableTools: [],
      tableHeader: [
        {
          label: '事项名称',
          prop: 'item_name',
          minWidth: '250px',
          align: 'left'
          // formatter: (row, col, val) => {
          //   return row.handing_item === null || val === undefined
          //     ? row.item_name
          //     : row.item_name + "【" + row.handing_item + "】";
          // }
        },
        { label: '事项编码', prop: 'item_code', align: 'left', minWidth: '180px' },
        { label: '办理项名称', prop: 'handing_item', align: 'left', minWidth: '180px' },
        { label: '实施机构', prop: 'impl_org_name', align: 'left', minWidth: '180px' },
        // {
        //   label: '事项类型',
        //   prop: 'item_type',
        //   minWidth: '120px',
        //   formatter: (row, col, val) => {
        //     return val === null || val === undefined ? '' : Enum.mattersTypeList.find(i => i.value === val).label
        //   }
        // },
        {
          label: '事项证明状态',
          prop: 'item_clear_status',
          minWidth: '120px',
          align: 'left',
          formatter: (row, col, val) => {
            return val === null || val === undefined ? '' : this.proofStatusList.find(i => i.value === val).label
          }
        },
        { label: '操作', slot: 'operate', width: '120px', align: 'left', fixed: 'right' }
      ]
    }
  },
  mounted: function () {
    this.initData()
  },
  methods: {
    initData: async function () {
      const proofStatusListRes = await getProofStatusList()
      this.proofStatusList = proofStatusListRes.data || []
      this.query(1)
    },
    search(data) {
      this.query(1, 'search', data)
    },
    query(currentPage, type, data) {
      // if (_.isNumber(currentPage)) {
      //   this.tableData.currentPage = currentPage
      // }
      this.tableData.loading = true
      const sendData = Object.assign({}, type === 'search' ? data : this.$refs['queryForm'].form)
      sendData['item_type'] = sendData['item_type'].length > 0 ? sendData['item_type'].join() : ''
      sendData['page_size'] = this.tableData.pageSize
      sendData['page_number'] = this.tableData.currentPage
      sendData['page_direction'] = this.tableData.pageDirection
      sendData['item_clear_status'] = this.tableData.itemClearStatus
      sendData['item_source'] = sendData['item_source'].join(',')
      sendData['item_type'] = sendData['item_type'] != '' ? sendData['item_type'] : ''
      // sendData['item_status'] = 'WORK'
      getWaitForCleanPage(sendData)
        .then(res => {
          this.tableData.content = res.data.content || []
          this.tableData.loading = false
          this.tableData.total = Number(res.data.total_elements)
        })
        .catch(() => {
          this.tableData.content = []
          this.tableData.loading = false
        })
      // getWaitForCleanPageCount(sendData)
      //   .then(res => {
      //     // this.tableData.loading = false
      //     this.tableData.total = Number(res.data.total_elements)
      //   })
      //   .catch(() => {
      //     // this.tableData.loading = false
      //   })
    },
    hasDataPermission(value, permission_code) {
      return hasDataPermission(value, permission_code)
    },
    // 事项证明档案
    goItemProofFile(row) {
      this.$router.push({
        name: 'item_cleared_info_way',
        query: {
          id: row.item_code,
          item_clear_status: 'DO_NOT_CLEAN',
          type: 'show'
        }
      })
    },
    proofClearAdd(row) {
      this.$router.push({
        name: 'item_cleared_add',
        query: {
          id: row.item_code,
          item_clear_status: 'CLEAN'
        }
      })
    }
  }
}
</script>

<style scoped>
.box-card {
  border: 0;
}
</style>
