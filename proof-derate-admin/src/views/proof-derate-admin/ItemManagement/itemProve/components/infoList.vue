<template>
  <div>
    <el-row>
      <el-col :span="24">
        <el-tabs v-model="dataConfig.activeName" tab-position="top" @tab-click="handleClick">
          <el-tab-pane label="详情" name="desc">
            <detail-list />
          </el-tab-pane>
          <el-tab-pane label="变更日志" name="change">
            <el-row v-for="(item,index) in proofRecordList" :key="index">
              <el-row>&nbsp;</el-row>
              <el-col :span="24">
                <el-card>
                  <el-row>
                    <el-col :span="8">操作人:{{item.account_name}}</el-col>
                    <el-col :span="9" :offset="2">修改账号：{{item.account}}</el-col>
                  </el-row>
                  <br />
                  <el-row>
                    <el-col :span="8">操作时间:{{item.last_modification_time}}</el-col>
                    <el-col :span="9" :offset="2">操作名称：{{item.operation_name}}</el-col>
                  </el-row>
                  <br />
                  <el-row>
                    <el-col :span="8">修改对比:</el-col>
                  </el-row>
                  <br />
                  <el-row class="amend">
                    <el-col :span="12">修改前</el-col>
                    <el-col :span="12">修改后</el-col>
                  </el-row>
                  <el-row class="amendInfo" v-for="(itemChange,indexChange) in item.change_prefix_and_post_list" :key="indexChange">
                    <el-col :span="12">{{itemChange.change_prefix}}</el-col>
                    <el-col :span="12">{{itemChange.change_post}}</el-col>
                  </el-row>
                  <!--<el-row class="amendInfo">
                    <el-col :span="12">所属行业部门:{{item.change_prefix}}</el-col>
                    <el-col :span="12">所属行业部门:{{item.change_post}}</el-col>
                  </el-row>-->
                </el-card>
              </el-col>
              <br />
            </el-row>
          </el-tab-pane>
        </el-tabs>
      </el-col>
    </el-row>
  </div>
</template>
<script>
import Enum from '@/utils/enum'
import CustomTable from '@/components/Element/Table'
import detailList from './detail'
import { getProveInfoById, getProveChangeLog } from '@/api/ItemManagement/itemProve'
import divisionSelector from '@/components/DivisionSelector'
import { getStandardTypeList, getIMaterialSource, getItemMaterialStandardType } from '@/api/common/dict'

export default {
  name: 'infoList',
  components: {
    detailList
  },
  data() {
    return {
      dataConfig: {
        id: '',
        title: '证明档案',
        activeName: 'desc'
      },
      //关联证明
      form: {
        item_name: '',
        item_code: '',
        item_type: '',
        item_source: '',
        division_code: '',
        impl_org_name: '',
        credit_code: '',
        item_material_list: []
      },
      tableDataChange: {
        content: [], // 表格数据
        loading: false, // 控制表格加载效果
        total: 0, // 分页查询时的总数，去掉该属性，不显示分页条
        currentPage: 1, // 当前页码
        pageSize: 10,
        pageDirection: 'desc',
        isShowIndex: true
      },
      tableHeaderChange: [
        {
          label: '事项名称',
          prop: 'item_name',
          minWidth: '200px'
        },
        { label: '实施机构', prop: 'impl_org_name', minWidth: '160px' },
        {
          label: '事项标准类型',
          prop: 'item_source',
          minWidth: '160px',
          formatter: (row, col, val) => {
            return val === null || val === undefined ? '' : Enum.standardTypeList.find(i => i.value === val).label
          }
        },
        {
          label: '证明材料名称',
          prop: 'material_name',
          minWidth: '180px'
        },
        {
          label: '证明材料标准类型',
          prop: 'item_material_source',
          minWidth: '160px',

          formatter: (row, col, val) => {
            return val === null || val === undefined ? '' : Enum.materialTypeList.find(i => i.value === val).label
          }
        },
        {
          label: '操作',
          slot: 'operate',
          prop: 'operate',
          minWidth: '50px',
          fixed: 'right'
        }
      ],
      organizationList: [], //实施机构
      materialTypeList: Enum.materialTypeList, //材料标准类型
      standardTypeList: Enum.standardTypeList, //事项标准类型
      item_source: [], // 事项标准类型
      item_material_source: [], //材料标准类型
      //变更日志
      changeId: '',
      proofRecordList: []
    }
  },
  watch: {
    'form.division_code': {
      handler(val) {
        if (val) {
          this.form.credit_code = ''
        } else {
          this.organizationList = []
        }
      },
      immediate: true
    },
    materialTypeList: {
      handler(val) {
        if (val.length > 0) {
          const itemType = []
          Enum.materialTypeList.forEach(item => {
            itemType.push(item.value)
          })
          this.form['item_material_source'] = itemType
        }
      }
    },
    standardTypeList: {
      handler(val) {
        if (val.length > 0) {
          const itemType = []
          Enum.standardTypeList.forEach(item => {
            itemType.push(item.value)
          })
          this.form['item_source'] = itemType
        }
      }
    }
  },
  mounted() {
    this.changeId = this.$route.query.id
    this.form.proof_catalog_id = this.$route.query.id
    this.initData()
  },
  methods: {
    //变更日志初始化
    getProveChangeLog() {
      getProveChangeLog(this.changeId)
        .then(res => {
          this.proofRecordList = res.data
        })
        .catch(() => {})
    },
    initData: async function () {
      let standardTypeListRes = await getStandardTypeList()
      this.getStandardTypeList(standardTypeListRes)
      let getIMaterialSourceRes = await getItemMaterialStandardType()
      this.getIMaterialSource(getIMaterialSourceRes) //材料类型
      this.query(1, 'init')
    },
    handleClick(tab, event) {
      if (tab.name === 'change') {
        this.getProveChangeLog()
      }
    },

    // 证明目录详情
    lookChange(row) {
      console.log(row)

      this.$router.push({
        name: 'item_audit_info_way',
        query: {
          id: row.proof_list_id,
          item_clear_status: 'DO_NOT_CLEAN',
          type: 'show'
        }
      })
    },
    //事项标准类型
    getStandardTypeList(res) {
      const data = res.data || []
      Enum.standardTypeList.splice(0)
      Enum.standardTypeList.push(...data)
      const item_source = [] // 事项标准类型
      _.forEach(Enum.standardTypeList, item => {
        item_source.push(item.value)
      })
      this.item_source = item_source
    },
    //材料类型
    getIMaterialSource(res) {
      const data = res.data || []
      Enum.materialTypeList.splice(0)
      Enum.materialTypeList.push(...data)
      const item_material_source = []
      _.forEach(Enum.materialTypeList, item => {
        item_material_source.push(item.value)
      })
      this.item_material_source = item_material_source
    },
    search(data) {
      this.query(1, 'search', data)
    },
    query(currentPage, type, data) {
      if (_.isNumber(currentPage)) {
        this.tableDataChange.currentPage = currentPage
      }
      const formData = Object.assign({}, this.form)
      const item_source = type === 'init' ? this.item_source.join() : formData['item_source'].join()
      const item_material_source = type === 'init' ? this.item_material_source.join() : formData['item_material_source'].join()
      let sendData = {
        ...formData,
        page_size: this.tableDataChange.pageSize,
        page_number: this.tableDataChange.currentPage,
        page_direction: this.tableDataChange.pageDirection,
        item_source: item_source,
        item_material_source: item_material_source
      }
      /*getCatalogRelationPage(sendData)
          .then(res => {
            const data = res.data === null ? [] : res.data.content;
            this.tableDataChange.content = data;
            this.tableDataChange.total = res.data.totalElements;
          })
          .catch(() => {});*/
    }
  }
}
</script>
<style scoped>
.amendInfo {
  text-align: center;
  padding: 10px 0;
}

.amend {
  background: #f4f4f4;
  text-align: center;
  padding: 10px 0;
}
</style>
