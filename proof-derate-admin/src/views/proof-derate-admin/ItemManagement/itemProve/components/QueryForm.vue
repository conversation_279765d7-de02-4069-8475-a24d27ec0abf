<template>
  <div>
    <el-form ref="form" :model="form" label-width="120px">
      <el-row>
        <el-col :xs="24" :sm="12" :md="12" :lg="9">
          <el-form-item label="事项名称">
            <el-input v-model="form.item_name" clearable placeholder="请输入事项名称" />
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12" :md="12" :lg="9">
          <el-form-item label="事项编码">
            <el-input v-model="form.item_code" clearable placeholder="请输入事项编码" />
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12" :md="12" :lg="6">
          <el-form-item>
            <el-button type="primary" @click="search">查询</el-button>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :xs="24" :sm="12" :md="12" :lg="9">
          <el-form-item label="实施区划">
            <!-- <division-selector v-model="form.division_code" @change="divisionChange"></division-selector> -->
            <AdministrativeDivisionCascader
              :key="divisionCode"
              ref="AdministrativeDivisionSelect"
              :division-code="divisionCode"
              :permission-code="'catalog:biz:credential:list'"
              @setDivisionCodeAndName="setDivisionCodeAndName"
            />
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12" :md="12" :lg="9">
          <el-form-item label="实施机构">
            <el-select v-model="form.credit_code" filterable placeholder="请选择" style="width:100%" clearable>
              <el-option v-for="item in organizationList" :key="item.credit_code" :label="item.name" :value="item.credit_code" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <!-- <el-col :xs="24" :sm="12" :md="12" :lg="18"> -->
          <el-form-item label="事项类型">
            <el-checkbox-group v-model="form.item_type">
              <el-checkbox v-for="item in mattersTypeList" :key="item.value" :label="item.value">{{ item.label }}</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :xs="24" :sm="12" :md="12" :lg="9">
          <el-form-item label="事项标准类型">
            <el-checkbox-group v-model="form.item_source">
              <el-checkbox v-for="item in standardTypeList" :key="item.value" :label="item.value">{{ item.label }}</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <!-- <el-col :xs="24" :sm="12" :md="12" :lg="9"> -->
          <el-form-item label="事项使用状态">
            <el-radio-group v-model="form.item_status">
              <el-radio v-for="item in serviceStatusList" :key="item.value" :label="item.value">{{ item.label }}</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <!-- <el-col :xs="24" :sm="12" :md="12" :lg="9"> -->
        <!-- <el-form-item label="材料标准类型">
            <el-checkbox-group v-model="form.item_material_source">
              <el-checkbox
                v-for="item in materialTypeList"
                :key="item.value"
                :label="item.value"
              >{{ item.label }}</el-checkbox>
            </el-checkbox-group>
        </el-form-item>-->
        <!-- </el-col> -->
      </el-row>
      <el-row />
    </el-form>
  </div>
</template>
<script>
import Enum from '@/utils/enum'
import { getOrgListNoAuth } from '@/api/admin/org.js'
import { getOrganizationList } from '@/api/commonPack/platManege'
export default {
  name: 'QueryForm',
  components: { AdministrativeDivisionCascader: () => import('@/components/AdministrativeDivisionCascader') },
  data() {
    return {
      form: {
        item_name: '',
        // material_name: "",
        division_code: '',
        credit_code: '',
        item_type: [], // 事项类型
        item_code: '', // 事项编码
        // item_material_source: [],//材料标准类型
        item_source: [], // 事项标准类型
        // item_status: "true"
        item_status: 'WORK'
      },
      materialTypeList: Enum.materialTypeList, // 材料标准类型
      standardTypeList: Enum.standardTypeList, // 事项标准类型
      mattersTypeList: Enum.mattersTypeList, // 事项类型
      organizationList: [],
      serviceStatusList: [
        // { value: "", label: "全部" },
        // { value: "true", label: "在用" },
        // { value: "false", label: "取消" }
        { value: '', label: '全部' },
        { value: 'WORK', label: '在用' },
        // { value: "SUSPEND", label: "暂停" },
        { value: 'CANCEL', label: '取消' }
      ],
      divisionCode: '' // 行政区划编码
    }
  },
  computed: {},
  watch: {
    'form.division_code': {
      handler(val) {
        if (val) {
          this.form.credit_code = ''
        } else {
          this.organizationList = []
        }
      },
      immediate: true
    },
    mattersTypeList: {
      handler(val) {
        if (val.length > 0) {
          const itemType = []
          Enum.mattersTypeList.forEach(item => {
            itemType.push(item.value)
          })
          this.form['item_type'] = itemType
        }
      }
    },
    materialTypeList: {
      handler(val) {
        console.log(val)
        if (val.length > 0) {
          const itemType = []
          Enum.materialTypeList.forEach(item => {
            itemType.push(item.value)
          })
          // this.form["item_material_source"] = itemType;
          // console.log(this.form.item_material_source,'----88888');
        }
      }
    },
    standardTypeList: {
      handler(val) {
        if (val.length > 0) {
          const itemType = []
          Enum.standardTypeList.forEach(item => {
            itemType.push(item.value)
          })
          this.form['item_source'] = itemType
        }
      }
    }
  },
  created() {},
  methods: {
    divisionChange(d) {
      this.form.division_code = d.code || ''
      // this.params.divisionCode = this.params.divisionCode.replace(/(0+)$/g, ""); //末尾去0
      this.organizationList = []
      this.form.credit_code = ''
      if (d.code) {
        getOrgListNoAuth({
          pageSize: 1000,
          pageNumber: 0,
          divisionCode: d.code
        }).then(res => {
          this.organizationList = res.content.map(i => {
            return { label: i.name, value: i.tyshxydm }
          })
        })
      }
    },
    setDivisionCodeAndName(data) {
      this.form.division_code = data.code || ''
      // this.params.divisionCode = this.params.divisionCode.replace(/(0+)$/g, ""); //末尾去0
      this.organizationList = []
      this.form.credit_code = ''
      this.getOrganizationList(this.form.division_code)
    },
    // 获取实施机构
    getOrganizationList(id) {
      const data = {
        division_code: id,
        permission_code: 'catalog:biz:credential:list',
        scope: true
      }
      getOrganizationList(data).then(res => {
        if (res.meta.code === '200') {
          this.organizationList = res.data
        }
      })
    },
    search() {
      this.$emit('click', this.form)
    }
  }
}
</script>

<style scoped>
.el-select {
  width: 100%;
}
</style>
