<template>
  <div class="content-wrapper detail_add_edit padding-10">
    <!-- <section class="content-header title-algin-midle">
      <span />
      <span>
        <el-button plain type="primary" icon="el-icon-edit" :disabled="isSubmitDisabled" @click.native="saveData">保存</el-button>
        <el-button type="warning" plain icon="el-icon-back" @click="goToList">返回</el-button>
      </span>
    </section>-->
    <CardTitle :title-name="form.item_name?form.item_name:'新增事项'" :ifback="true" @back="goToList()">
      <template>
        <el-button plain type="primary" icon="el-icon-edit" :disabled="isSubmitDisabled" @click.native="saveData">保存</el-button>
        <!-- <el-button type="warning" plain icon="el-icon-back" @click="goToList">返回</el-button> -->
      </template>
    </CardTitle>
    <!-- <section class="content" style="margin-top: 10px"> -->
    <el-card class="box-card">
      <!-- <el-divider content-position="left">基础信息</el-divider> -->
      <span class="margin-left-10 info-wrap">
        <img :src="arrow" alt />
        <span class="info-title">基础信息</span>
      </span>
      <el-form ref="form" :model="form" label-width="140px">
        <el-row>
          <el-col :xs="24" :sm="12" :md="12" :lg="12">
            <el-form-item ref="item_name" label="事项名称" prop="item_name" :rules="[{ required: true, message: '请填写事项名称', trigger: 'blur' }]">
              <el-input v-model="form.item_name" clearable placeholder="事项名称" />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="12">
            <el-form-item ref="item_code" label="事项编码" prop="item_code" :rules="[{ required: true, message: '请填写事项编码', trigger: 'blur' }]">
              <el-input v-model="form.item_code" clearable :disabled="$route.query.flagCatalog === 'edit'" placeholder="事项编码" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :xs="24" :sm="12" :md="12" :lg="12">
            <el-form-item
              ref="division_code"
              label="实施区划"
              prop="division_code"
              :rules="[{ required: true, message: '请选择实施区划', trigger: 'blur' }]"
            >
              <!-- <division-selector ref="divisionRef" v-model="form.division_code" @change="divisionChange" /> -->
              <AdministrativeDivisionCascader
                :key="divisionCode"
                ref="AdministrativeDivisionSelect"
                :division-code="divisionCode"
                :permission-code="'catalog:biz:credential:list'"
                @setDivisionCodeAndName="setDivisionCodeAndName"
              />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="12">
            <el-form-item
              ref="credit_code"
              label="实施机构"
              prop="credit_code"
              :rules="[{ required: true, message: '请选择实施机构', trigger: 'blur' }]"
            >
              <el-select filterable v-model="form.credit_code" placeholder="请选择" @change="orgSelChange">
                <el-option v-for="item in organizationList" :key="item.credit_code" :label="item.name" :value="item.credit_code" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :xs="24" :sm="12" :md="12" :lg="12">
            <el-form-item ref="item_type" label="事项类型" prop="item_type" :rules="[{ required: true, message: '请选择事项类型', trigger: 'blur' }]">
              <el-select v-model="form.item_type" placeholder="请选择">
                <el-option
                  v-for="(matterType, index) in mattersTypeList"
                  :key="index + matterType.value"
                  :value="matterType.value"
                  :label="matterType.label"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="12">
            <el-form-item
              ref="item_source"
              label="事项标准类型"
              prop="item_source"
              :rules="[{ required: true, message: '请选择事项标准类型', trigger: 'blur' }]"
            >
              <el-select v-model="form.item_source" placeholder="请选择">
                <el-option
                  v-for="(standardType,index) in standardTypeList"
                  :key="index + standardType.value"
                  :value="standardType.value"
                  :label="standardType.label"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :xs="24" :sm="12" :md="12" :lg="12">
            <el-form-item
              ref="item_status"
              label="事项状态"
              prop="item_status"
              :rules="[{ required: true, message: '请选择事项状态', trigger: 'blur' }]"
            >
              <el-select v-model="form.item_status" placeholder="请选择" :disabled="itemStatusDisabled">
                <el-option v-for="(item, index) in itemStatusList" :key="index + item.value" :value="item.value" :label="item.label" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="12">
            <el-form-item
              ref="project_type"
              label="办件类型"
              prop="project_type"
              :rules="[{ required: true, message: '请选择办件类型', trigger: 'blur' }]"
            >
              <el-select v-model="form.project_type" placeholder="请选择">
                <el-option v-for="(item,index) in projectTypeList" :key="index + item.value" :value="item.value" :label="item.label" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :xs="24" :sm="12" :md="12" :lg="12">
            <el-form-item label="办理条件">
              <el-input v-model="form.accept_condition" clearable placeholder="请输入办理条件" />
            </el-form-item>
          </el-col>
        </el-row>
        <br />
        <div class="el-tabs">
          <!-- <el-divider content-position="left">证明材料</el-divider> -->
          <span class="margin-left-10 info-wrap">
            <img :src="arrow" alt />
            <span class="info-title">证明材料</span>
          </span>
          <div class="addIcon" @click="addRow">
            <img src="@/assets/proof-derate-admin-images/u746.png" alt />
          </div>
        </div>
        <el-row>
          <el-col :span="24">
            <el-table ref="item_material_list" :data="form.item_material_list" class="gt-el-table" :border="true">
              <el-table-column label="材料排序" align="center">
                <template slot="header" slot-scope="scope">
                  <span class="starName">材料排序</span>
                  <span class="star">*</span>
                </template>
                <template slot-scope="scope">
                  <el-form-item
                    :ref="'item_material_list.' + scope.$index + '.order_num'"
                    label-width="auto"
                    :prop="'item_material_list.' + scope.$index + '.order_num'"
                  >
                    <el-input v-model="scope.row.order_num" :disabled="true" />
                  </el-form-item>
                </template>
              </el-table-column>

              <el-table-column label="证明材料名称" align="center">
                <template slot="header" slot-scope="scope">
                  <span class="starName">证明材料名称</span>
                  <span class="star">*</span>
                </template>
                <template slot-scope="scope">
                  <el-form-item
                    :ref="'item_material_list.' + scope.$index + '.material_name'"
                    label-width="auto"
                    :prop="'item_material_list.' + scope.$index + '.material_name'"
                    :rules="[{ required: true, message: '证明材料名称不能为空', trigger: 'blur' }]"
                  >
                    <el-input v-model="scope.row.material_name" maxlength="100" :show-word-limit="false" />
                  </el-form-item>
                </template>
              </el-table-column>

              <el-table-column prop="material_type" label="证明材料类型" align="center">
                <template slot="header" slot-scope="scope">
                  <span class="starName">证明材料类型</span>
                  <span class="star">*</span>
                </template>
                <template slot-scope="scope">
                  <el-form-item
                    :ref="'item_material_list.' + scope.$index + '.material_type'"
                    label-width="auto"
                    :prop="'item_material_list.' + scope.$index + '.material_type'"
                    :rules="[{ required: true, message: '证明材料类型不能为空', trigger: 'blur' }]"
                  >
                    <el-select v-model="scope.row.material_type" clearable placeholder="请选择">
                      <el-option
                        v-for="(proveMaterialType,index) in proveMaterialTypeList"
                        :key="index + proveMaterialType.value"
                        :value="proveMaterialType.value"
                        :label="proveMaterialType.label"
                      />
                    </el-select>
                  </el-form-item>
                </template>
              </el-table-column>

              <el-table-column prop="standardType" label="证明材料标准类型" align="center">
                <template slot="header" slot-scope="scope">
                  <span class="starName">证明材料标准类型</span>
                  <span class="star">*</span>
                </template>
                <template slot-scope="scope">
                  <el-form-item
                    :ref="'item_material_list.' + scope.$index + '.item_material_source'"
                    label-width="auto"
                    :prop="'item_material_list.' + scope.$index + '.item_material_source'"
                    :rules="[{ required: true, message: '证明材料标准类型不能为空', trigger: 'blur' }]"
                  >
                    <el-select v-model="scope.row.item_material_source" clearable placeholder="请选择">
                      <el-option
                        v-for="(standardType,index) in materialTypeList"
                        :key="index + standardType.value"
                        :value="standardType.value"
                        :label="standardType.label"
                      />
                    </el-select>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column prop="license_name" label="已关联证照目录" align="center" min-width="50" :show-overflow-tooltip="true">
                <template slot-scope="scope">
                  <div v-if="scope.row.license_name" @click="selectLicenseCatalogue(scope.$index,'license')">{{ scope.row.license_name }}</div>
                  <div v-else>
                    <el-button size="small" type="text" @click="selectLicenseCatalogue(scope.$index,'license')">选择目录</el-button>
                  </div>
                </template>
              </el-table-column>

              <el-table-column prop="uploadExample" label="样例" align="center" min-width="50" :show-overflow-tooltip="true">
                <template slot-scope="scope">
                  <div v-if="scope.row.sample_file_name">
                    <!-- <el-button size="small" type="text" @click="uploading(scope.$index, 'sample_file_name', 'sample_file_id')">
                        {{scope.row.sample_file_name}}
                    </el-button>-->
                    <el-button
                      v-if="typeof(scope.row.sample_file)=='object'"
                      size="small"
                      type="text"
                      @click="downFile(scope.$index, 'sample_file', 'sample_file_id')"
                    >
                      示例样表
                      <i class="el-icon-download" />
                    </el-button>
                    <el-button
                      v-if="typeof(scope.row.sample_file)=='undefined'"
                      size="small"
                      type="text"
                      @click="downloadFile('sample', scope.row)"
                    >
                      示例样表
                      <i class="el-icon-download" />
                    </el-button>
                    <!--                      <el-button class="my-el-icon-error" icon="el-icon-error" style="border: none;padding: -1px 20px"></el-button>-->
                  </div>
                  <div v-else>
                    <el-button size="small" type="text" @click="uploading(scope.$index,'sample_file_name', 'sample_file_id')">上传</el-button>
                  </div>
                </template>
              </el-table-column>

              <el-table-column prop="emptyFile" label="空白表格" align="center" min-width="50" :show-overflow-tooltip="true">
                <template slot-scope="scope">
                  <div v-if="scope.row.blank_file_name">
                    <!-- <el-button size="small" type="text" @click="uploading(scope.$index, 'blank_file_name', 'blank_file_id')">
                        {{scope.row.blank_file_name}}
                    </el-button>-->

                    <el-button
                      v-if="typeof(scope.row.blank_file)=='object'"
                      size="small"
                      type="text"
                      @click="downFile(scope.$index, 'blank_file', 'blank_file_id')"
                    >
                      空白表格
                      <i class="el-icon-download" />
                    </el-button>
                    <el-button
                      v-if="typeof(scope.row.blank_file)=='undefined'"
                      size="small"
                      type="text"
                      @click="downloadFile('blank_file', scope.row)"
                    >
                      空白表格
                      <i class="el-icon-download" />
                    </el-button>

                    <!-- <el-button size="small" type="text" @click="downFile(scope.$index, 'blank_file', 'blank_file_id')">
                        空白表格<i class="el-icon-download"></i>
                    </el-button>-->
                  </div>
                  <div v-else>
                    <el-button size="small" type="text" @click="uploading(scope.$index,'blank_file_name', 'blank_file_id')">上传</el-button>
                  </div>
                </template>
              </el-table-column>

              <el-table-column label="操作" align="center" width="120">
                <template slot-scope="scope">
                  <el-button type="text" class="del-btn" @click="handleDelete(scope.$index, scope.row)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
    <!-- </section> -->

    <!--上传弹框-->
    <el-dialog title="上传文件" :visible.sync="uploadingDialog" width="50%" center>
      <uploadDialog
        ref="uploadDialog"
        :tips_message="tips_message"
        :template="false"
        :upload-title="'请选择您要上传文件'"
        :limit-file-type="limitFileType"
      />
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="importFile(uploadingRowFieldIndex, uploadingRowFieldName, uploadingRowFieldKey)">导入</el-button>
        <el-button @click="uploadingDialog = false">取 消</el-button>
      </span>
    </el-dialog>
    <!-- <licenseCatalogueDialog
      :license-catalogue-dialog-show="licenseCatalogueDialog"
      @closeDialog="closeDialog"
      @getSlectItem="getSlectItem"
    />-->
    <licenseDialog
      v-if="licenseDialogVisible"
      :license-dialog-visible="licenseDialogVisible"
      :selected-value="chosedContent"
      @closelicenseDialog="closelicenseDialog"
      @getlicenseData="getlicenseData"
    />
  </div>
</template>
<script>
import { prove_attachment, itemProveCreate, getProveInfoById, itemProveEdit } from '@/api/ItemManagement/itemProve'
import Enum from '@/utils/enum'
import divisionSelector from '@/components/DivisionSelector'
import { getOrgListNoAuth } from '@/api/admin/org.js'
import uploadDialog from '@/views/proof-derate-admin/components/uploadDialog'
import { getStandardTypeList, getMattersTypeList, getIMaterialSource, getItemMaterialStandardType, getItemMaterialType } from '@/api/common/dict'
import { goLicenseItemView } from '@/api/itemBiz/list'
import { Loading } from 'element-ui'
import { downloadSampleFile, downloadBlankFile } from '@/api/common/download'
import { dataURLtoDownload } from '@/utils/index'
import CustomTable from '@/components/Element/Table'
// import licenseCatalogueDialog from './licenseCatalogueDialog.vue'
import licenseDialog from '@/views/proof-derate-admin/certificationManagement/certificationList/components/licenseDialog'
import { getOrganizationList } from '@/api/commonPack/platManege'
import CardTitle from '@/components/CardTitle'
export default {
  name: 'InfoList',
  components: {
    licenseDialog,
    uploadDialog,
    divisionSelector,
    CustomTable,
    // licenseCatalogueDialog,
    CardTitle,
    AdministrativeDivisionCascader: () => import('@/components/AdministrativeDivisionCascader')
  },
  filters: {
    ellipsis(value) {
      if (!value) return ''
      if (value.length > 30) {
        return value.slice(0, 30) + '...'
      }
      return value
    }
  },
  data() {
    return {
      // 提交按钮禁用标记
      isSubmitDisabled: false,
      loadingInstance: null,
      templateFlag: false,
      tips_message: ' 温馨提示：请选择以后缀名为.doc/docx/pdf文件且上传文件大小不得超过1M！',
      limitFileType: '.doc,.docx,.pdf',
      uploadTips: false,
      itemStatusDisabled: false,
      dataConfig: {
        id: '',
        title: '证明档案',
        activeName: 'desc'
      },
      uploadData: {
        file_name: '',
        file_key: 'file',
        business_id: ''
      },
      curRowIndex: null,
      licenseCatalogueDialog: false,
      licenseDialogVisible: false,
      licenseCatalogueform: {
        proof_catalog_name: '',
        page_direction: 'DESC',
        page_number: 1,
        page_size: 10
      },
      form: {
        item_name: '',
        item_code: '',
        item_type: '',
        item_status: '', // 事项状态
        project_type: '', // 办件类型
        item_source: '',
        division_code: '',
        impl_org_name: '',
        credit_code: '',
        accept_condition: '', // 办理条件
        item_material_list: []
      },
      // 校验
      formRule: {
        material_name: [{ required: true, message: '证明材料名称', trigger: 'blur' }]
      },
      organizationList: [],
      // 证明材料
      /* tableDataMaterial: {
           content: [], // 表格数据
           loading: false, // 控制表格加载效果
           total: 0, // 分页查询时的总数，去掉该属性，不显示分页条
           currentPage: 1, // 当前页码
           pageSize: 10,
           pageDirection: "desc"
         },*/
      standardTypeList: Enum.standardTypeList,
      mattersTypeList: Enum.mattersTypeList,
      // 证明标准类型
      materialTypeList: Enum.materialTypeList,
      // 证明材料类型
      proveMaterialTypeList: Enum.proveMaterialTypeList,
      // 事项状态
      itemStatusList: [
        {
          label: '在用',
          value: 'WORK'
        },
        // {
        //   label: '暂停',
        //   value: 'SUSPEND'
        // },
        {
          label: '取消',
          value: 'CANCEL'
        }
      ],
      // 办件类型
      projectTypeList: [
        {
          label: '未知办件类型',
          value: 'UNKNOWN_ITEM'
        },
        {
          label: '即办件',
          value: 'IMMEDIATE_ITEM'
        },
        {
          label: '承诺件',
          value: 'COMMITMENT_ITEM'
        }
      ],
      file: '',
      uploadingDialog: false,
      uploadingRowFieldName: '',
      uploadingRowFieldKey: '',
      uploadingRowFieldIndex: null,
      licenseCatalogueIndex: 0,
      licenseCatalogueName: '',
      itemTypeList: [
        {
          value: 'WORK',
          label: '在用'
        },
        {
          value: 'SUSPEND',
          label: '暂停'
        },
        {
          value: 'CANCEL',
          label: '取消'
        }
      ],
      handlingtypeList: [
        {
          value: '1',
          label: '即办件'
        },
        {
          value: '2',
          label: '承诺件'
        }
      ],
      arrow: require('@/assets/proof-derate-admin-images/arrow.png'),
      divisionCode: '',
      chosedContent: []
      // organizationList: []
    }
  },
  watch: {
    'form.division_code': {
      handler(val) {
        if (val) {
          // this.dataSharingForm.investigation_dept_code = "";
          this.divisionCode = val
          this.getOrganizationList(this.form.division_code)
        } else {
          // this.organizationList = [];
        }
      },
      immediate: true
    }
  },
  async created() {
    this.form = {}
    this.form.item_material_list = []
    await this.initData()
  },
  mounted() {},
  methods: {
    async getInfoById(findId) {
      await getProveInfoById(findId)
        .then(res => {
          this.form = res.data
          if (this.form.item_material_list) {
            // 删除无用FILE属性
            const nonFileDataList = this.form.item_material_list.map(value => {
              // 1. 删除file属性:
              // const { blank_file, simple_file, ...noFileSource } = value
              value.license_name = value.license_list.map(i => {
                return i.license_name
              })
              value.license_name = value.license_name.join(',')
              const { ...noFileSource } = value
              return noFileSource
            })
            this.form.item_material_list = nonFileDataList
          }
          if (this.form.division_code) {
            this.$refs['divisionRef'].loadCode = this.form.division_code
          }

          const data = { code: this.form.division_code }
          // getOrgListNoAuth({
          //   pageSize: 1000,
          //   pageNumber: 0,
          //   divisionCode: data.code
          // }).then(res => {
          //   this.organizationList = res.content.map(i => {
          //     return { label: i.name, value: i.tyshxydm }
          //   })
          // })
        })
        .catch(() => {})
    },
    // 新增初始化
    initData: async function () {
      // 事项标准类型
      const standardTypeListRes = await getStandardTypeList()
      // 事项类型
      const mattersTypeListRes = await getMattersTypeList()
      // 证明标准类型
      const getIMaterialSourceRes = await getItemMaterialStandardType()
      // 证明材料类型
      const itemMaterialType = await getItemMaterialType()
      this.getStandardTypeList(standardTypeListRes)
      this.getMattersTypeList(mattersTypeListRes)
      this.getIMaterialSource(getIMaterialSourceRes)
      this.itemMaterialType(itemMaterialType)

      if (this.$route.query.flagCatalog === 'edit') {
        if (this.$route.query.id) {
          await this.getInfoById(this.$route.query.id)
        }
      } else {
        this.form.item_status = 'WORK'
        this.itemStatusDisabled = true
      }
    },
    goToList() {
      if (this.$route.query.flagCatalog === 'add' || this.$route.query.flagCatalog === '') {
        this.$router.push({
          name: 'ItemManagement_itemProve'
        })
      } else {
        this.$router.go(-1)
      }
    },
    // 事项标准类型
    getStandardTypeList(res) {
      const data = res.data || []
      Enum.standardTypeList.splice(0)
      Enum.standardTypeList.push(...data)
    },
    // 证明标准类型
    getIMaterialSource(res) {
      const data = res.data || []
      Enum.materialTypeList.splice(0)
      Enum.materialTypeList.push(...data)
    },
    // 证明材料类型
    itemMaterialType(res) {
      const data = res.data || []
      Enum.proveMaterialTypeList.splice(0)
      Enum.proveMaterialTypeList.push(...data)
    },
    // 事项类型
    getMattersTypeList(res) {
      const data = res.data || []
      Enum.mattersTypeList.splice(0)
      Enum.mattersTypeList.push(...data)
    },
    // 添加行按钮
    addRow() {
      console.log(this.form.item_material_list)
      const tempData = {
        material_name: '',
        item_code: '',
        item_material_source: '',
        material_type: '',
        sample_file_name: '',
        blank_file_name: '',
        // sample_file: '', // 当前上传样例文件
        // blank_file: '', // 当前上传空白文件
        license_code: '', // 已关联证照目录编码
        license_name: '', // 已关联证照目录名称
        order_num: this.form.item_material_list.length + 1
      }

      this.form.item_material_list.push(tempData)
    },
    // 导入
    uploadProcess(file, fileList, row, filedName, fieldKey) {
      // 上传前检查
      if (!this.beforeUpload(file)) {
        return
      }
      row[filedName] = null
      row[fieldKey] = null
      const name = file.name
      const fileData = file.raw
      const fd = new FormData()
      fd.append('file', fileData)
      fd.append('name', name)
      prove_attachment(fd)
        .then(res => {
          const type = res.meta.code === '200' ? 'success' : 'error'
          const message = res.meta.code === '200' ? '上传成功' : '上传失败'
          if (type === 'success' && res.data) {
            row[filedName] = name
            row[fieldKey] = res.data.sessionId
          }
          this.$message({
            message: message,
            type: type
          })
        })
        .catch(() => {
          this.$message({
            message: '上传失败',
            type: 'error'
          })
        })
    },
    /** 删除按钮操作 */
    handleDelete(index, row) {
      this.form.item_material_list.splice(index, 1)
    },
    handleSucess(response, file, fileList) {
      this.uploadData.file_name = file.file_name
    },
    handleError(err, file, fileList) {},
    // 上传前检查
    beforeUpload: function (file) {
      const _this = this
      const fileType = file.name.substring(file.name.lastIndexOf('.') + 1)
      const isDoc = fileType === 'doc'
      const isDocx = fileType === 'docx'
      const isPdf = fileType === 'pdf'
      const isTypeAllow = isDoc || isDocx || isPdf // 限制图片格式为jpg / png
      const isLt2M = file.size / 1024 / 1024 < 2 // 限制图片大小
      if (!isTypeAllow) {
        this.$message.error('上传文件只能是 doc、docx 或 pdf 格式!')
      }
      if (!isLt2M) {
        var t = setTimeout(function () {
          _this.$message.error('上传文件大小不能超过 2MB!')
        }, 500)
      }
      return isTypeAllow && isLt2M
    },
    divisionChange(d) {
      this.form.division_code = d.code || ''
      this.organizationList = []
      this.form.credit_code = ''
      if (d.code) {
        getOrgListNoAuth({
          pageSize: 1000,
          pageNumber: 0,
          divisionCode: d.code
        }).then(res => {
          if (res && res.content) {
            this.organizationList = res.content.map(i => {
              return { label: i.name, value: i.tyshxydm }
            })
          }
        })
      }
    },
    orgSelChange(val) {
      if (val) {
        const orgData = this.organizationList.find(i => i.credit_code === val)
        this.form.impl_org_name = orgData.name
      }
      this.$forceUpdate()
    },
    uploading(rowIndex, uploadFieldName, uploadFieldKey) {
      this.uploadingDialog = true
      this.uploadingRowFieldIndex = rowIndex
      this.uploadingRowFieldName = uploadFieldName
      this.uploadingRowFieldKey = uploadFieldKey
    },
    // 导入
    importFile(index, fieldName, fieldKey) {
      this.form.item_material_list[index][fieldName] = null
      this.form.item_material_list[index][fieldKey] = null
      if (this.$refs.uploadDialog.input) {
        const file = this.$refs.uploadDialog.file.raw
        // 上传前检查
        if (!this.beforeUpload(file)) {
          return
        }
        const name = this.$refs.uploadDialog.input
        this.file = this.$refs.uploadDialog.file
        const fd = new FormData()
        fd.append('file', file)
        fd.append('name', name)

        prove_attachment(fd)
          .then(res => {
            const type = res.meta.code === '200' ? 'success' : 'error'
            const message = res.meta.code === '200' ? '上传成功' : '上传失败'
            if (type === 'success' && res.data) {
              this.form.item_material_list[index][fieldName] = name
              this.form.item_material_list[index][fieldKey] = res.data.sessionId
              // 上传成功后 获取当前上传的文件
              if (fieldName === 'sample_file_name') {
                // this.form.item_material_list[index]['sample_file'] = this.$refs.uploadDialog.file.raw
              } else if (fieldName === 'blank_file_name') {
                // this.form.item_material_list[index]['blank_file'] = this.$refs.uploadDialog.file.raw
                // console.log(this.form.item_material_list[index]['blank_file'])
              }

              // console.log('this.form.item_material_list',this.form.item_material_list)
            }
            this.$message({
              message: message,
              type: type
            })

            this.uploadingDialog = false
          })
          .catch(() => {
            this.$message({
              message: '上传失败',
              type: 'error'
            })
          })
      } else {
        this.$message({
          message: '请选择文件，再导入！',
          type: 'warning'
        })
      }
    },
    // 保存
    saveData() {
      /*	this.loadingInstance = Loading.service({
            // 动画中的文字
            text: '处理中',
            // 要加载动画的容器
            target: '.app-container'
          });*/
      this.isSubmitDisabled = true

      try {
        if (!this.form.item_material_list || this.form.item_material_list.length <= 0) {
          this.$message.error('需填写至少一条证明材料')
          return
        }
        this.$refs['form'].validate((valid, object) => {
          // console.log(object)
          if (valid) {
            const formData = Object.assign({}, this.form)

            const sendData = {
              // 基本信息
              ...formData
            }

            /* this.$router.push({
                name: "ItemManagement_itemProve"
              });*/
            // return;
            if (this.$route.query.flagCatalog === 'edit') {
              itemProveEdit(this.$route.query.id, sendData)
                .then(res => {
                  if (res.data != null) {
                    const success = res.meta.code === '200'
                    const noneDeptPermission = res.meta.internal_code && res.meta.internal_code.code === '2010203007'

                    if (success) {
                      this.$message({
                        message: '保存成功',
                        type: 'success'
                      })
                      this.$router.push({
                        name: 'itemManagement_itemProve_info',
                        query: {
                          id: this.$route.query.id
                        }
                      })
                    } else if (noneDeptPermission) {
                      this.$message.error('当前账号仅能修改本部门的事项')
                    }
                  } else {
                    this.$message.error(res.meta.message)
                  }
                })
                .catch(() => {})
            } else {
              // 新增
              itemProveCreate(sendData)
                .then(res => {
                  const success = res.meta.code === '200'
                  const noneDeptPermission = res.meta.internal_code && res.meta.internal_code.code === '2010203007'
                  if (success) {
                    this.$message({
                      message: '保存成功',
                      type: 'success'
                    })
                    this.$router.push({
                      name: 'itemManagement_itemProve_info',
                      query: {
                        id: res.data
                      }
                    })
                  } else if (noneDeptPermission) {
                    this.$message.error('当前账号仅能新建本部门的事项')
                  } else {
                    this.$message.error(res.meta.message)
                  }
                })
                .catch(() => {})
            }
          } else {
            const str = []
            for (const key in object) {
              object[key].map(item => {
                str.push(item.message)
              })
              let dom = this.$refs[Object.keys(object)[0]]
              if (Object.prototype.toString.call(dom) !== '[object Object]') {
                dom = dom[0]
                break // 结束语句并跳出语句，进行下个语句执行
              }
              // console.log('dom',dom)
              // 定位代码
              dom.$el.scrollIntoView({
                block: 'center',
                behavior: 'smooth'
              })
            }
            // return false
          }
        })
      } catch (e) {
      } finally {
        this.isSubmitDisabled = false
        // this.loadingInstance.close();
      }
    },
    // 下载当前上传的文件
    downFile(index, fileName, id) {
      const raw = this.form.item_material_list[index][fileName]
      const blob = new Blob([raw])
      const downLink = document.createElement('a')
      downLink.download = raw.name
      downLink.href = URL.createObjectURL(blob)
      // 触发点击
      document.body.appendChild(downLink)
      downLink.click()
      // 然后移除
      document.body.removeChild(downLink)
    },
    downloadFile(type, row) {
      if (type === 'sample') {
        downloadSampleFile({ attachment_id: row.sample_file_id }).then(res => {
          if (res.meta.code === '200') {
            dataURLtoDownload(res.data, row.sample_file_name)
          }
        })
      } else {
        downloadBlankFile({ attachment_id: row.blank_file_id }).then(res => {
          if (res.meta.code === '200') {
            dataURLtoDownload(res.data, row.blank_file_name)
          }
        })
      }
    },
    selectLicenseCatalogue(index, name) {
      // this.licenseCatalogueDialog = true
      this.licenseDialogVisible = true
      this.licenseCatalogueIndex = index
      this.licenseCatalogueName = name
      this.chosedContent = []
      if (this.form.item_material_list[this.licenseCatalogueIndex]['license_list']) {
        this.chosedContent = this.form.item_material_list[this.licenseCatalogueIndex]['license_list']
      }
    },
    closeDialog() {
      this.licenseCatalogueDialog = false
    },
    closelicenseDialog() {
      this.licenseDialogVisible = false
    },
    getSlectItem(data) {
      this.form.item_material_list[this.licenseCatalogueIndex]['license_code'] = data[0].code
      this.form.item_material_list[this.licenseCatalogueIndex]['license_name'] = data[0].name
      this.licenseCatalogueDialog = false
    },
    setDivisionCodeAndName(data) {
      this.form.division_code = data.code || ''
      // this.params.divisionCode = this.params.divisionCode.replace(/(0+)$/g, ""); //末尾去0
      this.organizationList = []
      this.form.credit_code = ''
      this.getOrganizationList(this.form.division_code)
    },
    // 获取实施机构
    getOrganizationList(id) {
      const data = {
        division_code: id,
        permission_code: 'catalog:biz:credential:list',
        scope: true
      }
      getOrganizationList(data).then(res => {
        if (res.meta.code === '200') {
          this.organizationList = res.data
        }
      })
    },
    getlicenseData(data) {
      console.log('getlicenseData', data)

      this.licenseDialogVisible = false
      if (data.length !== 0) {
        this.form.item_material_list[this.licenseCatalogueIndex]['license_list'] = []
        let license_name_str = []
        data.forEach(e => {
          let item = {
            license_name: e.license_name,
            license_code: e.license_code
          }
          this.form.item_material_list[this.licenseCatalogueIndex]['license_list'].push(item)
          license_name_str.push(e.license_name)
        })
        this.form.item_material_list[this.licenseCatalogueIndex]['license_name'] = license_name_str.join(',')
      }
    }
  }
}
</script>
<style scoped lang="scss">
.detail_add_edit {
  .el-tabs {
    position: relative;
  }

  .addIcon {
    position: absolute;
    right: 0;
    bottom: 5px;
  }

  .el-select {
    display: block;
  }
}
</style>
<style>
.detail_add_edit .el-transfer-panel {
  width: 300px;
}
</style>
<style>
.content .el-upload__input {
  display: none !important;
}

.my-el-icon-error:before {
  font-size: 2px;
  visibility: hidden;
}
.star {
  color: #f56c6c;
  font-size: 14px;
  margin-left: 4px;
}
.starName {
  font-size: 14px;
  font-weight: bold;
}
.del-btn {
  color: red;
}
.margin-left-10 {
  margin-left: 10px;
}
.info-title {
  font-size: 20px;
  color: #333333;
}
.info-wrap {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.info-wrap img {
  width: 35px;
  height: 35px;
  margin-right: 10px;
}
</style>
