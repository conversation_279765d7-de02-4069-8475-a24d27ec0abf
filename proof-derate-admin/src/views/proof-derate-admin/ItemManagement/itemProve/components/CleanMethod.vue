<template>
  <div>
    <el-row>
      <el-col :span="21">
        <h3 style="z-index:9999">{{dataConfig.title}}: {{ form.material_name }}</h3>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24">
        <el-tabs v-model="dataConfig.activeName" type="border-card" @tab-click="handleClick">
          <el-tab-pane label="详情" name="desc">
            <el-divider content-position="left">基本信息</el-divider>
            <el-row>
              <el-col :span="8" :offset="2">所属事项: {{ form.item_name }}</el-col>
              <el-col :span="8" :offset="2">实施机构: {{ form.impl_org_name }}</el-col>
            </el-row>
            <br />

            <el-row>
              <el-col :span="8" :offset="2">事项类型: {{ form.item_type }}</el-col>
              <el-col :span="8" :offset="2">实施区划: {{ form.division_code }}</el-col>
            </el-row>
            <br />

            <el-divider content-position="left">证明材料清理</el-divider>
            <el-row>
              <el-col :span="8" :offset="2">材料提供依据: {{ material.law_name==null?"无":"" }}</el-col>

              <el-col :span="8" :offset="2">所属证明目录: {{ proof_catalog_vo.name ? proof_catalog_vo.name : "无"}}</el-col>
            </el-row>
            <br />
            <el-row>
              <el-col
                :span="20"
                :offset="2"
              >证明清理类型: {{ item_detail.material.proof_clear_type_text }}</el-col>
            </el-row>
            <br />
              <template v-if="item_detail.material.proof_clear_type === 'DO_NOT_CLEAN'">
                <el-row>
                  <el-col
                    :span="20"
                    :offset="2"
                  >无需清理说明: {{ item_detail.material.not_clean_description }}</el-col>
                </el-row>
                <br />
              </template>
              <template v-else-if="item_detail.material.proof_clear_type === 'DIRECTLY_CANCEL'">
                <el-row>
                  <el-col
                    :span="20"
                    :offset="2"
                  >直接取消说明: {{ item_detail.material.direct_description }}</el-col>
                </el-row>
                <br />
              </template>
            <template v-else>
              <el-row>
                <el-col :span="20" :offset="2">替代取消方式: {{ material.replace_cancel_way_text }}</el-col>
              </el-row>
              <br />
              <template
                v-if="hasWay(material.replace_cancel_way,'TURN_LICENSE_OR_OTHER_LICENSE_WAY')"
              >
                <el-divider>{{ replaceCancelWayList[0].label }}</el-divider>
                <el-row>
                  <el-col :span="24">
                    <custom-table
                      ref="table"
                      :is-card-type="false"
                      :table-data="tableData"
                      :table-header="tableHeader"
                      @show="show"
                    />
                  </el-col>
                  <br />
                  <el-col :span="20" :offset="2">转化证照说明: {{ material.license_description }}</el-col>
                </el-row>
              </template>
              <br />
              <template v-if="hasWay(material.replace_cancel_way,'HANDLE_AFFAIRS_PROMISE')">
                <el-divider>{{ replaceCancelWayList[1].label }}</el-divider>
                <el-row>
                  <el-col :span="20" :offset="2">
                    承诺书:
                    <a
                      href="javascript::void(0)"
                      @click="getAttachmentBase64"
                    >{{ material.commit_attachment_name }}</a>
                  </el-col>
                </el-row>
                <br />
                <el-row>
                  <el-col :span="20" :offset="2">承诺书说明: {{ material.commit_book_description }}</el-col>
                </el-row>
              </template>
              <br />
              <template v-if="hasWay(material.replace_cancel_way,'DATA_SHARING')">
                <el-divider>{{ replaceCancelWayList[2].label }}</el-divider>
                <!-- <el-row>
                  <el-col
                    :span="20"
                    :offset="2"
                  >证明开具单位类型: {{ material.proof_provide_type_dataShared }}</el-col>
                </el-row>
                <br /> -->
                <el-row>
                  <el-col :span="20" :offset="2">数据共享说明: {{ material.data_shared_description }}</el-col>
                </el-row>
              </template>
              <br />
              <template v-if="hasWay(material.replace_cancel_way,'ARTIFICIAL_INVESTIGATION')">
                <el-divider>{{ replaceCancelWayList[3].label }}</el-divider>
                <!-- <el-row>
                  <el-col
                    :span="20"
                    :offset="2"
                  >证明开具单位类型: {{ material.proof_provide_type_investigation }}</el-col>
                </el-row>
                <br /> -->
                <el-row>
                  <el-col :span="20" :offset="2">人工协查说明: {{ material.investigation_description }}</el-col>
                </el-row>
              </template>
              <br />
              <template v-if="hasWay(material.replace_cancel_way,'DEPARTMENT_INVESTIGATION')">
                <el-divider>{{ replaceCancelWayList[4].label }}</el-divider>
                <el-row>
                  <el-col :span="20" :offset="2">部门自行调查说明: {{ material.dept_cancel_description }}</el-col>
                </el-row>
              </template>
              <br />
              <template v-if="hasWay(material.replace_cancel_way,'OTHER_WAY')">
                <el-divider>{{ replaceCancelWayList[5].label }}</el-divider>
                <el-row>
                  <el-col :span="20" :offset="2">其他说明: {{ material.other_clear_description }}</el-col>
                </el-row>
              </template>

              <template v-if="type === 'confirm'">
                <br /><br /><br />
                <el-form
                  ref="formRelevance"
                  :model="formRelevance"
                  label-width="100px"
                  class="formRelevance"
                >
                  <el-row :span="24">
                    <el-col :span="10" :offset="2">
                      <el-form-item label="关联证明目录">
                        <el-input v-model="formRelevance.relevance" placeholder="请输入内容"></el-input>
                      </el-form-item>
                    </el-col>
                    <div class="select" @click="selectEvent">
                      <a>选择</a>
                    </div>
                  </el-row>
                </el-form>
              </template>
            </template>
          </el-tab-pane>
          <el-tab-pane label="过程信息" name="info" v-if="type != 'firstDraft'">
            <!--提交记录-->
            <el-row v-if="proofRecordList[0] != undefined">
              <el-col :span="2">
                <el-row>&nbsp;</el-row>
                <el-row>
                  <el-col>
                    <div style="text-align:center;">
                      {{proofRecordList[0]['date']}}
                      <br />
                      <span class="time">{{proofRecordList[0]['minute']}}</span>
                    </div>
                  </el-col>
                </el-row>
                <el-row>&nbsp;</el-row>
              </el-col>
              <el-col :span="22">
                <el-card>
                  <el-row>
                    <el-col :span="3">
                      <span class="status">提交申请</span>
                    </el-col>
                    <el-col :span="8">经办人: {{proofRecordList[0]['account_name']}}</el-col>
                    <el-col :span="9" :offset="2">实施机构：{{proofRecordList[0]['account_dept_name']}}</el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="8" :offset="3">提交时间: {{proofRecordList[0]['time']}}</el-col>
                    <el-col
                      :span="9"
                      :offset="2"
                      v-if="material.item_proof_status != 'CARDING_UNCONFIRMED'"
                    >
                      <el-button
                        type="text"
                        @click="preliminaryDrafts(proofRecordList[0]['proof_list_id'])"
                      >查看清理初稿</el-button>
                    </el-col>
                  </el-row>
                </el-card>
              </el-col>
            </el-row>
            <br />
            <!--梳理记录-->
            <el-row v-if="proofRecordList[1] != undefined">
              <el-col :span="2">
                <el-row>&nbsp;</el-row>
                <el-row>
                  <el-col>
                    <div style="text-align:center;">
                      {{proofRecordList[1]['date']}}
                      <br />
                      <span class="time">{{proofRecordList[1]['minute']}}</span>
                    </div>
                  </el-col>
                </el-row>
                <el-row>&nbsp;</el-row>
              </el-col>
              <el-col :span="22">
                <el-card>
                  <el-row>
                    <el-col :span="3">
                      <span class="status">梳理确认</span>
                    </el-col>
                    <el-col :span="8">经办人: {{proofRecordList[1]['tease_account_name']}}</el-col>
                    <el-col :span="9" :offset="2">确认状态: 已确认梳理</el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="8" :offset="3">确认时间: {{proofRecordList[1]['time']}}</el-col>
                  </el-row>
                </el-card>
              </el-col>
            </el-row>
            <br />
            <!--审核记录-->
            <el-row v-if="proofRecordList[2] != undefined">
              <el-col :span="2">
                <el-row>&nbsp;</el-row>
                <el-row>
                  <el-col>
                    <div style="text-align:center;">
                      {{proofRecordList[2]['date']}}
                      <br />
                      <span class="time">{{proofRecordList[2]['minute']}}</span>
                    </div>
                  </el-col>
                </el-row>
                <el-row>&nbsp;</el-row>
              </el-col>
              <el-col :span="22">
                <el-card>
                  <el-row>
                    <el-col :span="3">
                      <span class="status">审核</span>
                    </el-col>
                    <el-col :span="8">经办人: {{proofRecordList[2]['audit_operator']}}</el-col>
                    <el-col :span="9" :offset="2">实施机构：{{proofRecordList[2]['account_dept_name']}}</el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="8" :offset="3">审核时间: {{proofRecordList[2]['time']}}</el-col>
                    <el-col :span="9" :offset="2">审核状态: {{proofRecordList[2]['operator_type']}}</el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="8" :offset="3">意见内容: {{proofRecordList[2]['audit_description']}}</el-col>
                  </el-row>
                </el-card>
              </el-col>
            </el-row>
          </el-tab-pane>
        </el-tabs>
      </el-col>
    </el-row>
    <!--梳理确认-->
    <div class="fixed-bottom" v-if="type === 'confirm'">
      <div class="btn-group">
        <!-- 只能状态为待梳理确认可以修改 -->
        <!-- <el-button
          v-if="material.item_proof_status === 'CARDING_UNCONFIRMED'"
          type="warning"
          @click="goEditClearWay"
        >修改</el-button> -->
        <el-button :loading="examineApproveLoading" type="primary" @click="examineApprove">完成</el-button>
      </div>
    </div>
    <!--清单修改-->
    <div class="fixed-bottom" v-if="type === 'list'">
      <div class="btn-group">
        <!-- 只能状态为待梳理确认可以修改 -->
        <el-button
          v-if="material.item_proof_status === 'CARDING_UNCONFIRMED'"
          type="warning"
          @click="goEditClearWay"
        >修改</el-button>
      </div>
    </div>
    <br />
    <!--审核确认-->
    <template v-if="type ==='audit'">
      <el-row>
        <el-col :span="24">
          <el-tabs type="border-card" class="mt-10px">
            <el-tab-pane label="审核意见">
              <!-- <el-divider content-position="center">审核意见</el-divider> -->
              <el-row>
                <el-form
                  id="auditFormId"
                  ref="auditForm"
                  :model="auditForm"
                  :rules="auditRules"
                  label-width="100px"
                >
                  <el-row>
                    <el-col>
                      <el-form-item label="审核结果" prop="audit">
                        <el-radio-group v-model="auditForm.audit" >
                          <el-radio
                            v-for="item in auditList"
                            :key="item.value"
                            :label="item.value"
                            :disabled="auditForm.auditDisabled"
                          >{{ item.label }}</el-radio>
                        </el-radio-group>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col>
                      <el-form-item label="意见内容">
                        <el-input
                          type="textarea"
                          v-model="auditForm.remark"
                          :disabled="auditForm.auditDisabled"
                          clearable
                          placeholder="请输入意见内容"
                        />
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <!-- <el-row>
                    <el-col :span="24">
                      <div class="fixed-bottom">
                        <div class="btn-group">
                          <el-form-item>
                            <el-button @click="backPrev">取消</el-button>
                            <el-button type="primary" @click="submitAuditForm">完成</el-button>
                          </el-form-item>
                        </div>
                      </div>
                    </el-col>
                  </el-row>-->
                </el-form>
              </el-row>
            </el-tab-pane>
          </el-tabs>
        </el-col>
      </el-row>
      <div class="fixed-bottom">
        <div class="btn-group">
          <el-button @click="backPrev">取消</el-button>
          <el-button :loading="examineApproveLoading" v-if="!auditForm.auditDisabled" type="primary" @click="submitAuditForm">完成</el-button>
        </div>
      </div>
    </template>
    <!-- 关联证明目录弹框 -->
    <el-dialog title="关联目录" width="60%" :visible.sync="selectDialog">
      <el-form
        ref="selectForm"
        :model="selectForm"
        label-width="120px"
        @submit.native.prevent
        class="selectForm"
      >
        <el-row>
          <el-col :xs="24" :sm="12" :md="12" :lg="24">
            <el-form-item
              label="证明目录名称"
              prop="proof_catalog_name"
              style="width:80%;margin-right: 10px"
              :rules="[{ required: false, message: '请填写证明目录名称', trigger: 'blur' }]"
            >
              <el-input v-model="selectForm.proof_catalog_name" clearable placeholder="请填写证明目录名称" />
              <div class="catalogBtn">
                <el-button type="primary" :loading="selectLoading" @click="selectQuery">查询</el-button>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <custom-table
              ref="tableDataSelect"
              :is-card-type="false"
              :table-data="tableDataSelect"
              :table-header="tableHeaderSelect"
              @query="query"
              @refresh="query(1)"
            >
              <template #select="{row,$index}">
                <el-radio
                  :label="$index"
                  v-model="radio"
                  @change.native="selectionChange(row,$index)"
                >{{""}}</el-radio>
              </template>
              <template #operate="{row,$index}">
                <el-button type="text" @click="selectLook(row,$index)">查看</el-button>
              </template>
            </custom-table>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="selectFirm()">确 定</el-button>
        <el-button @click="selectDialog = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapState } from "vuex";
import CustomTable from "@/components/Element/Table";
import {
  getProofStatusList,
  getItemTypeList,
  getReplaceCancelWay,
  getUnitTypeList
} from "@/api/common/dict";
import { codeItemList } from "@/api/admin/org";
import {
  getProofListFormView,
  goLicenseItemView,
  proofListConfirmCreate,
  getProofSubmitInfo,
  getProofConfirmInfo,
  getProofAuditInfo,
  getProofListFirstDrafts,
  getCatalogReplace
} from "@/api/itemBiz/list";
import {
  getAttachmentBase64,
  getPreliminaryDraftsAttachment
} from "@/api/common/download";
import { proofAuditListExamine } from "@/api/itemBiz/audit";
import { dataURLtoDownload } from "@/utils/index";
import { getGetproofCatalogPage } from "@/api/certificationManagement/certificationList";
import moment from "moment";

export default {
  name: "CleanMethod",
  components: {
    CustomTable
  },
  props: {
    data: {
      type: Object,
      default: function() {
        return {};
      }
    },
    type: {
      type: String,
      default: "show"
    },
    proofRoute: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      dataConfig: {
        id: "",
        title: "证明档案",
        activeName: "desc"
      },
      auditForm: {
        //是否禁用
        auditDisabled : false,
        audit: "",
        remark: ""
      },
      auditRules: {
        audit: [
          { required: true, message: "请选择审核结果", trigger: "change" }
        ]
      },
      form: {
        material_name: "",
        item_name: "",
        impl_org_name: "",
        item_type: "",
        item_type_text: ""
      },
      material: {
        law_id: "",
        law_name: null,
        proof_clear_type: "",
        replace_cancel_way: ""
      },
      proof_catalog_vo: {
        name: ""
      },
      tableData: {
        content: [] // 表格数据
      },
      // 表头配置
      tableHeader: [
        { label: "证照名称", prop: "license_name", minWidth: "200px" }, // 配置slot属性，可支持使用插槽
        {
          label: "操作",
          prop: "operateColumn", // prop为“operateColumn”时，可配置actions按钮列表
          minWidth: "50px",
          fixed: "right",
          actions: [
            {
              type: "text",
              label: "查看",
              action: "show" // 按钮该按钮时，派发事件的名称
            }
          ]
        }
      ],
      replaceCancelWayList: [
        /* { label: '转化为电子证照/其他证件', value: 'TURN_LICENSE_OR_OTHER_LICENSE_WAY' },
        { label: '办事人承诺', value: 'HANDLE_AFFAIRS_PROMISE' },
        { label: '数据共享(证明开具部门)', value: 'DATA_SHARING' },
        { label: '人工协查(证明开具部门)', value: 'ARTIFICIAL_INVESTIGATION' },
        { label: '部门自行调查', value: 'DEPARTMENT_INVESTIGATION' },
        { label: '数据共享(其他)', value: 'OTHER_WAY' }*/
      ],
      proofStatusList: [], // 事项状态
      actualizeList: [], // 行政区划字典
      getItemTypeList: [], // 事项类型
      unitTypeList: [], // 证明开具单位类型:
      auditList: [
        { label: "同意", value: "APPROVED" },
        { label: "不同意", value: "UNAPPROVED" }
      ],
      examineApproveLoading: false,
      proofRecordList: Array(3),
      //关联电子证照
      selectDialog: false,
      selectLoading: false,
      formRelevance: {
        relevance: ""
      },
      selectForm: {
        proof_catalog_name: ""
      },
      tableDataSelect: {
        content: [], // 表格数据
        loading: false, // 控制表格加载效果
        total: 0, // 分页查询时的总数，去掉该属性，不显示分页条
        currentPage: 1, // 当前页码
        pageSize: 10,
        pageDirection: "desc"
      },
      tableHeaderSelect: [
        {
          label: "",
          slot: "select",
          prop: "select",
          minWidth: "50px",
          fixed: "right"
        },
        {
          label: "证明目录名称",
          prop: "name",
          minWidth: "200px"
        },
        {
          label: "操作",
          slot: "operate",
          prop: "operate",
          minWidth: "50px",
          fixed: "right"
        }
      ],
      radio: "",
      selectList: {}
    };
  },

  watch: {
    data: {
      handler(val) {
        if (!val) return;
        // 把父组件传过来的tableData赋值给tableConfig
        Object.keys(val).forEach(prop => {
          if (val[prop] !== undefined) {
            this.$set(this.dataConfig, prop, val[prop]);
          }
        });
      },
      deep: true,
      immediate: true
    },
    "form.division_code"(val) {
      const actualizeList = this.actualizeList;
      if (actualizeList.length > 0 && val !== "") {
        let info = _.find(
          actualizeList,
          i => i.value === this.form.division_code
        );
        this.form.division_code = info === undefined ? val : info.label;
      }
    },
    "form.item_type"(val) {
      const getItemTypeList = this.getItemTypeList;
      if (getItemTypeList.length > 0 && val !== "") {
        let info = _.find(
          getItemTypeList,
          i => i.value === this.form.item_type
        );
        this.form.item_type = info === undefined ? val : info.label;
      }
    }
  },
  computed: {
    ...mapState({
      account: state =>
        state.user &&
        state.user.userdata &&
        state.user.userdata.userAccount &&
        state.user.userdata.userAccount.account,
      roles: state =>
        state.user && state.user.userdata && state.user.userdata.roles,
      name: state =>
        state.user &&
        state.user.userdata &&
        state.user.userdata.userAccount &&
        state.user.userdata.userAccount.name,
      dept: state =>
        state.user &&
        state.user.userdata &&
        state.user.userdata.userInfo &&
        state.user.userdata.userInfo.orgName
    })
  },
  mounted() {
    this.initData();
  },
  methods: {
    codeItemList(id) {
      codeItemList({ value: id })
        .then(res => {
          const data = res.content || [];
          this.form.division_code =
            data.length > 0 ? data[0].name : this.form.division_code;
        })
        .catch(() => {});
    },
    initData: async function() {
      let proofStatusListRes = await getProofStatusList();
      let itemTypeListRes = await getItemTypeList();
      let replaceCancelWayRes = await getReplaceCancelWay();
      let unitTypeListRes = await getUnitTypeList();
      this.proofStatusList = proofStatusListRes.data || [];
      this.getItemTypeList = itemTypeListRes.data || [];
      this.replaceCancelWayList = replaceCancelWayRes.data || [];
      this.unitTypeList = unitTypeListRes.data || [];
      //被await阻塞的同步代码
      if (this.type === "firstDraft") {
        this.getProofListFirstDrafts();
      } else {
        this.getProofListFormView();
        this.getProofRecordInfo();
      }
    },
    handleClick(tab, event) {
      //console.log(tab, event);
    },
    getProofListFirstDrafts() {
      getProofListFirstDrafts(this.dataConfig.id, {
        item_clear_status: "WAIT_FOR_CLEAN"
      }).then(res => {
        this.reorganizeData(res);
      });
    },
    //请求的接口======zwm
    getProofListFormView() {
      getProofListFormView(this.dataConfig.id, {
        item_clear_status: "WAIT_FOR_CLEAN"
      }).then(res => {
        this.reorganizeData(res); //
      });
    },
    reorganizeData(res) {
      const item_material_vo = res.data.item_material_vo;
      const material = res.data.proof_list_vo;
      this.form = res.data.item_vo || {};
      this.form.material_name = item_material_vo.material_name;
      this.codeItemList(this.form.division_code); //转换区划
      this.proof_catalog_vo = res.data.proof_catalog_vo_list[0] || {};
      if(this.proof_catalog_vo){
        this.formRelevance.relevance= this.proof_catalog_vo.name
				this.selectForm.proof_catalog_name = this.proof_catalog_vo.name;
				this.selectList.id = this.proof_catalog_vo.id;
			}

      this.material =
        {
          item_proof_status: material.item_proof_status, // 梳理状态
          // 证明材料清理
          material_name: item_material_vo.material_name,
          law_name: item_material_vo.law_name,
          proof_clear_type: material.proof_clear_type, // 取消类型 DIRECTLY_CANCEL|REPLACE_CANCEL
          direct_description: material.direct_description, // 直接取消说明,
          not_clean_description: material.not_clean_description // 无需清理说明,

        } || {};

      if (material.proof_clear_type !== "DIRECTLY_CANCEL") {
        let replaceCancelWayList = material.replace_cancel_way.split(",");
        let replaceCancelWayListText = "";
        _.forEach(replaceCancelWayList, i => {
          const way = _.find(this.replaceCancelWayList, j => j.value === i);
          const wayLabel = way ? way.label : material.replace_cancel_way;
          replaceCancelWayListText += wayLabel + ",";
        });
        this.material = Object.assign(
          {},
          {
            item_proof_status: material.item_proof_status, // 梳理状态
            replace_cancel_way: material.replace_cancel_way, // 替代取消方式
            replace_cancel_way_text: replaceCancelWayListText.substring(
              0,
              replaceCancelWayListText.length - 1
            ), // 替代取消方式的文本
            license_name: material.license_name, // 电子证照名称，逗号隔开
            license_code: material.code, // 电子证照code，逗号隔开
            license_description: material.license_description, // 转换为电子证照说明
            commit_book_description: material.commit_book_description, // 承诺书说明
            commit_attachment_id: material.commit_attachment_id, // 承诺书说明
            commit_attachment_name: material.commit_attachment_name, // 承诺书说明
            industry_dept_name: material.industry_dept_name, // 所属行业部门名称
            industry_dept_code: material.industry_dept_code, // 所属行业部门代码
            proof_provide_type_investigation:
              material.proof_provide_type_investigation !== null
                ? this.unitTypeList.find(
                    i => i.value === material.proof_provide_type_investigation
                  ).label
                : material.proof_provide_type_investigation,
            proof_provide_type_dataShared:
              material.proof_provide_type_dataShared !== null
                ? this.unitTypeList.find(
                    i => i.value === material.proof_provide_type_dataShared
                  ).label
                : material.proof_provide_type_dataShared, // 证明开具单位类型

            data_shared_description: material.data_shared_description, // 数据共享说明
            investigation_description: material.investigation_description, // 人工协查说明
            dept_cancel_description: material.dept_cancel_description, // 自行调查说明 （部门自行调查)
            other_clear_description: material.other_clear_description // 其它说明
          }
        );
      }
      const license_name =
        material.license_name != null ? material.license_name.split(",") : "";
      const license_code =
        material.license_code != null ? material.license_code.split(",") : "";
      this.tableData.content = _.map(license_name, function(i, index) {
        return { license_name: i, license_code: license_code[index] };
      });
    },
    getProofRecordInfo() {
      getProofSubmitInfo({ proof_list_id: this.dataConfig.id }).then(res => {
        this.proofRecordList[0] =
          res.data.proof_list_id != null
            ? this.formatRecordInfo(res.data)
            : undefined;
      });
      getProofConfirmInfo({ proof_list_id: this.dataConfig.id }).then(res => {
        this.proofRecordList[1] =
          res.data.proof_list_id != null
            ? this.formatRecordInfo(res.data)
            : undefined;
      });
      getProofAuditInfo({ proof_list_id: this.dataConfig.id }).then(res => {
        this.proofRecordList[2] =
          res.data.proof_list_id != null
            ? this.formatRecordInfo(res.data)
            : undefined;

        //如果存在审核信息，则设置审核信息并设置审核只读标记
        if(this.proofRecordList[2]){
          this.auditForm.auditDisabled = true
          this.auditForm.audit = this.proofRecordList[2] && this.proofRecordList[2].operator_type_code === "APPROVED" ? "APPROVED" : "UNAPPROVED";
          this.auditForm.remark = this.proofRecordList[2].audit_description;
        }
      });
    },
    formatRecordInfo(data) {
      let result = {
        // id: data.id,
        // proof_list_id: data.proof_list_id,
        // operate_time: null,
        // audit_description: null,
        // audit_operator: null,
        // audit_operator_account: null,
        // operator_type: null,
        // account_dept_code: null,
        // account_dept_name: null,
        // created_date: null,
        ...data,
        operator_type: data["operator_type"]
          ? this.proofStatusList.find(i => i.value === data["operator_type"])
              .label
          : data["operator_type"],
        operator_type_code: data["operator_type"],
        date: moment(data.created_date).format("MM.DD"),
        minute: moment(data.created_date).format("HH:mm"),
        time: moment(data.created_date).format("YYYY-MM-DD HH:mm")
      };
      return result;
    },
    hasWay(arr, val) {
      return arr.indexOf(val) !== -1;
    },
    show(row) {
      goLicenseItemView(row.license_code).then(res => {
        window.open(res.data.url, "_blank");
      });
    },
    backPrev() {
      this.$router.go(-1);
    },
    preliminaryDrafts(proof_list_id) {
      const route = this.$router.resolve({
        name: this.proofRoute,
        query: {
          id: proof_list_id
        }
      });
      window.open(route.href, "_blank");
    },
    goEditClearWay() {
      //只有清单和梳理，证明状态为待梳理的情况下可以编辑修改证明
      let name = this.type === "list" ? "item_list_edit" : "item_carding_edit";
      this.$router.push({
        name: name,
        query: {
          id: this.dataConfig.id
        }
      });
    },
    examineApprove() {
      let _this = this;
      this.$confirm("确定梳理完成?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        if (JSON.stringify(this.selectList) != '{}' || _this.material.proof_clear_type === 'DIRECTLY_CANCEL') {
          proofListConfirmCreate({
            proof_list_id: _this.dataConfig.id,
            proof_catalog_id: _this.selectList.id,
            confirm_status: "CARDING_UNCONFIRMED",
            tease_account_name: this.name
          }).then(res => {
            let type = res.meta.code === "200" ? "success" : "warning";
            this.$message({
              type: type,
              message: "操作成功"
            });
            if (type === "success") this.$router.go(-1);
          });
        } else {
          this.$message({
            message: "请选择一条证明目录",
            type: "warning"
          });
        }
      });
    },
    submitAuditForm() {
      this.$refs["auditForm"].validate(valid => {
        if (valid) {
          this.$confirm("确定提交审核结果?", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning"
          }).then(() => {
            let sendData = {
              proof_list_id: this.dataConfig.id,
              operate_time: "",
              audit_description: this.auditForm.remark,
              audit_operator: this.name,
              audit_operator_account: this.account,
              operator_type: this.auditForm.audit,
              account_dept_code: "",
              account_dept_name: this.dept
            };
            proofAuditListExamine(sendData).then(res => {
              let type = res.meta.code === "200" ? "success" : "warning";
              this.$message({
                type: type,
                message: res.meta.msg
              });
              if (type === "success") this.$router.go(-1);
            });
          });
        }
      });
    },
    getAttachmentBase64() {
      if (this.type === "firstDraft") {
        getPreliminaryDraftsAttachment({
          proof_list_id: this.dataConfig.id
        }).then(res => {
          if (res.meta.code === "200") {
            dataURLtoDownload(res.data, this.material.commit_attachment_name);
          }
        });
      } else {
        getAttachmentBase64({ proof_list_id: this.dataConfig.id }).then(res => {
          if (res.meta.code === "200") {
            dataURLtoDownload(res.data, this.material.commit_attachment_name);
          }
        });
      }
    },
    //关联电子证照
    selectQuery(data) {
      this.$refs["selectForm"].validate(valid => {
        if (valid) {
          this.query(1, "search", data);
        } else {
          return false;
        }
      });
    },
    query(currentPage, type, data) {
      if (_.isNumber(currentPage)) {
        this.tableDataSelect.currentPage = currentPage;
      }
      const formData = Object.assign({}, this.selectForm);
      let sendData = {
        ...formData,
        page_size: this.tableDataSelect.pageSize,
        page_number: this.tableDataSelect.currentPage,
        page_direction: this.tableDataSelect.pageDirection
      };
      Object.keys(sendData).forEach(item => {
        if (!sendData[item]) delete sendData[item];
      });
      getGetproofCatalogPage(sendData)
        .then(res => {
          const data = res.data === null ? [] : res.data.content;
          this.tableDataSelect.content = res.data.content;
          this.tableDataSelect.total = res.data.totalElements;
        })
        .catch(() => {});
    },
    selectEvent() {
			this.query(1)
    	/*if(this.selectForm.proof_catalog_name){
				this.query(1, "search", this.selectForm.proof_catalog_name);
			}else{
				this.query(1)
      }*/
      this.selectDialog = true;
    },
    selectionChange(val) {
      this.selectList = val;
    },
    selectFirm() {
      if (JSON.stringify(this.selectList) != '{}') {
         getCatalogReplace({
           proof_list_id:this.dataConfig.id,
           proof_catalog_id: this.selectList.id
        }).then(res => {
          this.reorganizeData(res);
          this.formRelevance.relevance = this.selectList.name;
          this.selectDialog=false;
        });
      } else {
        this.$message({
          message: "请选择一条证明目录",
          type: "warning"
        });
      }
    },
    //查看
    selectLook(row){
     this.$router.push({
        name: "certification_List_info",
        query: {
          id: row.id
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.time {
  color: #919191;
}
.status {
  color: #2d76ce;
  font-size: 19px;
  font-weight: 900;
}
.fixed-bottom {
  width: 100%;
  transform: scale3d(1, 1, 1);
  .btn-group {
    background: rgba(255, 255, 255, 0.95);
    border-top: 1px solid #096dd9;
    padding: 20px 10px 10px 50px;
    // position: absolute;
    bottom: 0;
    right: 0;
    z-index: 100;
    text-align: right;
    height: 80px;
    width: inherit;
  }
}
.formRelevance {
  position: relative;
  .select {
    position: absolute;
    left: 52%;
    top: 18%;
    a {
      color: #409eff;
    }
  }
}
.selectForm {
  position: relative;
  .catalogBtn {
    position: absolute;
    right: -80px;
    top: -1%;
  }
}
</style>
<style scoped>
::v-deep >>> .el-radio {
  margin-left: 6px;
}
/*>>> .el-radio .el-radio__label {
  display: none;
}*/
</style>
