<template>
  <div>
    <span class="margin-left-10 info-wrap">
      <img :src="arrow" alt />
      <span class="info-title">基本信息</span>
    </span>
    <el-descriptions class="descriptions" title :column="2" border>
      <el-descriptions-item class="descriptions-item" :labelStyle="{width:'140px'}">
        <template slot="label">事项名称</template>
        {{ form.item_name }}
      </el-descriptions-item>
      <el-descriptions-item class="descriptions-item" :labelStyle="{width:'140px'}">
        <template slot="label">事项编码</template>
        {{ form.item_code}}
      </el-descriptions-item>
      <el-descriptions-item class="descriptions-item" :labelStyle="{width:'140px'}">
        <template slot="label">事项类型</template>
        {{ form.item_type_desc }}
      </el-descriptions-item>
      <el-descriptions-item class="descriptions-item" :labelStyle="{width:'140px'}">
        <template slot="label">实施机构</template>
        {{ form.impl_org_name }}
      </el-descriptions-item>
      <el-descriptions-item class="descriptions-item" :labelStyle="{width:'140px'}">
        <template slot="label">事项状态</template>
        <span v-if="form.item_status=='WORK'">在用</span>
        <span v-if="form.item_status=='SUSPEND'">暂停</span>
        <span v-if="form.item_status=='CANCEL'">取消</span>
      </el-descriptions-item>
      <el-descriptions-item class="descriptions-item" :labelStyle="{width:'140px'}">
        <template slot="label">事项标准类型</template>
        {{ form.item_source_desc }}
      </el-descriptions-item>
      <el-descriptions-item class="descriptions-item" :labelStyle="{width:'140px'}">
        <template slot="label">办件类型</template>
        <span v-if="form.project_type=='UNKNOWN_ITEM'">未知办件类型</span>
        <span v-if="form.project_type=='IMMEDIATE_ITEM'">即办件</span>
        <span v-if="form.project_type=='COMMITMENT_ITEM'">承诺件</span>
      </el-descriptions-item>
      <el-descriptions-item class="descriptions-item" :labelStyle="{width:'140px'}">
        <template slot="label">办理条件</template>
        {{ form.accept_condition }}
      </el-descriptions-item>
    </el-descriptions>
    <!-- <el-divider content-position="left">基础信息</el-divider> -->
    <!-- <el-row>
      <el-col :span="8" :offset="2">事项名称: {{ form.item_name }}</el-col>
     
      <el-col :span="8" :offset="2">事项编码: {{ form.item_code}}</el-col>
    </el-row>
    <br />
    <el-row>
      <el-col :span="8" :offset="2">事项类型: {{ form.item_type_desc}}</el-col>
     
      <el-col :span="8" :offset="2">实施机构: {{ form.impl_org_name }}</el-col>
    </el-row>
    <br />
    <el-row>
    
      <el-col :span="8" :offset="2">
        事项状态:
        <span v-if="form.item_status=='WORK'">在用</span>
        <span v-if="form.item_status=='SUSPEND'">暂停</span>
        <span v-if="form.item_status=='CANCEL'">取消</span>
      </el-col>
      <el-col :span="8" :offset="2">事项标准类型: {{form.item_source_desc}}</el-col>
    </el-row>
    <br />
    <el-row>
      <el-col :span="8" :offset="2">
        办件类型:
        <span v-if="form.project_type=='UNKNOWN_ITEM'">未知办件类型</span>
        <span v-if="form.project_type=='IMMEDIATE_ITEM'">即办件</span>
        <span v-if="form.project_type=='COMMITMENT_ITEM'">承诺件</span>
      </el-col>
      <el-col :span="8" :offset="2" v-if="form.accept_condition!=null">
        办理条件:
        <span>{{form.accept_condition}}</span>
      </el-col>
    </el-row>
    <br />-->
    <!-- <el-divider content-position="left">证明材料</el-divider> -->
    <span class="margin-left-10 info-wrap">
      <img :src="arrow" alt />
      <span class="info-title">证明材料</span>
    </span>
    <el-row v-if="!detailFlag">
      <el-col :span="24">
        <custom-table ref="table1" :is-card-type="false" :table-data="tableData" :table-header="tableHeader">
          <template #operate="{row}">
            <el-button v-if="row.proof_list_id" type="text" @click="goDetail(row)">查看</el-button>
          </template>
          <template #templateBlank="{ row }">
            <!-- <a class="download" @click="downloadFile('blank', row)">{{ row.blank_file_name }}</a> -->
            <a class="download" @click="downloadFile('blank', row)" v-if="row.blank_file_name">
              空白表格
              <i class="el-icon-download"></i>
            </a>

            <div v-else>
              <a class="download" @click="downloadblankFileList(row.blank_file_list)" v-if="row.blank_file_list">
                空白表格
                <i class="el-icon-download"></i>
              </a>
              <div v-else>无</div>
            </div>
          </template>
          <template #templateSimple="{ row }">
            <!-- <a class="download" @click="downloadFile('sample', row)">{{ row.sample_file_name }}</a> -->
            <a class="download" @click="downloadFile('sample', row)" v-if="row.sample_file_name">
              示例样表
              <i class="el-icon-download"></i>
            </a>
            <div v-else>
              <a class="download" @click="downloadblankFileList(row.sample_file_list)" v-if="row.sample_file_list">
                示例样表
                <i class="el-icon-download"></i>
              </a>
              <div v-else>无</div>
            </div>
          </template>
        </custom-table>
      </el-col>
    </el-row>
    <div class="toitemList—btn">
      <!-- v-if="isShowItemClear" -->
      <el-button type="primary" v-permission="'catalog:biz:credential:view'" @click="toItemList()">查看清理办件</el-button>
    </div>
  </div>
</template>
<script>
import { getStandardTypeList, getMattersTypeList, getIMaterialSource, getItemMaterialType, getItemMaterialStandardType } from '@/api/common/dict'
import CustomTable from '@/components/Element/Table'
import { getProveInfoById } from '@/api/ItemManagement/itemProve'
import { goLicenseItemView, getProofListFormViewCode } from '@/api/itemBiz/list'
import { downloadSampleFile, downloadBlankFile } from '@/api/common/download'
import { dataURLtoDownload } from '@/utils/index'
import Enum from '@/utils/enum'
export default {
  name: 'infoList',
  components: {
    CustomTable
  },
  data() {
    return {
      arrow: require('@/assets/proof-derate-admin-images/arrow.png'),
      form: {
        item_name: '',
        item_code: '',
        //事项类型
        item_type: '',
        item_type_desc: '',
        //事项标准类型
        item_source: '',
        item_source_desc: '',
        division_code: '',
        impl_org_name: '',
        credit_code: '',
        item_material_list: []
      },
      detailData: null,
      //材料详情标记
      detailFlag: false,
      isShowItemClear: false,
      standardTypeList: Enum.standardTypeList,
      mattersTypeList: Enum.mattersTypeList,
      //证明标准类型
      materialTypeList: Enum.materialTypeList,
      //证明材料类型
      proveMaterialTypeList: Enum.proveMaterialTypeList,
      //数据共享
      tableData: {
        // isShowIndex: true,
        border: false,
        content: [], // 表格数据
        clearData: {
          material_id: '',
          proof_name: '',
          proof_clear_type: '',
          direct_description: '',
          replace_cancel_way: '',
          clerk_commitment: {
            commit_book_description: '',
            commit_attachment_id: '',
            // file_data: ["", "", ""],
            commit_attachment_name: ''
          },
          replace_data_shared: {
            industry_dept_name: '',
            industry_dept_code: '',
            proof_provide_type: null,
            data_shared_description: ''
          },
          replace_dept_survey: {
            dept_name: '',
            dept_code: '',
            dept_cancel_description: ''
          },
          replace_investigation: {
            industry_dept_name: '',
            industry_dept_code: '',
            proof_provide_type: null,
            investigation_description: ''
          },
          replace_license: {
            license_description: '',
            license_code: '',
            license_name: ''
          },
          replace_other: {
            other_clear_description: ''
          },
          user_info: {
            account_dept_code: '',
            account_dept_name: ''
          }
        }
      },
      // 表头配置
      tableHeader: [
        {
          label: '材料排序',
          prop: 'index',
          minWidth: '50px',
          formatter: (row, col, val) => {
            return val + 1
          }
        },
        // { label: "材料排序", prop: "order_num", minWidth: "200px" },
        { label: '证明材料名称', prop: 'material_name', minWidth: '200px' }, // 配置slot属性，可支持使用插槽
        {
          label: '证明材料标准类型',
          prop: 'item_material_source',
          minWidth: '200px',
          formatter: (row, col, val) => {
            const itemSourceData = Enum.materialTypeList.find(i => i.value === val)
            if (itemSourceData) {
              return itemSourceData.label
            }
            return ''
          }
        },
        {
          label: '已关联证照目录',
          prop: 'license_name',
          minWidth: '200px'
        },
        {
          label: '样例',
          slot: 'templateSimple',
          prop: 'templateSimple',
          minWidth: '200px'
        },
        {
          label: '空白表格',
          slot: 'templateBlank',
          prop: 'templateBlank',
          minWidth: '200px'
        }
        // {
        //   label: "操作",
        //   slot: "operate",
        //   prop: "operate",
        //   minWidth: "50px",
        //   fixed: "right"
        // }
      ]
    }
  },
  mounted() {
    this.initData()
  },
  methods: {
    initData: async function () {
      // 事项标准类型
      let standardTypeListRes = await getStandardTypeList()
      // 事项类型
      let mattersTypeListRes = await getMattersTypeList()
      // 证明标准类型
      let getIMaterialSourceRes = await getItemMaterialStandardType()
      // 证明材料类型
      let itemMaterialType = await getItemMaterialType()
      this.getStandardTypeList(standardTypeListRes)
      this.getMattersTypeList(mattersTypeListRes)
      this.getIMaterialSource(getIMaterialSourceRes)
      this.itemMaterialType(itemMaterialType)
      this.getInfoById(this.$route.query.id)
    },
    getInfoById(findId) {
      /*this.form ={
        item_name: "1111",
        item_code: "2222",
        item_type: "ADMINISTRATIVE_LICENSE",
        item_source:"STANDARDIZATION",
        division_code:"5555",
        impl_org_name:"666",
        credit_code:"77777",
        item_material_list:[]
      }

      let tempData = {
        material_name: "1111",
        item_code: "2222",
        item_material_source: "NOT_STANDARDIZATION",
        material_type: "STANDARDIZATION",
        sample_file_name: "test.txt",
        blank_file_name: "blank.txt"
      }
      const itemSourceData = Enum.standardTypeList.find(i => i.value === this.form.item_source);
      if(itemSourceData){
        this.form.item_source_desc = itemSourceData.label
      }
      const itemTypeData = Enum.mattersTypeList.find(i => i.value === this.form.item_type);
      if(itemTypeData){
        this.form.item_type_desc = itemTypeData.label
      }
      this.form.item_material_list.push(tempData)
			let tempData2 = {
				material_name: "1111",
				item_code: "2222",
				item_material_source: "NOT_STANDARDIZATION",
				material_type: "STANDARDIZATION",
				sample_file_name: "test.txt",
				blank_file_name: "blank.txt"
			}
      this.form.item_material_list.push(tempData2)
      this.tableData.content =  this.form.item_material_list;*/

      getProveInfoById(findId)
        .then(res => {
          // console.log('getProveInfoById', res)
          this.form = res.data
          // this.tableData.content =  this.form.item_material_list;
          this.tableData.content = _.map(res.data.item_material_list, (item, index) => {
            // console.log('item', item)
            if (item.license_list !== null) {
              item.license_name = item.license_list.map(i => {
                return i.license_name
              })
              item.license_name = item.license_name.join(',')
            }

            return {
              index: index,
              ...item,
              clearData: {
                ...this.tableData.clearData,
                material_id: item.id,
                proof_name: item.material_name,
                blank_file: '',
                blank_file_id: item.blank_file_id,
                blank_file_name: item.blank_file_name,
                sample_file: '',
                sample_file_id: item.sample_file_id,
                sample_file_name: item.sample_file_name
              }
            }
          })
          const itemSourceData = this.standardTypeList.find(i => i.value === this.form.item_source)
          if (itemSourceData) {
            this.form.item_source_desc = itemSourceData.label
          }
          const itemTypeData = this.mattersTypeList.find(i => i.value === this.form.item_type)
          if (itemTypeData) {
            this.form.item_type_desc = itemTypeData.label
          }
          console.log('this.tableData.content----', this.tableData.content)
          this.getProofListFormViewCode(this.form.item_code)
        })
        .catch(() => {})
    },
    // 编辑证明材料页面
    goAddOrUpdate(row, type) {
      this.$router.push({
        name: 'itemManagement_itemProve_add',
        query: {
          flagCatalog: type,
          id: this.$route.query.id
        }
      })
    },
    // 编辑证明材料页面
    goDetail(row) {
      this.$router.push({
        name: 'itemManagement_itemProve_info_way',
        query: {
          id: row.proof_list_id
        }
      })
    },
    //事项标准类型
    getStandardTypeList(res) {
      const data = res.data || []
      Enum.standardTypeList.splice(0)
      Enum.standardTypeList.push(...data)
    },
    // 证明标准类型
    getIMaterialSource(res) {
      const data = res.data || []
      Enum.materialTypeList.splice(0)
      Enum.materialTypeList.push(...data)
    },
    //证明材料类型
    itemMaterialType(res) {
      const data = res.data || []
      Enum.proveMaterialTypeList.splice(0)
      Enum.proveMaterialTypeList.push(...data)
    },
    //事项类型
    getMattersTypeList(res) {
      const data = res.data || []
      Enum.mattersTypeList.splice(0)
      Enum.mattersTypeList.push(...data)
    },
    downloadFile(type, row) {
      if (type === 'sample') {
        downloadSampleFile({ attachment_id: row.sample_file_id }).then(res => {
          if (res.meta.code === '200') {
            dataURLtoDownload(res.data, row.sample_file_name)
          }
        })
      } else {
        downloadBlankFile({ attachment_id: row.blank_file_id }).then(res => {
          if (res.meta.code === '200') {
            dataURLtoDownload(res.data, row.blank_file_name)
          }
        })
      }
    },
    downloadblankFileList(data) {
      this.downblankFile(data)
    },
    downloadSampleFileList(data) {
      this.downSampleFile(data)
    },
    downblankFile(row) {
      if (row != 0) {
        // console.log(row.blank_file_list)
        const itemList = row
        // intranet_path attachment_name
        itemList.forEach(e => {
          // this.downURLfile(e.intranet_path, e.attachment_name)
          this.createIFrame(e.intranet_path, 1000, 1000)
        })
      } else {
        this.$message({
          showClose: true,
          message: '暂无文件下载',
          type: 'warning'
        })
      }
    },
    downSampleFile(row) {
      if (row.length != 0) {
        const itemList = row
        itemList.forEach((e, index) => {
          // this.downURLfile(e.intranet_path, e.attachment_name, index)
          this.createIFrame(e.intranet_path, 1000, 1000)
        })
      } else {
        this.$message({
          showClose: true,
          message: '暂无文件下载',
          type: 'warning'
        })
      }
    },
    createIFrame(url, triggerDelay, removeDelay) {
      setTimeout(function () {
        // 动态添加iframe,设置src,然后删除
        const frame = document.createElement('iframe') //创建a对象
        frame.setAttribute('style', 'display: none')
        frame.setAttribute('src', url)
        frame.setAttribute('id', 'iframeName')
        frame.setAttribute('referrerpolicy', 'no-referrer')
        document.body.appendChild(frame)
        setTimeout(function () {
          const node = document.getElementById('iframeName')
          node.parentNode.removeChild(node)
        }, removeDelay)
      }, triggerDelay)
    },
    toItemList() {
      this.$router.push({
        // path: 'itemBiz/itemList/info/way',
        name: 'item_list_info_way',
        query: {
          id: this.form.item_code,
          type: 'show'
        }
      })
    },
    // 获取事项详情判断是否显示查看清理按钮
    getProofListFormViewCode(code) {
      getProofListFormViewCode(code).then(res => {
        if (res.meta.code === '200' && res.data != null) {
          if (res.data.proof_data != null) {
            this.isShowItemClear = true
          } else {
            this.isShowItemClear = false
          }
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.toitemList—btn {
  display: flex;
  justify-content: center;
}
.descriptions {
  margin-bottom: 50px;
}
.info-wrap {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  img {
    margin-right: 10px;
  }
  .info-title {
    font-size: 20px;
    color: #333333;
  }
}
</style>