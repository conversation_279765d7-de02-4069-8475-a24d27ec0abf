<template>
  <div class="content-wrapper">
    <section class="content-header">
      <span class="breadcrumb" align="right">
        <el-button type="warning" plain @click="backPrev" icon="el-icon-back">返回</el-button>
      </span>
      <br />
    </section>
    <section class="content">
      <clean-method :data="data" :proofRoute="proofRoute"/>
    </section>
  </div>
</template>

<script>
import CleanMethod from "../components/CleanMethod";
export default {
  name: "ItemManagementItemProveInfoWay",
  components: {
    CleanMethod
  },
  data() {
    return {
      data: {
        id: "",
        title: "证明档案",
        activeName: "desc",
      },
      proofRoute: "itemManagement_itemProve_info_way"
    };
  },
  mounted() {
    this.data.id = this.$route.query["id"];
  },
  methods: {
    backPrev() {
      this.$router.go(-1);
    }
  }
};
</script>

<style lang="scss" scoped>

</style>
