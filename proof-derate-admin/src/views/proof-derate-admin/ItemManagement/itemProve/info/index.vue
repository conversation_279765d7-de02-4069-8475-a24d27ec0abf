<template>
  <div class="content-wrapper padding-10">
    <CardTitle :title-name="title" :ifback="true" @back="goToList()">
      <template>
        <el-button v-permission="'catalog:biz:credential:update'" plain type="primary" icon="el-icon-edit" @click="edit">修改</el-button>
      </template>
    </CardTitle>
    <info-list ref="infoListRef" @click="initInfo" />
  </div>
</template>
<script>
import infoList from '@/views/proof-derate-admin/ItemManagement/itemProve/components/infoList'
import { getProveInfoById } from '@/api/ItemManagement/itemProve'
import CardTitle from '@/components/CardTitle'
export default {
  name: 'ProveInfoWay',
  components: {
    infoList,
    CardTitle
  },
  data() {
    return {
      hasItemClear: false,
      haveSync_item_id: true,
      havaEditStatus: false,
      title: ''
    }
  },
  watch: {
    '$.division_code': {
      handler(val) {
        if (val) {
        } else {
        }
      },
      immediate: true
    }
  },
  mounted() {
    this.getInfoById(this.$route.query.id)
  },
  methods: {
    getInfoById(findId) {
      getProveInfoById(findId)
        .then(res => {
          console.log(res.data)
          this.title = res.data.item_name
          if (res.data.item_clear_status === 'WAIT_FOR_CLEAN') {
            this.havaEditStatus = true
          }
        })
        .then(() => {
        })
        .catch(() => {})
    },
    backPrev() {
      this.$router.go(-1)
    },
    goToList() {
      this.$router.push({
        name: 'ItemManagement_itemProve'
      })
    },
    initInfo(data) {
      this.hasItemClear = data
    },
    edit() {
      this.$router.push({
        name: 'itemManagement_itemProve_add',
        query: {
          flagCatalog: 'edit',
          id: this.$route.query.id
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.content-header {
  position: relative;
  i {
    font-size: 25px;
    cursor: pointer;
  }
}
.breadcrumbbtn {
  top: 4px;
}
</style>
