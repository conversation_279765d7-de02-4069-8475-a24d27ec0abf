<template>
  <div class="content-wrapper itemProve padding-10">
    <!-- <papeTitle :title-name="titleName" :is-has-back="false">
      <div>
        <el-button type="primary" v-permission="'catalog:biz:credential:add'" icon="el-icon-plus" @click="goAddOrUpdate('add')">新建事项</el-button>
        <el-button type="primary" v-permission="'catalog:biz:credential:import'" icon="el-icon-plus" @click="goProofCatalog">导入证明材料</el-button>
      </div>
    </papeTitle>-->
    <CardTitle :title-name="titleName">
      <template>
        <el-button type="primary" v-permission="'catalog:biz:credential:add'" icon="el-icon-plus" @click="goAddOrUpdate('add')">新建事项</el-button>
        <el-button type="primary" v-permission="'catalog:biz:credential:import'" icon="el-icon-plus" @click="goProofCatalog">导入证明材料</el-button>
      </template>
    </CardTitle>
    <!-- <section class="content"> -->
    <el-card class="box-card" shadow="never">
      <query-form ref="queryForm" style="padding:0 10px" @click="search" />
      <span style="color: #888; padding:20px 10px 0" class="dashed-line">
        共
        <span class="text-red">{{ tableData.total }}</span> 条符合查询条件
      </span>
      <custom-table
        ref="table"
        :is-card-type="false"
        :table-data="tableData"
        :table-header="tableHeader"
        :span-method="objectSpanMethod"
        :stripe="false"
        :table-tools="tableTools"
        style="margin-top: 10px"
        @query="query"
        @refresh="query(1)"
      >
        <template #operate="{row,$index}">
          <div>
            <el-button type="text" v-permission="'catalog:biz:credential:info'" @click="goDetail(row, $index)">查看</el-button>
          </div>
        </template>
        <template #item_name="{ row }">
          <el-button
            type="text"
            @click="goProofFile(row)"
          >{{ row.handing_item === null || val === undefined ? row.item_name : row.item_name + '【' + row.handing_item + '】' }}</el-button>
        </template>
      </custom-table>
    </el-card>
    <!-- </section> -->
    <el-dialog title="证明材料导入" :visible.sync="dialogFormVisible" width="50%" center>
      <uploadDialog ref="uploadDialog" :limit-file-type="limitFileType" :whitelist="whitelist" @toFather="download" />

      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="importFlie">导入</el-button>
        <el-button @click="dialogFormVisible = false">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { down_import_template, commit_attachment, page } from '@/api/ItemManagement/itemProve'
import { dataURLtoDownload, hasDataPermission } from '@/utils/index'
import { getStandardTypeList, getMattersTypeList, getIMaterialSource, getItemMaterialType, getItemMaterialStandardType } from '@/api/common/dict'
import { getOProofListPage, exportProofList } from '@/api/itemBiz/list'
import QueryForm from './components/QueryForm'
import { mergeTableRow1 } from '@/utils/index'
import { getProofStatusList } from '@/api/common/dict'
import CustomTable from '@/components/Element/Table'
import uploadDialog from '@/views/proof-derate-admin/components/uploadDialog'
import Enum from '@/utils/enum'
import papeTitle from '@/components/papeTitle'
import CardTitle from '@/components/CardTitle'
export default {
  name: 'CertificationList',
  components: {
    QueryForm,
    CustomTable,
    uploadDialog,
    papeTitle,
    CardTitle
  },
  data() {
    return {
      item_type: [], // 事项类型
      item_source: [], // 事项标准类型
      item_material_source: [], // 材料标准类型
      tableData: {
        border: false,
        content: [], // 表格数据
        loading: false, // 控制表格加载效果
        total: 0, // 分页查询时的总数，去掉该属性，不显示分页条
        currentPage: 1, // 当前页码
        pageSize: 10,
        isShowIndex: true,
        pageDirection: 'desc',
        isShowSelection: false // 是否显示多选框，默认false
      },
      tableHeader: [
        {
          label: '事项名称',
          prop: 'name_code',
          minWidth: '240px',
          align: 'left',
          // showOverflowTooltip:false,
          formatter: (row, col, val) => {
            return row.item_name
          }
        },
        { label: '事项编码', prop: 'item_code', minWidth: '280px', align: 'left' },
        { label: '办理项名称', prop: 'handing_item', minWidth: '180px', align: 'left' },
        { label: '实施机构', prop: 'impl_org_name', minWidth: '160px', align: 'left' },
        {
          label: '事项证明状态',
          prop: 'item_clear_status',
          minWidth: '80px',
          align: 'left',
          formatter: (row, col, val) => {
            // return Enum.proofStatusList.find(i => i.value === val).label;
            if (val) {
              return this.itemClearStatusList.find(i => i.value === val).label
            } else {
              return val
            }
          }
        },
        {
          label: '操作',
          width: '90px',
          fixed: 'right',
          slot: 'operate',
          align: 'left',
          prop: 'operate_name_code'
        }
      ],
      tableTools: [],
      dialogFormVisible: false,
      tips_message: ' 温馨提示：请选择以后缀名为.xlsx/.xls文件且上传文件大小不得超过1M！',
      limitFileType: '.xlsx,.xls',
      whitelist: ['xlsx', 'xls'],
      itemClearStatusList: [],
      titleName: '事项与证明管理'
    }
  },
  mounted: function () {
    this.initData()
  },
  methods: {
    initData: async function () {
      // 事项标准类型
      const standardTypeListRes = await getStandardTypeList()
      // 事项类型
      const mattersTypeListRes = await getMattersTypeList()
      // 证明标准类型
      const getIMaterialSourceRes = await getItemMaterialStandardType()
      // 证明材料类型
      const itemMaterialType = await getItemMaterialType()
      this.getProofStatusList()
      this.getStandardTypeList(standardTypeListRes)
      this.getMattersTypeList(mattersTypeListRes)
      this.getIMaterialSource(getIMaterialSourceRes)
      this.itemMaterialType(itemMaterialType)
      this.query(1, 'init')
    },
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      // const span = column["property"] + "-span";
      // if (row[span]) {
      //   return row[span];
      // }
    },
    search(data) {
      this.query(1, 'search', data)
    },
    query(currentPage, type, data) {
      // if (_.isNumber(currentPage)) {
      //   this.tableData.currentPage = currentPage
      // }
      const formData = Object.assign({}, this.$refs['queryForm'].form)
      const item_type = type === 'init' ? this.item_type.join() : formData['item_type'].join()
      const item_source = type === 'init' ? this.item_source.join() : formData['item_source'].join()
      // const item_material_source =
      //   type === "init"
      //     ? this.item_material_source.join()
      //     : formData["item_material_source"].join();

      const sendData = {
        ...formData,
        page_size: this.tableData.pageSize,
        page_number: this.tableData.currentPage,
        page_direction: this.tableData.pageDirection,
        item_source: item_source,
        item_type: item_type
        // item_material_source: item_material_source
      }
      // Object.keys(sendData).forEach(item => {
      //   if (!sendData[item]) delete sendData[item];
      // });
      page(sendData)
        .then(res => {
          const data = res.data === null ? [] : res.data.content
          const composeData = res.data.content.map(value => {
            return {
              ...value,
              name_code: value.item_name + '-' + value.item_code,
              operate_name_code: value.item_name + '-' + value.item_code
            }
          })
          console.log('composeData', composeData)
          this.tableData.content = mergeTableRow1(composeData, ['name_code', 'impl_org_name', 'item_type', 'item_source', 'operate_name_code'], 'single')

          this.tableData.total = Number(res.data.total_elements)
        })
        .catch(() => {})
    },
    // // 证明目录详情
    // goItemProofFile(row) {
    //   this.$router.push({
    //     name: "certification_List_info"
    //     // query: {
    //     //   id: row.proof_list_id,
    //     //   item_clear_status: "DO_NOT_CLEAN",
    //     //   type: "list"
    //     // }
    //   });
    // },
    hasDataPermission(value, permission_code) {
      return hasDataPermission(value, permission_code)
    },
    // 跳转到证明档案
    goProofFile(row) {
      this.$router.push({
        name: 'item_carding_info_proof',
        query: {
          id: row.item_code,
          item_clear_status: 'CLEAN'
        }
      })
    },
    // 新增证明目录页面
    goProofCatalog() {
      this.dialogFormVisible = true
      if (this.$refs.uploadDialog) {
        this.$refs.uploadDialog.clearFiles()
      }
    },
    // 新增证明目录页面
    goAddOrUpdate(type) {
      this.$router.push({
        name: 'itemManagement_itemProve_add',
        query: {
          flagCatalog: type
        }
      })
    },
    // 新增证明目录页面
    goDetail(row, index) {
      this.$router.push({
        name: 'itemManagement_itemProve_info',
        query: {
          id: row.item_id
        }
      })
    },
    // 导入证明材料
    importFlie() {
      if (this.$refs.uploadDialog.input) {
        const name = this.$refs.uploadDialog.input
        const file = this.$refs.uploadDialog.file.raw
        const fd = new FormData()
        fd.append('file', file)
        fd.append('name', name)
        commit_attachment(fd)
          .then(res => {
            const type = res.meta.code === '200' ? 'success' : 'error'
            const data = res.data ? res.data : res.meta.message
            const message = data.split('/').join('<br/>')
            this.$message({
              type: type,
              dangerouslyUseHTMLString: true,
              message: message
            })
            if (type === 'success') {
              this.dialogFormVisible = false
              this.query(1, 'search', data)
            }
          })
          .catch(() => {})
      } else {
        this.$message({
          message: '请选择文件，再导入！',
          type: 'warning'
        })
      }
    },
    download(str) {
      if (str) {
        down_import_template()
          .then(res => {
            var raw = window.atob(res.data)
            var uInt8Array = new Uint8Array(raw.length)
            for (var i = 0; i < raw.length; i++) {
              uInt8Array[i] = raw.charCodeAt(i)
            }
            const link = document.createElement('a')
            const blob = new Blob([uInt8Array], {
              type: 'application/vnd.ms-excel'
            })
            link.style.display = 'none'
            link.href = URL.createObjectURL(blob)
            link.setAttribute('download', '证明材料模板.xlsx')
            document.body.appendChild(link)
            link.click()
            document.body.removeChild(link)
            this.$message({
              message: '下载模板成功',
              type: 'warning'
            })
          })
          .catch(() => {
            this.$message({
              message: '下载模板失败',
              type: 'warning'
            })
          })
      }
    },
    // 事项标准类型
    getStandardTypeList(res) {
      const data = res.data || []
      Enum.standardTypeList.splice(0)
      Enum.standardTypeList.push(...data)
      const item_source = [] // 事项标准类型
      _.forEach(Enum.standardTypeList, item => {
        item_source.push(item.value)
      })
      this.item_source = item_source
    },
    // 证明标准类型
    getIMaterialSource(res) {
      const data = res.data || []
      Enum.materialTypeList.splice(0)
      Enum.materialTypeList.push(...data)
      const item_material_source = []
      _.forEach(Enum.materialTypeList, item => {
        item_material_source.push(item.value)
      })
      this.item_material_source = item_material_source
    },
    // 证明材料类型
    itemMaterialType(res) {
      const data = res.data || []
      Enum.proveMaterialTypeList.splice(0)
      Enum.proveMaterialTypeList.push(...data)
    },
    // 事项类型
    getMattersTypeList(res) {
      const data = res.data || []
      Enum.mattersTypeList.splice(0)
      Enum.mattersTypeList.push(...data)
      const item_type = [] // 事项标准类型
      _.forEach(Enum.mattersTypeList, item => {
        item_type.push(item.value)
      })
      this.item_type = item_type
      // getMattersTypeList()
      //   .then(res => {
      //     const data = res.data || [];
      //     Enum.mattersTypeList.splice(0);
      //     Enum.mattersTypeList.push(...data);
      //     const item_type = []; // 事项标准类型
      //     _.forEach(Enum.mattersTypeList, item => {
      //       item_type.push(item.value);
      //     });
      //     this.item_type = item_type;
      //   })
      //   .catch(() => {
      //     Enum.mattersTypeList.splice(0);
      //   });
    },
    getProofStatusList() {
      getProofStatusList().then(res => {
        console.log(res)
        if (res.meta.code === '200') {
          this.itemClearStatusList = res.data
          console.log('itemClearStatusList', this.itemClearStatusList)
        }
      })
    }
  }
}
</script>
<style lang="scss">
.itemProve .el-dialog__title {
  color: #409eff;
}
.itemProve .test-class {
  white-space: normal;
}
</style>
<style scoped>
.breadcrumb {
  top: 4px;
}
/* .content-wrapper {
  padding: 10px;
} */
</style>
