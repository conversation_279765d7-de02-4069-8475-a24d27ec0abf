<template>
  <div class="content-wrapper">
    <CardTitle :title-name="titleName">
      <template>
        <el-button type="primary" v-permission="'catalog:archives:compare:export'" :loading="exportProofListLoading" @click="exportProofList()">导出清单</el-button>
      </template>
    </CardTitle>
    <el-card class="box-card" shadow="never">
      <query-form ref="queryForm" style="padding: 0 10px" @click="search" />
      <div style="color: #888; padding: 20px 10px 0; display: flex; justify-content: space-between" class="dashed-line">
        <span>
          共
          <span class="text-red">{{ tableData.total }}</span> 条符合查询条件
        </span>
        <span> 对比时间：{{ compareTime }} </span>
      </div>
      <custom-table ref="table" :is-card-type="false" :table-data="tableData" :table-header="tableHeader" :stripe="false" :table-tools="tableTools" style="margin-top: 10px" @refresh="query(1)" @query="query">
        <template #operate="{ row }">
          <div>
            <el-button type="text" v-permission="'catalog:archives:compare:view'" @click="goDetail(row)">查看</el-button>
          </div>
        </template>
      </custom-table>
    </el-card>
  </div>
</template>

<script>
import CardTitle from '@/components/CardTitle'
import CustomTable from '@/components/Element/Table'
import QueryForm from '@/views/proof-derate-admin/archiveManage/mattersComparisonManage/components/QueryForm'
import { getComparePage, exportCompareList, getCompareMaxDate } from '@/api/itemBiz/clear'
import { dataURLtoDownload } from '@/utils/index.js'
import Enum from '@/utils/enum'
export default {
  name: 'mattersComparisonManageList',
  components: {
    CardTitle,
    QueryForm,
    CustomTable,
  },
  data() {
    return {
      titleName: '事项对比清单',
      tableHeader: [
        {
          label: '事项名称',
          prop: 'itemName',
          minWidth: '200px',
          align: 'left',
        },
        { label: '事项编码', prop: 'itemCode', minWidth: '180px', align: 'left' },
        { label: '办理项名称', prop: 'handingItem', minWidth: '180px', align: 'left' },
        { label: '实施机构', prop: 'implOrgName', minWidth: '160px', align: 'left' }, // 配置slot属性，可支持使用插槽
        // { label: "证明名称", prop: "material_name", minWidth: "180px" },
        {
          label: '对比结果',
          prop: 'itemCompareStatus',
          minWidth: '80px',
          align: 'left',
          formatter: (row, col, val) => {
            return Enum.comparisonResults.find((i) => i.value === val).label
          },
        },
        { label: '最后更新时间', prop: 'lastUpdateDate', minWidth: '160px', align: 'left' },
        { label: '操作', slot: 'operate', width: '120px', fixed: 'right', align: 'left' },
      ],
      tableTools: [],
      tableData: {
        border: false,
        content: [], // 表格数据
        loading: false, // 控制表格加载效果
        total: 0, // 分页查询时的总数，去掉该属性，不显示分页条
        currentPage: 1, // 当前页码
        pageSize: 10,
        pageDirection: 'desc',
        isShowIndex: true,
        isShowSelection: false, // 是否显示多选框，默认false
      },
      exportProofListLoading: false,
      compareTime: '', // 对比时间
    }
  },
  methods: {
    dataURLtoDownload,
    // 导出清单
    exportProofList() {
      const formData = Object.assign({}, this.$refs['queryForm'].form)
      const sendData = {
        page_size: this.tableData.pageSize,
        page_num: this.tableData.currentPage,
        // page_direction: this.tableData.pageDirection,
        item_name: formData.item_name,
        item_code: formData.item_code,
        division_code: formData.division_code,
        credit_code: formData.credit_code,
        compare_result_list: formData.compare_result_list,
      }
      this.exportProofListLoading = true
      exportCompareList(sendData).then((res) => {
        this.exportProofListLoading = false
        dataURLtoDownload(res.data.file_data_base64, res.data.file_name)
      })
    },
    search(data) {
      this.query('search', data)
    },
    query(type, data) {
      const formData = Object.assign({}, this.$refs['queryForm'].form)
      const sendData = {
        page_size: this.tableData.pageSize,
        page_num: this.tableData.currentPage,
        // page_direction: this.tableData.pageDirection,
        item_name: formData.item_name,
        item_code: formData.item_code,
        division_code: formData.division_code,
        credit_code: formData.credit_code,
        compare_result_list: formData.compare_result_list,
      }
      getComparePage(sendData).then((res) => {
        if (res.meta.code === '200' && res.data !== null) {
          this.tableData.content = res.data.content
          this.tableData.total = Number(res.data.total_elements)
        }
      })
    },
    // 获取最近同步时间
    getCompareMaxDate() {
      getCompareMaxDate().then((res) => {
        console.log('getCompareMaxDate', res)
        if (res.meta.code === '200' && res.data !== null) {
          this.compareTime = res.data
        }
      })
    },
    goDetail(row) {
      this.$router.push({ name: 'mattersComparisonManageDetail', query: { itemCode: row.itemCode } })
    },
  },
  mounted() {
    this.getCompareMaxDate()
    this.query()
  },
}
</script>

<style>
.content-wrapper {
  padding: 10px;
}
</style>