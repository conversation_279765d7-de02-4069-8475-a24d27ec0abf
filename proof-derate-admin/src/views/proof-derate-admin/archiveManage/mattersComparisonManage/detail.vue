<template>
  <div class="content-wrapper padding-10">
    <CardTitle :title-name="title" :ifback="true" @back="back()">
      <template>
        <el-button type="primary" v-if="tipStatus === 'DIFF' && sameVersion" v-permission="'catalog:archives:compare:update'" @click="upDate">更新</el-button>
      </template>
    </CardTitle>
    <el-card class="box-card">
      <div class="tip-alert" :class="tipStatus === 'SAME' ? 'success' : 'DIFF'">
        <i class="el-icon-info" v-if="tipStatus === 'DIFF'"></i>
        <i class="el-icon-success" v-if="tipStatus === 'SAME'"></i>
        <span class="tip-word" v-if="tipStatus === 'DIFF' && !sameVersion"> 对比结果不一致。事项版本不一致，请更新事项数据。</span>
        <span class="tip-word" v-if="tipStatus === 'DIFF' && sameVersion"> 对比结果不一致。请前往事项系统修订。</span>
        <span class="tip-word" v-if="tipStatus === 'SAME'"> 对比结果一致。无需处理。</span>
        <span class="update" v-if="tipStatus === 'DIFF' && !sameVersion" @click="toUpdate">去更新>></span>
      </div>
      <span class="margin-left-10 info-wrap">
        <img :src="arrow" alt />
        <span class="info-title">事项基本信息</span>
      </span>
      <el-descriptions class="descriptions" title :column="2" border>
        <el-descriptions-item :label-style="{ width: '140px' }">
          <template slot="label">事项名称</template>
          {{ dataContent.item_name }}
        </el-descriptions-item>
        <el-descriptions-item :label-style="{ width: '140px' }">
          <template slot="label">事项编码</template>
          {{ dataContent.item_code }}
        </el-descriptions-item>
        <el-descriptions-item :label-style="{ width: '140px' }">
          <template slot="label">实施机构</template>
          {{ dataContent.impl_org_name }}
        </el-descriptions-item>
        <el-descriptions-item :label-style="{ width: '140px' }">
          <template slot="label">事项类型</template>
          {{ dataContent.item_type_name }}
        </el-descriptions-item>
        <el-descriptions-item :label-style="{ width: '140px' }">
          <template slot="label">最新对比时间</template>
          {{ dataContent.last_update_date }}
        </el-descriptions-item>
        <el-descriptions-item :label-style="{ width: '140px' }">
          <template slot="label">事项版本</template>
          {{ dataContent.version_desc }}
        </el-descriptions-item>
      </el-descriptions>
      <span class="margin-left-10 info-wrap">
        <img :src="arrow" alt />
        <span class="info-title">材料对比结果</span>
      </span>
      <custom-table ref="table1" :is-card-type="false" :table-data="tableDataData" :span-method="objectSpanMethod" :table-header="tableHeaderData">
        <template #material_compare_status="{ row }">
          <span :class="row.material_compare_status === 'SAME' ? 'compareStatusSuccess' : 'compareStatusFail'">{{
            row.material_compare_status === 'SAME' ? '一致' : row.material_compare_status === 'WAIT_UPDATE' ? '待更新' : '不一致'
          }}</span>
        </template>
      </custom-table>
    </el-card>
  </div>
</template>
<script>
import CardTitle from '@/components/CardTitle'
import CustomTable from '@/components/Element/Table'
import { getCompareDetail, updateCompare } from '@/api/itemBiz/clear'
import { getMattersTypeList } from '@/api/common/dict'
export default {
  components: {
    CardTitle,
    CustomTable,
  },
  data() {
    return {
      title: '初级中学、小学、幼儿园教师资格认定',
      arrow: require('@/assets/proof-derate-admin-images/arrow.png'),
      tableDataData: {
        border: false,
        content: [], // 表格数据
        loading: false, // 控制表格加载效果
        total: 0, // 分页查询时的总数，去掉该属性，不显示分页条
        currentPage: 1, // 当前页码
        pageSize: 10,
        pageDirection: 'desc',
        isShowIndex: true,
        isShowSelection: false, // 是否显示多选框，默认false
      },
      tableHeaderData: [
        {
          label: '材料名称',
          prop: 'material_name',
          minWidth: '200px',
          align: 'left',
        },
        {
          label: '无证明系统实行措施',
          prop: 'replace_cancel_way_cn',
          minWidth: '50px',
          align: 'left',
        },
        {
          label: '事项系统实行措施',
          prop: 'system_cancel_way',
          minWidth: '50px',
          align: 'left',
        },
        {
          label: '对比结果',
          prop: 'material_compare_status',
          slot: 'material_compare_status',
          minWidth: '50px',
          align: 'left',
        },
        {
          label: '建议方案',
          prop: 'suggest',
          minWidth: '200px',
          align: 'left',
        },
      ],
      tipStatus: 'DIFF',
      sameVersion: false, // 事项版本是否一致
      dataContent: {
        item_code: '',
        item_name: '',
        credit_code: '',
        impl_org_name: '',
        item_compare_status: '',
        item_version: '',
        item_type: '',
        item_type_name: '',
      },
      mattersTypeList: [],
    }
  },
  methods: {
    getMattersTypeList() {
      return getMattersTypeList()
        .then((res) => {
          const data = res.data || []
          this.mattersTypeList.push(...data)
          console.log('this.mattersTypeList', this.mattersTypeList)
        })
        .catch(() => {})
    },
    back() {
      this.$router.go(-1)
    },
    toUpdate() {
      // console.log('this.dataContent', this.dataContent)
      this.$router.push({
        name: 'synchronizationInfo',
        query: { item_code: this.dataContent.item_code, middle_item_id: this.dataContent.middle_item_id, synchronize_status: 'WAIT_SYNCHRONIZE' },
      })
    },
    upDate() {
      this.updateCompare()
    },
    getCompareDetail() {
      let data = {
        item_code: this.$route.query.itemCode,
      }
      getCompareDetail(data).then((res) => {
        if (res.meta.code === '200' && res.data !== null) {
          this.dataContent = res.data
          this.title = this.dataContent.item_name

          let resData = _.orderBy(res.data.detail_list, 'material_id', 'asc')

          // const counts = _.countBy(res.data.detail_list)
          // const duplicateValues = _.keys(_.pickBy(counts, (count, element) => count > 1))
          // console.log(counts) // 输出重复值的列表 ['2', '1']
          // this.findDuplicateElements(res.data.detail_list)

          
          // let materialIdList = []
          // let detailList = resData
          // detailList.forEach((e) => {
          //   console.log(e)
          //   if (materialIdList.indexOf(e.material_id) === -1) {
          //     materialIdList.push(e.material_id)
          //   }
          // })
          // console.log('materialIdList', materialIdList)
          // let allList = []
          // materialIdList.forEach((e1, index) => {
          //   let itemIndex = 0
          //   detailList.forEach((e) => {
          //     console.log(materialIdList.indexOf(e.material_id))
          //     if (e1 === e.material_id) {
          //       // console.log('e1', e1)
          //       itemIndex = itemIndex + 1
          //       // console.log('itemIndex', itemIndex)
          //       // e.itemIndex = itemIndex
          //       let data = {
          //         key: e.material_id,
          //         itemIndex: itemIndex,
          //       }
          //       allList.push(data)
          //     }
          //   })
          // })
          // console.log('detailList', detailList)
          // console.log('allList', allList)

          // let allList1 = _.uniqBy(_.orderBy(allList, 'itemIndex', 'desc'), 'key')
          // console.log('allList1', allList1)
          // let itemList = []
          // allList1.forEach((e,index) => {
          //   resData.forEach((e1) => {
          //     if (e1.material_id === e.key) {
          //       console.log(11)
          //       if (itemList.find((i) => i.material_id === e1.material_id) === undefined) {
          //         e1.rowSpan = e.itemIndex
          //         itemList.push(e1)
          //       }
          //     }
          //   })
          // })
          // console.log('resData', resData)

          this.tableDataData.content = resData
          this.tipStatus = this.dataContent.item_compare_status
          this.sameVersion = this.dataContent.same_version
          // this.tipStatus = 'DIFF'
          console.log('this.dataContent.item_type', this.dataContent.item_type)
          this.dataContent.item_type_name = this.mattersTypeList.find((i) => i.value === this.dataContent.item_type).label

          console.log('this.dataContent.item_type_name', this.dataContent.item_type_name)
        }
      })
    },
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      // console.log('row', row)
      // columnIndex 为第几列
      // rowspan 为要合并的列数
      // colspan 为要合并的行数
      // 如果不合并 返回rowspan和colspan 为0
      // if (columnIndex == 1 || columnIndex == 0) {
      //   if (row.rowSpan !== undefined) {
      //     return {
      //       rowspan: row.rowSpan,
      //       colspan: 1,
      //     }
      //   } else {
      //     return {
      //       rowspan: 0,
      //       colspan: 0,
      //     }
      //   }
      // }
    },
    updateCompare() {
      let data = {
        item_code: this.$route.query.itemCode,
      }
      updateCompare(data).then((res) => {
        if (res.meta.code === '200' && res.data !== null) {
          this.$message({
            message: '更新成功！',
            type: 'success',
          })
          this.initData()
        }
      })
    },
    initData: async function () {
      this.getMattersTypeList().then(() => {
        this.getCompareDetail()
      })
    },
  },
  mounted() {
    this.initData()
  },
}
</script>
<style lang="scss" scoped>
.info-wrap {
  display: flex;
  align-items: center;
  margin-top: 20px;
  // margin-bottom: 10px;
  img {
    margin-right: 10px;
  }
  .info-title {
    font-size: 20px;
    color: #333333;
  }
}
.tip-alert {
  width: 100%;
  height: 35px;
  padding: 8px 16px;
  margin-left: 5px;
  margin-bottom: 5px;
  box-sizing: border-box;
  border-radius: 4px;
  position: relative;
  overflow: hidden;
  opacity: 1;
  display: flex;
  align-items: center;
  transition: opacity 0.2s;
  background-color: rgba(255, 235, 204, 1);
  color: #fff;
  i {
    margin-right: 10px;
    color: #e6a23c;
  }
  .el-icon-success {
    color: #1f9e73;
  }
}
.success {
  background-color: rgba(204, 245, 224, 1);
}
.tip-word {
  color: #666666;
}
.update {
  color: #01a463;
  cursor: pointer;
}
.descriptions {
  margin-top: 20px;
}
.compareStatusSuccess {
  color: #01a463;
}
.compareStatusFail {
  color: #ff5a5a;
}
</style>