<template>
  <div class="content-wrapper">
    <!-- <papeTitle :title-name="titleName" :is-has-back="false">
      <span>
        <el-button type="primary" @click="exportProofList('search')" v-permission="'catalog:archives:proof_list:export'">导出清单</el-button>
      </span>
    </papeTitle>-->
    <CardTitle :title-name="titleName">
      <template>
        <el-button
          v-permission="'catalog:archives:proof_list:export'"
          type="primary"
          :loading="exportProofListLoading"
          @click="exportProofList('search')"
        >导出清单</el-button>
      </template>
    </CardTitle>
    <!-- <section class="content"> -->
    <el-card class="box-card" shadow="never" :body-style="{'padding-bottom':'0px'}">
      <query-form ref="queryForm" style="padding:0 10px" @click="search" />
      <div style="color: #888; padding:20px 10px 0" class="dashed-line">
        共
        <span class="text-red">{{ tableData.total }}</span> 条符合查询条件
      </div>
      <custom-table
        ref="table"
        :is-card-type="false"
        :table-data="tableData"
        :table-header="tableHeader"
        :span-method="objectSpanMethod"
        :stripe="false"
        :table-tools="tableTools"
        style="margin-top: 10px"
        @refresh="query(1)"
        @query="query"
      >
        <template #item_name="{ row }">
          <div>
            <!-- <el-button type="text" @click="goProofFile(row)" >{{ row.handing_item === null || val === undefined ? row.item_name : row.item_name + '【' + row.handing_item + '】' }}</el-button> -->
            {{ row.handing_item === null || val === undefined ? row.item_name : row.item_name + '【' + row.handing_item + '】' }}
          </div>
          <!-- <div v-else>{{ row.handing_item === null || val === undefined ? row.item_name : row.item_name + '【' + row.handing_item + '】' }}</div> -->
        </template>
        <!-- v-if="isPermission(row.permission_codes,'catalog:archives:proof_list:view')" -->
        <template #operate="{ row }">
          <div>
            <el-button v-permission="'catalog:archives:proof_list:view'" type="text" @click="goItemProofFile(row)">查看</el-button>
          </div>
        </template>
      </custom-table>
    </el-card>
    <!-- </section> -->
  </div>
</template>

<script>
import QueryForm from '@/views/proof-derate-admin/archiveManage/itemList/components/QueryForm'
import { exportProofList } from '@/api/itemBiz/list'
import { getProoListPage, getProoListPageCount } from '@/api/itemBiz/clear'
import CustomTable from '@/components/Element/Table'
import Enum from '@/utils/enum'
import papeTitle from '@/components/papeTitle'
import { isPermission, exportsDown } from '@/utils/index.js'
import CardTitle from '@/components/CardTitle'
import { getProofStatusList, getStandardTypeList, getItemMaterialStandardType, getMattersTypeList } from '@/api/common/dict'
export default {
  name: 'MattersCheck',
  components: {
    QueryForm,
    CustomTable,
    papeTitle,
    CardTitle
  },
  data() {
    return {
      proofStatusList: [], // 事项证明状态
      proofStatusAllList: [], // 所有的事项证明状态
      item_source: [], // 事项标准类型
      item_material_source: [], // 证明标准类型
      formData: {
        sxmc: '', // 事项名称
        blxmc: '', // 办理项名称
        zmmc: '', // 证明名称
        zmslzt: '', // 证明梳理状态
        divisionCode: '', // 实施区划
        orgCode: '', // 实施机构
        standardSubject: [], // 事项标准类型
        standard: [], // 证明标准类型
        item_type: []
      },
      mattersTypeList: [],
      loading: false,
      tableData: {
        border: false,
        content: [], // 表格数据
        loading: false, // 控制表格加载效果
        total: 0, // 分页查询时的总数，去掉该属性，不显示分页条
        currentPage: 1, // 当前页码
        pageSize: 10,
        pageDirection: 'desc',
        isShowIndex: true,
        isShowSelection: false // 是否显示多选框，默认false
      },
      tableTools: [],

      tableHeader: [
        {
          label: '事项名称',
          slot: 'item_name',
          prop: 'item_name',
          minWidth: '200px',
          align: 'left',
          formatter: (row, col, val) => {
            return row.handing_item === null || val === undefined ? row.item_name : row.item_name + '【' + row.handing_item + '】'
          }
        },
        { label: '事项编码', prop: 'item_code', minWidth: '180px', align: 'left' },
        { label: '办理项名称', prop: 'handing_item', minWidth: '180px', align: 'left' },
        { label: '实施机构', prop: 'impl_org_name', minWidth: '160px', align: 'left' }, // 配置slot属性，可支持使用插槽
        // { label: "证明名称", prop: "material_name", minWidth: "180px" },
        {
          label: '事项证明状态',
          prop: 'item_clear_status',
          minWidth: '80px',
          align: 'left',
          formatter: (row, col, val) => {
            return Enum.proofStatusList.find(i => i.value === val).label
          }
        },
        { label: '操作', slot: 'operate', width: '120px', fixed: 'right', align: 'left' }
      ],
      showDetail: false,
      detailTabelData: [],
      detailId: '',
      align: 'center',
      userdata: {}, // 用户角色与权限
      titleName: '事项证明清单',
      exportProofListLoading: false
    }
  },
  mounted: function() {
    this.initData()
    this.getMattersTypeList()
    const { roles, rolePermissionVos } = this.$store.getters.userdata
    this.userdata = { roles, rolePermissionVos }
  },
  beforeRouteLeave(to, from, next) {
    from.meta.keepAlive = false
    next()
  },
  methods: {
    isPermission,
    getMattersTypeList() {
      getMattersTypeList()
        .then(res => {
          const data = res.data || []
          this.mattersTypeList.push(...data)
          this.mattersTypeList.forEach(e => {
            this.formData.item_type.push(e.value)
          })
        })
        .catch(() => {
          Enum.mattersTypeList.splice(0)
        })
    },
    initData: async function() {
      const proofStatusListRes = await getProofStatusList()
      const standardTypeListRes = await getStandardTypeList()
      const itemMaterialStandardTypeRes = await getItemMaterialStandardType()
      this.getProofStatusList(proofStatusListRes)
      this.getStandardTypeList(standardTypeListRes)
      this.getItemMaterialStandardType(itemMaterialStandardTypeRes)
      // 被await阻塞的同步代码
      this.query(1, 'init')
    },
    getProofStatusList(res) {
      let data = res.data || []
      this.proofStatusAllList = data
      data = data.filter(i => {
        return i.value === 'CARDING_UNCONFIRMED' || i.value === 'CARDING_CONFIRMED' || i.value === 'APPROVED' || i.value === 'WAIT_FOR_CLEAN' || i.value === 'TRANSFERRED'
      })
      Enum.proofStatusList.splice(0)
      Enum.proofStatusList.push(...data)
      const proofStatusList = [] // 事项证明状态
      _.forEach(Enum.proofStatusList, item => {
        proofStatusList.push(item.value)
      })
      this.proofStatusList = proofStatusList
    },
    getStandardTypeList(res) {
      const data = res.data || []
      Enum.standardTypeList.splice(0)
      Enum.standardTypeList.push(...data)

      const item_source = [] // 事项标准类型
      _.forEach(Enum.standardTypeList, item => {
        item_source.push(item.value)
      })
      this.item_source = item_source
    },
    getItemMaterialStandardType(res) {
      const data = res.data || []
      Enum.proofStandardTypeList.splice(0)
      Enum.proofStandardTypeList.push(...data)
      const item_material_source = [] // 证明标准类型
      _.forEach(Enum.proofStandardTypeList, item => {
        item_material_source.push(item.value)
      })
      this.item_material_source = item_material_source
    },
    search(data) {
      this.query(1, 'search', data)
    },
    exportProofList(type) {
      this.exportProofListLoading = true
      const formData = Object.assign({}, this.$refs['queryForm'].form)
      const proofStatusList = type === 'init' ? this.proofStatusList : formData['item_clear_status']
      const item_source = type === 'init' ? this.item_source : formData['item_source']
      console.log(item_source, 'item_source')
      const item_material_source = type === 'init' ? this.item_material_source : formData['item_material_source']
      const item_type = type === 'init' ? this.formData.item_type.join(',') : formData['item_type'].join(',')
      const sendData = {
        ...formData,
        item_type,
        item_clear_status: proofStatusList,
        // item_material_source: item_material_source,
        item_material_source: '',
        item_source: item_source
      }

      Object.keys(sendData).forEach(item => {
        if (!sendData[item]) delete sendData[item]
      })
      if (!sendData['item_clear_status']) {
        sendData['item_clear_status'] = 'CARDING_UNCONFIRMED,CARDING_CONFIRMED,APPROVED'
      }
      exportsDown(exportProofList(), sendData, '事项证明清单.xls')
      const that = this
      setTimeout(function() {
        that.exportProofListLoading = false
      }, 20000)
      /* exportProofList( sendData ).then( res => {
        this.exportProofListLoading = false
        const link = document.createElement('a')
        const blob = new Blob([res], {
          type: 'application/vnd.ms-excel'
        })
        link.style.display = 'none'
        link.href = URL.createObjectURL(blob)

        link.setAttribute('download', '事项证明清单.xls')
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        this.$message.success('导出成功')
      }).catch(() => {
        this.exportProofListLoading = false
      }) */
    },
    // currentPage: 当前页码
    query(currentPage, type, data) {
      this.tableData.loading = true
      // if (_.isNumber(currentPage)) {
      //   this.tableData.currentPage = currentPage
      // }
      const formData = Object.assign({}, this.$refs['queryForm'].form)
      const proofStatusList = type === 'init' ? this.proofStatusList.join() : formData['item_clear_status'].join()
      const item_source = type === 'init' ? this.item_source.join() : formData['item_source'].join()
      const item_material_source = type === 'init' ? this.item_material_source.join() : formData['item_material_source'].join()
      const item_type = type === 'init' ? this.formData.item_type.join(',') : formData['item_type'].join(',')
      const sendData = {
        ...formData,
        item_type,
        page_size: this.tableData.pageSize,
        page_number: this.tableData.currentPage,
        page_direction: this.tableData.pageDirection,
        item_clear_status: proofStatusList,
        // item_material_source: item_material_source,
        item_material_source: '',
        item_source: item_source
      }

      Object.keys(sendData).forEach(item => {
        if (!sendData[item]) delete sendData[item]
      })
      if (!sendData['item_clear_status']) {
        sendData['item_clear_status'] = 'CARDING_UNCONFIRMED,CARDING_CONFIRMED,APPROVED'
      }
      getProoListPage(sendData)
        .then(res => {
          const data = res.data === null ? [] : res.data.content
          this.tableData.content = data
          this.tableData.loading = false
          this.tableData.total = Number(res.data.total_elements)
        })
        .catch(() => {
          this.tableData.loading = false
          console.log('==证明材料查询error==')
        })
      // getProoListPageCount(sendData)
      //   .then(res => {
      //     // this.tableData.loading = false
      //     this.tableData.total = Number(res.data.total_elements)
      //   })
      //   .catch(() => {
      //     // this.tableData.loading = false
      //   })
    },
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      const span = column['property'] + '-span'
      if (row[span]) {
        return row[span]
      }
    },
    verify(row) {
      this.$router.push({
        path: '/mattersProof/verify',
        query: { id: row.id }
      })
    },
    // 事项证明档案
    goItemProofFile(row) {
      // this.$router.push({
      //   name: 'item_list_info_way',
      //   query: {
      //     id: row.item_code,
      //     item_clear_status: 'DO_NOT_CLEAN',
      //     type: 'show'
      //   }
      // })
      const routeData = this.$router.resolve({
        name: 'item_list_info_way',
        query: {
          id: row.item_code,
          item_clear_status: 'DO_NOT_CLEAN',
          type: 'show'
        }
      })
      window.open(routeData.href, '_blank')
    },
    // 跳转到证明档案
    goProofFile(row) {
      this.showDetail = false
      this.$router.push({
        name: 'item_list_info_proof',
        query: {
          id: row.item_code,
          item_clear_status: 'CLEAN'
        }
      })
    }
  }
}
</script>

<style scoped>
.content-wrapper {
  padding: 10px;
}
.content-wrapper /deep/ .test-class {
  overflow: initial;
}
</style>
