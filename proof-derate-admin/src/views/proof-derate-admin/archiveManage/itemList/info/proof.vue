<template>
  <div class="content-wrapper">
    <section class="content-header">
      <span class="breadcrumb" align="right">
        <el-button type="warning" plain @click="backPrev" icon="el-icon-back">返回</el-button>
      </span>
      <br />
    </section>
    <section class="content">
      <proof-file :data="data" proofRoute="item_list_info_way" />
    </section>
  </div>
</template>

<script>
import ProofFile from "@/views/proof-derate-admin/components/ProofFile";
export default {
  name: "ItemListInfo",
  components: {
    ProofFile
  },
  data() {
    return {
      data:{
        title: "事项证明档案",
        itemCode: "",
        itemClearStatus: "",
        pageType: "",
      }
    };
  },
  beforeRouteLeave(to, from, next) {
    if (to.name == "item_clear") {
      to.meta.keepAlive = true;
    } else {
      to.meta.keepAlive = false;
    }
    next();
  },
  created() {
    this.data = {
      itemCode : this.$route.query["id"],
      itemClearStatus : this.$route.query["item_clear_status"],
      pageType : this.itemClearStatus
    }
  },
  methods: {
    backPrev() {
      this.$router.go(-1);
    }
  }
};
</script>

<style lang="scss" scoped>

</style>