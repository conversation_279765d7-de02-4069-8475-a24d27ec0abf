<template>
  <div class="content-wrapper dataSharingManagement padding-10">
    <CardTitle :title-name="titleName">
      <template>
        <el-button v-permission="'catalog:data_manage:data:add'" type="primary" icon="el-icon-plus" @click="add">新建</el-button>
        <el-button v-permission="'catalog:data_manage:data:import'" type="primary" icon="el-icon-plus" @click="goProofCatalog">导入</el-button>
      </template>
    </CardTitle>
    <!-- <section class="content"> -->
    <el-card class="box-card">
      <el-form ref="sysForm" :model="dataThemeForm" label-width="120px">
        <el-row :gutter="24" type="flex">
          <el-col :span="20">
            <el-form-item label="数据主题名称:">
              <el-input v-model="dataThemeForm.data_theme_name" clearable placeholder="数据主题名称" />
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-button type="primary" @click="getCatalogDataSharedConfigPage">查询</el-button>
          </el-col>
        </el-row>
      </el-form>
      <div class="table">
        <div style="color: #888; padding:20px 10px 0" class="dashed-line">
          共
          <span class="text-red">{{ dataSubjectData.content.length }}</span> 条符合查询条件
        </div>
        <custom-table
          ref="table1"
          :is-card-type="false"
          :table-data="dataSubjectData"
          :table-header="dataSubjecHeaderData"
          @query="query"
          @refresh="query(1)"
        >
          <template #operate="{row}">
            <div>
              <el-button v-permission="'catalog:data_manage:data:view'" type="text" @click="getDetail(row)">查看</el-button>
              <el-button v-permission="'catalog:data_manage:data:update'" type="text" @click="edit(row)">编辑</el-button>
              <el-button v-permission="'catalog:data_manage:data:delete'" type="text" style="color:red" @click="delect(row)">删除</el-button>
            </div>
          </template>
        </custom-table>
      </div>
    </el-card>
    <!-- </section> -->
    <el-dialog title="导入文件" :visible.sync="dialogFormVisible" width="50%" center>
      <uploadDialog
        ref="uploadDialog"
        :template="templateFlag"
        :limit-file-type="limitFileType"
        :whitelist="whitelist"
        @toFather="download"
      />

      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="importFlie">导入</el-button>
        <el-button @click="dialogFormVisible = false">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import CustomTable from '@/components/Element/Table'
import uploadDialog from './uploadDialog'
import { getCatalogDataSharedConfigPage, catalogDataSharedConfigDelete, themeImport } from '@/api/sharedConfig'
import papeTitle from '@/components/papeTitle'
import CardTitle from '@/components/CardTitle'
export default {
  components: {
    CustomTable,
    uploadDialog,
    papeTitle,
    CardTitle
  },
  data() {
    return {
      sysForm: {
        data_theme_name: ''
      },
      sysOptions: [],
      dialogFormVisible: false,
      templateFlag: true,
      dataSubjectData: {
        border: false,
        loading: false, // 控制表格加载效果
        total: 0, // 分页查询时的总数，去掉该属性，不显示分页条
        currentPage: 1, // 当前页码
        pageSize: 10,
        maxHeight: '800px',
        pageDirection: 'desc',
        isShowIndex: true,
        multiple: false, // 是否多选 数据需要有id 属性值
        isShowSelection: false, // 是否显示多选框，默认false
        content: []
      },
      dataSubjecHeaderData: [
        {
          label: '数据主题名称',
          prop: 'data_theme_name',
          minWidth: '200px',
          align: 'left'
        },
        {
          label: '系统名称',
          prop: 'system_name',
          minWidth: '200px',
          align: 'left'
        },
        {
          label: '状态',
          prop: 'data_theme_status',
          align: 'left',
          formatter: (row, col, val) => {
            return row.data_theme_status === null || row.data_theme_status === undefined ? '' : row.data_theme_status === 'ENABLE' ? '启用' : '禁用'
          },
          minWidth: '200px'
        },
        {
          label: '最后更新时间',
          prop: 'last_modification_time',
          align: 'left',
          minWidth: '200px'
        },
        {
          label: '操作',
          prop: 'operate',
          slot: 'operate',
          align: 'left',
          minWidth: '200px'
        }
      ],
      limitFileType: '.xlsx,.xls',
      whitelist: ['xlsx', 'xls'],
      dataThemeForm: {
        data_theme_name: '',
        page_number: '1',
        page_size: '10',
        fieldName: ''
        // orgList: ''
      },
      titleName: '数据主题管理'
    }
  },

  mounted() {
    this.getCatalogDataSharedConfigPage()
  },

  methods: {
    selectChange() {},
    selectionChange() {},
    query() {},
    importFlie(data) {
      if (this.$refs.uploadDialog.input) {
        const file = this.$refs.uploadDialog.file.raw
        const formData = new FormData()
        formData.append('file', file)
        themeImport(formData).then(res => {
          if (res.meta.code == '200') {
            this.$message({
              message: '导入成功',
              type: 'success'
            })
            this.dialogFormVisible = false
            this.getCatalogDataSharedConfigPage()
          } else {
            this.$message({
              message: '导入失败',
              type: 'error'
            })
            this.dialogFormVisible = false
          }
        })
      } else {
        this.$message({
          message: '请选择文件，再导入！',
          type: 'warning'
        })
      }
    },
    download(str) {},
    getCatalogDataSharedConfigPage() {
      getCatalogDataSharedConfigPage(this.dataThemeForm).then(res => {
        if (res.meta.code === '200' && res.data != null) {
          this.dataSubjectData.content = res.data.content
          this.total = res.data.totalElements
          this.dataSubjectData.total = res.data.totalElements
        }
      })
    },
    edit(row) {
      console.log(row)
      this.$router.push({
        name: 'DataSharingManagementEdit',
        query: {
          type: 'edit',
          id: row.id
        }
      })
    },
    delect(row) {
      this.$confirm('是否删除该数据, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          catalogDataSharedConfigDelete({ id: row.id }).then(res => {
            this.$message({
              type: 'success',
              message: '删除成功!'
            })
            this.getCatalogDataSharedConfigPage()
          })
        })
        .catch(() => {})
    },
    getDetail(row) {
      this.$router.push({
        name: 'DataSharingManagementDetail',
        query: {
          id: row.id
        }
      })
    },
    add() {
      this.$router.push({
        name: 'DataSharingManagementEdit',
        query: {
          type: 'add'
        }
      })
    },
    // 新增证明目录页面
    goProofCatalog() {
      this.dialogFormVisible = true
      if (this.$refs.uploadDialog) {
        this.$refs.uploadDialog.clearFiles()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.padding-10 {
  padding: 10px;
}
</style>
