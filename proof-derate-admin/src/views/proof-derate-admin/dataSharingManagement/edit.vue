<template>
  <div class="content-wrapper dataSharing padding-10">
    <CardTitle :title-name="title" :ifback="true" @back="back()">
      <template>
        <el-button type="primary" icon="el-icon-edit" @click="saveSharingData">保存</el-button>
      </template>
    </CardTitle>
    <!-- <section class="content"> -->
    <el-card class="box-card">
      <el-form ref="sysForm" :model="sysForm" label-width="120px">
        <el-row :gutter="24" type="flex">
          <el-col :span="22">
            <el-form-item label="系统名称:" prop="system_code" :rules="[{ required: true, message: '请输入系统名称', trigger: 'change' }]">
              <el-select v-model="sysForm.system_code" placeholder="请选择" class="select" @change="selectChange">
                <el-option v-for="item in sysOptions" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24" type="flex">
          <el-col :span="22">
            <el-form-item label="数据主题名称:" prop="data_theme_name" :rules="[{ required: true, message: '请输入数据主题名称', trigger: 'change' }]">
              <el-input v-model="sysForm.data_theme_name" clearable placeholder="数据主题名称" maxlength="100" :show-word-limit="false" />
            </el-form-item>
          </el-col>
          <!-- <el-col :span="2">
              <el-button type="text" @click="dialogDataVisible=true">选择主题>></el-button>
          </el-col>-->
        </el-row>
        <el-row :gutter="24" type="flex">
          <el-col :span="22">
            <el-form-item label="接口访问地址:" prop="system_api_url" :rules="[{ required: true, message: '请输入接口访问地址', trigger: 'change' }]">
              <el-input v-model="sysForm.system_api_url" clearable placeholder="接口访问地址" maxlength="500" :show-word-limit="false" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24" type="flex">
          <el-col :span="22">
            <el-form-item label="接口编码:" prop="data_theme_code" :rules="[{ required: true, message: '请输入接口编码', trigger: 'change' }]">
              <el-input v-model="sysForm.data_theme_code" clearable placeholder="接口编码" maxlength="500" :show-word-limit="false" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div class="table-title">
        <!-- <span>数据项</span> -->
        <span class="margin-left-10 info-wrap">
          <img :src="arrow" alt />
          <span class="info-title">数据项</span>
        </span>
        <div>
          <!-- <el-checkbox :indeterminate="isIndeterminate" v-model="checked" @change="setAllCheck"></el-checkbox> -->
          <!-- <span class="check-text">全选/全不选</span> -->
          <i class="el-icon-circle-plus-outline plusicon" @click="addDataItem" />
        </div>
      </div>
      <custom-table ref="table1" :is-card-type="false" :table-data="tableDataData" :table-header="tableHeaderData">
        <template #display_name="{row,$index}">
          <el-form :ref="`zhNameform+${$index}`" :model="row" class="zhNameform">
            <el-form-item ref="display_name" prop="display_name" :rules="[{ required: true, message: '请输入中文名称', trigger: 'change' }]">
              <el-input v-model="row.display_name" placeholder="请输入内容" />
            </el-form-item>
          </el-form>
        </template>
        <template #data_item_name="{row,$index}">
          <el-form :ref="`fieldName+${$index}`" :model="row" class="fieldName">
            <el-form-item ref="data_item_name" prop="data_item_name" :rules="[{ required: true, message: '请输入字段名称', trigger: 'change' }]">
              <el-input v-model="row.data_item_name" placeholder="请输入内容" />
            </el-form-item>
          </el-form>
        </template>
        <template #data_type="{$index,row}">
          <!-- <el-input v-model="row.data_type" disabled placeholder="请输入内容"></el-input> -->
          <el-select v-model="row.data_type" placeholder="请选择" @change="dataTypeNameChange($index,row)">
            <el-option v-for="item in dataTypeNameList" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </template>
        <template #data_range_mapping="{row}">
          <el-input
            v-model="row.data_range_mapping"
            type="textarea"
            :disabled="(row.data_type==='ENUM'||row.data_type==='BOOLEAN')?false:true"
            placeholder="请输入内容"
          />
        </template>
        <template #DISPLAY="{row}">
          <el-checkbox v-model="row.DISPLAY" />
        </template>
        <template #operation="{row,$index}">
          <el-button type="text" class="red" @click="delectItem(row,$index)">删除</el-button>
        </template>
      </custom-table>
      <div class="table-title">
        <!-- <span>查询条件</span> -->
        <span class="margin-left-10 info-wrap">
          <img :src="arrow" alt />
          <span class="info-title">查询条件</span>
        </span>
        <div>
          <i class="el-icon-circle-plus-outline plusicon" @click="addData" />
        </div>
      </div>
      <custom-table
        :id="refreshId"
        ref="table1"
        :is-card-type="false"
        :table-data="tableDataData1"
        :table-header="tableHeaderData1"
        @cell-mouse-enter="cellMouseEnter"
      >
        <template #data_item_name="{$index,row}">
          <el-form :ref="`dataItemNameform+${$index}`" :model="row" class="rangeform">
            <el-form-item
              ref="data_item_name"
              prop="data_item_name"
              :rules="[{ required: true, trigger: 'change',validator: validatePass, }]"
            >
              <el-select v-model="row.data_item_name" placeholder="请选择" @change="dataItemNameChange($index,row)">
                <el-option
                  v-for="item in dataItemLsit"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                  :disabled="item.disabled"
                />
              </el-select>
            </el-form-item>
          </el-form>
        </template>
        <template #condition_operation_symbol="{row,$index}">
          <el-form :ref="`conditionOperationSymbolform+${$index}`" :model="row" class="rangeform">
            <el-form-item
              ref="condition_operation_symbol"
              prop="condition_operation_symbol"
              :rules="[{ required: true, message: '请输入条件操作符', trigger: 'change' }]"
            >
              <el-select v-model="row.condition_operation_symbol" placeholder="请选择" @change="conditionChange(row,$index)">
                <el-option
                  v-for="item in conditionList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                  :disabled="item.disabled"
                />
              </el-select>
            </el-form-item>
          </el-form>
        </template>
        <template #condition_key="{row,$index}">
          <el-form :ref="`conditionKeyform+${$index}`" :model="row" class="rangeform">
            <el-form-item ref="condition_key" prop="condition_key" :rules="[{ required: true, message: '请输入', trigger: 'change' }]">
              <el-select v-model="row.condition_key" placeholder="请选择" @change="range1Change(row,$index)">
                <el-option v-for="item in valueList" :key="item.value" :label="item.label" :value="item.value" :disabled="item.disabled" />
              </el-select>
            </el-form-item>
          </el-form>
        </template>
        <template #condition_value="{row,$index}">
          <div>
            <!-- :ref="`range1form+${$index}+item`" -->
            <el-form :ref="`range1form+${$index}`" :model="row" class="rangeform">
              <el-form-item
                ref="condition_value"
                prop="condition_value"
                :rules="[{ required: true, message: '请输入值域1', trigger: 'change' }]"
              >
                <el-select
                  v-if="row.condition_key === 'CUSTOM'&& row.condition_operation_symbol === 'BETWEEN'&&row.data_type==='DATETIME'"
                  v-model="row.condition_value"
                  placeholder="请选择"
                >
                  <el-option v-for="item in dataRangeMappingTime" :key="item" :label="item" :value="item" />
                </el-select>
                <el-input v-else v-model="row.condition_value" :disabled="row.range1Disabled" placeholder="请输入内容" />
              </el-form-item>
            </el-form>
          </div>
        </template>
        <template #name4="{row,$index}">
          <el-select v-model="row.name4" placeholder="请选择" @change="range2Change(row,$index)">
            <el-option v-for="item in valueList" :key="item.value" :label="item.label" :value="item.value" :disabled="item.disabled" />
          </el-select>
        </template>
        <template #condition_value_other="{row,$index}">
          <!-- <el-input v-model="row.name5" :disabled="row.range2Disabled" placeholder="请输入内容"></el-input> -->
          <el-form :ref="`range2form+${$index}`" :model="row" label-width="0px" class="rangeform">
            <el-form-item
              ref="condition_value_other"
              prop="condition_value_other"
              :rules="[{ required: true, message: '请输入值域2', trigger: 'change' }]"
            >
              <el-input
                v-model="row.condition_value_other"
                :disabled="row.range2Disabled||row.condition_key === 'CUSTOM'&& row.condition_operation_symbol === 'BETWEEN'&&row.data_type==='DATETIME'"
                placeholder="请输入内容"
              />
            </el-form-item>
          </el-form>
        </template>
        <template #operation="{row,$index}">
          <el-button type="text" class="red" @click="delect(row,$index)">删除</el-button>
        </template>
      </custom-table>
      <div class="table-title">
        <!-- <span>查询条件</span> -->
        <span class="margin-left-10 info-wrap">
          <img :src="arrow" alt />
          <span class="info-title">更多设置</span>
        </span>
        <div>
          <!-- <i class="el-icon-circle-plus-outline plusicon" @click="addData" /> -->
        </div>
      </div>
      <div class="green-line" />
      <div class="bar-title">
        <div class="vertical-bar" />
        <span>排序设置</span>
      </div>
      <div class="sort-wrap">
        <p v-for="(i,key) in sortTypeOptions" :key="key">
          <el-radio v-model="sysForm.list_sort_type" :label="i.value">{{ i.label }}</el-radio>
          <span>说明：指在免征办模块数据共享查看页面，当存在多条数据时，展示的数据随机排序</span>
        </p>
        <div v-if="sysForm.list_sort_type==='CUSTOM_SORT'" class="sort-select">
          <div class="sort-select-label">排序数据项：</div>
          <div>
            <el-select v-model="sysForm.list_sort_type_data_item_name" placeholder="请选择">
              <el-option v-for="item in dataItemLsit" :key="item.value" :label="item.label" :value="item.value" :disabled="item.disabled" />
            </el-select>
          </div>
        </div>
        <div v-if="sysForm.list_sort_type==='CUSTOM_SORT'" class="sort-select">
          <div class="sort-select-label">排序规则：</div>
          <div>
            <el-select v-model="sysForm.list_sort_rule" placeholder="请选择">
              <el-option v-for="item in sortOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </div>
        </div>
      </div>
      <div class="bar-title">
        <div class="vertical-bar" />
        <span>查询次数设置</span>
      </div>
      <div class="sort-wrap">
        <div class="sort-select">
          <div class="sort-select-label">限制查询次数：</div>
          <div>
            <el-input v-model="sysForm.search_limit_count" placeholder="请输入内容" />
          </div>
          <span class="sort-select-word">次</span>
          <span>说明：指在免证办模块数据共享查询条件页面，限制用户查询次数。当查询次数超过规定次数，则不允许用户查询。</span>
        </div>
      </div>
    </el-card>
    <!-- </section> -->
    <el-dialog title="选择数据主题" :visible.sync="dialogDataVisible" class="dialogwrap" width="60%" :top="is1366==true?'1vh':'15vh'">
      <div class="wrap">
        <el-form :model="themeParam" label-width="120px">
          <el-row :gutter="24" type="flex">
            <el-col :span="20">
              <el-form-item label="数据主题名称">
                <el-input v-model="themeParam.data_theme_name" autocomplete="off" />
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-button type="primary" @click="getThemePageBysearch">查询</el-button>
            </el-col>
          </el-row>
          <el-row :gutter="24" type="flex">
            <el-col :span="20">
              <el-form-item label="部门">
                <el-input v-model="themeParam.data_theme_org" autocomplete="off" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24" type="flex">
            <el-col :span="20">
              <el-form-item label="表名">
                <el-input v-model="themeParam.data_shared_theme_code" autocomplete="off" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div class="table">
        <custom-table
          ref="table1"
          :is-card-type="false"
          :table-data="dataSubjectData"
          :table-header="dataSubjecHeaderData"
          @selection-change="selectionChange"
          @query="query"
          @refresh="query(1)"
        />
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogDataVisible = false">取 消</el-button>
        <el-button type="primary" @click="getThemeData()">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import CustomTable from '@/components/Element/Table'
import { getThemePage, getSystemSelectItem, getConfigDetail, getConfigDetailByid } from '@/api/certificationManagement/certificationList'
import { getQueryFilterOperationSelectItem, getConditionKeySelectItem, getDataSharedListSortRule, getDataSharedListSortType } from '@/api/common/dict'
import { create, find, edit } from '@/api/sharedConfig'
import CardTitle from '@/components/CardTitle'
export default {
  components: {
    CustomTable,
    CardTitle
  },
  data() {
    return {
      sysForm: {
        system_code: '',
        data_theme_code: '',
        data_theme_name: '',
        system_api_url: '',
        system_name: '',
        list_sort_type_data_item_name: '',
        list_sort_rule: 'DESC',
        list_sort_type: 'DEFAULT_SORT',
        search_limit_count: '3'
      },
      refreshId: 0,
      themeData: {},
      title: '编辑数据主题',
      sysOptions: [],
      checked: true,
      isChekcList: [],
      isIndeterminate: false,
      dialogDataVisible: false,
      dataItemLsit: [],
      dataForm: {
        dataSubject: '',
        department: ''
      },
      themeParam: {
        data_theme_name: '',
        data_theme_org: '',
        data_shared_theme_code: '',
        data_shared_system: '',
        page_number: '1',
        page_size: '10'
      },
      saveData: null,
      themeSelectData: '',
      conditionList: [],
      radio: '1',
      valueList: [
        {
          value: '1',
          label: '办事人姓名'
        },
        {
          value: '2',
          label: '办事人证件类型'
        },
        {
          value: '3',
          label: '办事人证件号码'
        },
        {
          value: '4',
          label: '办事单位名称'
        },
        {
          value: '5',
          label: '办事单位证件类型'
        },
        {
          value: '6',
          label: '办事单位证件号码'
        },
        {
          value: '7',
          label: '事项编码'
        },
        {
          value: '8',
          label: '事项名称'
        },
        {
          value: '9',
          label: '实施机构'
        },
        {
          value: '10',
          label: '实施机构的统一社会信用代码'
        },
        {
          value: '11',
          label: '实施机构的行政区划代码'
        },
        {
          value: '12',
          label: '自定义'
        }
      ],
      dataTypeNameList: [
        {
          value: 'STRING',
          label: '字符型'
        },
        {
          value: 'NUMBER',
          label: '数字型'
        },
        {
          value: 'INTEGER',
          label: '整数型'
        },
        {
          value: 'DATETIME',
          label: '日期型'
        },
        {
          value: 'BINARY',
          label: '二进制'
        },
        {
          value: 'BOOLEAN',
          label: '布尔值'
        },
        {
          value: 'TEXT',
          label: '文本型'
        },
        {
          value: 'TIMESTAMP',
          label: '时间戳'
        },
        {
          value: 'FLOAT',
          label: '浮点型'
        },
        {
          value: 'ENUM',
          label: '枚举型'
        }
      ],
      tableDataData: {
        content: [], // 表格数据
        loading: false, // 控制表格加载效果
        total: 0, // 分页查询时的总数，去掉该属性，不显示分页条
        currentPage: 1, // 当前页码
        pageSize: 10,
        pageDirection: 'desc',
        isShowIndex: true
      },
      tableHeaderData: [
        {
          label: '中文名称',
          prop: 'display_name',
          slot: 'display_name',
          minWidth: '200px'
        },
        { label: '数据项名称', prop: 'data_item_name', slot: 'data_item_name', minWidth: '200px' },
        {
          label: '数据类型',
          prop: 'data_type',
          slot: 'data_type',
          minWidth: '200px'
        },
        {
          label: '值域',
          prop: 'data_range_mapping',
          slot: 'data_range_mapping',
          minWidth: '300px'
        },
        {
          label: '是否显示',
          prop: 'DISPLAY',
          slot: 'DISPLAY',
          minWidth: '200px'
        },
        {
          label: '操作',
          prop: 'operation',
          slot: 'operation',
          minWidth: '200px'
        }
      ],
      tableDataData1: {
        content: [], // 表格数据
        loading: false, // 控制表格加载效果
        total: 0, // 分页查询时的总数，去掉该属性，不显示分页条
        currentPage: 1, // 当前页码
        pageSize: 10,
        pageDirection: 'desc',
        isShowIndex: true
      },
      tableHeaderData1: [
        {
          label: '数据项名称',
          prop: 'data_item_name',
          slot: 'data_item_name',
          minWidth: '200px'
        },
        {
          label: '数据类型',
          prop: 'data_type_name',
          minWidth: '200px',
          formatter: (row, col, val) => {
            return row.data_type === null || row.data_type === undefined ? '' : this.dataTypeNameList.find(i => i.value === row.data_type).label
          }
        },
        { label: '条件操作符', prop: 'condition_operation_symbol', slot: 'condition_operation_symbol', minWidth: '200px' },
        {
          label: '值1',
          prop: 'condition_key',
          slot: 'condition_key',
          minWidth: '200px'
        },
        {
          label: '值域1',
          prop: 'condition_value',
          slot: 'condition_value',
          minWidth: '200px'
        },
        {
          label: '值域2',
          prop: 'condition_value_other',
          slot: 'condition_value_other',
          minWidth: '200px'
        },
        {
          label: '操作',
          prop: 'operation',
          slot: 'operation',
          minWidth: '200px'
        }
      ],
      dataSubjectData: {
        loading: false, // 控制表格加载效果
        total: 0, // 分页查询时的总数，去掉该属性，不显示分页条
        currentPage: 1, // 当前页码
        pageSize: 10,
        maxHeight: '300px',
        pageDirection: 'desc',
        multiple: false, // 是否多选 数据需要有id 属性值
        isShowSelection: true, // 是否显示多选框，默认false
        content: []
      },
      dataSubjecHeaderData: [
        {
          label: '数据主题名称',
          prop: 'data_shared_theme_name',
          minWidth: '200px'
        },
        {
          label: '表名',
          prop: 'data_shared_theme_code',
          minWidth: '200px'
        },
        {
          label: '部门',
          prop: 'data_shared_org',
          minWidth: '200px'
        }
        // {
        //   label: '来源信息系统',
        //   prop: 'source_information_system',
        //   minWidth: '200px'
        // }
      ],
      is1366: false,
      dataRangeMappingTime: [],
      arrow: require('@/assets/proof-derate-admin-images/arrow.png'),
      options: [],
      sortOptions: [],
      sortTypeOptions: [],
      value: '',
      dataItemLsit: []
    }
  },
  watch: {
    'tableDataData.content': {
      handler(val) {
        this.dataItemLsit = val.map(e => {
          return { value: e.data_item_name, label: e.data_item_name, data_type: e.data_type, data_type_name: e.data_type_name }
        })
        this.refreshId = Math.random()
      },
      deep: true,
      immediate: true
    }
  },
  beforeRouteLeave(to, from, next) {
    // 设置下一个路由的 meta
    // console.log(to, from)
    // to.meta.keepAlive = false // 缓存
    if (to.name !== 'certification_List_add') {
      localStorage.setItem('dataCache', '')
    }
    next()
  },
  mounted() {
    this.isChekcList = this.tableDataData.content.filter(e => {
      return e.ifshow
    })
    this.screenWidth()
    this.getThemePage()
    this.getSystemSelectItem()
    this.getConditionKeySelectItem()
    this.getQueryFilterOperationSelectItem()
    this.getDataSharedListSortRule()
    this.getDataSharedListSortType()
    if (this.$route.query.dataSharType === 'edit') {
      this.getConfigDetailByid()
    }
    if (this.$route.query.type === 'add') {
      this.title = '新增数据主题'
    }
    if (this.$route.query.type === 'edit') {
      this.title = '编辑数据主题'
      this.find(this.$route.query.id)
    }
  },
  methods: {
    validatePass(rule, value, callback) {
      const list = []
      this.tableDataData1.content.forEach(e => {
        if (e.data_item_name === value) {
          list.push(e)
        }
      })
      if (value !== '') {
        if (list.length > 2 || list.length === 2) {
          callback(new Error('查询条件重复，请重新选择'))
        } else {
          callback()
        }
      } else {
        callback('请选择数据项名称')
      }
    },
    find(id) {
      find(id).then(res => {
        if (res.meta.code === '200' && res.data !== null) {
          this.themeData = res.data
          this.sysForm.system_code = this.themeData.system_code
          this.sysForm.data_theme_code = this.themeData.data_theme_code
          this.sysForm.data_theme_name = this.themeData.data_theme_name
          this.sysForm.system_api_url = this.themeData.system_api_url
          this.sysForm.system_name = this.themeData.system_name
          this.sysForm.list_sort_type_data_item_name = this.themeData.list_sort_type_data_item_name
          this.sysForm.list_sort_rule = this.themeData.list_sort_rule
          this.sysForm.list_sort_type = this.themeData.list_sort_type
          this.sysForm.search_limit_count = this.themeData.search_limit_count
          this.tableDataData.content = this.themeData.data_item_list
          this.themeData.data_item_list.forEach(e => {
            if (e.data_type === 'DATETIME' && e.condition_value != '') {
              this.dataRangeMappingTime = e.data_range_mapping.split('、')
            }
          })
          this.themeData.search_condition_list.forEach(e => {
            console.log('e.condition_operation_symbol', e.condition_operation_symbol, 'e.condition_key', e.condition_key)
            // 数据类型为日期型
            if (e.data_type === 'DATETIME' && e.condition_operation_symbol === 'BETWEEN' && e.condition_key === 'CUSTOM') {
              e.range1Disabled = false
              e.range2Disabled = true
            } else if (e.condition_operation_symbol === 'BETWEEN' && e.condition_key === 'CUSTOM') {
              e.range1Disabled = false
              e.range2Disabled = false
            } else if (e.condition_operation_symbol === 'EQUAL' && e.condition_key === 'CUSTOM') {
              e.range1Disabled = false
              e.range2Disabled = true
            } else {
              e.range1Disabled = true
              e.range2Disabled = true
            }
          })
          this.tableDataData1.content = this.themeData.search_condition_list
        }
      })
    },
    dataTypeNameChange($index, row) {
      console.log($index, row)
      row.data_type_name = this.dataTypeNameList.find(i => i.value === row.data_type).label
      if (row.data_type === 'DATETIME') {
        row.data_range_mapping = '不限、最近一周、最近一个月、最近三个月、最近半年、最近一年'
        this.dataRangeMappingTime = row.data_range_mapping.split('、')
      } else {
        row.data_range_mapping = ''
      }
    },

    dataItemNameChange(index, row) {
      // console.log(index, row)
      // console.log(this.dataItemLsit.find(i => i.value === row.data_item_name))
      const data = this.dataItemLsit.find(i => i.value === row.data_item_name)
      row.data_type = data.data_type
      row.data_type_name = data.data_type_name
      if (row.data_type === 'DATETIME') {
        this.conditionList.forEach(e => {
          if (e.value !== 'BETWEEN') {
            e.disabled = true
          }
        })
      } else {
        this.conditionList.forEach(e => {
          e.disabled = false
        })
      }
    },
    cellMouseEnter(row, column, cell, event) {
      this.dataItemNameChange(0, row)
    },
    setAllCheck(val) {
      this.isIndeterminate = false
      if (val) {
        this.tableDataData.content.forEach(e => {
          e.DISPLAY = true
        })
      } else {
        this.tableDataData.content.forEach(e => {
          e.DISPLAY = false
        })
      }
    },
    itemCheck(val) {
      //   console.log(val)
      this.isChekcList = this.tableDataData.content.filter(e => {
        return e.DISPLAY
      })
      this.isChekcList.length !== this.tableDataData.content.length && this.isChekcList.length > 0 ? (this.isIndeterminate = true) : (this.isIndeterminate = false)
      if (this.isChekcList.length === 0) {
        this.checked = false
        this.isIndeterminate = false
      }
      if (this.isChekcList.length === this.tableDataData.content.length) {
        this.checked = true
        this.isIndeterminate = false
      }
    },
    addData() {
      this.tableDataData1.content.push({
        data_item_name: '',
        condition_operation_symbol: '',
        condition_key: '',
        condition_value: '',
        condition_value_other: '',
        range1Disabled: true,
        range2Disabled: true
      })
    },
    addDataItem() {
      this.tableDataData.content.push({
        display_name: '',
        data_item_name: '',
        data_type: 'STRING',
        data_type_name: '字符型',
        DISPLAY: true
      })
    },
    delectItem(row, index) {
      this.tableDataData.content.splice(index, 1)
    },
    delect(row, index) {
      this.tableDataData1.content.splice(index, 1)
    },
    // 校验到未填项滚动到具体的dom
    scrollIntoDowView(object) {
      const str = []
      for (const key in object) {
        object[key].map(item => {
          str.push(item.message)
        })
        let dom = this.$refs[Object.keys(object)[0]]
        if (Object.prototype.toString.call(dom) !== '[object Object]') {
          dom = dom[0]
          break // 结束语句并跳出语句，进行下个语句执行
        }
        // 定位代码
        dom.$el.scrollIntoView({
          block: 'center',
          behavior: 'smooth'
        })
      }
    },
    range1Change(row, index) {
      console.log('row.condition_key', row.condition_key)
      // 选择项为自定义
      if (row.condition_key === 'CUSTOM' && row.condition_operation_symbol === 'BETWEEN') {
        this.tableDataData1.content[index].range1Disabled = false
        this.tableDataData1.content[index].range2Disabled = false
      } else if (row.condition_operation_symbol === 'EQUAL' && row.condition_key === 'CUSTOM') {
        this.tableDataData1.content[index].range1Disabled = false
        this.tableDataData1.content[index].range2Disabled = true
      } else {
        this.tableDataData1.content[index].range1Disabled = true
        this.tableDataData1.content[index].range2Disabled = true
        // console.log(this.$refs[`range2form+${index}`])
        // console.log(this.$refs[`range1form+${index}`])
        this.$refs[`range1form+${index}`].resetFields()
        this.$refs[`range2form+${index}`].resetFields()
        // this.tableDataData1.content[index].condition_value = ''
        // this.tableDataData1.content[index].condition_value_other = ''
      }
      if (row.condition_key === 'CUSTOM' && row.condition_operation_symbol === 'BETWEEN' && row.data_type === 'DATETIME') {
        row.condition_value = '不限'
        this.tableDataData1.content[index].range1Disabled = false
        this.tableDataData1.content[index].range2Disabled = true
      }
    },
    range2Change(row, index) {
      // 选择项为自定义
      if (row.name4 === '12') {
        this.tableDataData1.content[index].range2Disabled = false
      } else {
        this.tableDataData1.content[index].range2Disabled = true
        this.$refs[`range2form+${index}`].clearValidate()
      }
    },
    conditionChange(row, index) {
      // 条件操作符 为之间,开放值域2
      console.log('row.condition_operation_symbol', row.condition_operation_symbol, 'row.condition_key', row.condition_key)
      if (row.condition_operation_symbol === 'BETWEEN' && row.condition_key === 'CUSTOM') {
        console.log(1)
        this.tableDataData1.content[index].range1Disabled = false
        this.tableDataData1.content[index].range2Disabled = false
      } else if (row.condition_operation_symbol === 'EQUAL' && row.condition_key === 'CUSTOM') {
        console.log(2)
        this.tableDataData1.content[index].range1Disabled = false
        this.tableDataData1.content[index].range2Disabled = true
      } else {
        this.tableDataData1.content[index].range1Disabled = true
        this.tableDataData1.content[index].range2Disabled = true

        // console.log(this.$refs[`range2form+${index}`])
        // console.log(this.$refs[`range1form+${index}`])
        this.$refs[`range1form+${index}`].resetFields()
        this.$refs[`range2form+${index}`].resetFields()
        // this.tableDataData1.content[index].condition_value = ''
        // this.tableDataData1.content[index].condition_value_other = ''
      }
    },
    saveSharingData() {
      this.$refs[`sysForm`].validate((valid, object) => {
        if (valid) {
          const validateList1 = []
          const validateList2 = []
          const validateList3 = []
          const validateList4 = []
          const validateList5 = []
          const validateList6 = []
          const validateList7 = []
          if (this.tableDataData.content.length !== 0) {
            this.tableDataData.content.forEach((e, index) => {
              this.$refs[`zhNameform+${index}`].validate((valid, object) => {
                if (!valid) {
                  this.scrollIntoDowView(object)
                } else {
                  validateList1.push(valid)
                }
              })

              this.$refs[`fieldName+${index}`].validate((valid, object) => {
                if (!valid) {
                  this.scrollIntoDowView(object)
                } else {
                  validateList2.push(valid)
                }
              })
            })
          }
          if (this.tableDataData1.content.length !== 0) {
            this.tableDataData1.content.forEach((e, index) => {
              this.$refs[`dataItemNameform+${index}`].validate((valid, object) => {
                if (valid) {
                  validateList3.push(valid)
                } else {
                  this.scrollIntoDowView(object)
                }
              })
              this.$refs[`conditionOperationSymbolform+${index}`].validate((valid, object) => {
                if (valid) {
                  validateList4.push(valid)
                } else {
                  this.scrollIntoDowView(object)
                }
              })
              this.$refs[`conditionKeyform+${index}`].validate((valid, object) => {
                if (valid) {
                  validateList5.push(valid)
                } else {
                  this.scrollIntoDowView(object)
                }
              })
              if (!e.range1Disabled) {
                this.$refs[`range1form+${index}`].validate((valid, object) => {
                  validateList6.push(valid)
                  if (!valid) {
                    this.scrollIntoDowView(object)
                  }
                })
              }
              console.log('e.range2Disabled', e.range2Disabled)
              if (!e.range2Disabled) {
                this.$refs[`range2form+${index}`].validate((valid, object) => {
                  validateList7.push(valid)
                  if (!valid) {
                    this.scrollIntoDowView(object)
                  }
                })
              }
            })
          } else {
            this.$message({
              message: '请添加查询条件',
              type: 'warning'
            })
            return false
          }
          if (
            validateList3.length === this.tableDataData1.content.length &&
            validateList4.length === this.tableDataData1.content.length &&
            validateList5.length === this.tableDataData1.content.length &&
            validateList6.indexOf(false) === -1 &&
            validateList7.indexOf(false) === -1
          ) {
            // console.log(123123)
            if (validateList1.length === this.tableDataData.content.length && validateList2.length === this.tableDataData.content.length) {
              this.saveData = {}
              Object.assign(this.saveData, this.sysForm)
              this.saveData.data_item_list = JSON.parse(JSON.stringify(this.tableDataData.content))
              this.saveData.search_condition_list = JSON.parse(JSON.stringify(this.tableDataData1.content))
              this.saveData.data_theme_status = 'ENABLE'
              if (this.saveData.search_condition_list.length !== 0) {
                this.saveData.search_condition_list.forEach(e => {
                  delete e.range1Disabled
                  delete e.range2Disabled
                  delete e.data_type_name
                })
              }
              if (this.saveData.data_item_list.length !== 0) {
                this.saveData.data_item_list.forEach(e => {
                  delete e.data_type_name
                })
              }
              console.log('this.saveData', this.saveData)
              if (this.$route.query.type === 'add') {
                create(this.saveData).then(res => {
                  console.log(res)
                  if (res.meta.code === '200' && res.data != null) {
                    this.$message({
                      message: '创建成功！',
                      type: 'success'
                    })
                    this.$router.push({ name: 'dataSharingManagement' })
                  } else {
                    this.$message({
                      message: res.meta.message,
                      type: 'error'
                    })
                  }
                })
              } else if (this.$route.query.type === 'edit') {
                this.saveData.id = this.$route.query.id
                edit(this.saveData).then(res => {
                  console.log(res)
                  if (res.meta.code === '200' && res.data != null) {
                    this.$message({
                      message: '编辑成功！',
                      type: 'success'
                    })
                    // this.find(this.$route.query.id)
                    this.$router.push({ name: 'dataSharingManagement' })
                  } else {
                    this.$message({
                      message: res.meta.message,
                      type: 'error'
                    })
                  }
                })
              }
            }
          }
        }
      })
    },
    query() {
      this.themeParam.page_number = this.dataSubjectData.currentPage
      this.themeParam.page_size = this.dataSubjectData.pageSize
      this.getThemePage()
    },
    getThemePageBysearch() {
      this.dataSubjectData.currentPage = 1
      this.dataSubjectData.pageSize = 10
      this.themeParam.page_number = 1
      this.themeParam.page_size = 10
      this.getThemePage()
    },
    getThemePage() {
      getThemePage(this.themeParam).then(res => {
        if (res.data != null && res.meta.code === '200') {
          this.dataSubjectData.content = res.data.content
          this.dataSubjectData.total = res.data.totalElements
          this.dataSubjectData.content.forEach((e, key) => {
            // e.id = key
            e.id = e.data_shared_theme_code
          })
        }
      })
    },
    getSystemSelectItem() {
      getSystemSelectItem().then(res => {
        if (res.data != null && res.meta.code === '200') {
          this.sysOptions = res.data
          // 默认选中'广东省数据资源一网共享'
          if (this.sysOptions[1]) {
            this.sysForm.system_code = this.sysOptions[1].value
            this.sysForm.system_name = this.sysOptions[1].label
          }
        }
      })
    },
    getConditionKeySelectItem() {
      getConditionKeySelectItem().then(res => {
        if (res.data != null && res.meta.code === '200') {
          this.valueList = res.data
        }
      })
    },
    getDataSharedListSortRule() {
      getDataSharedListSortRule().then(res => {
        if (res.data != null && res.meta.code === '200') {
          this.sortOptions = res.data
        }
      })
    },
    getDataSharedListSortType() {
      getDataSharedListSortType().then(res => {
        if (res.data != null && res.meta.code === '200') {
          this.sortTypeOptions = res.data
        }
      })
    },
    getThemeData() {
      if (this.themeSelectData != '') {
        this.dialogDataVisible = false
        this.sysForm.data_theme_name = this.themeSelectData.data_shared_theme_name
        this.sysForm.data_theme_code = this.themeSelectData.data_shared_theme_code
        this.getConfigDetail()
      } else {
        this.$message({
          message: '请选择主题',
          type: 'warning'
        })
      }
    },
    getConfigDetail() {
      if (this.sysForm.system_code != '') {
        getConfigDetail(this.sysForm).then(res => {
          if (res.data != null && res.meta.code === '200') {
            this.tableDataData.content = res.data.data_item_list

            this.dataItemLsit = this.tableDataData.content.map(e => {
              return { value: e.data_item_name, label: e.data_item_name }
            })
            // console.log('this.dataItemLsit',this.dataItemLsit)
            this.sysForm.system_api_url = res.data.system_api_url
          } else {
            this.$message({
              message: res.meta.message,
              type: 'error'
            })
          }
        })
      } else {
        this.$message({
          message: '请先选择系统名称，再选择数据主题名称',
          type: 'warning'
        })
      }
    },
    getConfigDetailByid() {
      getConfigDetailByid(this.$route.query.id).then(res => {
        if (res.data != null && res.meta.code === '200') {
          const sharData = res.data
          this.sysForm = {
            system_code: sharData.system_code,
            data_theme_code: sharData.data_theme_code,
            data_theme_name: sharData.data_theme_name,
            system_api_url: sharData.system_api_url,
            system_name: sharData.system_name,
            list_sort_type_data_item_name: sharData.list_sort_type_data_item_name,
            list_sort_rule: sharData.list_sort_rule,
            list_sort_type: sharData.list_sort_type,
            search_limit_count: sharData.search_limit_count
          }
          this.tableDataData.content = sharData.data_item_list
          this.dataItemLsit = this.tableDataData.content.map(e => {
            return { value: e.data_item_name, label: e.data_item_name }
          })
          console.log('this.sysForm', this.sysForm)
          this.tableDataData1.content = sharData.search_condition_list.map(e => {
            // if (e.condition_key === 'CUSTOM') {
            //   e.range1Disabled = false
            // } else {
            //   e.range1Disabled = true
            // }
            // if (e.condition_operation_symbol === 'BETWEEN') {
            //   e.range2Disabled = false
            // } else {
            //   e.range2Disabled = true
            // }
            if (e.condition_operation_symbol === 'BETWEEN' && e.condition_key === 'CUSTOM') {
              e.range1Disabled = false
              e.range2Disabled = false
            } else {
              e.range1Disabled = true
              e.range2Disabled = true
            }
            return { ...e }
          })
        }
      })
    },
    selectionChange(data) {
      this.themeSelectData = data[0]
    },
    getQueryFilterOperationSelectItem() {
      getQueryFilterOperationSelectItem().then(res => {
        if (res.data != null && res.meta.code === '200') {
          this.conditionList = res.data
          console.log('this.conditionList', this.conditionList)
        }
      })
    },
    selectChange(data) {
      const selectData = this.sysOptions.filter(e => {
        return e.value == data
      })
      this.sysForm.system_name = selectData[0].label
    },
    back() {
      if (this.$route.query.flagCatalog === 'add') {
        this.$router.push({
          params: { saveData: this.saveData },
          name: 'dataSharingManagement',
          query: this.$route.query
        })
      } else {
        this.$router.push({
          params: { saveData: this.saveData },
          name: 'dataSharingManagement',
          query: this.$route.query
        })
      }
    },
    screenWidth() {
      if (screen.width == 1920) {
        this.is1366 = false
      } else if (screen.width == 1366) {
        this.is1366 = true
      } else {
        this.is1366 = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.select {
  width: 100%;
}
.table-title {
  display: flex;
  justify-content: space-between;
  padding: 0 20px;
}
.check-text {
  margin-left: 10px;
}
.red {
  color: #f56c6c;
}
.plusicon {
  color: #409eff;
  font-size: 20px;
  cursor: pointer;
}
.rangeform ::v-deep .el-form-item {
  margin-top: 20px;
}
.dialogwrap::v-deep .el-dialog__body {
  padding-bottom: 0;
  padding-top: 5px;
}
.dialogwrap::v-deep .table {
  margin-bottom: 0px;
}
.dialogwrap::v-deep .el-card__body {
  padding-top: 0px;
}

.zhNameform ::v-deep .el-form-item {
  margin-top: 20px;
}
.fieldName ::v-deep .el-form-item {
  margin-top: 20px;
}
.info-title {
  font-size: 20px;
  color: #333333;
}
.info-wrap {
  display: flex;
  margin-bottom: 10px;
  align-items: center;
}
.info-wrap img {
  width: 35px;
  height: 35px;
  margin-right: 10px;
}
.green-line {
  border-bottom: 3px solid rgba(1, 164, 99, 0.5);
  margin: 0 20px;
}
.bar-title {
  display: flex;
  align-items: center;
  margin: 30px 30px;
  font-size: 20px;
  color: #333333;
  .vertical-bar {
    width: 3px;
    height: 20px;
    background: rgba(1, 164, 99, 0.5);
    margin-right: 10px;
  }
}
.sort-wrap {
  margin: 10px 30px;
  span {
    color: #9e9e9e;
  }
  .sort-select {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    &-label {
      width: 100px;
      margin-right: 10px;
    }
    &-word {
      color: #000;
      padding: 0 10px;
    }
  }
}
</style>
