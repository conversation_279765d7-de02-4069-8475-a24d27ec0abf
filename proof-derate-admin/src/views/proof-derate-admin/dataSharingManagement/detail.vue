<template>
  <div class="content-wrapper dataSharingManagement-detail padding-10">
    <CardTitle :title-name="themeData.data_theme_name" :ifback="true" @back="back()">
      <template>
        <el-button v-if="themeData.data_theme_status==='DISABLE'" type="warning" @click="isDisable">启用</el-button>
        <el-button v-else type="warning" @click="isDisable">禁用</el-button>
        <el-button v-permission="'catalog:data_manage:data:delete'" type="danger" @click="delectItem">删除</el-button>
        <el-button v-permission="'catalog:data_manage:data:update'" type="primary" @click="dataThemeEdit">编辑</el-button>
        <el-button type="primary" @click="outPut">导出</el-button>
      </template>
    </CardTitle>
    <!-- <section class="content"> -->
    <el-card class="box-card">
      <!-- <p>基本信息</p> -->
      <span class="margin-left-10 info-wrap">
        <img :src="arrow" alt>
        <span class="info-title">基本信息</span>
      </span>
      <el-descriptions class="descriptions" title :column="3" :size="size" border>
        <el-descriptions-item :label-style="{width:'140px'}">
          <template slot="label">系统名称</template>
          {{ themeData.system_name }}
        </el-descriptions-item>
        <el-descriptions-item :label-style="{width:'140px'}">
          <template slot="label">接口访问地址</template>
          {{ themeData.system_api_url }}
        </el-descriptions-item>
      </el-descriptions>
      <!-- <p>数据项信息</p> -->
      <span class="margin-left-10 info-wrap">
        <img :src="arrow" alt>
        <span class="info-title">数据项信息</span>
      </span>
      <custom-table ref="table1" :is-card-type="false" :table-data="dataSubjectData" :table-header="dataSubjecHeaderData" />
      <!-- <p>查询条件</p> -->
      <span class="margin-left-10 info-wrap">
        <img :src="arrow" alt>
        <span class="info-title">查询条件</span>
      </span>
      <custom-table ref="table1" :is-card-type="false" :table-data="tableDataData1" :table-header="tableHeaderData1" />
      <div class="table-title">
        <!-- <span>查询条件</span> -->
        <span class="margin-left-10 info-wrap">
          <img :src="arrow" alt>
          <span class="info-title">更多设置</span>
        </span>
        <div>
          <!-- <i class="el-icon-circle-plus-outline plusicon" @click="addData" /> -->
        </div>
      </div>
      <div class="green-line" />
      <div class="bar-title">
        <div class="vertical-bar" />
        <span>排序设置</span>
      </div>
      <div class="sort-wrap">
        <p v-for="(i,key) in sortTypeOptions" :key="key">
          <el-radio v-model="sysForm.list_sort_type" :disabled="true" :label="i.value">{{ i.label }}</el-radio>
          <span>说明：指在免征办模块数据共享查看页面，当存在多条数据时，展示的数据随机排序</span>
        </p>
        <div v-if="sysForm.list_sort_type==='CUSTOM_SORT'" class="sort-select">
          <div class="sort-select-label">排序数据项：</div>
          <div>
            <el-select v-model="sysForm.list_sort_type_data_item_name" :disabled="true" placeholder="请选择">
              <el-option
                v-for="item in dataItemLsit"
                :key="item.value"
                :label="item.label"
                :value="item.value"
                :disabled="item.disabled"
              />
            </el-select>
          </div>
        </div>
        <div v-if="sysForm.list_sort_type==='CUSTOM_SORT'" class="sort-select">
          <div class="sort-select-label">排序规则：</div>
          <div>
            <el-select v-model="sysForm.list_sort_rule" :disabled="true" placeholder="请选择">
              <el-option v-for="item in sortOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </div>
        </div>
      </div>
      <div class="bar-title">
        <div class="vertical-bar" />
        <span>查询次数设置</span>
      </div>
      <div class="sort-wrap">
        <div class="sort-select">
          <div class="sort-select-label">限制查询次数：</div>
          <div>
            <el-input v-model="sysForm.search_limit_count" :disabled="true" placeholder="请输入内容" />
          </div>
          <span class="sort-select-word">次</span>
          <span>说明：指在免证办模块数据共享查询条件页面，限制用户查询次数。当查询次数超过规定次数，则不允许用户查询。</span>
        </div>
      </div>
    </el-card>
    <!-- </section> -->
  </div>
</template>

<script>
import CustomTable from '@/components/Element/Table'
import { find, edit, downTemplate, catalogDataSharedConfigDelete } from '@/api/sharedConfig'
import { getConfigDetailByid } from '@/api/certificationManagement/certificationList'
import { getQueryFilterOperationSelectItem, getConditionKeySelectItem, getDataSharedListSortType, getDataSharedListSortRule } from '@/api/common/dict'
import { dataURLtoDownload, downURLfile } from '@/utils/index'
import CardTitle from '@/components/CardTitle'
export default {
  components: {
    CustomTable,
    CardTitle
  },
  data() {
    return {
      arrow: require('@/assets/proof-derate-admin-images/arrow.png'),
      size: '',
      sysForm: {
        system_code: '',
        data_theme_code: '',
        data_theme_name: '',
        system_api_url: '',
        system_name: '',
        list_sort_type_data_item_name: '',
        list_sort_rule: 'DESC',
        list_sort_type: 'DEFAULT_SORT',
        search_limit_count: '3'
      },
      themeData: {
        data_theme_name: ''
      },
      str: '',
      dataTypeNameList: [
        {
          value: 'STRING',
          label: '字符型'
        },
        {
          value: 'NUMBER',
          label: '数字型'
        },
        {
          value: 'INTEGER',
          label: '数字型'
        },
        {
          value: 'DATETIME',
          label: '日期型'
        },
        {
          value: 'BINARY',
          label: '二进制'
        },
        {
          value: 'BOOLEAN',
          label: '布尔值'
        },
        {
          value: 'TEXT',
          label: '文本型'
        },
        {
          value: 'TIMESTAMP',
          label: '文本型'
        },
        {
          value: 'FLOAT',
          label: '浮点型'
        },
        {
          value: 'ENUM',
          label: '枚举型'
        }
      ],
      valueList: [],
      conditionList: [],
      sortTypeOptions: [],
      dataItemLsit: [],
      sortOptions: [],
      dataSubjectData: {
        loading: false, // 控制表格加载效果
        total: 0, // 分页查询时的总数，去掉该属性，不显示分页条
        currentPage: 1, // 当前页码
        pageSize: 10,
        maxHeight: '300px',
        pageDirection: 'desc',
        isShowIndex: true,
        multiple: false, // 是否多选 数据需要有id 属性值
        isShowSelection: false, // 是否显示多选框，默认false
        content: []
      },
      dataSubjecHeaderData: [
        {
          label: '中文名称',
          prop: 'display_name',
          minWidth: '200px'
        },
        { label: '数据项名称', prop: 'data_item_name', minWidth: '200px' },
        {
          label: '数据类型',
          prop: 'data_type_name',
          minWidth: '200px'
        },
        {
          label: '值域',
          prop: 'data_range_mapping',
          minWidth: '300px'
        }
      ],
      tableDataData1: {
        content: [], // 表格数据
        loading: false, // 控制表格加载效果
        total: 0, // 分页查询时的总数，去掉该属性，不显示分页条
        currentPage: 1, // 当前页码
        pageSize: 10,
        pageDirection: 'desc',
        isShowIndex: true
      },
      tableHeaderData1: [
        {
          label: '数据项名称',
          prop: 'data_item_name',
          minWidth: '200px'
        },
        {
          label: '数据类型',
          prop: 'data_type_name',
          minWidth: '200px',
          formatter: (row, col, val) => {
            return row.data_type === null || row.data_type === undefined ? '' : this.dataTypeNameList.find(i => i.value === row.data_type).label
          }
        },
        {
          label: '条件操作符',
          prop: 'condition_operation_symbol',
          minWidth: '200px',
          formatter: (row, col, val) => {
            return val === null || val === undefined || this.conditionList.length === 0 ? '' : this.conditionList.find(i => i.value === val).label
          }
        },
        {
          label: '值1',
          prop: 'condition_key',
          minWidth: '200px',
          formatter: (row, col, val) => {
            return val === null || val === undefined || this.valueList.length === 0 ? '' : this.valueList.find(i => i.value === val).label
          }
        },
        {
          label: '值域1',
          prop: 'condition_value',
          minWidth: '200px'
        },
        {
          label: '值域2',
          prop: 'condition_value_other',
          minWidth: '200px'
        }
      ]
    }
  },
  watch: {
    'tableDataData1.content': {
      handler(val) {
        this.dataItemLsit = val.map(e => {
          return { value: e.data_item_name, label: e.data_item_name, data_type: e.data_type, data_type_name: e.data_type_name }
        })
        this.refreshId = Math.random()
      },
      deep: true,
      immediate: true
    }
  },
  mounted() {
    if (this.$route.query.id) {
      this.find(this.$route.query.id)
    }
    this.getConditionKeySelectItem()
    this.getQueryFilterOperationSelectItem()
    this.getDataSharedListSortType()
    this.getConfigDetailByid()
    this.getDataSharedListSortRule()
  },

  methods: {
    getDataSharedListSortType() {
      getDataSharedListSortType().then(res => {
        if (res.data != null && res.meta.code === '200') {
          this.sortTypeOptions = res.data
        }
      })
    },
    getDataSharedListSortRule() {
      getDataSharedListSortRule().then(res => {
        if (res.data != null && res.meta.code === '200') {
          this.sortOptions = res.data
          console.log('this.sortOptions', this.sortOptions)
        }
      })
    },
    getConfigDetailByid() {
      getConfigDetailByid(this.$route.query.id).then(res => {
        if (res.data != null && res.meta.code === '200') {
          const sharData = res.data
          this.sysForm = {
            system_code: sharData.system_code,
            data_theme_code: sharData.data_theme_code,
            data_theme_name: sharData.data_theme_name,
            system_api_url: sharData.system_api_url,
            system_name: sharData.system_name,
            list_sort_type_data_item_name: sharData.list_sort_type_data_item_name,
            list_sort_rule: sharData.list_sort_rule,
            list_sort_type: sharData.list_sort_type,
            search_limit_count: sharData.search_limit_count
          }
          this.tableDataData.content = sharData.data_item_list
          this.dataItemLsit = this.tableDataData.content.map(e => {
            return { value: e.data_item_name, label: e.data_item_name }
          })
          console.log('this.sysForm', this.sysForm)
          this.tableDataData1.content = sharData.search_condition_list.map(e => {
            // if (e.condition_key === 'CUSTOM') {
            //   e.range1Disabled = false
            // } else {
            //   e.range1Disabled = true
            // }
            // if (e.condition_operation_symbol === 'BETWEEN') {
            //   e.range2Disabled = false
            // } else {
            //   e.range2Disabled = true
            // }
            if (e.condition_operation_symbol === 'BETWEEN' && e.condition_key === 'CUSTOM') {
              e.range1Disabled = false
              e.range2Disabled = false
            } else {
              e.range1Disabled = true
              e.range2Disabled = true
            }
            return { ...e }
          })
        }
      })
    },
    find(id) {
      find(id).then(res => {
        if (res.meta.code === '200' && res.data !== null) {
          // console.log(res)
          this.themeData = res.data
          this.sysForm.system_code = this.themeData.system_code
          this.sysForm.data_theme_code = this.themeData.data_theme_code
          this.sysForm.data_theme_name = this.themeData.data_theme_name
          this.sysForm.system_api_url = this.themeData.system_api_url
          this.sysForm.system_name = this.themeData.system_name
          this.sysForm.list_sort_type_data_item_name = this.themeData.list_sort_type_data_item_name
          this.sysForm.list_sort_rule = this.themeData.list_sort_rule
          this.sysForm.list_sort_type = this.themeData.list_sort_type
          this.sysForm.search_limit_count = this.themeData.search_limit_count
          this.dataSubjectData.content = this.themeData.data_item_list
          this.tableDataData1.content = this.themeData.search_condition_list
          console.log('this.themeData', this.themeData)
        }
      })
    },
    isDisable() {
      // 0 禁用 1启用
      this.str = ''
      if (this.themeData.data_theme_status === 'DISABLE') {
        this.themeData.data_theme_status = 'ENABLE'
        this.str = '设置启用成功'
      } else if (this.themeData.data_theme_status === 'DISABLE') {
        this.themeData.data_theme_status = 'ENABLE'
        this.str = '设置禁用成功'
      } else {
        this.themeData.data_theme_status = 'DISABLE'
        this.str = '设置禁用成功'
      }
      const that = this
      edit(this.themeData).then(res => {
        if (res.meta.code === '200' && res.data !== null) {
          this.$message({
            message: that.str,
            type: 'success'
          })
        }
      })
    },
    getConditionKeySelectItem() {
      getConditionKeySelectItem().then(res => {
        if (res.data != null && res.meta.code === '200') {
          this.valueList = res.data
        }
      })
    },
    getQueryFilterOperationSelectItem() {
      getQueryFilterOperationSelectItem().then(res => {
        if (res.data != null && res.meta.code === '200') {
          this.conditionList = res.data
        }
      })
    },
    dataThemeEdit() {
      this.$router.push({
        name: 'DataSharingManagementEdit',
        query: {
          type: 'edit',
          id: this.$route.query.id
        }
      })
    },
    delectItem() {
      this.$confirm('是否删除该数据, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          catalogDataSharedConfigDelete({ id: this.$route.query.id }).then(res => {
            this.$message({
              type: 'success',
              message: '删除成功!'
            })
            this.$router.push({ name: 'dataSharingManagement' })
          })
        })
        .catch(() => {})
    },
    back() {
      this.$router.push({ name: 'dataSharingManagement' })
    },
    outPut() {
      const data = {
        id: this.$route.query.id
      }
      downTemplate(data).then(res => {
        if (res.meta.code === '200') {
          downURLfile('data:application/vnd.ms-excel;base64,' + res.data.fileData, res.data.file_name)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.margin-bottom-30 {
  margin-bottom: 30px;
  margin-top: 10px;
  padding: 0 10px;
}
.content-title {
  width: 70%;
  i {
    cursor: pointer;
  }
}
.info-wrap {
  display: flex;
  align-items: center;
  // margin-bottom: 10px;
  img {
    margin-right: 10px;
  }
  .info-title {
    font-size: 20px;
    color: #333333;
  }
}
.margin-left-10 {
  margin-left: 10px;
}
.descriptions {
  margin: 10px 0 20px;
  padding: 0 10px;
}
.green-line {
  border-bottom: 3px solid rgba(1, 164, 99, 0.5);
  margin: 0 20px;
}
.bar-title {
  display: flex;
  align-items: center;
  margin: 30px 30px;
  font-size: 20px;
  color: #333333;
  .vertical-bar {
    width: 3px;
    height: 20px;
    background: rgba(1, 164, 99, 0.5);
    margin-right: 10px;
  }
}
.sort-wrap {
  margin: 10px 30px;
  span {
    color: #9e9e9e;
  }
  .sort-select {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    &-label {
      width: 100px;
      margin-right: 10px;
    }
    &-word {
      color: #000;

      padding: 0 10px;
    }
  }
}
</style>
