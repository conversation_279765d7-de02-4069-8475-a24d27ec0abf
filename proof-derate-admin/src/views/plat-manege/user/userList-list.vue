<template>
  <div class="user-container">
    <CardTitle :title-name="titleName">
      <template />
    </CardTitle>
    <el-card class="department-box">
      <el-form :model="sendFrom" label-width="120px" @submit.native.prevent>
        <el-row :gutter="24">
          <el-col :span="9">
            <el-form-item label="账号">
              <el-input v-model="sendFrom.account_name" clearable placeholder="请输入账号" />
            </el-form-item>
          </el-col>
          <el-col :span="9">
            <el-form-item label="姓名">
              <el-input v-model="sendFrom.user_name" clearable placeholder="请输入姓名" />
            </el-form-item>
          </el-col>

          <el-col :span="6" class="submitbtn">
            <el-button type="primary" plain native-type="submit" @click="onSubmit">查询</el-button>
            <el-button plain native-type="submit" @click="reset">重置</el-button>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="9" class="submitbtn">
            <el-form-item label="行政区划">
              <AdministrativeDivisionCascader
                :key="divisionCode"
                ref="AdministrativeDivisionSelect"
                :division-code="divisionCode"
                :permission-code="'auth:account:list'"
                @setDivisionCodeAndName="setDivisionCodeAndName"
              />
            </el-form-item>
          </el-col>
          <el-col :span="9">
            <el-form-item label="实施机构">
              <el-select v-model="sendFrom.org_id" placeholder="请选择" clearable style="width:100%">
                <el-option v-for="item in divisionOptions" :key="item.id" :label="item.name" :value="item.id" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <custom-table
        ref="table"
        :is-card-type="false"
        :table-data="tableData"
        :table-header="tableHeader"
        @query="query"
        @refresh="query(1)"
      >
        <template #account_name="{ row }">
          <el-button
            v-if="isPermission(row.permission_codes,'auth:account:view')"
            type="text"
            @click="getDetail(row,'auth:account:view')"
          >{{ row.account_name }}</el-button>
          <span v-else>{{ row.account_name }}</span>
        </template>
        <template #handle="{ row }">
          <el-button
            v-permission="'platform:auth:user:view'"
            :disabled="!isPermission(row.permission_codes,'platform:auth:user:view')"
            type="text"
            @click="goDetail(row,$event)"
          >查看</el-button>
          <el-button
            v-permission="'platform:auth:user:edit'"
            :disabled="!isPermission(row.permission_codes,'platform:auth:user:edit')"
            type="text"
            @click="handleEdit(row)"
          >编辑</el-button>
          <el-button
            v-permission="'platform:auth:user:delete'"
            :disabled="!isPermission(row.permission_codes,'platform:auth:user:delete')"
            type="text"
            class="table-delete"
            @click="handleChangeStatus(row)"
          >删除</el-button>
        </template>
      </custom-table>
    </el-card>
  </div>
</template>

<script>
import { getOrganizationList } from '@/api/commonPack/platManege'
import { getAccountPage, deleteAccountInfo } from '@/api/user/index.js'
import CustomTable from '@/components/Element/Table'
import clip from '@/utils/clipboard'
import { status } from 'nprogress'
import { plusXing, isPermission } from '@/utils/index.js'
import { getOperationPermissionList } from '@/utils/index'
import CardTitle from '@/components/CardTitle'

export default {
  components: {
    CustomTable,
    CardTitle,
    // AdministrativeDivisionSelect: () => import('@/components/AdministrativeDivisionSelect')
    AdministrativeDivisionCascader: () => import('@/components/AdministrativeDivisionCascader')
  },

  data() {
    return {
      titleName: '用户管理',
      sendFrom: {
        org_id: '',
        user_name: '',
        account_name: '',
        division_code: '',
        status: null,
        page_num: 1,
        page_size: 10
      },
      statusOptions: [
        { value: null, label: '全部' },
        { value: 'NORMAL', label: '正常' },
        { value: 'DISABLE', label: '禁用' }
      ],
      tableData: {
        content: [{ account: '111' }], // 表格数据
        loading: false, // 控制表格加载效果
        total: 10, // 分页查询时的总数，去掉该属性，不显示分页条
        currentPage: 1, // 当前页码
        pageSize: 10,
        pageDirection: 'desc',
        isShowIndex: true,
        border: false
        // maxHeight: '420px'
      },
      tableHeader: [
        {
          label: '账号',
          prop: 'account_name',
          slot: 'account_name',
          minWidth: '160px',
          align: 'left'
        },
        {
          label: '姓名',
          prop: 'user_name',
          minWidth: '160px',
          align: 'left'
        },
        // {
        //   label: '手机号',
        //   prop: 'mobile_phone',
        //   minWidth: '160px',
        //   align: 'left'
        // },
        {
          label: '所属区划',
          prop: 'division_name',
          minWidth: '160px',
          align: 'left'
        },
        // {
        //   label: '账号状态',
        //   prop: 'status',
        //   minWidth: '160px',
        //   align: 'left',
        //   formatter: (row, col, val) => {
        //     return val === null ? val : val === 'NORMAL' ? '正常' : '禁用'
        //     // return val
        //   }
        // },
        {
          label: '最后更新时间',
          prop: 'last_modification_time',
          minWidth: '160px',
          align: 'left'
        },
        {
          label: '操作',
          prop: '',
          slot: 'handle',
          minWidth: '160px',
          align: 'left'
        }
      ],
      editData: {},
      editDialogVisible: false,
      repwDialogVisible: false,
      resetPassword: '',
      isCopypassword: false,
      passwordPlus: '',
      // divisionCode:'c1'
      divisionCode: '',
      permissionList: ['platform:auth:user:view', 'platform:auth:user:edit', 'platform:auth:user:delete'],
      optionData: [
        {
          divisionlist: [],
          disabled: false,
          placeholderText: '请选择',
          code: '',
          name: ''
        },
        {
          divisionlist: [],
          disabled: false,
          placeholderText: '请选择',
          code: '',
          name: ''
        },
        {
          divisionlist: [],
          disabled: false,
          placeholderText: '请选择',
          code: '',
          name: ''
        }
      ],
      divisionOptions: []
    }
  },
  mounted() {
    this.tableHeader = getOperationPermissionList(this.$route.meta.permission, this.permissionList, this.tableHeader)
    this.getAccountPage()
  },

  methods: {
    isPermission,
    getAccountPage() {
      getAccountPage(this.sendFrom)
        .then(res => {
          if (res.data != null && res.meta.code === '200') {
            if (res.data.content != null) {
              const { content } = res.data
              /* content.forEach(element => {
                element.hasPermission = isPermission(this.$route.meta.permission, element.permission_codes)
              })
              console.log(content, 'content') */
              this.tableData.content = content
              this.tableData.total = Number(res.data.total_elements)
            } else {
              this.tableData.content = []
              this.tableData.total = 0
            }
          }
        })
        .catch(() => {})
    },
    onSubmit() {
      this.sendFrom.page_num = 1
      this.sendFrom.page_size = 10
      this.tableData.currentPage = this.sendFrom.page_num
      this.tableData.pageSize = this.sendFrom.page_size
      this.getAccountPage()
      // if (this.sendFrom.page_num === 1) this.tableData.currentPage = 1
    },
    reset() {
      this.sendFrom = {
        org_id: '',
        user_name: '',
        account_name: '',
        division_code: '',
        status: null,
        page_num: 1,
        page_size: 10
      }
      this.divisionOptions = []
      // this.$refs.areaSelect.resetCodeAndName()
      this.$refs.AdministrativeDivisionSelect.clearChose()
      // this.tableData.currentPage = 1
    },
    query() {
      this.sendFrom.page_num = this.tableData.currentPage
      this.sendFrom.page_size = this.tableData.pageSize
      this.getAccountPage()
    },
    getDivisionCode(val) {
      this.sendFrom.division_code = val
    },
    setDivisionCodeAndName(data) {
      this.sendFrom.division_code = data.code
      this.getOrganizationList(this.sendFrom.division_code)
      // this.editDataForm.division_name = data.name
    },
    handleResetPassword(row) {
      this.$alert(`<span>是否确认重置密码</span><br/><span>账号：${row.account}</span>`, '警告提示', {
        dangerouslyUseHTMLString: true,
        type: 'warning',
        showCancelButton: true,
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      })
        .then(() => {
          this.isCopypassword = false
          this.setResetPassword(row.id, event)
        })
        .catch(() => {})
    },
    // 进入详情页
    goDetail(row) {
      this.$router.push({ name: 'userDetail', query: { id: row.id } })
    },
    handleEdit(row) {
      this.$router.push({ path: 'userAdd', query: { id: row.id, isEdit: '1' } })
    },
    // 变更状态
    handleChangeStatus(row) {
      const alert = {}
      alert.content = '是否确认删除此账号？'
      this.$alert(`<span>${alert.content}</span>`, '警告提示', {
        dangerouslyUseHTMLString: true,
        type: 'warning',
        showCancelButton: true,
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      })
        .then(() => {
          deleteAccountInfo(row.id).then(res => {
            if (res.meta.code === '200') {
              this.$message({
                message: '删除成功',
                type: 'success'
              })
              this.getAccountPage()
            }
          })
        })
        .catch(() => {
          /* this.$message({
          type: 'info',
          message: '已取消删除'
        }) */
        })
    },
    getDetail(row, key) {
      if (isPermission(this.$route.meta.permission, key)) {
        this.$router.push({ name: 'AccountDetail', query: { id: row.id, status: row.status } })
        if (row.auth_type === 'COMMON') {
          this.$store.commit('breadcrumbBtn/SET_PLATMANAGE', { accoundIdEdit: row.id })
        } else {
          this.$store.commit('breadcrumbBtn/SET_PLATMANAGE', { accoundIdEdit: '' })
        }
      }
    },

    setEditDialogVisible(val) {
      this.editDialogVisible = val
      this.getAccountPage()
    },
    toggleListTree(val) {
      this.$router.push({ name: 'DepartmentTree' })
    },
    getOrganizationList(id) {
      const data = {
        division_code: id,
        permission_code: 'auth:account:list',
        scope: true
      }
      getOrganizationList(data).then(res => {
        if (res.meta.code === '200') {
          this.divisionOptions = res.data
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.department-box ::v-deep .gt-table-box .el-card__body {
  padding-top: 0;
}
.user-container {
  padding: 10px;
  .reset-password {
    .boby {
      display: flex;
      align-items: center;
      .status {
        color: #67c23a;
        // transform: translateY(-50%);
        font-size: 24px !important;
      }
      .content {
        padding-left: 12px;
        padding-right: 12px;
      }
    }
  }
}
.department-box {
  &-title {
    height: 36px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 0px 0 30px 20px;
    &-h3 {
      margin: 0;
      padding: 0;
    }
    &-btn {
      display: flex;
      align-items: center;
      .btn {
        width: 36px;
        height: 36px;
        background: #f7f7f7;
        border: 1px solid #d9d9d9;
        border-radius: 4px;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        &:first-child {
          margin-right: 12px;
        }
        &.active {
          background: #e0f0ff;
          border: 1px solid #99ceff;
        }
      }
    }
  }
}
</style>
<style lang="scss">
.user-container {
  .table-delete {
    color: #ff2b2b;
  }
  .copy-passsword {
    color: red;
  }
  .reset-password {
    .el-dialog__body {
      padding: 0px 20px;
    }
  }
}
</style>
