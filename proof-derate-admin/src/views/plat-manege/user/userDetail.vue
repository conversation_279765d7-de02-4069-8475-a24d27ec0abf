<template>
  <div class="content-wrapper">
    <papeTitle :title-name="checkform.account_name" :is-has-back="true" @goToList="goToList">
      <template>
        <el-button type="primary" @click="edit">修改</el-button>
        <el-button type="danger" @click="deleteAccount">删除</el-button>
      </template>
    </papeTitle>
    <section class="content">
      <el-card class="box-card card1">
        <span class="margin-left-10 info-wrap">
          <img :src="arrow" alt />
          <span class="info-title">基本信息</span>
        </span>
        <el-descriptions class="descriptions" title :column="2" border>
          <el-descriptions-item :label-style="{width:'140px'}">
            <template slot="label">账号</template>
            {{ checkform.account_name }}
          </el-descriptions-item>
          <el-descriptions-item :label-style="{width:'140px'}">
            <template slot="label">姓名</template>
            {{ checkform.user_name }}
          </el-descriptions-item>
          <el-descriptions-item :label-style="{width:'140px'}">
            <template slot="label">证件类型</template>
            {{ checkform.identity_type_name }}
          </el-descriptions-item>
          <el-descriptions-item :label-style="{width:'140px'}">
            <template slot="label">证件号码</template>
            {{ checkform.identity_number }}
          </el-descriptions-item>
          <el-descriptions-item :label-style="{width:'140px'}">
            <template slot="label">所属区划</template>
            {{ checkform.division_name }}
          </el-descriptions-item>
          <el-descriptions-item :label-style="{width:'140px'}">
            <template slot="label">手机号</template>
            {{ checkform.mobile_phone }}
          </el-descriptions-item>
          <el-descriptions-item :label-style="{width:'140px'}">
            <template slot="label">固定电话</template>
            {{ checkform.contact_phone }}
          </el-descriptions-item>
          <el-descriptions-item :label-style="{width:'140px'}">
            <template slot="label">邮箱</template>
            {{ checkform.email }}
          </el-descriptions-item>
        </el-descriptions>
      </el-card>
    </section>
  </div>
</template>

<script>
import papeTitle from '@/components/papeTitle'
import { getView, deleteAccountInfo } from '@/api/user/index'
export default {
  components: {
    papeTitle
  },
  data() {
    return {
      titleName: '',
      arrow: require('@/assets/proof-exemption-admin-images/arrow.png'),
      checkform: {
        account: '',
        account_name: '',
        user_name: '',
        identity_number: '',
        identity_type: '',
        identity_type_name: '',
        contact_phone: '',
        mobile_phone: '',
        email: ''
      },
      userIdType: []
    }
  },

  mounted() {
    if (this.$route.query.id) {
      this.getView(this.$route.query.id)
    }
  },

  methods: {
    goToList() {
      this.$router.go(-1)
    },
    edit() {
      this.$router.push({ name: 'userAdd', query: { id: this.$route.query.id, isEdit: '1' } })
    },
    //
    deleteAccount() {
      const alert = {}
      alert.content = '是否确认删除此账号？'
      this.$alert(`<span>${alert.content}</span>`, '警告提示', {
        dangerouslyUseHTMLString: true,
        type: 'warning',
        showCancelButton: true,
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      })
        .then(() => {
          deleteAccountInfo(this.$route.query.id).then(res => {
            if (res.meta.code === '200') {
              this.$message({
                message: '删除成功',
                type: 'success'
              })
              this.$router.push({ name: 'userList' })
            }
          })
        })
        .catch(() => {})
    },
    // 获取用户信息详情
    getView(id) {
      // is_edit 是否编辑 编辑状态手机号脱敏
      getView(id,{is_edit:false}).then(res => {
        if (res.meta.code === '200') {
          Object.assign(this.checkform, res.data)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.info-wrap {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}
.info-wrap img {
  width: 35px;
  height: 35px;
  margin-right: 10px;
}
</style>
