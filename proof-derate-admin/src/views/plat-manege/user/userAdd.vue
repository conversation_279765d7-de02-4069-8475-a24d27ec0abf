<template>
  <div class="content-wrapper">
    <papeTitle :title-name="titleName" :is-has-back="true" @goToList="goToList">
      <template>
        <el-button type="primary" @click="submit">提交</el-button>
      </template>
    </papeTitle>
    <section class="content">
      <el-form ref="checkform" :model="checkform" label-width="200px" class="el-check-form" :rules="rules">
        <el-card class="box-card card1">
          <el-row :gutter="24">
            <el-col :span="20">
              <el-form-item label="账号" prop="account_name">
                <!-- <el-input v-model="checkform.account_name" clearable placeholder="请输入账号" /> -->
                <el-select
                  v-model="checkform.account_name"
                  :disabled="$route.query.isEdit==='1'"
                  filterable
                  placeholder="请选择"
                  style="width:100%"
                  @change="selectChange"
                >
                  <el-option v-for="item in optionList" :key="item.value" :label="item.label" :value="item.label" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="20">
              <el-form-item label="姓名">
                <el-input v-model="checkform.user_name" :disabled="true" clearable placeholder="请输入姓名" />
              </el-form-item>
            </el-col>
            <el-col :span="20">
              <el-form-item label="证件类型">
                <el-select v-model="checkform.identity_type" placeholder="请选择" style="width:100%">
                  <el-option v-for="item in userIdType" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="20">
              <el-form-item label="手机号">
                <el-input v-model="checkform.mobile_phone" clearable placeholder="请输入手机号" />
              </el-form-item>
            </el-col>
            <el-col :span="20">
              <el-form-item label="证件号码">
                <el-input v-model="checkform.identity_number" clearable placeholder="请输入证件号码" />
              </el-form-item>
            </el-col>
            <el-col :span="20">
              <el-form-item label="固定电话">
                <el-input v-model="checkform.contact_phone" clearable placeholder="请输入固定电话" />
              </el-form-item>
            </el-col>
            <el-col :span="20">
              <el-form-item label="电子邮箱">
                <el-input v-model="checkform.email" clearable placeholder="请输入电子邮箱" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>
      </el-form>
    </section>
  </div>
</template>

<script>
import papeTitle from '@/components/papeTitle'
import { accountInfoIdentityType } from '@/api/common/dict'
import { getAllAccount, create, getView, edit } from '@/api/user/index'
export default {
  components: {
    papeTitle
  },
  data() {
    return {
      titleName: '新增用户信息',
      checkform: {
        account: '',
        account_name: '',
        user_name: '',
        identity_number: '',
        identity_type: '',
        contact_phone: '',
        mobile_phone: '',
        email: ''
      },
      rules: {
        account_name: [{ required: true, message: '账号不能为空', trigger: 'blur' }]
      },
      userIdType: [
        { value: '10', label: '身份证' },
        { value: '11', label: '军官证' },
        { value: '12', label: '士兵证' },
        { value: '13', label: '警官证' },
        { value: '14', label: '港澳居民来往内地通行证' },
        { value: '15', label: '台湾居民来往大陆通行证' },
        { value: '40', label: '其他有效个人身份证件' }
      ],
      optionList: []
    }
  },

  mounted() {
    this.accountInfoIdentityType()
    this.getAllAccount()
    if (this.$route.query.isEdit === '1') {
      this.titleName = '编辑用户信息'
      this.getView(this.$route.query.id)
    }
  },

  methods: {
    goToList() {
      this.$router.go(-1)
    },
    // 获取证件类型
    accountInfoIdentityType() {
      accountInfoIdentityType().then(res => {
        if (res.meta.code === '200') {
          this.userIdType = res.data
        }
      })
    },
    getAllAccount() {
      getAllAccount().then(res => {
        // console.log(res)
        if (res.meta.code === '200') {
          this.optionList = res.data
        }
      })
    },
    selectChange(e) {
      const data = this.optionList.find(i => {
        return i.label === e
      })
      this.checkform.user_name = data.value
    },
    submit() {
      if (this.$route.query.isEdit === '1') {
        this.edit()
      } else {
        this.create()
      }
    },
    create() {
      this.$refs['checkform'].validate(valid => {
        if (valid) {
          if (this.checkform.identity_type === '') {
            delete this.checkform.identity_type
          }
          create(this.checkform).then(res => {
            if (res.meta.code === '200') {
              this.$message({
                message: '新增成功',
                type: 'success'
              })
              this.goToList()
            }
          })
        }
      })
    },
    edit() {
      this.$refs['checkform'].validate(valid => {
        if (valid) {
          if (this.checkform.identity_type === '') {
            delete this.checkform.identity_type
          }
          edit(this.checkform).then(res => {
            if (res.meta.code === '200') {
              this.$message({
                message: '修改成功',
                type: 'success'
              })
              this.goToList()
            }
          })
        }
      })
    },
    getView(id) {
      // is_edit 是否编辑 编辑状态手机号脱敏
      getView(id, { is_edit: true }).then(res => {
        if (res.meta.code === '200') {
          Object.assign(this.checkform, res.data)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
