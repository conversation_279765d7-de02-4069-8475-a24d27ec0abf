<template>
  <div class="detail-account">
    <el-card>
      <div class="header">
        <div class="icon">
          <img src="@/assets/commonPack_images/account-detail.png" alt="" srcset="">
        </div>
        <span class="tit">{{ accountInfo.operation_event }}</span>
      </div>
      <div class="detail-cont">
        <ul>
          <li v-for="item in accountData" :key="item.key">
            <span class="key">{{ item.key }}</span>
            <div class="value">
              <div v-if="item.type==='tag'" class="tag">
                <el-tag v-for="cloumn in item.value" :key="cloumn" size="small">{{ cloumn }}</el-tag>
              </div>
              <span v-else>{{ item.value }}</span>
            </div>
          </li>
        </ul>
      </div>
    </el-card>
  </div>
</template>

<script>
import { getOperationDetail } from '@/api/commonPack/platManege'

export default {
  data() {
    return {
      accountData: {},
      accountInfo: {}
    }
  },
  mounted() {
    this.getOperationDetail()
  },
  methods: {
    getOperationDetail() {
      getOperationDetail(this.$route.query).then(res => {
        if (res.meta.code !== '200') return
        const { data } = res
        this.accountInfo = data
        this.accountData = [
          { key: '用户账号：', value: data.user_account },
          { key: '用户姓名：', value: data.user_name },
          { key: '所属部门：', value: data.user_dept_name },
          { key: '客户端地址：', value: data.client_address },
          { key: '服务端地址：', value: data.server_address },
          { key: '操作事件：', value: data.operation_event },
          { key: '请求客户端：', value: data.request_client },
          { key: '请求地址：', value: data.request_url },
          { key: '操作结果：', value: data.operation_result === 'SUCCESS' ? '成功' : '失败' },
          { key: '备注说明：', value: data.remarks },
          { key: '操作时间：', value: data.last_modification_time }
        ]
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.detail-account{
  padding:10px;
  .header{
    height:40px;
    line-height: 40px;
    // margin: 10px 0;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #cccccc;
    .icon{
      width:22px;
      height: 22px;
      display: flex;
      align-items: center;
      img{
        width: 100%;
        height: 100%;
      }
    }
    .tit{
      padding-left: 5px;
    }
  }
  .detail-cont{
    margin-top: 40px;
    ul,li{
      list-style: none;
    }
    li{
      height: 40px;
      font-size: 14px;
      color: #333333;
      display: flex;
    }
    .key{
      width: 120px;
      text-align: right;;
      padding-right: 20px;
    }
  }
}

</style>
<style lang="scss">
.width50{
  width:130px;
  text-align: right !important;
}
.detail-account{
  .el-tag--small{
    border-radius: 13px;
  }
}
</style>
