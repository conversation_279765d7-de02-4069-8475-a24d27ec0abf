<template>
  <div class="department-container">
    <CardTitle :title-name="titleName">
      <template />
    </CardTitle>
    <el-card class="department-box">
      <el-form :model="searchForm" label-width="120px" @submit.native.prevent>
        <el-row :gutter="24">
          <el-col :span="9">
            <el-form-item label="操作事件">
              <el-input v-model="searchForm.operation_event" clearable placeholder="请输入操作事件" />
            </el-form-item>
          </el-col>
          <el-col :span="9">
            <el-form-item label="客户端IP">
              <el-input v-model="searchForm.client_address" clearable placeholder="请输入客户端IP" />
            </el-form-item>
          </el-col>
          <el-col :span="6" class="submitbtn">
            <el-button type="primary" plain native-type="submit" @click="onSubmit">查询</el-button>
            <el-button plain native-type="submit" @click="reset">重置</el-button>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="9">
            <el-form-item label="用户账号">
              <el-input v-model="searchForm.user_account" clearable placeholder="请输入用户账号" />
            </el-form-item>
          </el-col>
          <el-col :span="9">
            <el-form-item label="操作时间">
              <el-date-picker
                v-model="date"
                type="datetimerange"
                range-separator="至"
                value-format="yyyy-MM-dd HH:mm"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :picker-options="pickerOptions"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="9">
            <el-form-item label="操作结果">
              <el-radio-group v-model="searchForm.operation_result">
                <el-radio v-for="(item, idx) in operationResult" :key="idx" :label="item.value">{{ item.label }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <custom-table
        ref="table"
        :is-card-type="false"
        :table-data="tableData"
        :table-header="tableHeader"
        @query="query"
        @refresh="query(1)"
      >
        <template #operation_event="{ row }">
          <el-button v-if="isPermission(row.permission_codes,'log:access:view')" type="text" @click="getDetail(row,'log:access:view')">{{ row.operation_event }}</el-button>
          <span v-else>{{ row.operation_event }}</span>
        </template>
        <template #operation="{ row }">
          <span
            :class="row.operation_result === 'SUCCESS'?'success':row.operation_result === 'FAILURE' ?'fail':''"
          >{{ row.operation_result === 'SUCCESS' ? '成功' : row.operation_result === 'FAILURE' ? '失败' : '' }}</span>
        </template>
      </custom-table>
    </el-card>
  </div>
</template>

<script>
import { getLogOperationPage, deleteOrganization } from '@/api/commonPack/platManege'
import CustomTable from '@/components/Element/Table'
import { debounce, isPermission } from '@/utils'
import CardTitle from '@/components/CardTitle'
export default {
  components: {
    CustomTable,
    CardTitle
    // editDepartment: () => import('./components/edit-department.vue'),
    // newDepartment: () => import('./components/new-department.vue')
  },

  data() {
    return {
      titleName: '操作日志',
      selectDate: '',
      pickerOptions: {
        onPick: ({ maxDate, minDate }) => {
          console.log('==maxDate==', maxDate)
          console.log('==minDate==', minDate)
          this.selectDate = minDate
          if (maxDate) {
            this.selectDate = ''
          }
        },
        disabledDate: time => {
          if (this.selectDate !== '') {
            const nowTime = Date.now()
            if (time.getTime() > nowTime) {
              return true
            }
            if (this.selectDate.getFullYear() != time.getFullYear()) {
              return true
            }
          }
        }
      },
      searchForm: {
        operation_event: '',
        user_account: '',
        client_address: '',
        begin_time: '',
        end_time: '',
        operation_result: null,
        page_num: 1,
        page_size: 10
      },
      date: '',
      operationResult: [
        { value: null, label: '全部' },
        { value: 'SUCCESS', label: '成功' },
        { value: 'FAILURE', label: '失败' }
      ],
      tableData: {
        content: [], // 表格数据
        loading: false, // 控制表格加载效果
        total: 10, // 分页查询时的总数，去掉该属性，不显示分页条
        currentPage: 1, // 当前页码
        pageSize: 10,
        pageDirection: 'desc',
        isShowIndex: true,
        border: false
      },
      tableHeader: [
        {
          label: '操作事件',
          prop: 'operation_event',
          slot: 'operation_event',
          minWidth: '180px',
          align: 'left'
        },
        {
          label: '操作时间',
          prop: 'last_modification_time',
          minWidth: '160px',
          align: 'left'
        },
        {
          label: '用户账号',
          prop: 'user_account',
          minWidth: '100px',
          align: 'left'
        },
        {
          label: '客户端IP',
          prop: 'client_address',
          minWidth: '120px',
          align: 'left'
        },
        {
          label: '所属部门',
          prop: 'user_dept_name',
          minWidth: '200px',
          align: 'left'
        },
        {
          label: '操作结果',
          prop: 'operation_result',
          slot: 'operation',
          minWidth: '100px',
          align: 'left'
        }
      ],
      editData: {},
      editDialogVisible: false,
      addDialogVisible: false
    }
  },
  computed: {
    isDeparmentAdd() {
      return this.$store.state.breadcrumbBtn.platManage.isDeparmentAdd
    }
  },
  watch: {
    isDeparmentAdd(value) {
      // 监听到有变化就重新获取数据
      this.addDialogVisible = value
    }
  },
  mounted() {
    this.getLogOperationPage()
  },

  methods: {
    isPermission,
    searchHandler: debounce(
      function() {
        // 时间处理
        const dateTime = this.date
        if (dateTime) {
          this.searchForm.begin_time = dateTime[0]
          this.searchForm.end_time = dateTime[1]
        } else {
          this.searchForm.begin_time = ''
          this.searchForm.end_time = ''
        }
        this.searchForm.current = 1
        this.getLogOperationPage()
      },
      200,
      true
    ),
    getLogOperationPage() {
      getLogOperationPage(this.searchForm)
        .then(res => {
          if (res.data != null && res.meta.code === '200') {
            if (res.data.content != null) {
              this.tableData.content = res.data.content
              this.tableData.total = Number(res.data.total_elements)
            } else {
              this.tableData.content = []
              this.tableData.total = 0
            }
          }
        })
        .catch(() => {})
    },
    onSubmit() {
      this.searchForm.page_num = 1
      this.searchForm.page_size = 10
      this.tableData.currentPage = this.searchForm.page_num
      this.tableData.pageSize = this.searchForm.page_size
      this.searchHandler()
      // if (this.searchForm.page_num === 1) this.tableData.currentPage = 1
    },
    reset() {
      this.searchForm = {
        operation_event: '',
        user_account: '',
        client_address: '',
        operation_result: null,
        begin_time: '',
        end_time: '',
        page_num: 1,
        page_size: 10
      }
      this.date = ''
    },
    query() {
      this.searchForm.page_num = this.tableData.currentPage
      this.searchForm.page_size = this.tableData.pageSize
      this.getLogOperationPage()
    },
    getDetail(row, key) {
      if (isPermission(this.$route.meta.permission, key)) {
        this.$router.push({ name: 'OperationDetail', query: { id: row.id, year: row.year }})
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.department-box ::v-deep .gt-table-box .el-card__body {
  padding-top: 0;
}
.department-container {
  padding: 10px;
}
.department-box {
  &-title {
    height: 36px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 0px 0 30px 20px;
    &-h3 {
      margin: 0;
      padding: 0;
    }
    &-btn {
      display: flex;
      align-items: center;
      .btn {
        width: 36px;
        height: 36px;
        background: #f7f7f7;
        border: 1px solid #d9d9d9;
        border-radius: 4px;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        &:first-child {
          margin-right: 12px;
        }
        &.active {
          background: #e0f0ff;
          border: 1px solid #99ceff;
        }
      }
    }
  }
  .success {
    color: #67c23a;
  }
  .fail {
    color: #ff2b2b;
  }
}
</style>
<style lang="scss">
.department-container {
  .table-delete {
    color: #ff2b2b;
  }
}
</style>
