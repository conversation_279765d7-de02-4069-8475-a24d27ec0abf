<template>
  <div class="department-container">
    <CardTitle :title-name="titleName">
      <template />
    </CardTitle>
    <el-card class="department-box">
      <el-form :model="searchForm" label-width="120px" @submit.native.prevent>
        <el-row :gutter="24">
          <el-col :span="9">
            <el-form-item label="用户账号">
              <el-input v-model="searchForm.account" clearable placeholder="请输入用户账号" />
            </el-form-item>
          </el-col>
          <el-col :span="9">
            <el-form-item label="用户名称">
              <el-input v-model="searchForm.user_name" clearable placeholder="请输入用户名称" />
            </el-form-item>
          </el-col>
          <el-col :span="6" class="submitbtn">
            <el-button type="primary" plain native-type="submit" @click="onSubmit">查询</el-button>
            <el-button plain native-type="submit" @click="reset">重置</el-button>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="9">
            <el-form-item label="所属部门" prop="credit_code">
              <el-select v-model="searchForm.credit_code" filterable placeholder="请选择所属部门" clearable style="width: 100%;">
                <el-option v-for="item in departmentOptions" :key="item.name" :label="item.name" :value="item.credit_code" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <custom-table
        ref="table"
        :is-card-type="false"
        :table-data="tableData"
        :table-header="tableHeader"
        @query="query"
        @refresh="query(1)"
      >
        <template #operation="{ row }">
          <el-button v-permission="'auth:online_user:kick_out'" type="text" :disabled="!isPermission(row.permission_codes,'auth:online_user:kick_out')" @click="handleLogout(row, 'auth:online_user:kick_out')">强退</el-button>
        </template>
      </custom-table>
    </el-card>
  </div>
</template>

<script>
import { getOnlineUserList, getOrganizationList, kickOutOnlineUser } from '@/api/commonPack/platManege'
import CustomTable from '@/components/Element/Table'
import { debounce, isPermission } from '@/utils'
import { getOperationPermissionList } from '@/utils/index'
import CardTitle from '@/components/CardTitle'
export default {
  components: {
    CustomTable,
    CardTitle
  },

  data() {
    return {
      titleName: '在线用户',
      searchForm: {
        account: '',
        credit_code: '',
        page_num: 1,
        page_size: 10
      },
      departmentOptions: [],
      tableData: {
        content: [], // 表格数据
        loading: false, // 控制表格加载效果
        total: 10, // 分页查询时的总数，去掉该属性，不显示分页条
        currentPage: 1, // 当前页码
        pageSize: 10,
        pageDirection: 'desc',
        isShowIndex: true,
        border: false
      },
      tableHeader: [
        {
          label: '用户账号',
          prop: 'account',
          minWidth: '130px',
          align: 'left'
        },
        {
          label: '用户姓名',
          prop: 'user_name',
          minWidth: '130px',
          align: 'left'
        },
        {
          label: '所属部门',
          prop: 'org_name',
          minWidth: '200px',
          align: 'left'
        },
        {
          label: '登录IP',
          prop: 'ip',
          minWidth: '120px',
          align: 'left'
        },
        {
          label: '浏览器',
          prop: 'browser',
          minWidth: '200px',
          align: 'left'
        },
        {
          label: '登录时间',
          prop: 'last_login_time',
          minWidth: '200px',
          align: 'left'
        },
        {
          label: '操作',
          prop: 'operation',
          slot: 'operation',
          // minWidth: '100px',
          align: 'left'
        }
      ],
      editData: {},
      editDialogVisible: false,
      addDialogVisible: false,
      permissionList: ['auth:online_user:kick_out']
    }
  },

  mounted() {
    this.tableHeader = getOperationPermissionList(this.$route.meta.permission, this.permissionList, this.tableHeader)
    this.getOnlineUser()
    this.getOrganizationList()
  },

  methods: {
    isPermission,
    searchHandler: debounce(
      function() {
        this.searchForm.current = 1
        this.getOnlineUser()
      },
      200,
      true
    ),

    /**
     * 获取所属部门列表
     */
    getOrganizationList() {
      getOrganizationList({ scope: true, permission_code: 'auth:online_user:list' }).then(res => {
        /*  if (res.data.length === 1) {
          this.searchForm.credit_code = res.data[0].credit_code
        } */
        this.departmentOptions = res.data
      })
    },
    /**
     * 获取在线用户列表
     */
    getOnlineUser() {
      getOnlineUserList(this.searchForm)
        .then(res => {
          if (res.data != null && res.meta.code === '200') {
            if (res.data.content != null) {
              this.tableData.content = res.data.content
              this.tableData.total = Number(res.data.total_elements)
            } else {
              this.tableData.content = []
              this.tableData.total = 0
            }
          }
        })
        .catch(() => {})
    },
    onSubmit() {
      this.searchForm.page_num = 1
      this.searchForm.page_size = 10
      this.tableData.currentPage = this.searchForm.page_num
      this.tableData.pageSize = this.searchForm.page_size
      this.searchHandler()
      // if (this.searchForm.page_num === 1) this.tableData.currentPage = 1
    },
    reset() {
      this.searchForm = {
        account: '',
        user_name: '',
        credit_code: '',
        page_num: 1,
        page_size: 10
      }
      this.searchHandler()
    },
    query() {
      this.searchForm.page_num = this.tableData.currentPage
      this.searchForm.page_size = this.tableData.pageSize
      this.getOnlineUser()
    },
    handleLogout(row, key) {
      console.log('row', row)
      console.log('key', key)
      // if (isPermission(this.$route.meta.permission, key)) {
      this.$confirm('是否确认强退该用户？', '警告提示', {
        cancelButtonText: '取消',
        confirmButtonText: '确定',
        type: 'warning'
      })
        .then(() => {
          this.kickOut(row)
        })
        .catch(() => {})
    },

    /**
     * 强退
     */
    kickOut(row) {
      const params = {
        account: row.account,
        fingerprint: row.fingerprint
      }
      kickOutOnlineUser(params).then(res => {
        console.log('提出用户成功', res)
        this.$confirm('该用户已被强退成功', '成功提示', {
          cancelButtonText: '取消',
          confirmButtonText: '确定',
          type: 'success'
        })
          .then(() => {})
          .catch(() => {})
      })
        .catch(err => {
          console.log('提出用户失败', err)
        })
    }
  }
  // }
}
</script>

<style lang="scss" scoped>
.department-container {
  padding: 10px;
}

.department-box {
  &-title {
    height: 36px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 0px 0 30px 20px;

    &-h3 {
      margin: 0;
      padding: 0;
    }

    &-btn {
      display: flex;
      align-items: center;

      .btn {
        width: 36px;
        height: 36px;
        background: #f7f7f7;
        border: 1px solid #d9d9d9;
        border-radius: 4px;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;

        &:first-child {
          margin-right: 12px;
        }

        &.active {
          background: #e0f0ff;
          border: 1px solid #99ceff;
        }
      }
    }
  }

  .success {
    color: #67c23a;
  }
}

::v-deep .el-message-box__wrapper {
  height: 180px;
}
</style>
<style lang="scss">
.department-container {
  .table-delete {
    color: #ff2b2b;
  }
}
</style>
