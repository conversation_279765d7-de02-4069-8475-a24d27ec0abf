<template>
  <div id="detail-list">
    <el-card class="department-box">
      <div class="detail-list-application-edit-table">
        <div class="detail-list-application-edit-table-bar">
          <div class="detail-list-application-edit-table-bar-item">
            <img src="~@/assets/images/file.png" alt />
            <span>{{ name }}</span>
          </div>
          <span></span>
        </div>
        <el-divider></el-divider>
      </div>
      <custom-table ref="table" :is-card-type="false" :table-data="tableData" :table-header="tableHeader" @query="query"
        @refresh="query(1)">
        <!-- <template #key="{ row }">
          <div>
            <el-button type="text" @click="goDetail(row,'auth:account:view')">{{ row.key }}</el-button>
          </div>
        </template>-->
        <template #handle="{ row }">
          <div>
            <el-button v-permission="'auth:dict:detail:edit'" type="text" @click="handleEdit(row)">编辑</el-button>
            <el-button v-permission="'auth:dict:detail:del'" type="text" class="table-delete"
              @click="handleDelete(row)">删除</el-button>
          </div>
        </template>
      </custom-table>
    </el-card>
    <!-- 新建弹窗 -->
    <el-dialog :visible.sync="addDialogVisible" width="30%" title="新建" @close="handleClose">
      <el-form :model="addForm" :rules="addRules" ref="addForm" label-width="80px" @submit.native.prevent>
        <el-form-item label="字典标签" prop="key">
          <el-input v-model="addForm.key" clearable placeholder="请输入字典标签" />
        </el-form-item>

        <el-form-item label="字典值" prop="value">
          <el-input v-model="addForm.value" clearable placeholder="请输入字典值" />
        </el-form-item>

        <el-form-item label="描述" prop="description">
          <el-input v-model="addForm.description" maxlength="200" clearable placeholder="请输入描述" />
        </el-form-item>

        <el-form-item label="排序" key="sort">
          <el-input v-model="addForm.sort" maxlength="3" @input="changeValue" clearable placeholder="请输入排序" />
        </el-form-item>

        <el-form-item label="加密状态" prop="has_encrypt">
          <el-radio-group v-model="addForm.has_encrypt">
            <el-radio v-for="item in encryptOptions" :key="item.value" :label="item.value">{{ item.label }}</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div class="dialog-footer">
        <el-button @click="addCancel('addForm')">取 消</el-button>
        <el-button type="primary" @click="add('addForm')">确 定</el-button>
      </div>
    </el-dialog>

    <el-dialog :visible.sync="editDialogVisible" width="30%" title="编辑">
      <el-form :model="sendForm" :rules="addRules" ref="editForm" label-width="80px" @submit.native.prevent>
        <el-form-item label="字典标签" prop="key">
          <el-input v-model="sendForm.key" clearable placeholder="请输入字典标签" />
        </el-form-item>

        <el-form-item label="字典值" prop="value">
          <el-input v-model="sendForm.value" clearable placeholder="请输入字典值" />
        </el-form-item>

        <el-form-item label="描述" prop="description">
          <el-input v-model="sendForm.description" maxlength="200" clearable placeholder="请输入描述" />
        </el-form-item>

        <el-form-item label="排序" prop="sort">
          <el-input v-model="sendForm.sort" maxlength="3" @input="changeEditValue" clearable placeholder="请输入排序" />
        </el-form-item>

        <el-form-item label="加密状态" prop="has_encrypt">
          <el-radio-group v-model="sendForm.has_encrypt">
            <el-radio v-for="item in encryptOptions" :key="item.value" :label="item.value">{{ item.label }}</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div class="dialog-footer">
        <el-button @click="editCancel('editForm')">取 消</el-button>
        <el-button type="primary" @click="edit('editForm')">确 定</el-button>
      </div>
    </el-dialog>

    <el-dialog :visible.sync="dialogDelVisible" width="30%" title="提示">
      <div style="padding-bottom:20px">是否删除该字典明细？删除后将无法查看。</div>
      <div class="dialog-footer">
        <el-button @click="deleteCancel">取 消</el-button>
        <el-button type="primary" @click="deleteSubmit">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog :visible.sync="editPageDialogVisible" width="30%" title="编辑">
      <el-form :model="sendPageForm" :rules="editRules" ref="editPageForm" label-width="80px" @submit.native.prevent>
        <el-form-item label="字典名称" prop="name">
          <el-input v-model="sendPageForm.name" clearable placeholder="请输入字典名称" />
        </el-form-item>

        <el-form-item label="描述" prop="description">
          <el-input v-model="sendPageForm.description" clearable placeholder="请输入描述" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="sendPageForm.status">
            <el-radio v-for="item in statusOptions" :key="item.value" :label="item.value">{{ item.label }}</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div class="dialog-footer">
        <el-button @click="editPageCancel('editPageForm')">取 消</el-button>
        <el-button type="primary" @click="editPage('editPageForm')">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog :visible.sync="dialogPageDelVisible" width="30%" title="提示">
      <div style="padding-bottom:20px">是否删除该字典信息？删除后将无法查看。</div>
      <div class="dialog-footer">
        <el-button @click="deletePageCancel">取 消</el-button>
        <el-button type="primary" @click="deletePageSubmit">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getDictDetail, addDictDetail, editDictDetail, deleteDictDetail, editDict, getDictDetailView, deleteDict } from '@/api/commonPack/platManege'
import CustomTable from '@/components/Element/Table'
export default {
  components: {
    CustomTable
  },
  computed: {
    isAddEditConfigManage() {
      return this.$store.state.breadcrumbBtn.platManage.isAddEditConfigManage
    },
    isConfigManageDetailEdit() {
      return this.$store.state.breadcrumbBtn.platManage.isConfigManageDetailEdit
    },
    isConfigManageDetailDel() {
      return this.$store.state.breadcrumbBtn.platManage.isConfigManageDetailDel
    }
  },
  watch: {
    isAddEditConfigManage(value) {
      // 监听到有变化就重新获取数据
      if (value) {
        this.addDialogVisible = true
      }
    },
    isConfigManageDetailEdit(value) {
      console.log('isConfigManageDetailEdit')
      this.editPageDialogVisible = true
    },
    isConfigManageDetailDel() {
      console.log('isConfigManageDetailDel')
      this.dialogPageDelVisible = true
    }
  },
  data() {
    return {
      form: {
        dict_id: '',
        page_num: 1,
        page_size: 10
      },
      name: '',
      id: '',

      addForm: {
        dict_id: '',
        key: '',
        description: '',
        value: '',
        sort: '',
        has_encrypt: false
      },
      sendPageForm: {
        id: '',
        name: '',
        status: '',
      },
      tableData: {
        content: [], // 表格数据
        loading: false, // 控制表格加载效果
        total: 10, // 分页查询时的总数，去掉该属性，不显示分页条
        currentPage: 1, // 当前页码
        pageSize: 10,
        pageDirection: 'desc',
        isShowIndex: true,
        border: false
      },
      tableHeader: [
        {
          label: '字典标签',
          prop: 'key',
          minWidth: '160px'
          // align: 'left',
        },
        {
          label: '字典值',
          prop: 'value',
          minWidth: '120px'
          // align: 'left',
        },
        {
          label: '描述',
          prop: 'description',
          minWidth: '120px'
          // align: 'left',
        },
        {
          label: '加密状态',
          prop: 'has_encrypt',
          minWidth: '120px',
          // align: 'left',
          formatter: (row, col, val) => {
            return val === true ? '密文' : '明文'
            // return val
          }
        },
        {
          label: '排序',
          prop: 'sort',
          minWidth: '160px'
          // align: 'left',
        },
        {
          label: '操作',
          prop: '',
          slot: 'handle',
          minWidth: '160px'
          // align: 'left',
        }
      ],
      sendForm: {
        id: '',
        key: '',
        description: '',
        value: '',
        sort: '',
        has_encrypt: '',
      },
      encryptOptions: [
        { value: false, label: '明文' },
        { value: true, label: '密文' }
      ],
      statusOptions: [
        { value: 'PUBLISH', label: '发布' },
        { value: 'DISUSE', label: '废置' }
      ],
      editDialogVisible: false,
      editPageDialogVisible: false,
      addDialogVisible: false,
      dialogDelVisible: false,
      dialogPageDelVisible: false,
      addRules: {
        key: [{ required: true, message: '请输入字典标签', trigger: 'change' }],
        value: [{ required: true, message: '请输入字典值', trigger: 'change' }]
      },
      editRules: {
        name: [{ required: true, message: '请输入字典名称', trigger: 'change' }],
        status: [{ required: true, message: '请输入字典名称', trigger: 'blur' }]
      },
      deleteId: '' //删除id
    }
  },

  mounted() {
    this.id = this.$route.query.id
    this.name = this.$route.query.name
    this.query(1)
    this.getDictDetailView()
  },

  methods: {
    getDetail(id) {
      this.form.dict_id = id
      getDictDetail(this.form)
        .then(res => {
          console.log('获取详情成功', res)
          if (res.data != null && res.meta.code === '200') {
            if (res.data.content != null) {
              this.tableData.content = res.data.content
              this.tableData.total = Number(res.data.total_elements)
            } else {
              this.tableData.content = []
              this.tableData.total = 0
            }
          }
        })
        .catch(err => {
          console.log('获取详情失败', err)
        })
    },
    goDetail(row) { },
    query(currentPage) {
      if (currentPage) {
        this.tableData.currentPage = currentPage
      }
      this.form.page_num = this.tableData.currentPage
      this.form.page_size = this.tableData.pageSize
      this.getDetail(this.id)
    },
    handleEdit(row) {
      this.editDialogVisible = true
      console.log('row', row)
      this.sendForm = Object.assign({}, row)
    },
    handlePageEdit(row) {
      console.log('编辑数据', row)
      this.editPageDialogVisible = true
      this.sendPageForm = {
        id: row.id,
        name: row.name,
        description: row.description,
        status: row.status
      }
    },
    getDictDetailView() {
      getDictDetailView(this.$route.query.id).then(res => {
        if (res.meta.code === '200' && res.data !== null) {
          Object.assign(this.sendPageForm, res.data)
        }
      })
    },
    /**
     * 编辑
     */
    editPage(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          console.log('提交的表单', this.sendPageForm)
          editDict(this.sendPageForm)
            .then(res => {
              this.$message({
                type: 'success',
                message: '修改成功'
              })
              this.editPageDialogVisible = false
              console.log('编辑成功', res)
              this.$refs[formName].resetFields()
              this.query()
              this.getDictDetailView()
            })
            .catch(err => {
              this.$message({
                type: 'error',
                message: '修改失败'
              })
              this.editPageDialogVisible = false
              console.log('编辑失败', err)
            })
        } else {
          return false
        }
      })
    },
    /**
     * 编辑取消
     */
    editPageCancel(formName) {
      this.$refs[formName].resetFields()
      this.editPageDialogVisible = false
    },
    handleClose() {
      this.$store.commit('breadcrumbBtn/SET_PLATMANAGE', {
        isAddEditConfigManage: false
      })
    },

    /**
     * 新建
     */
    add(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.addDialogVisible = false
          this.addForm.dict_id = this.id
          addDictDetail(this.addForm)
            .then(res => {
              if ('200' === res.meta.code) {
                this.$refs[formName].resetFields()
                this.addForm = {
                  dict_id: '',
                  key: '',
                  description: '',
                  value: '',
                  sort: '',
                  has_encrypt: false
                }
                this.query(1)
              }
            })
            .catch(err => { })
        } else {
          return false
        }
      })
    },

    /**
     * 添加取消
     */
    addCancel(formName) {
      this.$refs[formName].resetFields()
      this.addDialogVisible = false
    },

    /**
     * 编辑
     */
    edit(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          console.log('提交的表单', this.sendForm)
          this.editDialogVisible = false
          this.sendForm.dict_id = this.id
          editDictDetail(this.sendForm)
            .then(res => {
              console.log('编辑成功', res)
              this.$refs[formName].resetFields()
              this.query(1)
            })
            .catch(err => {
              console.log('编辑失败', err)
            })
        } else {
          return false
        }
      })
    },
    deletePageCancel() {
      this.dialogPageDelVisible = false
    },
    deletePageSubmit() {
      deleteDict(this.id)
        .then(res => {
          console.log('删除成功', res)
          this.dialogPageDelVisible = false
          this.$router.go('-1')
        })
        .catch(err => {
          console.log('删除失败', err)
        })
    },
    /**
     * 编辑取消
     */
    editCancel(formName) {
      this.$refs[formName].resetFields()
      this.editDialogVisible = false
    },

    /**
     * 删除按钮
     */
    handleDelete(row) {
      this.dialogDelVisible = true
      this.deleteId = row.id
    },

    /**
     * 删除确认
     */
    deleteSubmit() {
      deleteDictDetail(this.deleteId)
        .then(res => {
          console.log('删除成功', res)
          this.dialogDelVisible = false
          this.query(1)
        })
        .catch(err => {
          console.log('删除失败', err)
        })
    },

    /**
     * 删除取消
     */
    deleteCancel() {
      this.dialogDelVisible = false
    },

    changeEditValue(value) {
      this.sendForm.sort = /^[0-9]*$/.test(parseInt(value)) ? String(parseInt(value)).replace('.', '') : ''
    },

    changeValue(value) {
      this.addForm.sort = /^[0-9]*$/.test(parseInt(value)) ? String(parseInt(value)).replace('.', '') : ''
    }
  }
}
</script>

<style lang="scss" scoped>
#detail-list {
  padding: 10px;

  .table-delete {
    color: #ff2b2b;
  }

  .dialog-footer {
    text-align: center;
  }
}

.detail-list-application-edit-table {
  margin-bottom: 20px;

  &-bar {
    display: flex;
    align-items: center;
    justify-content: space-between;

    //   margin-top: 51px;
    &-item {
      display: flex;
      align-items: flex-end;
    }

    img {
      width: 22px;
      height: 22px;
      margin-right: 5px;
    }

    span {
      color: #333333;
    }
  }
}

.detail-list-application-edit-table ::v-deep .el-divider--horizontal {
  margin: 9px 0 21px;
}
</style>
