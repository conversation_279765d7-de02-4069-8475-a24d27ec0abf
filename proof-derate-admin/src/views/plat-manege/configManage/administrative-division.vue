<template>
  <div class="administrative-division">
    <CardTitle :title-name="titleName">
      <template>
        <div class="department-box-icon-btn">
          <img
            v-if="isListAndTree == 'list'"
            src="~@/assets/commonPack_images/list-active-icon.png"
            alt
            @click="changeListAndTree('list')"
          >
          <img v-else src="~@/assets/commonPack_images/list-icon.png" alt @click="changeListAndTree('list')">
        </div>
        <div class="department-box-icon-btn">
          <img
            v-if="isListAndTree == 'tree'"
            src="~@/assets/commonPack_images/tree-active-icon.png"
            alt
            @click="changeListAndTree('tree')"
          >
          <img v-else src="~@/assets/commonPack_images/tree-con.png" alt @click="changeListAndTree('tree')">
        </div>

        <!-- <div class="btn active">
          <img src="@/assets/commonPack_images/list-active-icon.png" alt srcset />
        </div>
         <div class="btn active">
          <img src="@/assets/commonPack_images/list-active-icon.png" alt srcset />
        </div>-->
      </template>
    </CardTitle>
    <el-card class="department-box">
      <!-- <div slot="header" class="department-box-wrap">
        <span class="department-box-title">行政区划列表</span>
        <div class="department-box-icon">
          <div class="department-box-icon-btn">
            <img v-if="isListAndTree=='list'" src="~@/assets/commonPack_images/list-active-icon.png" alt @click="changeListAndTree('list')" />
            <img v-else src="~@/assets/commonPack_images/list-icon.png" alt @click="changeListAndTree('list')" />
          </div>
          <div class="department-box-icon-btn">
            <img v-if="isListAndTree=='tree'" src="~@/assets/commonPack_images/tree-active-icon.png" alt @click="changeListAndTree('tree')" />
            <img v-else src="~@/assets/commonPack_images/tree-con.png" alt @click="changeListAndTree('tree')" />
          </div>
        </div>
      </div>-->
      <div v-if="isListAndTree == 'list'">
        <el-form :model="sendFrom" label-width="120px" @submit.native.prevent>
          <el-row :gutter="24">
            <el-col :span="8">
              <el-form-item label="行政区划代码">
                <el-input v-model="searchFrom.code" clearable placeholder="请输入行政区划代码" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="行政区划名称">
                <el-input v-model="searchFrom.name" clearable placeholder="请输入行政区划名称" />
              </el-form-item>
            </el-col>
            <el-col :span="4" :offset="2" class="submitbtn">
              <el-button type="primary" plain native-type="submit" @click="onSubmit">查询</el-button>
              <el-button plain native-type="submit" @click="reset">重置</el-button>
            </el-col>
          </el-row>
        </el-form>

        <custom-table
          ref="table"
          :is-card-type="false"
          :table-data="tableData"
          :table-header="tableHeader"
          @query="query"
          @refresh="query(1)"
        >
          <!-- <template #code="{ row }">
            <div>
              <el-button type="text" @click="getDetail(row,'auth:account:view')">{{ row.code }}</el-button>
            </div>
          </template>-->
          <template #handle="{ row }">
            <div>
              <el-button v-permission="'auth:division:edit'" type="text" @click="handleEdit(row)">编辑</el-button>
              <el-button
                v-permission="'auth:division:del'"
                type="text"
                class="table-delete"
                @click="handleDel(row)"
              >删除</el-button>
            </div>
          </template>
        </custom-table>
      </div>
      <div v-if="isListAndTree == 'tree'" class="department-container">
        <div class="department-box-content">
          <div class="department-box-content-tree">
            <div class="tit">行政区划信息</div>
            <div class="tree">
              <el-scrollbar style="height: 555px">
                <el-tree
                  :data="data"
                  :props="defaultProps"
                  default-expand-all
                  node-key="value"
                  @node-click="handleNodeClick"
                >
                  <span slot-scope="{ node }" class="span-ellipsis">
                    <span>{{ node.label }}</span>
                  </span>
                </el-tree>
              </el-scrollbar>
            </div>
          </div>

          <div class="detail edit">
            <template v-if="data.length">
              <el-button
                v-permission="'auth:division:edit'"
                type="primary"
                plain
                class="edit-btn"
                @click="editDialogVisible = true"
              >编辑</el-button>
              <el-button
                v-permission="'auth:division:del'"
                type="primary"
                plain
                class="del-btn"
                @click="handleDel(sendEditFrom)"
              >删除</el-button>
            </template>
            <div class="tree-content">
              <p>
                <span>行政区划代码</span>
                <span>{{ sendEditFrom.code }}</span>
              </p>
              <p>
                <span>行政区划名称</span>
                <span>{{ sendEditFrom.name }}</span>
              </p>
              <p>
                <span>上级行政区划</span>
                <span>{{ sendEditFrom.parent_code }}</span>
              </p>
            </div>
          </div>
        </div>
      </div>
    </el-card>
    <el-dialog :visible.sync="editDialogVisible" width="30%" title="编辑" @close="handleEditClose" @open="handleEditOpen">
      <el-form ref="eddForm" :model="sendEditFrom" label-width="120px" @submit.native.prevent>
        <el-row :gutter="24">
          <el-col :span="24">
            <el-form-item label="行政区划代码" prop="code" :rules="[{ required: true, message: '请输入行政区划代码', trigger: 'blur' }]">
              <el-input v-model="sendEditFrom.code" clearable placeholder="请输入字典名称" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="24">
            <el-form-item label="行政区划名称" prop="name" :rules="[{ required: true, message: '请输入行政区划名称', trigger: 'blur' }]">
              <el-input v-model="sendEditFrom.name" clearable placeholder="请输入描述" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="24">
            <el-form-item label="上级行政区划">
              <!-- <el-input v-model="sendAddFrom.parent_code" clearable placeholder="请输入描述" /> -->
              <el-select v-model="sendEditFrom.parent_code" filterable clearable placeholder="请选择">
                <el-option v-for="item in divisionEditList" :key="item.code" :label="item.name" :value="item.code" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div class="dialog-footer">
        <el-button @click="editDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="divisionEdit">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog :visible.sync="dialogAddVisible" width="30%" title="新增" @close="handleClose">
      <el-form ref="addForm" :model="sendAddFrom" label-width="120px" @submit.native.prevent>
        <el-row :gutter="24">
          <el-col :span="24">
            <el-form-item prop="code" label="行政区划代码" :rules="[{ required: true, message: '请输入行政区划代码', trigger: 'blur' }]">
              <el-input v-model="sendAddFrom.code" clearable placeholder="请输入行政区划代码" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="24">
            <el-form-item label="行政区划名称" prop="name" :rules="[{ required: true, message: '请输入行政区划名称', trigger: 'blur' }]">
              <el-input v-model="sendAddFrom.name" clearable placeholder="请输入行政区划名称" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="24">
            <el-form-item label="上级行政区划">
              <!-- <el-input v-model="sendAddFrom.parent_code" clearable placeholder="请输入描述" /> -->
              <el-select v-model="sendAddFrom.parent_code" filterable clearable placeholder="请选择">
                <el-option v-for="item in divisionList" :key="item.code" :label="item.name" :value="item.code" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div class="dialog-footer">
        <el-button @click="dialogAddVisible = false">取 消</el-button>
        <el-button type="primary" @click="addDivision">确 定</el-button>
      </div>
    </el-dialog>
    <!-- <fileDialog ref="fileDialog" :whitelist="whitelist" :file-size-limit="fileSizeLimit"
      :file-dialog-title="fileDialogTitle" :multiple="multiple" :is-show-temple="true" :is-check-file-name="true"
      application-type="divisionCode" @dialogClose="dialogClose" @getFilelist="getFilelist" @downTemple="exportTemple" /> -->
    <!-- <el-dialog title="提示" :visible.sync="importErrorDialog" width="30%" :show-close="false" top="300px">
      <p>
        <i class="el-icon-warning-outline" style="color: chocolate;font-size: 18px;"></i>
        {{importFailList.length}}条信息导入失败，失败原因如下：
      </p>
      <div class="el-dialog-div">
        <div class="error-content" v-for="(item, index) in importFailList" :key="index">
          {{item}}
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="importErrorDialog = false">确 定</el-button>
        <el-button @click="importErrorDialog = false">取 消</el-button>
      </span>
    </el-dialog> -->
    <!-- <import-error-dialog ref="importErrorDialog" :importFailList="importFailList"></import-error-dialog> -->
    <fileDialog
      ref="fileDialog"
      :accept="accept"
      :limit="limit"
      :file-size-limit="fileSizeLimit"
      :is-show-temple="isShowTemple"
      :import-result-data="importResultData"
      @getFilelist="getFilelist"
      @downTemple="exportTempleDownload"
    />
  </div>
</template>

<script>
import CustomTable from '@/components/Element/Table'
import fileDialog from '@/components/fileDialog/index2.vue'
import { exportsDown } from '@/utils'
// import clip from '@/utils/clipboard'
import { convertToCamelCase } from '@/utils/index.js'
import { getDivisionlPage, divisionCreate, getDivisionList, delDivision, editDivision, getDivisionTree, importDivision, divisionDownTemplate } from '@/api/commonPack/platManege'
import { getOperationPermissionList } from '@/utils/index'
import CardTitle from '@/components/CardTitle'
export default {
  components: {
    CustomTable,
    fileDialog,
    CardTitle
    // ImportErrorDialog: () => import('@/components/ImportErrorDialog')
  },

  data() {
    return {
      accept: '.xlsx,.xls',
      fileSizeLimit: 1,
      limit: 1,
      isShowTemple: true,
      importResultData: {},

      titleName: '行政区划列表',
      logo: 'Group1',
      whitelist: ['xlsx', 'xls'],
      fileSizeLimit: 1,
      multiple: false,
      fileDialogTitle: '导入文件',
      data: [],
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      sendFrom: {
        user_name: '',
        account: '',
        division_code: '',
        status: null,
        radio: 3,
        page_num: 1,
        page_size: 10
      },
      searchFrom: {
        name: '',
        code: '',
        page_num: 1,
        page_size: 10
      },

      sendAddFrom: {
        name: '',
        code: '',
        parent_code: '',
        level: 'PARENT'
      },
      sendEditFrom: {
        name: '',
        code: '',
        parent_code: '',
        level: 'PARENT'
      },
      statusOptions: [
        { value: null, label: '全部' },
        { value: 'NORMAL', label: '正常' },
        { value: 'DISABLE', label: '禁用' }
      ],
      tableData: {
        content: [], // 表格数据
        loading: false, // 控制表格加载效果
        total: 10, // 分页查询时的总数，去掉该属性，不显示分页条
        currentPage: 1, // 当前页码
        pageSize: 10,
        pageDirection: 'desc',
        isShowIndex: true,
        border: false
      },
      tableHeader: [
        {
          label: '行政区划代码',
          prop: 'code',
          minWidth: '160px',
          align: 'left'
        },
        {
          label: '行政区划名称',
          prop: 'name',
          minWidth: '250px',
          align: 'left'
        },
        {
          label: '上级区划代码',
          prop: 'parent_code',
          minWidth: '160px',
          align: 'left'
        },

        {
          label: '操作',
          prop: '',
          slot: 'handle',
          minWidth: '160px',
          align: 'left'
        }
      ],
      divisionList: [],
      divisionEditList: [],
      editData: {},
      editDialogVisible: false,
      repwDialogVisible: false,
      resetPassword: '',
      isCopypassword: false,
      passwordPlus: '',
      dialogAddVisible: false,
      isListAndTree: 'list',
      permissionList: ['auth:division:edit', 'auth:division:del'],
      importErrorDialog: false, // 导入报错弹窗提示
      importFailList: []// 导入报错提示
    }
  },
  computed: {
    isaddAdministrativeDivision() {
      return this.$store.state.breadcrumbBtn.platManage.isaddAdministrativeDivision
    },
    fileImportAdministrativeDivision() {
      return this.$store.state.breadcrumbBtn.platManage.fileImportAdministrativeDivision
    }
  },
  watch: {
    isaddAdministrativeDivision(value) {
      // 监听到有变化就重新获取数据
      if (value) {
        this.dialogAddVisible = true
      }
    },
    fileImportAdministrativeDivision(value) { // 导入
      // 监听到有变化就重新获取数据
      if (value) {
        this.$refs.fileDialog.dialogVisible = true
        this.$store.commit('breadcrumbBtn/SET_PLATMANAGE', {
          fileImportAdministrativeDivision: false
        })
      }
    },
    isListAndTree(val) {
      if (val === 'tree') {
        console.log(this.data[0])
        this.sendEditFrom = {
          id: this.data[0].expand,
          name: this.data[0].label,
          code: this.data[0].value,
          parent_code: this.data[0].parent
        }
      }
    }
  },
  mounted() {
    this.tableHeader = getOperationPermissionList(this.$route.meta.permission, this.permissionList, this.tableHeader)
    this.getDivisionlPage()
    this.getDivisionList()
    this.getDivisionTree()
  },

  methods: {
    /**
     * 下载导入模板
     */
    exportTempleDownload() {
      exportsDown(divisionDownTemplate(), {}, '', 'licc')
    },
    getDivisionlPage() {
      getDivisionlPage(this.searchFrom).then(res => {
        if (res.meta.code === '200') {
          this.tableData.content = res.data.content
          this.tableData.total = Number(res.data.total_elements)
          // this.tableData.currentPage = res.data.page_num
          // this.tableData.pageSize = res.data.page_size
        }
      })
    },
    getDivisionTree() {
      getDivisionTree().then(res => {
        if (res.meta.code === '200') {
          this.data = res.data
        }
      })
    },
    addDivision() {
      console.log('this.sendAddFrom', this.sendAddFrom)
      this.$refs.addForm.validate(valid => {
        if (valid) {
          console.log('this.sendAddFrom', this.sendAddFrom)

          this.divisionCreate()
        }
      })
    },
    divisionEdit() {
      this.$refs.eddForm.validate(valid => {
        if (valid) {
          this.editDivision()
        }
      })
    },
    editDivision() {
      editDivision(this.sendEditFrom).then(res => {
        this.editDialogVisible = false
        if (res.meta.code === '200') {
          this.$message({
            message: '编辑成功！',
            type: 'success'
          })
          this.getDivisionlPage()
          this.getDivisionTree()
        } else {
          this.$message({
            message: res.meta.message,
            type: 'warning'
          })
        }
      })
    },
    divisionCreate() {
      // console.log('this.sendAddFrom', this.sendAddFrom)
      divisionCreate(this.sendAddFrom).then(res => {
        this.dialogAddVisible = false
        this.sendAddFrom = {
          name: '',
          code: '',
          parent_code: ''
        }
        if (res.meta.code === '200') {
          this.$message({
            message: '新增成功！',
            type: 'success'
          })
          this.getDivisionlPage()
          this.getDivisionTree()
        } else {
          this.$message({
            message: res.meta.message,
            type: 'warning'
          })
        }
      })
    },
    getDivisionList() {
      const data = {
        // code: '',
        // level: 1,
      }
      getDivisionList(data).then(res => {
        if (res.meta.code === '200' && res.data != null) {
          this.divisionList = res.data
        }
      })
    },
    getEditDivisionList() {
      const data = {
        // code: '',
        // level: 1,
        filter_code: [this.sendEditFrom.code].join(',')
      }
      // data.filter_code.join(',')
      getDivisionList(data).then(res => {
        if (res.meta.code === '200' && res.data != null) {
          this.divisionEditList = res.data
          console.log(this.divisionEditList)
        }
      })
    },

    onSubmit() {
      // this.getAccountList()
      this.searchFrom.page_num = 1
      this.searchFrom.page_size = 10
      this.tableData.currentPage = this.searchFrom.page_num
      this.tableData.pageSize = this.searchFrom.page_size
      this.getDivisionlPage()
    },

    reset() {
      this.searchFrom = {
        name: '',
        code: '',
        page_num: 1,
        page_size: 10
      }

      // this.tableData.currentPage = 1
    },
    query() {
      this.searchFrom.page_num = this.tableData.currentPage
      this.searchFrom.page_size = this.tableData.pageSize
      this.getDivisionlPage()
    },
    getDivisionCode(val) {
      this.sendFrom.division_code = val
    },

    handleEdit(row) {
      //   this.setEditDialogVisible()
      this.editDialogVisible = true
      this.sendEditFrom = {
        id: row.id,
        name: row.name,
        code: row.code,
        parent_code: row.parent_code
      }
    },
    handleDel(row) {
      console.log(row.code)
      this.$confirm('是否删除改行政区划, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          delDivision(row.code).then(res => {
            if (res.meta.code === '200') {
              this.$message({
                type: 'success',
                message: '删除成功!'
              })
              this.getDivisionlPage()
              this.getDivisionTree()
            }
          })

          // })
        })
        .catch(() => {
          // this.$message({
          //   type: 'info',
          //   message: '已取消删除',
          // })
        })
    },
    getDetail(row, key) {
      this.$router.push({ name: 'configManageDetail' })
    },

    setEditDialogVisible(val) {
      this.editDialogVisible = !this.editDialogVisible
      //   this.getAccountList()
    },
    toggleListTree(val) {
      this.$router.push({ name: 'DepartmentTree' })
    },
    handleClose() {
      this.$refs.addForm.resetFields()
      this.$store.commit('breadcrumbBtn/SET_PLATMANAGE', {
        isaddAdministrativeDivision: false
      })
    },
    handleEditClose() {
      this.$refs.eddForm.resetFields()
    },
    handleEditOpen() {
      this.getEditDivisionList()
    },
    changeListAndTree(val) {
      this.isListAndTree = val
    },
    handleNodeClick(data) {
      this.sendEditFrom = {
        id: data.expand,
        name: data.label,
        code: data.value,
        parent_code: data.parent
      }
    },
    dialogClose() {
      this.$store.commit('breadcrumbBtn/SET_PLATMANAGE', {
        fileImportAdministrativeDivision: false
      })
      this.$refs.fileDialog.fileList = []
    },
    getFilelist(list) {
      console.log(list, this.$refs.fileDialog.fileList)
      this.importDivision()
    },
    exportTemple() {
      /*  console.log(process.env.VUE_DOWN_API_URL)
      const url = process.env.VUE_APP_DOWN_URL + '/materials-api/auth/v1/common/menu/export'
      window.open(url, '导出菜单') */
      exportsDown('/auth/webapi/v1/common/division/download_template', {}, '行政区划导入模板.xlsx')
    },
    importDivision() {
      if (this.$refs.fileDialog.fileList.length != 0) {
        // importDivision().then(res => {})
        const params = {
          config: this.$refs.fileDialog.config
        }
        const file = this.$refs.fileDialog.fileList[0].raw
        const fd = new FormData()
        fd.append('file', file)
        importDivision(fd, params).then(res => {
          const { common } = convertToCamelCase(res.data)
          this.$refs.fileDialog.dialogVisible = false
          this.$refs.fileDialog.fileList = []
          this.importResultData = common
          this.$refs.fileDialog.dialogVisibleStatus = true
          /* this.$refs.fileDialog.fileList = []
          const data = res.data
          if (res.meta.code !== '200') {
            this.$message({
              type: 'error',
              dangerouslyUseHTMLString: true,
              message: res.meta.message
            })
          } else {
            if (!data) { // 完全请求成功
              this.$message({
                type: 'success',
                message: '全部信息导入成功'
              })
            } else {
              this.importFailList = res.data
              this.$refs.importErrorDialog.dialogVisible = true
            }
            this.$refs.fileDialog.loading = false
            this.$refs.fileDialog.dialogVisible = false
          } */
          this.getDivisionlPage()
          this.getDivisionTree()
        }).catch(() => {
          this.$refs.fileDialog.dialogVisible = false
          this.$refs.fileDialog.fileList = []
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.administrative-division {
  padding: 10px;
}

// .department-box {
//   &-wrap {
//     display: flex;
//     justify-content: space-between;
//   }

//   &-icon {
//     display: flex;

//     &-btn {
//       width: 36px;
//       height: 36px;
//       border: 1px solid #d9d9d9;
//       border-radius: 4px;
//       display: flex;
//       justify-content: center;
//       align-items: center;
//       cursor: pointer;
//       margin-right: 12px;
//     }

//     img {
//       // margin: 0 5px;
//       cursor: pointer;
//     }
//   }

//   &-title {
//     font-size: 16px;
//     font-weight: 600;

//     &-h3 {
//       margin: 0;
//       padding: 0;
//     }

//     &-btn {
//       display: flex;
//       align-items: center;

//       .btn {
//         width: 36px;
//         height: 36px;
//         background: #f7f7f7;
//         border: 1px solid #d9d9d9;
//         border-radius: 4px;
//         display: flex;
//         justify-content: center;
//         align-items: center;
//         cursor: pointer;

//         &:first-child {
//           margin-right: 12px;
//         }

//         &.active {
//           background: #e0f0ff;
//           border: 1px solid #99ceff;
//         }
//       }
//     }
//   }
// }

.department-box {
  &-icon {
    display: flex;

    &-btn {
      width: 36px;
      height: 36px;
      border: 1px solid #d9d9d9;
      border-radius: 4px;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      margin-right: 12px;
    }
    img {
      // margin: 0 5px;
      cursor: pointer;
    }
  }

  &-title {
    height: 36px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 0px 0 30px 20px;

    &-h3 {
      margin: 0;
      padding: 0;
    }

    &-btn {
      display: flex;
      align-items: center;

      .btn {
        width: 36px;
        height: 36px;
        background: #f7f7f7;
        border: 1px solid #d9d9d9;
        border-radius: 4px;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;

        &:first-child {
          margin-right: 12px;
        }

        &.active {
          background: #e0f0ff;
          border: 1px solid #99ceff;
        }
      }
    }
  }

  &-content {
    display: flex;

    &-tree {
      // width: 245px;
      width: 485px;
      border: 1px solid #e9e9e9;

      .tit {
        height: 50px;
        line-height: 50px;
        font-size: 14px;
        color: #666;
        background: #e9e9e9;
        padding-left: 15px;
      }

      .tree {
        padding: 15px 20px 30px 15px;
        // overflow: auto;
        // height: 500px;
      }
    }

    .detail {
      flex: 1;
      border-left: none;
      position: relative;
      padding-top: 30px;

      &.edit {
        border: 1px solid #d9d9d9;
      }

      .edit-btn {
        position: absolute;
        top: 20px;
        right: 100px;
      }

      .del-btn {
        position: absolute;
        top: 20px;
        right: 20px;
      }
    }
  }
}

.tree-title {
  font-size: 16px;
  color: #333;
  font-weight: 600;
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.tree-wrap ::v-deep .el-scrollbar__wrap {
  overflow-x: hidden;
}

.tree-content {
  margin-left: 20px;

  // margin-top: 20px;
  span {
    margin-left: 20px;
  }
}

::v-deep .el-dialog__footer {
  text-align: center;
}

::v-deep .el-dialog__body {
  padding: 0px 20px 30px 20px;
}

.detail {
  flex: 1;
  border-left: none;
  position: relative;
  padding-top: 30px;

  &.edit {
    border: 1px solid #d9d9d9;
  }

  .edit-btn {
    position: absolute;
    top: 20px;
    right: 100px;
  }

  .del-btn {
    position: absolute;
    top: 20px;
    right: 20px;
  }
}
</style>
<style lang="scss">
.administrative-division {
  .table-delete {
    color: #ff2b2b;
  }

  .copy-passsword {
    color: red;
  }

  .reset-password {
    .el-dialog__body {
      padding: 0px 20px;
    }
  }
}

.dialog-footer {
  text-align: center;
}

/*树悬浮框: 名字过长则显示...*/
.span-ellipsis {
  width: calc(100% - 4px);
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  min-width: auto;
  padding: 4px 2px;
  border-radius: 4px;
}
.administrative-division ::v-deep .el-scrollbar__wrap {
  overflow-x: hidden;
}
</style>
