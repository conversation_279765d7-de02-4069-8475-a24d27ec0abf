<template>
  <div class="account-container">
    <CardTitle :title-name="titleName">
      <template />
    </CardTitle>
    <el-card class="department-box">
      <el-form :model="form" label-width="120px" @submit.native.prevent>
        <el-row :gutter="24">
          <el-col :span="8">
            <el-form-item label="名称">
              <el-input v-model="form.name" clearable placeholder="请输入名称" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="描述">
              <el-input v-model="form.description" clearable placeholder="请输入描述" />
            </el-form-item>
          </el-col>
          <el-col :span="4" :offset="2" class="submitbtn">
            <el-button type="primary" plain native-type="submit" @click="onSubmit">查询</el-button>
            <el-button plain native-type="submit" @click="reset">重置</el-button>
          </el-col>
        </el-row>
      </el-form>

      <custom-table
        ref="table"
        :is-card-type="false"
        :table-data="tableData"
        :table-header="tableHeader"
        @query="query"
        @refresh="query(1)"
      >
        <template #name="{ row }">
          <el-button
            type="text"
            v-if="isPermission($route.meta.permission, 'auth:dict:view')"
            @click="getDetail(row,'auth:dict:view')"
          >{{ row.name }}</el-button>
          <span v-else>{{ row.name }}</span>
        </template>
        <template #handle="{ row }">
          <div>
            <el-button v-permission="'auth:dict:edit'" type="text" @click="handleEdit(row)">编辑</el-button>
            <el-button v-permission="'auth:dict:del'" type="text" class="table-delete" @click="handleDelete(row)">删除</el-button>
          </div>
        </template>
      </custom-table>
    </el-card>
    <el-dialog :visible.sync="editDialogVisible" width="30%" title="编辑">
      <el-form ref="editForm" :model="sendForm" :rules="editRules" label-width="80px" @submit.native.prevent>
        <el-form-item label="字典名称" prop="name">
          <el-input v-model="sendForm.name" clearable placeholder="请输入字典名称" />
        </el-form-item>

        <el-form-item label="描述" prop="description">
          <el-input v-model="sendForm.description" clearable placeholder="请输入描述" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="sendForm.status">
            <el-radio v-for="item in statusOptions" :key="item.value" :label="item.value">{{ item.label }}</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div class="dialog-footer">
        <el-button @click="editCancel('editForm')">取 消</el-button>
        <el-button type="primary" @click="edit('editForm')">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog :visible.sync="dialogAddVisible" width="30%" title="新建" @close="handleClose">
      <el-form ref="addForm" :model="addForm" :rules="addRules" label-width="80px" @submit.native.prevent>
        <el-form-item label="字典名称" prop="name">
          <el-input v-model="addForm.name" clearable placeholder="请输入字典名称" />
        </el-form-item>

        <el-form-item label="描述" prop="description">
          <el-input v-model="addForm.description" clearable placeholder="请输入描述" />
        </el-form-item>
      </el-form>
      <div class="dialog-footer">
        <el-button @click="addCancel('addForm')">取 消</el-button>
        <el-button type="primary" @click="add('addForm')">确 定</el-button>
      </div>
    </el-dialog>

    <el-dialog :visible.sync="dialogDelVisible" width="30%" title="提示">
      <div style="padding-bottom:20px">是否删除该字典信息？删除后将无法查看。</div>
      <div class="dialog-footer">
        <el-button @click="deleteCancel">取 消</el-button>
        <el-button type="primary" @click="deleteSubmit">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getDictList, addDict, editDict, deleteDict } from '@/api/commonPack/platManege'
import CustomTable from '@/components/Element/Table'
import { getOperationPermissionList, isPermission } from '@/utils/index'
import CardTitle from '@/components/CardTitle'
export default {
  components: {
    CustomTable,
    CardTitle
  },

  data() {
    return {
      titleName: '字典管理',
      form: {
        name: '',
        description:'',
        page_num: 1,
        page_size: 10
      },
      sendForm: {
        id: '',
        name: '',
        description: '',
        status: ''
      },
      addForm: {
        name: '', // 字典名称
        description: '' // 描述
      }, // 新建
      addRules: {
        name: [{ required: true, message: '请输入字典名称', trigger: 'change' }]
      },
      editRules: {
        name: [{ required: true, message: '请输入字典名称', trigger: 'change' }],
        status: [{ required: true, message: '请输入字典名称', trigger: 'blur' }]
      },
      statusOptions: [
        { value: 'PUBLISH', label: '发布' },
        { value: 'DISUSE', label: '废置' }
      ],
      tableData: {
        content: [], // 表格数据
        loading: false, // 控制表格加载效果
        total: 10, // 分页查询时的总数，去掉该属性，不显示分页条
        currentPage: 1, // 当前页码
        pageSize: 10,
        pageDirection: 'desc',
        isShowIndex: true,
        border: false
      },
      tableHeader: [
        {
          label: '名称',
          prop: 'name',
          slot: 'name',
          minWidth: '160px',
          align: 'left'
        },
        {
          label: '描述',
          prop: 'description',
          minWidth: '160px',
          align: 'left'
        },
        {
          label: '状态',
          prop: 'status',
          minWidth: '160px',
          align: 'left',
          formatter: (row, col, val) => {
            return val === null ? val : val === 'PUBLISH' ? '已发布' : '已废置'
          }
        },
        {
          label: '最后更新时间',
          prop: 'last_modification_time',
          minWidth: '160px',
          align: 'left'
        },
        {
          label: '操作',
          prop: '',
          slot: 'handle',
          minWidth: '160px',
          align: 'left'
        }
      ],
      editDialogVisible: false,
      dialogAddVisible: false,
      dialogDelVisible: false,
      deleteId: '', // 删除id
      permissionList: ['auth:dict:edit', 'auth:dict:del']
    }
  },

  computed: {
    isaddConfigManage() {
      return this.$store.state.breadcrumbBtn.platManage.isaddConfigManage
    }
  },
  watch: {
    isaddConfigManage(value) {
      // 监听到有变化就重新获取数据
      if (value) {
        this.dialogAddVisible = true
      }
    }
  },

  mounted() {
    this.tableHeader = getOperationPermissionList(this.$route.meta.permission, this.permissionList, this.tableHeader)
    this.getList()
  },

  methods: {
    isPermission,
    getList() {
      getDictList(this.form)
        .then(res => {
          if (res.data != null && res.meta.code === '200') {
            if (res.data.content != null) {
              this.tableData.content = res.data.content
              this.tableData.total = Number(res.data.total_elements)
            } else {
              this.tableData.content = []
              this.tableData.total = 0
            }
          }
        })
        .catch(err => {})
    },
    onSubmit() {
      this.form.page_num = 1
      this.form.page_size = 10
      this.tableData.currentPage = this.form.page_num
      this.tableData.pageSize = this.form.page_size
      this.getList()
      // if (this.form.page_num === 1) this.tableData.currentPage = 1
    },
    reset() {
      this.form = {
        name: '',
        description:'',
        page_num: 1,
        page_size: 10
      }
      this.getList()
    },
    query() {
      this.form = {
        name: '',
        description:'',
        page_num: 1,
        page_size: 10
      }
      this.form.page_num = this.tableData.currentPage
      this.form.page_size = this.tableData.pageSize
      this.getList()
    },

    handleClose() {
      this.$refs['addForm'].resetFields()
      this.$store.commit('breadcrumbBtn/SET_PLATMANAGE', { isaddConfigManage: false })
    },

    handleEdit(row) {
      console.log('编辑数据', row)
      this.editDialogVisible = true
      this.sendForm = {
        id: row.id,
        name: row.name,
        description: row.description,
        status: row.status
      }
    },
    /**
     * 编辑
     */
    edit(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          console.log('提交的表单', this.sendForm)
          editDict(this.sendForm)
            .then(res => {
              this.editDialogVisible = false
              console.log('编辑成功', res)
              this.$refs[formName].resetFields()
              this.query()
            })
            .catch(err => {
              this.editDialogVisible = false
              console.log('编辑失败', err)
            })
        } else {
          return false
        }
      })
    },

    /**
     * 编辑取消
     */
    editCancel(formName) {
      this.$refs[formName].resetFields()
      this.editDialogVisible = false
    },

    getDetail(row, key) {
      console.log('row', row)
      if (isPermission(this.$route.meta.permission, key)) {
        this.$router.push({
          name: 'configManageDetail',
          query: {
            id: row.id,
            name: row.name
          }
        })
      }
    },

    add(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          addDict(this.addForm)
            .then(res => {
              if (res.meta.code === '200') {
                this.dialogAddVisible = false
                this.$refs[formName].resetFields()
                this.query()
              }
            })
            .catch(err => {
              this.editDialogVisible = false
            })
        } else {
          return false
        }
      })
    },

    /**
     * 添加取消
     */
    addCancel(formName) {
      this.$refs[formName].resetFields()
      this.dialogAddVisible = false
    },

    /**
     * 删除按钮
     */
    handleDelete(row) {
      this.dialogDelVisible = true
      this.deleteId = row.id
    },

    /**
     * 删除确认
     */
    deleteSubmit() {
      deleteDict(this.deleteId)
        .then(res => {
          console.log('删除成功', res)
          this.dialogDelVisible = false
          this.query()
        })
        .catch(err => {
          console.log('删除失败', err)
        })
    },

    /**
     * 删除取消
     */
    deleteCancel() {
      this.dialogDelVisible = false
    }
  }
}
</script>

<style lang="scss" scoped>
.account-container {
  padding: 10px;
  .reset-password {
    .boby {
      display: flex;
      align-items: center;
      .status {
        color: #67c23a;
        // transform: translateY(-50%);
        font-size: 24px !important;
      }
      .content {
        padding-left: 12px;
        padding-right: 12px;
      }
    }
  }
}
.department-box {
  &-title {
    height: 36px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 0px 0 30px 20px;
    &-h3 {
      margin: 0;
      padding: 0;
    }
    &-btn {
      display: flex;
      align-items: center;
      .btn {
        width: 36px;
        height: 36px;
        background: #f7f7f7;
        border: 1px solid #d9d9d9;
        border-radius: 4px;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        &:first-child {
          margin-right: 12px;
        }
        &.active {
          background: #e0f0ff;
          border: 1px solid #99ceff;
        }
      }
    }
  }
}
</style>
<style lang="scss">
.account-container {
  .table-delete {
    color: #ff2b2b;
  }
  .copy-passsword {
    color: red;
  }
  .reset-password {
    .el-dialog__body {
      padding: 0px 20px;
    }
  }
}
.dialog-footer {
  text-align: center;
}
</style>
