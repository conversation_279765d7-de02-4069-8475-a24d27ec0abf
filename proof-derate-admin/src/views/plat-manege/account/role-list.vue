<template>
  <div id="role-list">
    <CardTitle :title-name="titleName">
      <template />
    </CardTitle>
    <el-card class="department-box">
      <el-form :model="sendFrom" label-width="120px" @submit.native.prevent>
        <el-row :gutter="24">
          <el-col :span="9">
            <el-form-item label="角色名称">
              <el-input v-model="sendFrom.name" clearable placeholder="请输入角色名称" />
            </el-form-item>
          </el-col>
          <el-col :span="9">
            <el-form-item label="角色状态">
              <el-radio-group v-model="sendFrom.status">
                <el-radio label>全部</el-radio>
                <el-radio label="NORMAL">正常</el-radio>
                <el-radio label="DISABLE">禁用</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <!-- <el-col :span="4">
            <el-form-item>
              <div class="telescoping">
                <el-button type="primary" plain native-type="submit" @click="search">查询</el-button>
              </div>
            </el-form-item>
          </el-col>-->
          <el-col :span="6">
            <el-button type="primary" plain native-type="submit" @click="search">查询</el-button>
            <el-button plain native-type="submit" @click="reset">重置</el-button>
          </el-col>
        </el-row>
      </el-form>
      <custom-table
        ref="table"
        :is-card-type="false"
        :table-data="tableData"
        :table-header="tableHeader"
        @query="query"
        @refresh="query(1)"
      >
        <template #name="{ row }">
          <el-button type="text" @click="goDetail(row,'auth:role:view')">{{ row.name }}</el-button>
          <!-- <span v-else>{{ row.name }}</span> -->
        </template>
        <template #operation="{ row }">
          <div v-if="row.authType!=='AUDIT'&&row.authType!=='IMPLEMENT'&&row.authType!=='ROLE'">
            <el-button
              v-if="row.status==='DISABLE'"
              v-permission="'auth:role:recovery'"
              type="text"
              class="table-delete"
              @click="setDisable(row)"
            >
              <span>恢复</span>
            </el-button>
            <el-button v-else v-permission="'auth:role:disable'" type="text" class="table-delete" @click="setDisable(row)">
              <span>禁用</span>
            </el-button>
            <el-button v-permission="'auth:role:edit'" type="text" @click="editData(row)">编辑</el-button>
            <el-button v-permission="'auth:role:delete'" type="text" class="table-delete" @click="delItem(row)">删除</el-button>
          </div>
        </template>
      </custom-table>
    </el-card>
    <delPasswordDialog ref="delPasswordDialog" :tips="delTips" title="删除角色" @submit="handleSubmitDel" />
  </div>
</template>

<script>
import CustomTable from '@/components/Element/Table'
import delPasswordDialog from './components/del-password-dialog.vue'
import { getRolePage, checkBindUser, changeRoleStatus, deleteRole } from '@/api/commonPack/platManege'
import { isPermission } from '@/utils/index.js'
import { getOperationPermissionList } from '@/utils/index'
import CardTitle from '@/components/CardTitle'
import { sm2Encode } from '@/utils/sm-encrypt-utils'

export default {
  components: {
    CustomTable,
    CardTitle,
    delPasswordDialog
  },
  data() {
    return {
      titleName: '角色管理',
      dialogVisible: false,
      idband: false,
      sendFrom: {
        name: '',
        status: '',
        page_num: '1',
        page_size: '10'
      },
      tableData: {
        content: [], // 表格数据
        loading: false, // 控制表格加载效果
        total: 10, // 分页查询时的总数，去掉该属性，不显示分页条
        currentPage: 1, // 当前页码
        pageSize: 10,
        pageDirection: 'desc',
        isShowIndex: true,
        border: false
      },
      tableHeader: [
        {
          label: '角色名称',
          prop: 'name',
          // minWidth: '200px',
          align: 'left',
          slot: 'name'
        },
        {
          label: '角色代码',
          prop: 'code',
          // minWidth: '160px',
          align: 'left'
        },
        {
          label: '状态',
          prop: 'status',
          // minWidth: '160px',
          formatter: (row, col, val) => {
            return val === null ? '——' : val === 'NORMAL' ? '正常' : '禁用'
            // return val
          }
        },

        {
          label: '最后更新时间',
          prop: 'last_modification_time'
          // minWidth: '160px'
        },
        {
          label: '操作',
          prop: 'operation',
          minWidth: '160px',
          // align: 'left',
          slot: 'operation'
        }
      ],
      permissionList: ['auth:role:recovery', 'auth:role:disable', 'auth:role:edit'],
      delValidateForm: {
        password: '',
        id: ''
      },
      delTips: ''
      /* delDialogVisible: false,
      delRules: { password: [{ required: true, message: '当前登录账号的密码不能为空', trigger: 'blur' }] },
      passwordType: 'password'
 */
    }
  },

  mounted() {
    this.tableHeader = getOperationPermissionList(this.$route.meta.permission, this.permissionList, this.tableHeader)
    this.getRolePage()
  },

  methods: {
    isPermission,
    query() {
      this.sendFrom.page_num = this.tableData.currentPage
      this.sendFrom.page_size = this.tableData.pageSize
      this.getRolePage()
    },
    search() {
      this.sendFrom.page_num = '0'
      this.sendFrom.page_size = '10'
      this.tableData.currentPage = Number(this.sendFrom.page_num) + 1
      this.tableData.pageSize = Number(this.sendFrom.page_size)
      this.getRolePage()
    },
    reset() {
      this.sendFrom.name = ''
      this.sendFrom.status = ''
      this.sendFrom.page_num = '1'
      this.sendFrom.page_size = '10'
    },
    getRolePage() {
      const data = {
        name: this.sendFrom.name,
        page_num: this.sendFrom.page_num,
        page_size: this.sendFrom.page_size,
        // status: this.sendFrom.status.split(',')
        status: this.sendFrom.status === '' ? null : this.sendFrom.status
      }
      getRolePage(data).then(res => {
        if (res.data != null && res.meta.code === '200') {
          this.tableData.content = res.data.content
          this.tableData.total = Number(res.data.total_elements)
        }
      })
    },
    checkBindUser(id) {
      return checkBindUser(id).then(res => {
        console.log(res)
        if (res.data != null && res.meta.code === '200') {
          this.idband = res.data
        }
      })
    },
    setDisable(row) {
      this.checkBindUser(row.id).then(() => {
        this.handleChangeStatus(row)
      })
      // this.dialogVisible = true
    },
    handleChangeStatus(row) {
      const alert = {}
      if (row.status === 'NORMAL') {
        alert.content = '是否确认禁用此角色？'
        alert.status = 'DISABLE'
      } else {
        alert.content = '是否确认恢复此角色？'
        alert.status = 'NORMAL'
      }
      if (this.idband) {
        if (row.status === 'NORMAL') {
          alert.content = '该角色已绑定用户，是否确认禁用？'
          alert.status = 'DISABLE'
        } else {
          alert.content = '该角色已绑定用户，是否恢复此角色？'
          alert.status = 'NORMAL'
        }
      }
      this.$alert(`<span>${alert.content}</span><br/><span>角色名称：${row.name}</span>`, '警告提示', {
        dangerouslyUseHTMLString: true,
        type: 'warning',
        showCancelButton: true,
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      })
        .then(() => {
          const par = {
            id: row.id,
            status: alert.status
          }
          changeRoleStatus(par).then(res => {
            if (res.meta.code === '200') {
              this.getRolePage()
              this.$message({
                type: 'success',
                message: '修改成功'
              })
            }
          })
        })
        .catch(() => {
          /* this.$message({
          type: 'info',
          message: '已取消删除'
        }) */
        })
    },
    editData(row) {
      this.$router.push({ name: 'RoleDetail', query: { type: 'edit', id: row.id }})
    },
    delItem(row) {
      // this.delDialogVisible = true
      this.delValidateForm.id = row.id
      this.delTips = `角色（${row.name}）删除后将无法使用，请谨慎操作`
      this.$refs.delPasswordDialog.delDialogVisible = true
    },
    // 删除角色
    handleSubmitDel(val) {
      const params = {}
      params.password = sm2Encode(val, this.$appConfig.VUE_APP_ENCRYPT_KEY || process.env.VUE_APP_ENCRYPT_KEY)
      params.id = this.delValidateForm.id
      deleteRole(params).then(() => {
        this.$refs.delPasswordDialog.delDialogVisible = false
        this.$message({
          message: '删除成功',
          type: 'success'
        })
        this.sendFrom.page_num = '0'
        this.sendFrom.page_size = '10'
        this.tableData.currentPage = Number(this.sendFrom.page_num) + 1
        this.tableData.pageSize = Number(this.sendFrom.page_size)
        this.getRolePage()
      })
    },
    goDetail(row, key) {
      /* const permissionList = this.$route.meta.permission
      const _ = []
      permissionList.forEach(element => {
        _.push(element.key)
      }) */
      if (isPermission(this.$route.meta.permission, key)) {
        this.$router.push({ name: 'RoleDetailWatch', query: { type: 'watch', id: row.id }})
      }
    }
  }
}
</script>

<style lang="scss" scoped>

#role-list {
  padding: 10px;
  .table-delete {
    color: #ff2b2b;
  }
  .copy-passsword {
    color: red;
  }
}
</style>
