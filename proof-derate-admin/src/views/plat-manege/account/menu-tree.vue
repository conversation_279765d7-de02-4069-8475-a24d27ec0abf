<template>
  <div class="department-container">
    <!-- <el-card class="department-box"> -->
    <!-- <div class="department-box-title">
        <h3 class="department-box-title-h3">菜单树状图</h3>
        <div class="department-box-title-btn">
          <div class="btn" @click="toggleListTree()"><img src="@/assets/commonPack_images/list-icon.png" alt="" srcset=""></div>
          <div class="btn active"><img src="@/assets/commonPack_images/tree-active-icon.png" alt="" srcset=""></div>
        </div>
    </div>-->
    <div class="department-box-content">
      <div class="department-box-content-tree">
        <div class="tit">菜单信息</div>
        <div class="tree">
          <el-scrollbar style="height: 500px">
            <el-tree :data="treeData" :props="defaultProps" default-expand-all node-key="value" @node-click="handleNodeClick">
              <span slot-scope="{ node, data }" class="span-ellipsis">
                <!-- <el-tooltip class="item" effect="dark" :content="node.label||''" placement="top"> -->
                <span>{{ node.label }}</span>
                <!-- </el-tooltip> -->
              </span>
            </el-tree>
          </el-scrollbar>
        </div>
      </div>
      <div :class="['detail',{edit:!isEdit}]">
        <template v-if="treeData.length">
          <el-button v-if="!isEdit" v-permission="'auth:menu:edit'" type="primary" plain class="edit-btn" @click="handleEdit">编辑菜单</el-button>
          <el-button v-if="!isEdit" v-permission="'auth:menu:delete'" type="primary" plain class="del-btn" @click="handleDel(menuId)">删除</el-button>
        </template>
        <detail-tree v-if="!isEdit" :id="menuId" />
        <setting-menu v-else :id="menuId" :source="source" :is-edit="true" @canse="saveEdit" />
        <!-- <edit-department v-else :id="deparmentDetailId" :source="source" @canse="saveEdit" /> -->
      </div>
    </div>

    <!-- </el-card> -->
  </div>
</template>

<script>
import { getMenuTree, delMenu } from '@/api/commonPack/platManege'
import { isPermission } from '@/utils/index'

export default {
  components: {
    detailTree: () => import('./components/detail-menu-tree.vue'),
    settingMenu: () => import('./components/setting-menu.vue')
  },
  data() {
    return {
      treeData: [],
      data: [
        {
          label: '一级 1',
          children: [
            {
              label: '二级 1-1',
              children: [
                {
                  label: '三级 1-1-1'
                }
              ]
            }
          ]
        },
        {
          label: '一级 2',
          children: [
            {
              label: '二级 2-1',
              children: [
                {
                  label: '三级 2-1-1'
                }
              ]
            },
            {
              label: '二级 2-2',
              children: [
                {
                  label: '三级 2-2-1'
                }
              ]
            }
          ]
        },
        {
          label: '一级 3',
          children: [
            {
              label: '二级 3-1',
              children: [
                {
                  label: '三级 3-1-1'
                }
              ]
            },
            {
              label: '二级 3-2',
              children: [
                {
                  label: '三级 3-2-1'
                }
              ]
            }
          ]
        }
      ],
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      menuId: '',
      isEdit: false,
      source: 'tree'
    }
  },
  mounted() {
    this.getMenuTree()
  },
  methods: {
    isPermission,
    getMenuTree() {
      getMenuTree().then(res => {
        this.treeData = res.data || []
        this.menuId = this.treeData.length > 0 ? this.treeData[0].value : ''
        console.log(this.menuId)
      })
    },
    handleNodeClick(data) {
      this.isEdit = false
      this.menuId = data.value
    },
    toggleListTree(val) {
      this.$router.push({ name: 'MenuList' })
    },
    handleEdit() {
      this.isEdit = true
    },
    handleDel(id) {
      this.$confirm('确定删除吗？如果存在下级菜单将一并删除，此操作将不能撤销', '警告提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          delMenu(id).then(res => {
            if (res.meta.code === '200') {
              this.getMenuTree()
              this.$message({
                type: 'success',
                message: '删除成功!'
              })
            }
          })
        })
        .catch(() => {
          /* this.$message({
          type: 'info',
          message: '已取消删除'
        }) */
        })
    },
    saveEdit() {
      this.isEdit = false
      this.getMenuTree()
    }
  }
}
</script>

<style lang="scss" scoped>
.department-container {
  padding: 10px;
}
.department-box {
  &-title {
    height: 36px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 0px 0 30px 20px;
    &-h3 {
      margin: 0;
      padding: 0;
    }
    &-btn {
      display: flex;
      align-items: center;
      .btn {
        width: 36px;
        height: 36px;
        background: #f7f7f7;
        border: 1px solid #d9d9d9;
        border-radius: 4px;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        &:first-child {
          margin-right: 12px;
        }
        &.active {
          background: #e0f0ff;
          border: 1px solid #99ceff;
        }
      }
    }
  }
  &-content {
    display: flex;
    &-tree {
      // width: 245px;
      width: 485px;
      border: 1px solid #e9e9e9;
      .tit {
        height: 50px;
        line-height: 50px;
        font-size: 14px;
        color: #666;
        background: #e9e9e9;
        padding-left: 15px;
      }
      .tree {
        padding: 15px 20px 30px 15px;
        // overflow: auto;
        // height: 500px;
      }
    }
    .detail {
      flex: 1;
      border-left: none;
      position: relative;
      padding-top: 30px;
      &.edit {
        border: 1px solid #d9d9d9;
      }
      .edit-btn {
        position: absolute;
        top: 20px;
        right: 100px;
      }
      .del-btn {
        position: absolute;
        top: 20px;
        right: 20px;
      }
    }
  }
}
::v-deep .el-scrollbar__wrap {
  overflow-x: hidden;
}
/*树悬浮框: 名字过长则显示...*/
.span-ellipsis {
  width: calc(100% - 4px);
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  min-width: auto;
  padding: 4px 2px;
  border-radius: 4px;
}
</style>
<style lang="scss">
</style>
