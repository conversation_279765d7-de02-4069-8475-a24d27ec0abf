<template>
  <div class="account-container">
    <CardTitle :title-name="titleName">
      <template />
    </CardTitle>
    <el-card class="department-box">
      <el-form :model="sendFrom" label-width="120px" @submit.native.prevent>
        <el-row :gutter="24">
          <el-col :span="9">
            <el-form-item label="账号">
              <el-input v-model="sendFrom.account" clearable placeholder="请输入账号" />
            </el-form-item>
          </el-col>
          <el-col :span="9">
            <el-form-item label="用户名">
              <el-input v-model="sendFrom.user_name" clearable placeholder="请输入用户名" />
            </el-form-item>
          </el-col>

          <el-col :span="6" class="submitbtn">
            <el-button type="primary" plain native-type="submit" @click="onSubmit">查询</el-button>
            <el-button plain native-type="submit" @click="reset">重置</el-button>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="9" class="submitbtn">
            <el-form-item label="行政区划">
              <AdministrativeDivisionCascader
                :key="divisionCode"
                ref="AdministrativeDivisionSelect"
                :division-code="divisionCode"
                :permission-code="'auth:account:list'"
                @setDivisionCodeAndName="setDivisionCodeAndName"
              />
            </el-form-item>
          </el-col>
          <el-col :span="9">
            <el-form-item label="所属部门" prop="credit_code">
              <el-select v-model="sendFrom.credit_code" filterable placeholder="请选择所属部门" clearable style="width: 100%;">
                <el-option v-for="item in departmentOptions" :key="item.name" :label="item.name" :value="item.credit_code" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="9">
            <el-form-item label="账号状态">
              <el-radio-group v-model="sendFrom.status">
                <el-radio v-for="(item,idx) in statusOptions" :key="idx" :label="item.value">{{ item.label }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <custom-table
        ref="table"
        :is-card-type="false"
        :table-data="tableData"
        :table-header="tableHeader"
        @query="query"
        @refresh="query(1)"
      >
        <template #account="{ row }">
          <el-button
            v-if="isPermission(row.permission_codes,'auth:account:view')"
            type="text"
            @click="getDetail(row,'auth:account:view')"
          >{{ row.account }}</el-button>
          <span v-else>{{ row.account }}</span>
        </template>
        <template #handle="{ row }">
          <div v-if="row.auth_type==='COMMON'">
            <el-button
              v-permission="'auth:account:reset_password'"
              :disabled="!isPermission(row.permission_codes,'auth:account:reset_password') || row.status==='CANCEL'"
              type="text"
              @click="handleResetPassword(row,$event)"
            >重置密码</el-button>
            <el-button
              v-permission="'auth:account:edit'"
              :disabled="!isPermission(row.permission_codes,'auth:account:edit') || row.status==='CANCEL'"
              type="text"
              @click="handleEdit(row)"
            >编辑</el-button>
            <el-button
              v-if="row.status ==='NORMAL'"
              v-permission="'auth:account:disable'"
              :disabled="!isPermission(row.permission_codes,'auth:account:disable') || row.status==='CANCEL'"
              type="text"
              class="table-delete"
              @click="handleChangeStatus(row)"
            >禁用</el-button>
            <el-button
              v-else
              v-permission="'auth:account:recovery'"
              :disabled="!isPermission(row.permission_codes,'auth:account:recovery') || row.status==='CANCEL'"
              type="text"
              class="table-delete"
              @click="handleChangeStatus(row)"
            >恢复</el-button>
            <el-button
              v-permission="'auth:account:delete'"
              :disabled="!isPermission(row.permission_codes,'auth:account:delete')"
              type="text"
              class="table-delete"
              @click="delItem(row)"
            >删除</el-button>
          </div>
        </template>
      </custom-table>
    </el-card>
    <el-dialog :visible.sync="editDialogVisible" width="50%">
      <!-- <edit-department :dialog-data="editData" @canse="setEditDialogVisible" /> -->
    </el-dialog>
    <el-dialog class="reset-password" title="成功提示" top="25%" :visible.sync="repwDialogVisible" width="30%">
      <div class="boby">
        <div class="status el-icon-success" />
        <div class="content">
          <p>密码重置成功</p>
          <p>
            初始化密码：{{ passwordPlus }}
            <el-button
              :type="isCopypassword?'info':'primary'"
              :disabled="isCopypassword"
              plain
              size="mini"
              @click="copyPassword($event)"
            >{{ isCopypassword?'已复制':'一键复制' }}</el-button>
          </p>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="repwDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmReview">确定</el-button>
      </span>
    </el-dialog>
    <delPasswordDialog ref="delPasswordDialog" :tips="delTips" title="删除账号" @submit="handleSubmitDel" />

  </div>
</template>

<script>
import { getAccountList, resetPassword, changeStatus, deleteAccount, getOrganizationList } from '@/api/commonPack/platManege'
import CustomTable from '@/components/Element/Table'
import delPasswordDialog from './components/del-password-dialog.vue'
import clip from '@/utils/clipboard'
import { status } from 'nprogress'
import { plusXing, isPermission } from '@/utils/index.js'
import { getOperationPermissionList } from '@/utils/index'
import { sm2Encode } from '@/utils/sm-encrypt-utils'

import CardTitle from '@/components/CardTitle'
export default {
  components: {
    CustomTable,
    CardTitle,
    AreaSelect: () => import('@/components/AreaSelect'),
    // AdministrativeDivisionSelect: () => import('@/components/AdministrativeDivisionSelect')
    AdministrativeDivisionCascader: () => import('@/components/AdministrativeDivisionCascader'),
    delPasswordDialog
  },

  data() {
    return {
      titleName: '账号管理',
      sendFrom: {
        user_name: '',
        credit_code: '',
        account: '',
        division_code: '',
        status: null,
        page_num: 1,
        page_size: 10
      },
      statusOptions: [
        { value: null, label: '全部' },
        { value: 'NORMAL', label: '正常' },
        { value: 'DISABLE', label: '禁用' },
        { value: 'CANCEL', label: '注销' }
      ],
      tableData: {
        content: [{ account: '111' }], // 表格数据
        loading: false, // 控制表格加载效果
        total: 10, // 分页查询时的总数，去掉该属性，不显示分页条
        currentPage: 1, // 当前页码
        pageSize: 10,
        pageDirection: 'desc',
        isShowIndex: true,
        border: false
        // maxHeight: '420px'
      },
      tableHeader: [
        {
          label: '账号',
          prop: 'account',
          slot: 'account',
          minWidth: '160px',
          align: 'left'
        },
        {
          label: '姓名',
          prop: 'user_name',
          minWidth: '160px',
          align: 'left'
        },
        {
          label: '行政区划',
          prop: 'division_name',
          minWidth: '160px',
          align: 'left'
        },
        {
          label: '账号状态',
          prop: 'status',
          minWidth: '160px',
          align: 'left',
          formatter: (row, col, val) => {
            return this.statusOptions.find(item => item.value === val).label
            // return val === null ? val : val === 'NORMAL' ? '正常' : '禁用'
            // return val
          }
        },
        {
          label: '最后更新时间',
          prop: 'last_modification_time',
          minWidth: '160px',
          align: 'left'
        },
        {
          label: '操作',
          prop: '',
          slot: 'handle',
          minWidth: '200px',
          align: 'left'
        }
      ],
      editData: {},
      editDialogVisible: false,
      repwDialogVisible: false,
      resetPassword: '',
      isCopypassword: false,
      passwordPlus: '',
      // divisionCode:'c1'
      divisionCode: '',
      permissionList: ['auth:account:reset_password', 'auth:account:edit', 'auth:account:disable', 'auth:account:recovery'],
      optionData: [
        {
          divisionlist: [],
          disabled: false,
          placeholderText: '请选择',
          code: '',
          name: ''
        },
        {
          divisionlist: [],
          disabled: false,
          placeholderText: '请选择',
          code: '',
          name: ''
        },
        {
          divisionlist: [],
          disabled: false,
          placeholderText: '请选择',
          code: '',
          name: ''
        }
        // {
        //   divisionlist: [],
        //   disabled: false,
        //   code: '',
        //   name: ''
        // }
      ],
      delValidateForm: {
        password: '',
        id: ''
      },
      delTips: '',
      departmentOptions: []

    }
  },
  mounted() {
    this.tableHeader = getOperationPermissionList(this.$route.meta.permission, this.permissionList, this.tableHeader)
    this.getAccountList()
    this.getOrganizationList()
  },

  methods: {
    isPermission,
    /**
     * 获取所属部门列表
     */
    getOrganizationList() {
      getOrganizationList({ scope: true, permission_code: 'auth:account:list', division_code: this.sendFrom.division_code }).then(res => {
        /*  if (res.data.length === 1) {
          this.searchForm.credit_code = res.data[0].credit_code
        } */
        this.departmentOptions = res.data
      })
    },
    delItem(row) {
      // this.delDialogVisible = true
      this.delValidateForm.id = row.id
      const userInfo = JSON.parse(sessionStorage.getItem('accountInfo'))
      if (userInfo.account === 'system_admin') {
        this.delValidateForm.deleted = true
        this.delTips = `<span style="color:#F56C6C">账号（${row.account}）删除后将无法使用，请谨慎操作,所有关联账号模块可能报错！请谨慎操作！</span>`
      } else {
        this.delValidateForm.deleted = false
        this.delTips = `账号（${row.account}）删除后将无法使用，请谨慎操作`
      }
      this.$refs.delPasswordDialog.delDialogVisible = true
    },
    // 删除账号
    handleSubmitDel(val) {
      const params = {}
      params.password = sm2Encode(val, this.$appConfig.VUE_APP_ENCRYPT_KEY || process.env.VUE_APP_ENCRYPT_KEY)
      params.id = this.delValidateForm.id
      deleteAccount(params).then(() => {
        this.$refs.delPasswordDialog.delDialogVisible = false
        this.$message({
          message: '删除成功',
          type: 'success'
        })
        this.sendFrom.page_num = '0'
        this.sendFrom.page_size = '10'
        this.tableData.currentPage = Number(this.sendFrom.page_num) + 1
        this.tableData.pageSize = Number(this.sendFrom.page_size)
        this.getAccountList()
      })
    },
    getAccountList() {
      getAccountList(this.sendFrom)
        .then(res => {
          if (res.data != null && res.meta.code === '200') {
            if (res.data.content != null) {
              const { content } = res.data
              /* content.forEach(element => {
                element.hasPermission = isPermission(this.$route.meta.permission, element.permission_codes)
              })
              console.log(content, 'content') */
              this.tableData.content = content
              this.tableData.total = Number(res.data.total_elements)
            } else {
              this.tableData.content = []
              this.tableData.total = 0
            }
          }
        })
        .catch(() => {})
    },
    onSubmit() {
      this.sendFrom.page_num = 1
      this.sendFrom.page_size = 10
      this.tableData.currentPage = this.sendFrom.page_num
      this.tableData.pageSize = this.sendFrom.page_size
      this.getAccountList()
      // if (this.sendFrom.page_num === 1) this.tableData.currentPage = 1
    },
    reset() {
      this.sendFrom = {
        name: '',
        status: null,
        page_num: 1,
        page_size: 10
      }
      // this.$refs.areaSelect.resetCodeAndName()
      this.$refs.AdministrativeDivisionSelect.clearChose()
      // this.tableData.currentPage = 1
    },
    query() {
      this.sendFrom.page_num = this.tableData.currentPage
      this.sendFrom.page_size = this.tableData.pageSize
      this.getAccountList()
    },
    getDivisionCode(val) {
      this.sendFrom.division_code = val
    },
    setDivisionCodeAndName(data) {
      console.log('data', data)
      this.sendFrom.division_code = data.code
      this.getOrganizationList()
      // this.editDataForm.division_name = data.name
    },
    handleResetPassword(row) {
      this.$alert(`<span>是否确认重置密码</span><br/><span>账号：${row.account}</span>`, '警告提示', {
        dangerouslyUseHTMLString: true,
        type: 'warning',
        showCancelButton: true,
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      })
        .then(() => {
          this.isCopypassword = false
          this.setResetPassword(row.id, event)
        })
        .catch(() => {})
    },
    // 重置密码
    setResetPassword(id, event) {
      resetPassword(id).then(res => {
        // if (res.meta.code !== 200) return
        this.resetPassword = res.data
        this.passwordPlus = plusXing(res.data, 0, 0, '*')
        this.repwDialogVisible = true
        /* this.$alert(`<span>密码重置成功</span><br/><span>初始化密码：${res.data}</span>`, '成功提示', {
          dangerouslyUseHTMLString: true,
          type: 'success',
          showCancelButton: true,
          confirmButtonText: '确定',
          cancelButtonText: '取消'
        }).then(() => {
          clip(res.data, event)
        }).catch(() => {
        }) */
      })
    },
    copyPassword(event) {
      clip(this.resetPassword, event)
      this.isCopypassword = true
    },
    confirmReview() {
      this.repwDialogVisible = false
      console.log(this.isCopypassword)
      if (!this.isCopypassword) {
        this.$alert(`<span>请复制初始化密码</span><br/><span>点击“一键复制”按钮，复制初始化密码</span>`, '警告提示', {
          dangerouslyUseHTMLString: true,
          type: 'warning',
          confirmButtonText: '确定'
        }).then(() => {
          this.repwDialogVisible = true
        })
      }
    },
    handleEdit(row) {
      this.$router.push({ name: 'AccountSetting', query: { id: row.id, isEdit: 1 }})
    },
    // 变更状态
    handleChangeStatus(row) {
      const alert = {}
      if (row.status === 'NORMAL') {
        alert.content = '是否确认禁止此账号登录系统？'
        alert.status = 'DISABLE'
      } else {
        alert.content = '是否确认恢复此账号登录系统？'
        alert.status = 'NORMAL'
      }
      this.$alert(`<span>${alert.content}</span><br/><span>账号：${row.account}</span>`, '警告提示', {
        dangerouslyUseHTMLString: true,
        type: 'warning',
        showCancelButton: true,
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      })
        .then(() => {
          const par = {
            id: row.id,
            status: alert.status
          }
          changeStatus(par).then(res => {
            if (res.meta.code === '200') {
              this.getAccountList()
              this.$message({
                type: 'success',
                message: '修改成功'
              })
            }
          })
        })
        .catch(() => {
          /* this.$message({
          type: 'info',
          message: '已取消删除'
        }) */
        })
    },
    getDetail(row, key) {
      if (isPermission(this.$route.meta.permission, key)) {
        this.$router.push({ name: 'AccountDetail', query: { id: row.id, status: row.status }})
        if (row.auth_type === 'COMMON') {
          this.$store.commit('breadcrumbBtn/SET_PLATMANAGE', { accoundIdEdit: row.id })
        } else {
          this.$store.commit('breadcrumbBtn/SET_PLATMANAGE', { accoundIdEdit: '' })
        }
      }
    },

    setEditDialogVisible(val) {
      this.editDialogVisible = val
      this.getAccountList()
    },
    toggleListTree(val) {
      this.$router.push({ name: 'DepartmentTree' })
    }
  }
}
</script>

<style lang="scss" scoped>
.department-box ::v-deep .gt-table-box .el-card__body {
  padding-top: 0;
}
.account-container {
  padding: 10px;
  .reset-password {
    .boby {
      display: flex;
      align-items: center;
      .status {
        color: #67c23a;
        // transform: translateY(-50%);
        font-size: 24px !important;
      }
      .content {
        padding-left: 12px;
        padding-right: 12px;
      }
    }
  }
}
.department-box {
  &-title {
    height: 36px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 0px 0 30px 20px;
    &-h3 {
      margin: 0;
      padding: 0;
    }
    &-btn {
      display: flex;
      align-items: center;
      .btn {
        width: 36px;
        height: 36px;
        background: #f7f7f7;
        border: 1px solid #d9d9d9;
        border-radius: 4px;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        &:first-child {
          margin-right: 12px;
        }
        &.active {
          background: #e0f0ff;
          border: 1px solid #99ceff;
        }
      }
    }
  }
}
</style>
<style lang="scss">
.account-container {
  .table-delete {
    color: #ff2b2b;
  }
  .copy-passsword {
    color: red;
  }
  .reset-password {
    .el-dialog__body {
      padding: 0px 20px;
    }
  }
}
</style>
