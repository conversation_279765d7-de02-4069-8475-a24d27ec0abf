<template>
  <div class="department-container">
    <el-card class="department-box">
      <div class="department-box-title">
        <h3 class="department-box-title-h3">{{ currentTab === 0 ? '菜单列表' : '菜单树状图' }}</h3>
        <div class="department-box-title-btn">
          <!-- <div class="btn active"><img src="@/assets/commonPack_images/list-active-icon.png" alt="" srcset=""></div>
          <div class="btn" @click="toggleListTree('tree')"><img src="@/assets/commonPack_images/tree-con.png" alt="" srcset=""></div>-->
          <icon-tab @selected="handleSelect" />
        </div>
      </div>
      <template v-if="currentTab === 0">
        <el-form :model="sendFrom" label-width="120px" @submit.native.prevent>
          <el-row :gutter="24">
            <el-col :span="18">
              <el-form-item label="菜单标题">
                <el-input v-model="sendFrom.menu_name" clearable placeholder="请输入菜单标题" />
              </el-form-item>
            </el-col>
            <el-col :span="6" class="submitbtn">
              <el-button type="primary" plain native-type="submit" @click="onSubmit">查询</el-button>
              <el-button plain native-type="submit" @click="reset">重置</el-button>
            </el-col>
          </el-row>
        </el-form>

        <custom-table ref="table" :is-card-type="false" :table-data="tableData" :table-header="tableHeader" @query="query" @refresh="query(1)">
          <template #icon="{ row }">
            <div v-if="row.icon !== null">
              <svg-icon :icon-class="row.icon" style="height: 30px; width: 16px" />
            </div>
          </template>
          <template #handle="{ row }">
            <el-button v-permission="'auth:menu:edit'" type="text" @click="handleEdit(row)">编辑</el-button>
            <el-button v-permission="'auth:menu:delete'" type="text" class="table-delete" @click="handleDelete(row.menu_id)">删除</el-button>
          </template>
        </custom-table>
      </template>

      <template v-else>
        <menu-tree :key="freeFalsKey" />
      </template>
    </el-card>
    <el-dialog :title="dialogTitle" :visible.sync="editDialogVisible" width="50%" :top="'5vh'" @close="closeEditDialog">
      <div class="setting-menu-wrap">
        <setting-menu :id="menuId" :is-edit="isEdit" :toggle-edit="toggleEdit" @canse="setEditDialogVisible" @setDialogTitle="getDialogTitle" />
      </div>
    </el-dialog>
    <menuChoseDialog ref="menuChoseDialog" @close="closeMenuChoseDialog" @output="output" />
    <!-- <el-dialog title="新建部门" :visible.sync="addDialogVisible" width="50%">
      <new-department @canse="setAddDialogVisible" />
    </el-dialog>-->
  </div>
</template>

<script>
import { getMenuPage, delMenu, exportMenu } from '@/api/commonPack/platManege'
import CustomTable from '@/components/Element/Table'
import { getOperationPermissionList, isPermission, exportsDown } from '@/utils/index'
import menuChoseDialog from '@/components/menuChoseDialog'
import axios from 'axios'
export default {
  components: {
    CustomTable,
    settingMenu: () => import('./components/setting-menu.vue'),
    // newDepartment: () => import('./components/new-department.vue')
    iconTab: () => import('./components/icon-tab.vue'),
    menuTree: () => import('./menu-tree.vue'),
    menuChoseDialog
  },
  data() {
    return {
      sendFrom: {
        menu_name: '',
        page_num: 1,
        page_size: 10
      },
      statusOptions: [
        { value: null, label: '全部' },
        { value: 'NORMAL', label: '正常' },
        { value: 'CANCEL', label: '注销' }
      ],
      tableData: {
        content: [], // 表格数据
        loading: false, // 控制表格加载效果
        total: 10, // 分页查询时的总数，去掉该属性，不显示分页条
        currentPage: 1, // 当前页码
        pageSize: 10,
        pageDirection: 'desc',
        isShowIndex: true,
        border: false
      },
      tableHeader: [
        {
          label: '菜单标题',
          prop: 'menu_name',
          minWidth: '160px',
          align: 'left'
        },
        {
          label: '菜单图标',
          prop: 'icon',
          slot: 'icon',
          minWidth: '100px',
          align: 'left'
        },
        {
          label: '列表排序',
          prop: 'menu_sort',
          minWidth: '80px',
          align: 'left'
        },
        {
          label: '权限标识',
          prop: 'permission_code',
          minWidth: '200px',
          align: 'left'
        },
        {
          label: '菜单是否可见',
          prop: 'menu_status',
          minWidth: '100px',
          align: 'left',
          formatter: (row, col, val) => {
            return val === null ? val : val === 'SHOW' ? '显示' : '隐藏'
          }
        },
        {
          label: '路由地址',
          prop: 'menu_route_url',
          minWidth: '200px',
          align: 'left'
        },
        {
          label: '组件地址',
          prop: 'component',
          minWidth: '200px',
          align: 'left'
        },
        {
          label: '操作',
          prop: '',
          slot: 'handle',
          minWidth: '80px',
          align: 'left'
        }
      ],
      menuId: '',
      editDialogVisible: false,
      addDialogVisible: false,
      isEdit: true,
      dialogTitle: '',
      toggleEdit: false,
      currentTab: 0,
      permissionList: ['auth:menu:edit', 'auth:menu:delete'],
      freeFalsKey: Math.random(),
      menuData: ''
    }
  },
  computed: {
    isMenuAdd() {
      return this.$store.state.breadcrumbBtn.platManage.isMenuAdd
    },
    exportChosedMenu() {
      return this.$store.state.breadcrumbBtn.platManage.exportChosedMenu
    }
  },
  watch: {
    isMenuAdd(value) {
      // 监听到有变化就重新获取数据
      this.editDialogVisible = value
      this.isEdit = false
      this.menuId = ''
      this.dialogTitle = '新建目录'
    },
    exportChosedMenu(value) {
      console.log('exportChosedMenu', value)
      this.$refs.menuChoseDialog.dialogVisible = value
    }
  },
  mounted() {
    this.tableHeader = getOperationPermissionList(this.$route.meta.permission, this.permissionList, this.tableHeader)
    // 显示按钮，防止切换到树菜单时，直接切换到其他页面，回来按钮不显示
    this.$store.commit('breadcrumbBtn/SET_PLATMANAGE', { showMenuListButton: true })
    this.getMenuPage()
  },

  methods: {
    isPermission,
    getMenuPage() {
      console.log('this.sendFrom', this.sendFrom)
      getMenuPage(this.sendFrom)
        .then((res) => {
          if (res.data != null && res.meta.code === '200') {
            if (res.data.content != null) {
              this.tableData.content = res.data.content || []
              this.tableData.total = Number(res.data.total_elements)
            } else {
              this.tableData.content = []
              this.tableData.total = 0
            }
          }
        })
        .catch(() => {})
    },
    onSubmit() {
      this.sendFrom.page_num = 1
      this.sendFrom.page_size = 10
      this.tableData.currentPage = this.sendFrom.page_num
      this.tableData.pageSize = this.sendFrom.page_size
      this.getMenuPage()
      // if (this.sendFrom.page_num === 1) this.tableData.currentPage = 1
    },
    reset() {
      this.sendFrom = {
        menu_name: '',
        status: null,
        page_num: 1,
        page_size: 10
      }
    },
    query() {
      this.sendFrom.page_num = this.tableData.currentPage
      this.sendFrom.page_size = this.tableData.pageSize
      this.getMenuPage()
    },
    handleEdit(row) {
      this.menuId = row.menu_id
      this.isEdit = true
      this.editDialogVisible = true
      this.dialogTitle = '编辑'
      this.toggleEdit = !this.toggleEdit
    },
    handleDelete(id) {
      this.$confirm('确定删除吗？如果存在下级菜单将一并删除，此操作将不能撤销', '警告提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          delMenu(id).then((res) => {
            if (res.meta.code === '200') {
              this.getMenuPage()
              this.$message({
                type: 'success',
                message: '删除成功!'
              })
            }
          })
        })
        .catch(() => {
          /* this.$message({
          type: 'info',
          message: '已取消删除'
        }) */
        })
    },
    setEditDialogVisible(val) {
      this.editDialogVisible = val
      this.getMenuPage()
      this.$store.commit('breadcrumbBtn/SET_PLATMANAGE', { isMenuAdd: false })
      this.freeFalsKey = Math.random()
    },
    getDialogTitle(val) {
      this.dialogTitle = val
    },
    setAddDialogVisible() {
      // this.addDialogVisible = val
      this.getMenuPage()
      this.$store.commit('breadcrumbBtn/SET_PLATMANAGE', { isMenuAdd: false })
    },
    closeEditDialog() {
      this.$store.commit('breadcrumbBtn/SET_PLATMANAGE', { isMenuAdd: false })
    },
    closeMenuChoseDialog() {
      this.$refs.menuChoseDialog.clearChosedNode()
      this.$store.commit('breadcrumbBtn/SET_PLATMANAGE', { exportChosedMenu: false })
    },
    toggleListTree(val) {
      this.$router.push({ name: 'MenuTree' })
    },
    handleSelect(e) {
      this.currentTab = e
      this.getMenuPage()
      // const flag = e == 0 ? true : false;
      // this.$store.commit('breadcrumbBtn/SET_PLATMANAGE', { showMenuListButton: flag })
    },
    output(node) {
      if (node.length !== 0) {
        exportsDown('/auth/webapi/v1/common/menu/export', { menu_ids: node }, '导出菜单.ljson', 'licc')
        this.closeMenuChoseDialog()
      } else {
        this.$message({
          message: '请选中至少一条数据',
          type: 'warning'
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.department-container {
  padding: 10px;
}
.department-box {
  &-title {
    height: 36px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 0px 0 30px 20px;
    &-h3 {
      margin: 0;
      padding: 0;
    }
    &-btn {
      display: flex;
      align-items: center;
      .btn {
        width: 36px;
        height: 36px;
        background: #f7f7f7;
        border: 1px solid #d9d9d9;
        border-radius: 4px;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        &:first-child {
          margin-right: 12px;
        }
        &.active {
          background: #e0f0ff;
          border: 1px solid #99ceff;
        }
      }
    }
  }
}
.setting-menu-wrap {
  max-height: 726px;
  overflow-y: auto;
  overflow-x: hidden;
}
</style>
<style lang="scss">
.department-container {
  .table-delete {
    color: #ff2b2b;
  }
}
</style>
