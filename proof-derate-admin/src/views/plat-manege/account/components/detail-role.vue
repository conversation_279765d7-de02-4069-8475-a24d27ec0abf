<template>
  <div id="detail-role">
    <el-card>
      <div class="detail-role-card">
        <div class="detail-role-card-bar">
          <img src="~@/assets/images/file.png" alt>
          <span>{{ tpyeText }}</span>
        </div>
        <el-divider />
        <div class="detail-role-card-wrap">
          <el-row :gutter="24">
            <el-col :span="8">
              <tree-select ref="treeSelect" :key="timer" :tree-data="treeData" />
            </el-col>
            <el-col :span="16">
              <div v-if="$route.query.type == 'edit'|| $route.query.type == 'watch'" class="detail-role-card-wrap-rigth">
                <el-form :model="sendFrom" label-width="80px">
                  <el-form-item label="角色名称">
                    <span class="detail-role-card-wrap-rigth-content">{{ sendFrom.name }}</span>
                  </el-form-item>
                  <el-form-item label="角色代码">
                    <span class="detail-role-card-wrap-rigth-content">{{ sendFrom.code }}</span>
                  </el-form-item>
                  <el-form-item label="角色说明">
                    <span class="detail-role-card-wrap-rigth-content">
                      <el-input
                        v-model="sendFrom.explain"
                        type="textarea"
                        :disabled="$route.query.type== 'watch'"
                        :rows="4"
                        placeholder="请输入内容"
                      />
                    </span>
                  </el-form-item>
                  <el-form-item label="角色状态">
                    <span class="detail-role-card-wrap-rigth-content">
                      <el-radio-group v-model="sendFrom.status" :disabled="$route.query.type== 'watch'">
                        <el-radio label="NORMAL">正常</el-radio>
                        <el-radio label="DISABLE">禁用</el-radio>
                      </el-radio-group>
                    </span>
                  </el-form-item>
                </el-form>
                <div v-if="$route.query.type!= 'watch'" class="detail-role-card-wrap-rigth-bottom">
                  <el-button type="primary" class="btn" @click="sumbit()">确定</el-button>
                  <el-button class="btn" @click="goback()">取消</el-button>
                </div>
              </div>

              <div v-if="$route.query.type == 'add'" class="detail-role-card-wrap-rigth">
                <el-form ref="addForm" :model="addForm" :rules="rules" label-width="80px">
                  <el-form-item label="角色名称" prop="name">
                    <span class="detail-role-card-wrap-rigth-content">
                      <el-input v-model="addForm.name" placeholder="请输入角色名称" class="width-100-103" />
                    </span>
                  </el-form-item>
                  <el-form-item label="角色代码">
                    <span class="detail-role-card-wrap-rigth-content">
                      <el-input v-model="addForm.code" placeholder="请输入角色代码" disabled class="width-100-103" />
                    </span>
                  </el-form-item>
                  <el-form-item label="角色说明">
                    <span class="detail-role-card-wrap-rigth-content">
                      <el-input v-model="addForm.explain" type="textarea" :rows="4" placeholder="请输入内容" />
                    </span>
                  </el-form-item>
                </el-form>
                <div class="detail-role-card-wrap-rigth-bottom">
                  <el-button type="primary" class="btn" @click="addData()">确定</el-button>
                  <el-button class="btn" @click="goback()">取消</el-button>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import TreeSelect from '@/components/Treeselect'
import { getBindMenu, getRoleView, editRole, getAutoCode, createRoleData, changeRoleStatus, checkBindUser } from '@/api/commonPack/platManege'
export default {
  components: {
    TreeSelect
  },
  data() {
    return {
      treeData: [],
      value: '',
      textarea: '',

      sendFrom: { name: '', code: '', status: '', explain: '' },
      addForm: { name: '', code: '', explain: '' },
      rules: {
        name: [{ required: true, message: '请输入角色名称', trigger: 'blur' }]
      },
      tpyeText: '',
      selectKeyList: [],
      getSelectkeyList: [],
      checkStrictly: true,
      timer: ''
    }
  },
  computed: {
    isRoleDisable() {
      return this.$store.state.breadcrumbBtn.platManage.isRoleDisable
    }
  },
  watch: {
    isRoleDisable(value) {
      // 监听到有变化就重新获取数据
      console.log('isRoleDisable', this.sendFrom)
      this.setDisable(this.sendFrom)
    }
  },

  mounted() {
    this.inintData()
  },
  methods: {
    inintData() {
      this.getBindMenu()
      this.initText()
      this.selectKeyList = []
    },
    initText() {
      if (this.$route.query.type === 'edit') {
        this.tpyeText = '编辑角色'
        this.getRoleView()
        this.$refs.treeSelect.ifEdit = false
      } else if (this.$route.query.type === 'watch') {
        this.tpyeText = '查看角色'
        this.getRoleView()
        this.$refs.treeSelect.ifEdit = true
      } else {
        this.tpyeText = '新增角色'
        this.getAutoCode()
        this.$refs.treeSelect.ifEdit = false
      }
    },
    getBindMenu() {
      const data = {
        id: this.$route.query.id,
        update: this.$route.query.type !== 'watch'
      }
      getBindMenu(data).then(res => {
        if (res.data != null && res.meta.code === '200') {
          this.treeData = res.data
          this.checkStrictly = true
          this.getSelectkey(this.treeData)
          // console.log(this.$refs)

          this.$refs.treeSelect.setSelectkey(this.selectKeyList)
          this.$refs.treeSelect.setfillerOptions(this.treeData)
          // console.log(this.treeData)
        }
      })
    },
    getRoleView() {
      getRoleView(this.$route.query.id).then(res => {
        if (res.data != null && res.meta.code === '200') {
          Object.assign(this.sendFrom, res.data)
          this.$store.commit('breadcrumbBtn/SET_PLATMANAGE', { roleStatus: this.sendFrom.status })
          // console.log(res)
        }
      })
    },
    sumbit() {
      this.getSelectkeyList = this.$refs.treeSelect.getChecked()
      this.setSelectkey(this.treeData)
      // this.$refs['from' + val.menuId].validate((valid, val) => {

      // })
      this.$refs.treeSelect.validateTreeList = []
      this.$refs.treeSelect.validateTree(this.treeData)
      if (this.$refs.treeSelect.validateTreeList.indexOf(false) === -1) {
        this.editRole()
      }
    },
    validateTree(data) {
      data.forEach(e => {
        console.log(this.$refs.treeSelect['from' + e.menuId])
        if (e.children != null) {
          this.validateTree(e.children)
        }
      })
    },
    setSelectkey(data) {
      data.forEach(e => {
        if (this.getSelectkeyList.indexOf(e.menuId) != -1) {
          e.bindMenu = true
        } else {
          e.bindMenu = false
        }
        if (e.children != null) {
          this.setSelectkey(e.children)
        }
      })
    },
    getSelectkey(data) {
      data.forEach(e => {
        // if (e.bindMenu && e.children == null) {
        //   this.selectKeyList.push(e.menuId)
        // }
        if (e.bindMenu) {
          this.selectKeyList.push(e.menuId)
        }
        if (e.children != null) {
          this.getSelectkey(e.children)
        }
      })
    },
    getAutoCode() {
      getAutoCode().then(res => {
        if (res.data != null && res.meta.code === '200') {
          this.addForm.code = res.data
        }
      })
    },
    addData() {
      this.$refs['addForm'].validate(valid => {
        if (valid) {
          this.getSelectkeyList = this.$refs.treeSelect.getChecked()
          this.setSelectkey(this.treeData)
          //  this.validateTree(this.treeData)
          this.$refs.treeSelect.validateTreeList = []
          this.$refs.treeSelect.validateTree(this.treeData)
          if (this.$refs.treeSelect.validateTreeList.indexOf(false) == -1) {
            this.createRoleData()
          }
        }
      })
    },
    createRoleData() {
      this.addForm.bind_menu = this.treeData
      createRoleData(this.addForm).then(res => {
        if (res.meta.code === '200') {
          this.$message({
            message: '新建成功',
            type: 'success'
          })
          this.goback()
        } else {
          this.$message({
            message: res.meta.message,
            type: 'error'
          })
        }
      })
    },
    editRole() {
      const data = {}
      Object.assign(data, this.sendFrom)
      data.bind_menu = this.treeData
      this.timer = new Date().getTime()
      editRole(data).then(res => {
        this.inintData()
        if (res.meta.code === '200') {
          this.$message({
            message: '保存成功',
            type: 'success'
          })
          // this.goback()
        } else {
          this.$message({
            message: res.meta.message,
            type: 'error'
          })
        }
      })
    },
    goback() {
      this.$router.push({ name: 'RoleList' })
    },
    checkBindUser(id) {
      return checkBindUser(id).then(res => {
        console.log(res)
        if (res.data != null && res.meta.code === '200') {
          this.idband = res.data
        }
      })
    },
    setDisable(row) {
      this.checkBindUser(row.id).then(() => {
        this.handleChangeStatus(row)
      })
      // this.dialogVisible = true
    },
    handleChangeStatus(row) {
      const alert = {}
      if (row.status === 'NORMAL') {
        alert.content = '是否确认禁用此角色？'
        alert.status = 'DISABLE'
      } else {
        alert.content = '是否确认恢复此角色？'
        alert.status = 'NORMAL'
      }
      if (this.idband) {
        if (row.status === 'NORMAL') {
          alert.content = '该角色已绑定用户，是否确认禁用？'
          alert.status = 'DISABLE'
        } else {
          alert.content = '该角色已绑定用户，是否恢复此角色？'
          alert.status = 'NORMAL'
        }
      }
      this.$alert(`<span>${alert.content}</span><br/><span>角色名称：${row.name}</span>`, '警告提示', {
        dangerouslyUseHTMLString: true,
        type: 'warning',
        showCancelButton: true,
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      })
        .then(() => {
          const par = {
            id: row.id,
            status: alert.status
          }
          changeRoleStatus(par).then(res => {
            if (res.meta.code === '200') {
              // this.getRolePage()
              this.getRoleView()
              this.$message({
                type: 'success',
                message: '修改成功'
              })
            }
          })
        })
        .catch(() => {
          /* this.$message({
          type: 'info',
          message: '已取消删除'
        }) */
        })
    }
  }
}
</script>

<style lang="scss" scoped>
#detail-role {
  padding: 10px;
}
.detail-role-card {
  &-bar {
    display: flex;
    align-items: flex-end;
    img {
      width: 22px;
      height: 22px;
      margin-right: 5px;
    }
    span {
      color: #333333;
    }
  }
  &-wrap {
    // height: 400px;
    // padding-bottom: 20px;
    &-left {
      width: 417px;
      &-title {
        height: 49px;
        border: 1px solid #e9e9e9;
        background: #f8f8f8;
        font-size: 14px;
        color: #666666;
        line-height: 49px;
        padding-left: 20px;
        // display: flex;
        // align-items: center;
      }
      &-content {
        border: 1px solid #e9e9e9;
      }
    }
    &-rigth {
      &-content {
        margin-left: 60px;
      }
      &-bottom {
        display: flex;
        justify-content: center;
        margin-top: 109px;
      }
      &-item {
        margin-bottom: 37px;
        &-name {
          margin-right: 103px;
        }
      }
    }
  }
}
.detail-role-card::v-deep .el-divider--horizontal {
  margin: 9px 0 21px;
}
.custom-tree-node-select {
  margin-left: 40px;
}
.detail-role-card::v-deep .el-tree-node__content {
  // height: 40px;
  height: 100%;
  margin-bottom: 4px;
  padding: 5px 0;
}
.detail-role-card::v-deep .el-input__inner {
  height: 32px;
  line-height: 32px;
}
.detail-role-card::v-deep .el-input__icon {
  line-height: 32px;
}
.iteminput {
  display: flex;
  span {
    width: 83px;
    margin-right: 103px;
  }
}
.detail-role-card::v-deep .el-textarea__inner {
  width: calc(100% - 103px);
}
.detail-role-card::v-deep .el-textarea {
  width: calc(100% - 103px);
}
.detail-role-card::v-deep .el-form-item__label {
  //   font-size: 16px;
  color: #333333;
}
.detail-role-card::v-deep .is-error .el-form-item__content {
  margin: 2px 0;
}
.btn {
  width: 120px;
  height: 40px;
  margin-left: 20px;
}
.width-100-103 {
  width: calc(100% - 103px);
}
.width-100-103 ::v-deep .el-input__inner {
  width: calc(100% - 103px);
  height: 40px;
  line-height: 40px;
}
.detail-role-card::v-deep .el-form-item__error {
  left: 132px;
}
</style>
