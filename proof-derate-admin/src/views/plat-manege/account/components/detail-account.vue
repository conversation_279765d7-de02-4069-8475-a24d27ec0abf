<template>
  <div class="detail-account">
    <el-card>
      <div class="header">
        <div class="icon">
          <img src="@/assets/commonPack_images/account-detail.png" alt srcset>
        </div>
        <span class="tit">账号信息</span>
      </div>
      <div class="detail-cont">
        <ul>
          <li v-for="item in accountData" :key="item.key">
            <span class="key">{{ item.key }}</span>
            <div class="value">
              <div v-if="item.type==='tag'" class="tag">
                <el-tag v-for="cloumn in item.value" :key="cloumn.id" size="small">{{ cloumn.name }}</el-tag>
              </div>
              <span v-else>{{ item.value }}</span>
            </div>
          </li>
        </ul>
      </div>
    </el-card>
    <el-dialog class="reset-password" title="成功提示" top="25%" :visible.sync="repwDialogVisible" width="30%">
      <div class="boby">
        <div class="status el-icon-success" />
        <div class="content">
          <p>密码重置成功</p>
          <p>
            初始化密码：{{ passwordPlus }}
            <el-button
              :type="isCopypassword?'info':'primary'"
              :disabled="isCopypassword"
              plain
              size="mini"
              @click="copyPassword($event)"
            >{{ isCopypassword?'已复制':'一键复制' }}</el-button>
          </p>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="repwDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmReview">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getAccountView, getOrganizationList, getRoleList, resetPassword, changeStatus } from '@/api/commonPack/platManege'
import { plusXing, isPermission } from '@/utils/index.js'
import clip from '@/utils/clipboard'
export default {
  data() {
    return {
      accountData: {},
      accountInfo: {},
      repwDialogVisible: false,
      isCopypassword: false,
      passwordPlus: ''
    }
  },
  computed: {
    isAccountResetPassword() {
      return this.$store.state.breadcrumbBtn.platManage.isAccountResetPassword
    },
    isAccountDissable() {
      return this.$store.state.breadcrumbBtn.platManage.isAccountDissable
    }
  },
  watch: {
    isAccountResetPassword(value) {
      // 监听到有变化就重新获取数据
      // console.log('isAccountResetPassword', this.accountInfo)
      this.handleResetPassword(this.accountInfo)
    },
    isAccountDissable(value) {
      this.handleChangeStatus(this.accountInfo)
    }
  },
  mounted() {
    this.getAccountView()
  },
  methods: {
    getAccountView() {
      getAccountView(this.$route.query.id).then(res => {
        if (res.meta.code !== '200') return
        const { data } = res
        this.accountInfo = data
        this.$store.commit('breadcrumbBtn/SET_PLATMANAGE', { accountStatus: this.accountInfo.status })
        this.accountData = [
          { key: '姓名', value: data.user_name },
          { key: '账号', value: data.account },
          // { key: '所属部门', value: data.division_name },
          { key: '最近一次登录时间', value: data.last_login_time }
        ]
        this.getOrganizationList()
        this.getRoleList()
      })
    },
    async getOrganizationList() {
      getOrganizationList({}).then(res => {
        const deparment = res.data.filter(o => o.id === this.accountInfo.org_id)
        const name = deparment.length > 0 ? deparment[0].name : ''
        this.accountData.splice(2, 0, { key: '所属部门', value: name })
      })
    },
    async getRoleList() {
      const contain = this.accountInfo.role_id || []
      getRoleList({ name: '', contain }).then(res => {
        const roles = []
        res.data.forEach(item => {
          if (!this.accountInfo.role_id) return
          this.accountInfo.role_id.forEach(element => {
            if (item.id === element) {
              roles.push(item)
            }
          })
        })
        this.accountData.splice(3, 0, { key: '角色', value: roles, type: 'tag' })
      })
    },
    handleResetPassword(row) {
      this.$alert(`<span>是否确认重置密码</span><br/><span>账号：${row.account}</span>`, '警告提示', {
        dangerouslyUseHTMLString: true,
        type: 'warning',
        showCancelButton: true,
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      })
        .then(() => {
          this.isCopypassword = false
          this.setResetPassword(row.id, event)
        })
        .catch(() => {})
    },
    // 重置密码
    setResetPassword(id, event) {
      resetPassword(id).then(res => {
        // if (res.meta.code !== 200) return
        this.resetPassword = res.data
        this.passwordPlus = plusXing(res.data, 0, 0, '*')
        this.repwDialogVisible = true
        /* this.$alert(`<span>密码重置成功</span><br/><span>初始化密码：${res.data}</span>`, '成功提示', {
          dangerouslyUseHTMLString: true,
          type: 'success',
          showCancelButton: true,
          confirmButtonText: '确定',
          cancelButtonText: '取消'
        }).then(() => {
          clip(res.data, event)
        }).catch(() => {
        }) */
      })
    },
    // 变更状态
    handleChangeStatus(row) {
      const alert = {}
      if (row.status === 'NORMAL') {
        alert.content = '是否确认禁止此账号登录系统？'
        alert.status = 'DISABLE'
      } else {
        alert.content = '是否确认恢复此账号登录系统？'
        alert.status = 'NORMAL'
      }
      this.$alert(`<span>${alert.content}</span><br/><span>账号：${row.account}</span>`, '警告提示', {
        dangerouslyUseHTMLString: true,
        type: 'warning',
        showCancelButton: true,
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      })
        .then(() => {
          const par = {
            id: row.id,
            status: alert.status
          }
          changeStatus(par).then(res => {
            if (res.meta.code === '200') {
              this.getAccountView()
              this.$message({
                type: 'success',
                message: '修改成功'
              })
            }
          })
        })
        .catch(() => {
          /* this.$message({
          type: 'info',
          message: '已取消删除'
        }) */
        })
    },
    confirmReview() {
      this.repwDialogVisible = false
      console.log(this.isCopypassword)
      if (!this.isCopypassword) {
        this.$alert(`<span>请复制初始化密码</span><br/><span>点击“一键复制”按钮，复制初始化密码</span>`, '警告提示', {
          dangerouslyUseHTMLString: true,
          type: 'warning',
          confirmButtonText: '确定'
        }).then(() => {
          this.repwDialogVisible = true
        })
      }
    },
    copyPassword(event) {
      clip(this.resetPassword, event)
      this.isCopypassword = true
    }
  }
}
</script>

<style lang="scss" scoped>
.detail-account {
  padding: 10px;
  .header {
    height: 40px;
    line-height: 40px;
    // margin: 10px 0;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #cccccc;
    .icon {
      width: 22px;
      height: 22px;
      display: flex;
      align-items: center;
      img {
        width: 100%;
        height: 100%;
      }
    }
    .tit {
      padding-left: 5px;
    }
  }
  .detail-cont {
    margin-top: 40px;
    ul,
    li {
      list-style: none;
    }
    li {
      height: 40px;
      font-size: 14px;
      color: #333333;
      display: flex;
    }
    .key {
      width: 150px;
      text-align: left;
      padding-right: 20px;
    }
  }
}
</style>
<style lang="scss">
.width50 {
  width: 130px;
  text-align: right !important;
}
.detail-account {
  .el-tag--small {
    border-radius: 13px;
  }
}
.reset-password {
  .boby {
    display: flex;
    align-items: center;
    .status {
      color: #67c23a;
      // transform: translateY(-50%);
      font-size: 24px !important;
    }
    .content {
      padding-left: 12px;
      padding-right: 12px;
    }
  }
}
</style>
