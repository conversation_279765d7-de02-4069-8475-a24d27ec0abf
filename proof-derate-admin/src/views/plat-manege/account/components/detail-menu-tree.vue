<template>
  <div class="detail-cont">
    <ul>
      <li v-for="item in detailData" :key="item.key">
        <span class="key">{{ item.key }}</span>
        <div class="value">
          <div v-if="item.type==='radio'">
            <el-radio-group v-model="item.value" disabled>
              <el-radio v-for="clounm in item.options" :key="clounm.label" :label="clounm.value">{{ clounm.label }}</el-radio>
            </el-radio-group>
          </div>
          <div v-else-if="item.type ==='permission'">
            <span v-for="o in item.value" :key="o.value">{{ o.label }}</span>
          </div>
          <span v-else>{{ item.value }}</span>
        </div>
      </li>
    </ul>
  </div>
</template>

<script>
import { getMenuView, getMenuList } from '@/api/commonPack/platManege'

export default {
  props: {
    id: {
      type: String,
      default: () => {
        return ''
      }
    }
  },
  data() {
    return {
      detailData: [],
      menuInfo: {},
      permissionScope: [
        { label: '全局', value: 'ALL' },
        { label: '本级', value: 'CURRENT' },
        { label: '本级及下级', value: 'CURRENT_SUB' },
        { label: '本级及子孙级', value: 'CURRENT_LAST' },
        { label: '本部门', value: 'ORG' },
        { label: '本部门及下级部门', value: 'ORG_SUB' },
        { label: '本部门及子孙级部门', value: 'ORG_LAST' },
        { label: '仅下级部门', value: 'ONLY_SUB_ORG' },
        { label: '仅子孙级部门', value: 'ONLY_LAST_ORG' }
      ]
    }
  },
  watch: {
    id(value) {
      console.log('w   watch: ' + value)
      if (value) {
        this.getMenuView()
      } else {
        this.detailData = []
      }
      // 监听到有变化就重新获取数据
    }

  },

  mounted() {
    this.getMenuView()
  },

  methods: {
    getMenuView() {
      console.log(this.id)
      if (!this.id) return
      getMenuView(this.id).then(res => {
        const { data } = res
        this.menuInfo = data
        console.log('data', data)
        const per = []
        // console.log('Permission scopesOptions ', this.permissionScope, data)
        this.permissionScope.forEach(o => {
          if (!data.permission_scope) return
          data.permission_scope.forEach(el => {
            if (o.value === el) per.push(o)
          })
        })
        switch (data.menu_type) {
          case 'CATALOGUE':
            this.detailData = [
              { key: '菜单类型', value: data.menu_type, type: 'radio', options: [{ label: '目录', value: 'CATALOGUE' }, { label: '菜单', value: 'MENU' }, { label: '按钮', value: 'BUTTON' }]	 },
              { key: '目录标题', value: data.menu_name },
              { key: '列表排序', value: data.menu_sort },
              { key: '路由地址', value: data.menu_route_url },
              { key: '组件地址', value: data.component },
              // { key: '上级类目', value: data.menu_parent_id },
              { key: '菜单是否可见', value: data.menu_status, type: 'radio', options: [{ label: '显示', value: 'SHOW' }, { label: '隐藏', value: 'HIDE' }]	 },
              { key: '是否链接至外部', value: data.menu_link_external, type: 'radio', options: [{ label: '是', value: true }, { label: '否', value: false }]	 },
              { key: '打开链接方式', value: data.menu_open_way, type: 'radio', options: [{ label: '当前页', value: 'CUR_WINDOW' }, { label: '新窗口', value: 'NEW_WINDOW' }]	 },
              { key: '外部链接地址', value: data.menu_external_url }
            ]
            break
          case 'MENU':
            this.detailData = [
              { key: '菜单类型', value: data.menu_type, type: 'radio', options: [{ label: '目录', value: 'CATALOGUE' }, { label: '菜单', value: 'MENU' }, { label: '按钮', value: 'BUTTON' }]	 },
              { key: '菜单标题', value: data.menu_name },
              { key: '列表排序', value: data.menu_sort },
              { key: '路由地址', value: data.menu_route_url },
              { key: '组件地址', value: data.component },
              // { key: '上级类目', value: data.menu_parent_id },
              { key: '权限标识', value: data.permission_code },
              { key: '菜单是否可见', value: data.menu_status, type: 'radio', options: [{ label: '显示', value: 'SHOW' }, { label: '隐藏', value: 'HIDE' }]	 },
              { key: '数据范围', value: per, type: 'permission' },
              { key: '是否链接至外部', value: data.menu_link_external, type: 'radio', options: [{ label: '是', value: true }, { label: '否', value: false }]	 },
              { key: '打开链接方式', value: data.menu_open_way, type: 'radio', options: [{ label: '当前页', value: 'CUR_WINDOW' }, { label: '新窗口', value: 'NEW_WINDOW' }]	 },
              { key: '外部链接地址', value: data.menu_external_url }

            ]
            break
          case 'BUTTON':
            this.detailData = [
              { key: '菜单类型', value: data.menu_type, type: 'radio', options: [{ label: '目录', value: 'CATALOGUE' }, { label: '菜单', value: 'MENU' }, { label: '按钮', value: 'BUTTON' }]	 },
              { key: '按钮名称', value: data.menu_name },
              // { key: '上级类目', value: data.menu_parent_id },
              { key: '权限标识', value: data.permission_code },
              { key: '数据范围', value: per, type: 'permission' }

            ]
        }
        console.log(this.detailData)
        /*  this.detailData = [
          { key: '菜单类型', value: data.menu_type, type: 'radio', options: [{ label: '目录', value: 'CATALOGUE' }, { label: '菜单', value: 'MENU' }, { label: '按钮', value: 'BUTTON' }]	 },
          { key: '菜单标题', value: data.menu_name },
          { key: '注册部门', value: data.menu_sort },
          { key: '列表排序', value: data.menu_sort },
          { key: '路由地址', value: data.menu_route_url },
          // { key: '上级类目', value: data.menu_parent_id },
          { key: '权限标识', value: data.permission_code },
          { key: '菜单是否可见', value: data.menu_status, type: 'radio', options: [{ label: '显示', value: 'SHOW' }, { label: '隐藏', value: 'HIDE' }]	 },
          { key: '数据范围', value: data.permission_scope }

        ] */
        this.getMenuList()
      }).catch(() => {})
    },
    getMenuList() {
      const data = { scope: true, menu_link_external: true }
      getMenuList(data).then(res => {
        if (res.meta.code === '200' && res.data.length) {
          const _ = res.data.filter(item => item.menu_id === this.menuInfo.menu_parent_id)
          const name = _.length ? _[0].menu_name : ''
          this.detailData.splice(5, 0, { key: '上级类目', value: name })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
  .detail-cont{
    ul,li{
      list-style: none;
    }
    li{
      height: 40px;
      font-size: 14px;
      color: #888888;
      display: flex;
    }
    .key{
      width: 150px;
      text-align: right;
      padding-right: 20px;
    }
    .value{
      max-width: 500px;
    }
  }
</style>
