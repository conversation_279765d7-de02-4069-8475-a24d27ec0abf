<template>
  <div class="detail-account">
    <el-card>
      <div class="header">
        <div class="icon">
          <img :src="imgUrl" alt="" srcset="">
        </div>
        <span class="tit">{{ isEdit?'编辑用户':'新增用户' }}</span>
      </div>
      <div class="detail-cont">
        <el-form ref="accountForm" :model="accountForm" :rules="rules" label-width="100px">
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="姓名" prop="user_name">
                <el-input v-model="accountForm.user_name" placeholder="请输入用户名称" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="所属部门" prop="org_id">
                <el-select filterable v-model="accountForm.org_id" clearable placeholder="请选择所属部门">
                  <el-option v-for="item in departmentOptions" :key="item.id" :label="item.name" :value="item.id" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="账号" prop="account">
                <!-- onkeyup="value=value.replace(/[^\w\.\/]/ig,'')" -->
                <el-input v-model="accountForm.account" minlength="2" maxlength="20" :disabled="isEdit" placeholder="请输入账号" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="角色" prop="role_id">
                <el-select v-model="accountForm.role_id" placeholder="请选择角色" multiple>
                  <el-option v-for="item in rolesOptions" :key="item.id" :label="item.name" :value="item.id" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item v-if="isEdit" label="账号状态" prop="status">
            <el-radio-group v-model="accountForm.status">
              <el-radio v-for="(item,idx) in statusOptions" :key="idx" :label="item.value">{{ item.label }}</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item v-if="!isEdit" label="初始化密码">
            <span class="password">{{ passwordPlus }}</span>
            <el-button type="primary" plain size="mini" @click="copyPassword($event)">一键复制</el-button>
          </el-form-item>
        </el-form>
        <div slot="footer" class="account-footer">
          <el-button type="primary" @click="onSubmit">确 定</el-button>
          <el-button @click="onCanse">取 消</el-button>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import { getAccountView, getOrganizationList, getRoleList, editAccount, createAccount, initPassword } from '@/api/commonPack/platManege'
import clip from '@/utils/clipboard'
import { plusXing } from '@/utils/index.js'

export default {
  data() {
    const checkAccount = (rule, value, callback) => {
      var reg = /^[a-zA-Z0-9_]{0,}$/
      if (!value) {
        callback(new Error('账号不能为空'))
      } else if (!reg.test(value)) {
        callback(new Error('不支持填写中文及特殊字符'))
      } else if (value < 2) {
        callback(new Error(' 请输入2到20个字符'))
      } else {
        callback()
      }
    }
    return {
      accountData: {},
      accountForm: {
        user_name: '',
        org_id: '',
        account: '',
        division_name: '',
        role_id: [],
        status: '',
        password: ''
      },
      rules: {
        user_name: [
          { required: true, message: '姓名不能为空', trigger: 'blur' }
        ],
        account: [
          { required: true, validator: checkAccount, trigger: 'blur' }
        ],
        role_id: [
          { type: 'array', required: true, message: '角色不能为空', trigger: 'focus' }
        ],
        status: [
          { required: true, message: '账号状态不能为空', trigger: 'focus' }
        ]
      },
      statusOptions: [
        { value: 'NORMAL', label: '正常' },
        { value: 'DISABLE', label: '禁用' }
      ],
      departmentOptions: [],
      rolesOptions: [],
      passwordPlus: ''
    }
  },
  computed: {
    isEdit() {
      return this.$route.query.isEdit === 1
    },
    imgUrl() {
      return this.isEdit ? require('@/assets/commonPack_images/account-edit.png') : require('@/assets/commonPack_images/account-add.png')
    }
  },
  mounted() {
    if (this.isEdit) {
      this.getAccountView()
    } else {
      this.getOrganizationList()
      this.getRoleList()
      this.initPassword()
    }
  },
  methods: {
    getAccountView() {
      getAccountView(this.$route.query.id).then(res => {
        if (res.meta.code !== '200') return
        const { data } = res
        this.accountForm = data
        this.getOrganizationList()
        this.getRoleList()
      })
    },
    getOrganizationList() {
      getOrganizationList({ scope: true, status: 'NORMAL' }).then(res => {
        this.departmentOptions = res.data
      })
    },
    getRoleList() {
      const contain = this.accountForm.role_id || []
      console.log(contain)
      getRoleList({ name: '', scope: true, status: 'NORMAL', contain }).then(res => {
        this.rolesOptions = res.data
      })
    },
    initPassword() {
      initPassword().then(res => {
        this.accountForm.password = res.data
        this.passwordPlus = plusXing(res.data, 0, 0, '*')
      })
    },
    copyPassword(event) {
      clip(this.accountForm.password, event)
    },
    onSubmit() {
      this.$refs['accountForm'].validate((valid) => {
        if (valid) {
          if (this.isEdit) {
            this.editAccount()
          } else {
            this.createAccount()
          }
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    editAccount() {
      editAccount(this.accountForm).then((res) => {
        if (res.meta.code !== '200') return
        this.$message({
          message: '保存成功',
          type: 'success'
        })
        this.$router.go(-1)
      })
    },
    createAccount() {
      delete this.accountForm.status
      createAccount(this.accountForm).then((res) => {
        if (res.meta.code !== '200') return
        this.$message({
          message: '保存成功',
          type: 'success'
        })
        this.$router.go(-1)
      })
    },
    onCanse() {
      this.$router.go(-1)
    }
  }
}
</script>

<style lang="scss" scoped>
.detail-account{
  padding:10px;
  .header{
    height:40px;
    line-height: 40px;
    // margin: 10px 0;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #cccccc;
    .icon{
      width:22px;
      height: 22px;
      display: flex;
      align-items: center;
      img{
        width: 100%;
        height: 100%;
      }
    }
    .tit{
      padding-left: 5px;
    }
  }
  .detail-cont{
    margin-top: 30px;
    .account-footer{
      text-align: center;
    }
    .password{
      padding-right: 10px;
    }
  }
}

</style>
<style lang="scss">
.width50{
  width:130px;
  text-align: right !important;
}
.detail-account{
}
</style>
