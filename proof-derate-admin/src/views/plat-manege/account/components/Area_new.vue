<template>
  <div class="area-container">
    <!-- <el-select v-model="division.province" placeholder="请选择" clearable class="padding-10" @change="provinceChange">
      <el-option v-for="item in division.provinceOptions" :key="item.value" :label="item.label" :value="item.value" />
    </el-select>
    <el-select v-model="division.city" placeholder="请选择" clearable class="padding-10" @change="cityChange">
      <el-option v-for="item in division.cityOptions" :key="item.value" :label="item.label" :value="item.value" />
    </el-select>
    <el-select v-model="division.district" placeholder="请选择" clearable class="padding-10" @change="districtChange">
      <el-option v-for="item in division.districtOptions" :key="item.value" :label="item.label" :value="item.value" />
    </el-select>-->
    <el-select v-model="division.province" placeholder="请选择" clearable class="padding-10" @change="provinceChange" @clear="getDivisionCode" :disabled="disabled1">
      <el-option v-for="item in parentDivisionlist" :key="item.code" :label="item.name" :value="item.code" />
    </el-select>
    <el-select v-model="division.city" placeholder="请选择" clearable class="padding-10" @change="cityChange" @clear="getDivisionCode" :disabled="disabled2">
      <el-option v-for="item in parentDivisionlist1" :key="item.code" :label="item.name" :value="item.code" />
    </el-select>
    <el-select v-model="division.district" placeholder="请选择" clearable class="padding-10" @change="districtChange" @clear="getDivisionCode" :disabled="disabled3">
      <el-option v-for="item in parentDivisionlist2" :key="item.code" :label="item.name" :value="item.code" />
    </el-select>
  </div>
</template>

<script>
import { getAlldivision, getDivisionList } from '@/api/commonPack/platManege'

export default {
  data() {
    return {
      division_code: '',
      division_name: '',
      divisionTree: [],
      division: {
        province: '',
        provinceName: '',
        provinceOptions: [],
        city: '',
        cityName: '',
        cityOptions: [],
        district: '',
        districtName: '',
        districtOptions: []
      },
      disabled1: false,
      disabled2: false,
      disabled3: false,
      parentDivisionlist: [],
      parentDivisionlist1: [],
      parentDivisionlist2: []
    }
  },
  props: {
    divisionCode: { type: String, default: '' }
  },
  watch: {
    divisionCode(value) {
      console.log('w   watch: ' + value)
      this.initData()
      // 监听到有变化就重新获取数据
    }
  },
  mounted() {
    // this.getAlldivision()
    this.initData()
  },
  methods: {
    initData() {
      console.log('divisionCode', this.divisionCode)
      const data = {
        code: '',
        level: 'SUB'
      }
      this.getDivisionList(data)
      if (this.divisionCode != '') {
        const data1 = {
          code: this.divisionCode,
          level: 'PARENT'
        }
        this.getDivisionListByCode(data1)
      }
    },
    // 获取省市区数据
    getAlldivision() {
      return getAlldivision().then(res => {
        if (res.meta.code === '200' && res.data != null) {
          this.divisionTree = res.data.root
          this.division.province = this.divisionTree.value
          this.division.provinceName = this.divisionTree.label
          this.division.provinceOptions.push(this.divisionTree)
          this.division.cityOptions = this.divisionTree.children
          this.getNextDivision(this.divisionTree.children, this.division_code, 'city')
        }
      })
    },
    // cityChange(val) {
    //   this.division_code = ''
    //   this.division.district = ''
    //   const options = this.division.cityOptions.filter((i) => {
    //     if (val === i.value) {
    //       this.division.cityName = i.label
    //     }
    //     return val === i.value
    //   })
    //   this.division.districtOptions = options[0].children
    //   this.division.city = val
    //   this.getDivisionCode()
    // },
    // 获取下一级
    getNextDivision(data, code, val) {
      console.log(data, code)
      data.forEach(e => {
        // console.log(e, code)
        if (e.value === code) {
          if (val === 'city') {
            this.division.city = code
            this.division.cityName = e.label
            this.division.cityOptions = data
            this.division.districtOptions = e.children
          } else {
            this.division.district = code
            this.division.districtOptions = data
            this.division.city = e.parent
            this.division.cityName = this.division.cityOptions.find(item => item.value === e.parent).label
            this.division.districtName = e.label
          }
        } else {
          if (e.children != null) {
            this.getNextDivision(e.children, code)
          }
        }
      })
    },
    // provinceChange() {
    //   this.division.city = ''
    //   this.division.district = ''
    //   this.getDivisionCode()
    // },
    // 获取division_code
    getDivisionCode() {
      this.division_code = this.division.district || this.division.city || this.division.province
      this.division_name = this.division.provinceName + this.division.cityName + this.division.districtName
      console.log('this.division.district || this.division.city || this.division.province', this.division.district || this.division.city || this.division.province)
      console.log('this.division_name', this.division_name)
      this.$emit('setDivisionCode', this.division_code)
      this.$emit('setDivisionName', this.division_name)
    },
    districtChange(val) {
      let opt = {}
      opt = this.parentDivisionlist2.find(item => {
        return item.code === val
      })
      this.division.districtName = opt.name
      this.getDivisionCode()
    },

    getDivisionListByCode(data) {
      getDivisionList(data).then(res => {
        if (res.meta.code === '200' && res.data != null) {
          // this.parentDivisionlist = res.data
          console.log('getDivisionList', res.data)
          this.division.province = res.data[0].code
          this.disabled1 = true
          let data1 = {
            code: res.data[0].code,
            level: 'SUB'
          }
          console.log('data1', data1)
          this.getDivisionList1(data1).then(() => {
            if (res.data[1] !== undefined) {
              this.division.city = res.data[1].code
            }
          })
          if (res.data[1] !== undefined) {
            this.disabled2 = true
            let data2 = {
              code: res.data[1].code,
              level: 'SUB'
            }
            console.log('data2', data2)
            this.getDivisionList2(data2).then(() => {
              this.division.district = res.data[2].code
              this.disabled3 = true
            })
          }
        }
      })
    },

    getDivisionList(data) {
      getDivisionList(data).then(res => {
        if (res.meta.code === '200' && res.data != null) {
          this.parentDivisionlist = res.data
          console.log(this.parentDivisionlist)
        }
      })
    },
    getDivisionList1(data) {
      this.parentDivisionlist1 = []
      this.division.city = ''
      this.parentDivisionlist2 = []
      this.division.district = ''
      return getDivisionList(data).then(res => {
        if (res.meta.code === '200' && res.data != null) {
          this.parentDivisionlist1 = res.data
          console.log(res.data)
        }
      })
    },
    getDivisionList2(data) {
      this.parentDivisionlist2 = []
      this.division.district = ''
      console.log('data', data)
      return getDivisionList(data).then(res => {
        if (res.meta.code === '200' && res.data != null) {
          this.parentDivisionlist2 = res.data
          console.log(res.data)
        }
      })
    },
    provinceChange(val) {
      console.log('division.province', this.division.province)
      let opt = {}
      opt = this.parentDivisionlist.find(item => {
        return item.code === val
      })
      this.division.provinceName = opt.name
      this.getDivisionCode()
      let data = {
        code: this.division.province,
        level: 'SUB'
      }
      this.getDivisionList1(data)
    },
    cityChange(val) {
      console.log('division.city', this.division.city)
      let data = {
        code: this.division.city,
        level: 'SUB'
      }
      let opt = {}
      opt = this.parentDivisionlist1.find(item => {
        return item.code === val
      })
      this.division.cityName = opt.name
      this.getDivisionCode()
      this.getDivisionList2(data)
    }
  }
}
</script>
