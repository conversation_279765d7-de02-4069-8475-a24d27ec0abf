<template>
  <div class="edit-list-container">
    <el-form ref="form" :model="editDataForm" :rules="rules" label-width="160px">
      <div v-if="!isTree" class="col1">
        <el-row :gutter="24">
          <el-col :span="20">
            <el-form-item label="菜单类型" prop="menu_type">
              <el-radio-group v-model="editDataForm.menu_type" @change="menuTypeChange">
                <el-radio v-for="item in menuTypeOptions" :key="item.value" :label="item.value">{{ item.label }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="20">
            <el-form-item :label="menuName" prop="menu_name">
              <el-input v-model="editDataForm.menu_name" maxlength="100" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="!isBtnShow" :gutter="24">
          <el-col :span="20">
            <el-form-item label="列表排序" prop="menu_sort">
              <el-input v-model="editDataForm.menu_sort" type="number" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="!isBtnShow" :gutter="24">
          <el-col :span="20">
            <el-form-item label="路由地址" prop="menu_route_url">
              <el-input v-model="editDataForm.menu_route_url" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="!isBtnShow" :gutter="24">
          <el-col :span="20">
            <el-form-item label="组件地址" prop="component">
              <el-input v-model="editDataForm.component" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="20" class="industryOrgName">
            <el-form-item label="上级类目" prop="menu_parent_id">
              <el-select filterable v-model="editDataForm.menu_parent_id" placeholder="请选择" class="width-100">
                <el-option v-for="item in menuList" :key="item.menu_id" :label="item.menu_name" :value="item.menu_id" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="!isCatelogShow" :gutter="24">
          <el-col :span="20">
            <el-form-item label="权限标识" prop="permission_code">
              <el-input v-model="editDataForm.permission_code" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="!isBtnShow" :gutter="24">
          <el-col :span="20">
            <el-form-item label="菜单是否可见" prop="menu_status">
              <el-radio-group v-model="editDataForm.menu_status">
                <el-radio v-for="item in menuStatusOptions" :key="item.value" :label="item.value">{{ item.label }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="!isBtnShow" :gutter="24">
          <el-col :span="20">
            <el-form-item label="是否链接至外部" prop="menu_link_external">
              <el-radio-group v-model="editDataForm.menu_link_external">
                <el-radio v-for="item in menuLinkExternalOptions" :key="item.value" :label="item.value">{{ item.label }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="!isBtnShow" :gutter="24">
          <el-col v-if="editDataForm.menu_link_external===true" :span="20">
            <el-form-item label="打开链接方式" prop="menu_open_way">
              <el-radio-group v-model="editDataForm.menu_open_way">
                <el-radio v-for="item in menuOpenWayOptions" :key="item.value" :label="item.value">{{ item.label }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="!isBtnShow&&editDataForm.menu_link_external===true" :gutter="24">
          <el-col :span="20">
            <el-form-item label="链接地址" prop="menu_external_url">
              <el-input v-model="editDataForm.menu_external_url" maxlength="100" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="!isCatelogShow" :gutter="24">
          <el-col :span="20">
            <el-form-item label="数据范围" prop="permission_scope">
              <el-select v-model="editDataForm.permission_scope" placeholder="请选择" multiple class="width-100">
                <el-option v-for="item in permissionScope" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="!isBtnShow" :gutter="24">
          <el-col :span="20">
            <el-form-item label="菜单图标" prop="icon">
              <el-popover placement="bottom-start" width="550" trigger="click" popper-class="popper-class" @show="$refs.iconSelect.reset()">
                <icon-select ref="iconSelect" :name="editDataForm.icon" @selected="handleSelected" />
                <el-input slot="reference" v-model="editDataForm.icon" placeholder="请输入内容" style="cursor: pointer" clearable>
                  <template slot="prepend">
                    <svg-icon :icon-class="editDataForm.icon" style="height: 30px; width: 16px" />
                  </template>
                </el-input>
              </el-popover>
            </el-form-item>
          </el-col>
        </el-row>
      </div>
      <div v-else class="col2">
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="菜单类型" prop="menu_type">
              <el-radio-group v-model="editDataForm.menu_type" @change="menuTypeChange">
                <el-radio v-for="item in menuTypeOptions" :key="item.value" :label="item.value">{{ item.label }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="menuName" prop="menu_name">
              <el-input v-model="editDataForm.menu_name" maxlength="100" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col v-if="!isBtnShow" :span="12">
            <el-form-item label="列表排序" prop="menu_sort">
              <el-input v-model="editDataForm.menu_sort" type="number" />
            </el-form-item>
          </el-col>
          <el-col v-if="!isBtnShow" :span="12">
            <el-form-item label="路由地址" prop="menu_route_url">
              <el-input v-model="editDataForm.menu_route_url" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col v-if="!isBtnShow" :span="12">
            <el-form-item label="组件地址" prop="component">
              <el-input v-model="editDataForm.component" />
            </el-form-item>
          </el-col>
          <el-col :span="12" class="industryOrgName">
            <el-form-item label="上级类目" prop="menu_parent_id">
              <el-select v-model="editDataForm.menu_parent_id" placeholder="请选择" class="width-100">
                <el-option v-for="item in menuList" :key="item.menu_id" :label="item.menu_name" :value="item.menu_id" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col v-if="!isBtnShow" :span="12" class="industryOrgName">
            <el-form-item label="菜单是否可见" prop="menu_status">
              <el-radio-group v-model="editDataForm.menu_status">
                <el-radio v-for="item in menuStatusOptions" :key="item.value" :label="item.value">{{ item.label }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col v-if="!isBtnShow" :span="12">
            <el-form-item label="菜单图标" prop="icon">
              <el-popover placement="bottom-start" width="550" trigger="click" popper-class="popper-class" @show="$refs.iconSelect.reset()">
                <icon-select ref="iconSelect" :name="editDataForm.icon" @selected="handleSelected" />
                <el-input slot="reference" v-model="editDataForm.icon" placeholder="请输入内容" clearable style="cursor: pointer">
                  <template slot="prepend">
                    <svg-icon :icon-class="editDataForm.icon" style="height: 30px; width: 16px" />
                  </template>
                </el-input>
              </el-popover>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="!isBtnShow" :gutter="24">
          <el-col :span="12">
            <el-form-item label="是否链接至外部" prop="menu_link_external">
              <el-radio-group v-model="editDataForm.menu_link_external">
                <el-radio v-for="item in menuLinkExternalOptions" :key="item.value" :label="item.value">{{ item.label }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col v-if="editDataForm.menu_link_external===true" :span="12">
            <el-form-item label="打开链接方式" prop="menu_open_way">
              <el-radio-group v-model="editDataForm.menu_open_way">
                <el-radio v-for="item in menuOpenWayOptions" :key="item.value" :label="item.value">{{ item.label }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="!isBtnShow&&editDataForm.menu_link_external===true" :gutter="24">
          <el-col :span="20">
            <el-form-item label="链接地址" prop="menu_external_url">
              <el-input v-model="editDataForm.menu_external_url" maxlength="100" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col v-if="!isCatelogShow" :span="12">
            <el-form-item label="权限标识" prop="permission_code">
              <el-input v-model="editDataForm.permission_code" />
            </el-form-item>
          </el-col>
          <el-col v-if="!isCatelogShow" :span="12">
            <el-form-item label="数据范围" prop="permission_scope">
              <el-select v-model="editDataForm.permission_scope" placeholder="请选择" multiple class="width-100">
                <el-option v-for="item in permissionScope" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </div>
    </el-form>
    <div slot="footer" class="editDialog-footer">
      <el-button type="primary" @click="onSubmit">确 定</el-button>
      <el-button @click="onCanse">取 消</el-button>
    </div>
  </div>
</template>

<script>
import { getMenuList, createMenu, getMenuView, EditMenu } from '@/api/commonPack/platManege'
import IconSelect from '@/components/IconSelect/index.vue'
export default {
  components: { IconSelect },
  props: {
    id: {
      type: String,
      default: () => {
        return ''
      }
    },
    isEdit: {
      type: Boolean,
      default: () => {
        return true
      }
    },
    toggleEdit: {
      type: Boolean,
      default: () => {
        return false
      }
    },
    source: {
      type: String,
      default: () => {
        return ''
      }
    }
  },
  data() {
    return {
      menuTypeOptions: [
        { label: '目录', value: 'CATALOGUE' },
        { label: '菜单', value: 'MENU' },
        { label: '按钮', value: 'BUTTON' }
      ],
      menuStatusOptions: [
        { label: '显示', value: 'SHOW' },
        { label: '隐藏', value: 'HIDE' }
      ],
      menuLinkExternalOptions: [
        { label: '是', value: true },
        { label: '否', value: false }
      ],
      menuOpenWayOptions: [
        { label: '当前页', value: 'CUR_WINDOW' },
        { label: '新窗口', value: 'NEW_WINDOW' }
      ],
      permissionScope: [
        { label: '全局', value: 'ALL' },
        { label: '本级', value: 'CURRENT' },
        { label: '本级及下级', value: 'CURRENT_SUB' },
        { label: '本级及子孙级', value: 'CURRENT_LAST' },
        { label: '本部门', value: 'ORG' },
        { label: '本部门及下级部门', value: 'ORG_SUB' },
        { label: '本部门及子孙级部门', value: 'ORG_LAST' },
        { label: '仅下级部门', value: 'ONLY_SUB_ORG' },
        { label: '仅子孙级部门', value: 'ONLY_LAST_ORG' }
      ],
      editDataForm: {
        menu_type: 'CATALOGUE',
        menu_name: '',
        menu_sort: '',
        menu_status: 'SHOW',
        menu_parent_id: '',
        permission_code: '',
        menu_route_url: '',
        permission_scope: null,
        component: '',
        icon: '',
        menu_link_external: false,
        menu_open_way: 'CUR_WINDOW',
        menu_external_url: ''
      },
      rules: {
        menu_type: [{ required: true, message: '菜单类型不能为空', trigger: 'focus' }],
        menu_name: [{ required: true, message: '目录标题不能为空', trigger: 'blur' }],
        menu_sort: [{ required: true, message: '列表排序不能为空', trigger: 'blur' }],
        menu_route_url: [{ required: true, message: '路由地址不能为空', trigger: 'blur' }],
        component: [{ required: true, message: '组件地址不能为空', trigger: 'blur' }],
        permission_code: [{ required: true, message: '权限标识不能为空', trigger: 'blur' }],
        menu_status: [{ required: true, message: '菜单是否可见不能为空', trigger: 'focus' }],
        menu_link_external: [{ required: true, message: '是否链接至外部不能为空', trigger: 'focus' }],
        menu_open_way: [{ required: true, message: '打开链接方式不能为空', trigger: 'focus' }],
        menu_external_url: [{ required: true, message: '链接地址不能为空', trigger: 'blur' }]
      },
      menuName: '目录标题',
      isCatelogShow: true,
      isMenuShow: false,
      isBtnShow: false,
      menuParams: {
        menu_type: ['CATALOGUE'],
        menu_link_external: true
      },
      menuList: [],

      isTree: false
    }
  },
  computed: {
    isMenuAdd() {
      return this.$store.state.breadcrumbBtn.platManage.isMenuAdd
    }
  },
  watch: {
    isMenuAdd: {
      handler(val) {
        if (!val) return
        this.editDataForm = {
          menu_type: 'CATALOGUE',
          menu_name: '',
          menu_sort: '',
          menu_status: 'SHOW',
          menu_parent_id: '',
          permission_code: '',
          menu_route_url: '',
          permission_scope: null,
          component: '',
          icon: '',
          menu_link_external: false,
          menu_open_way: 'CUR_WINDOW',
          menu_external_url: ''
        }
      },
      deep: true,
      immediate: true
    },
    toggleEdit: {
      handler(val) {
        if (this.id) this.getMenuView()
      },
      deep: true,
      immediate: true
    },
    id: {
      handler(val) {
        if (!val) return
        this.getMenuView()
      },
      deep: true,
      immediate: true
    },
    isEdit: {
      handler(val) {
        if (!val) return
      },
      deep: true,
      immediate: true
    },
    source: {
      handler(val) {
        if (!val) return
        this.isTree = val === 'tree'
      },
      deep: true,
      immediate: true
    },
    'editDataForm.menu_type': {
      handler(val) {
        this.menuTypeChange(val)
      },
      deep: true,
      immediate: true
    }
  },
  created() {},
  mounted() {
    this.getMenuList()
    console.log(this.isEdit)
    // if (this.isEdit) this.getMenuView()
  },
  methods: {
    // 获取菜单详情
    getMenuView() {
      getMenuView(this.id).then(res => {
        if (res.meta.code !== '200') return
        this.editDataForm = res.data
        const _ = this.menuTypeOptions.filter(x => x.value === this.editDataForm.menu_type)
        this.$emit('setDialogTitle', `编辑${_[0].label}`)
        this.menuTypeChange(_[0].value)
      })
    },
    // 获取菜单列表
    getMenuList() {
      getMenuList(this.menuParams).then(res => {
        this.menuList = res.data
      })
    },
    menuTypeChange(val) {
      let menuName = ''
      if (val === this.menuTypeOptions[0].value) {
        menuName = '目录标题'
        this.isCatelogShow = true
        this.isMenuShow = false
        this.isBtnShow = false
        this.menuParams.menu_type = [this.menuTypeOptions[0].value]
        this.rules.menu_name = [{ required: true, message: '目录标题不能为空', trigger: 'blur' }]
        this.$emit('setDialogTitle', this.isEdit ? '编辑目录' : '新建目录')
      } else if (val === this.menuTypeOptions[1].value) {
        menuName = '菜单标题'
        this.isCatelogShow = false
        this.isMenuShow = true
        this.isBtnShow = false
        this.menuParams.menu_type = [this.menuTypeOptions[0].value]
        this.rules.menu_name = [{ required: true, message: '菜单标题不能为空', trigger: 'blur' }]
        this.$emit('setDialogTitle', this.isEdit ? '编辑菜单' : '新建菜单')
      } else if (val === this.menuTypeOptions[2].value) {
        menuName = '按钮名称'
        this.isCatelogShow = false
        this.isMenuShow = false
        this.isBtnShow = true
        this.menuParams.menu_type = [this.menuTypeOptions[1].value]
        this.rules.menu_name = [{ required: true, message: '按钮名称不能为空', trigger: 'blur' }]
        this.$emit('setDialogTitle', this.isEdit ? '编辑按钮' : '新建按钮')
      }
      this.menuName = menuName
      this.getMenuList()
    },
    onSubmit() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.isEdit) {
            this.EditMenu()
          } else {
            this.createMenu()
          }
        }
      })
    },
    EditMenu() {
      if (!this.editDataForm.menu_link_external) {
        delete this.editDataForm.menu_open_way
        delete this.editDataForm.menu_external_url
      }
      EditMenu(this.editDataForm).then(res => {
        if (res.meta.code !== '200') return
        this.$message({
          message: '保存成功',
          type: 'success'
        })
        this.$emit('canse', false)
      })
    },
    createMenu() {
      if (!this.editDataForm.menu_link_external) {
        delete this.editDataForm.menu_open_way
        delete this.editDataForm.menu_external_url
      }
      createMenu(this.editDataForm).then(res => {
        if (res.meta.code !== '200') return
        this.$message({
          message: '保存成功',
          type: 'success'
        })
        this.$emit('canse', false)
      })
    },
    onCanse() {
      this.$emit('canse', false)
    },
    handleSelected(e) {
      this.editDataForm.icon = e
    }
  }
}
</script>

<style lang="scss" scoped>
.editDialog-footer {
  text-align: center;
}
</style>
