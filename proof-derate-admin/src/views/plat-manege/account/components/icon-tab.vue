<template>
  <div class="btn_wrap">
    <div class="btn" v-for="(item, index) in list" :key="index" :class="currentIndex==index ? 'active' :''" @click="handleClick(index)">
        <img v-if="currentIndex==index" :src="item.activeIcon" alt="">
        <img v-else :src="item.icon" alt="">
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      currentIndex:0,
      list: [
        {
          icon: require("@/assets/commonPack_images/list-icon.png"),
          activeIcon: require("@/assets/commonPack_images/list-active-icon.png"),
        },
        {
          icon: require("@/assets/commonPack_images/tree-con.png"),
          activeIcon: require("@/assets/commonPack_images/tree-active-icon.png"),
        },
      ],
    };
  },

  methods: {
    handleClick(e) {
        console.log('e', e);
        this.currentIndex = e;
        this.$emit('selected', e)
    }
  }
};
</script>

<style lang='scss' scoped>
.btn_wrap{
        display: flex;
        align-items: center;
        .btn{
          width: 36px;
          height: 36px;
          background: #f7f7f7;
          border: 1px solid #d9d9d9;
          border-radius: 4px;
          display: flex;
          justify-content:center;
          align-items: center;
          cursor: pointer;
          &:first-child{
            margin-right: 12px;
          }
          &.active{
            background: #e0f0ff;
            border: 1px solid #99ceff;
          }
        }
      }
</style>