<!--  -->
<template>
  <div>
    <el-dialog
      :title="title"
      :visible.sync="delDialogVisible"
      width="30%"
    >
      <el-form ref="delValidateForm" :model="delValidateForm" label-width="0px" class="del-ruleForm" :rules="delRules">
        <el-form-item
          label=""
          prop="password"
        >
          <el-input ref="password" v-model="delValidateForm.password" class="password-input" :type="passwordType" placeholder="请输入当前登录账号的密码" autocomplete="off" @paste.native.capture.prevent="handPaste" />
          <span class="show-pwd" @click="showPwd">
            <svg-icon :icon-class="passwordType === 'password' ? 'eye' : 'eye-open'" />
          </span>
        </el-form-item>
        <p class="tips" v-html="tips" />
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="delDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="delRole">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: '',
  components: {},
  props: {
    tips: { type: String, default: '' },
    title: { type: String, default: '' }
  },
  data() {
    return {
      delValidateForm: {
        password: '',
        id: ''
      },
      delDialogVisible: false,
      delRules: { password: [{ required: true, message: '当前登录账号的密码不能为空', trigger: 'blur' }] },
      passwordType: 'password'

    }
  },

  computed: {},

  mounted() {},

  methods: {
    showPwd() {
      if (this.passwordType === 'password') {
        this.passwordType = ''
      } else {
        this.passwordType = 'password'
      }
      this.$nextTick(() => {
        this.$refs.password.focus()
      })
    },
    delRole() {
      this.$refs['delValidateForm'].validate(valid => {
        if (valid) {
          this.$emit('submit', this.delValidateForm.password)
        }
      })
    },
    handPaste() {
      return false
    }
  }
}

</script>
<style lang='scss' scoped>
.del-ruleForm{
  ::v-deep .el-form-item__content{
    display: flex;
    flex-flow: row nowrap;
    align-items: center;
    position: relative;
    border-bottom: 1px solid #dcdfe6;
    .el-input input{
      padding-right: 34px;
      border: none !important;
    }
  }
  .show-pwd {
    position: absolute;
    right: 10px;
    top: 2px;
    font-size: 16px;
    cursor: pointer;
    user-select: none;
  }
  .tips{
    font-size: 14px;
  }
}
</style>
