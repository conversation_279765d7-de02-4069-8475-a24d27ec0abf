<template>
  <div class="area-container">
    <el-select
      v-model="index.code"
      placeholder="请选择"
      clearable
      class="padding-5"
      @change="selctchange"
      @clear="clearDivisionCode(key)"
      @focus="focus(key)"
      :disabled="index.disabled"
      v-for="(index,key) in optionData"
      :key="key"
    >
      <el-option v-for="item in index.divisionlist" :key="item.code" :label="item.name" :value="item.code" />
    </el-select>
  </div>
</template>

<script>
import { getDivisionList } from '@/api/commonPack/platManege'
export default {
  data() {
    return {
      selectIndex: 0
    }
  },
  props: {
    divisionCode: { type: String, default: '' },
    // 行政区划层级 默认为3级
    level: {
      type: Number,
      default: 3
    },
    optionData: {
      type: Array,
      default: () => []
    }
  },
  watch: {
    divisionCode(value) {
      this.initData()
      // 监听到有变化就重新获取数据
    }
  },
  created() {},

  mounted() {
    // this.getAlldivision()
    this.initData()
  },
  methods: {
    focus(val) {
      this.selectIndex = val
    },

    initData() {
      this.optionData.forEach(i => {
        i.divisionlist = []
      })
      const data = {
        code: '',
        level: 'SUB'
      }
      this.getDivisionList(data, 0)
      if (this.divisionCode != '') {
        const data1 = {
          code: this.divisionCode,
          level: 'PARENT'
        }
        // this.optionData[0].name=''
        this.getDivisionListByCode(data1)
      }
    },

    // 获取division_code
    getDivisionCode() {
      // console.log('this.optionData', this.optionData)
      for (let i = this.optionData.length - 1; i > -1; i--) {
        if (this.optionData[i].code !== '') {
          this.division_code = this.optionData[i].code

          this.division_name = this.optionData[i].divisionlist.find(item => {
            return item.code === this.optionData[i].code
          }).name
          // console.log('this.optionData[i]', this.optionData[i].divisionlist)
          break
        }
      }
      // console.log('this.division_code', this.division_code)
      // console.log('this.division_name', this.division_name)
      this.$emit('setDivisionCode', this.division_code)
      this.$emit('setDivisionName', this.division_name)
    },

    clearDivisionCode(key) {
      this.optionData.forEach((i, index) => {
        if (index > key) {
          i.code = ''
          i.divisionlist = []
        }
      })
      if (key === 0) {
        this.division_code = ''
        this.division_name = ''
      }
      this.getDivisionCode()
    },
    // 外部组件获取部门编码和部门名字
    getAllCodeAndName() {
      let divisionCodeList = []
      let divisionNameList = []
      for (let index = 0; index < this.optionData.length; index++) {
        let opt = {}
        opt = this.optionData[index].divisionlist.find(item => {
          return item.code === this.optionData[index].code
        })
        if (opt !== undefined) {
          divisionCodeList.push(opt.code)
          divisionNameList.push(opt.name)
        }
      }
      return {
        division_code: divisionCodeList[divisionCodeList.length - 1],
        division_name: divisionNameList.join(',')
        // division_name:divisionNameList[divisionNameList.length - 1]
      }
    },
    selctchange(val) {
      // 点击清空按钮会触发选择改变事件，需判断是否为清空操作
      if (val !== '') {
        let opt = {}
        opt = this.optionData[this.selectIndex].divisionlist.find(item => {
          return item.code === val
        })
        if (opt !== undefined) {
          this.optionData[this.selectIndex].name = opt.name
        } else {
          this.optionData[this.selectIndex].name = ''
        }

        let data = {
          code: this.optionData[this.selectIndex].code,
          level: 'SUB'
        }
        // this.getDivisionList3(data)
        this.getDivisionList(data, this.selectIndex + 1)
        this.getDivisionCode()
      }
    },
    getDivisionListByCode(data) {
      getDivisionList(data).then(res => {
        if (res.meta.code === '200' && res.data != null) {
          // this.optionData[0].divisionlist = res.data

          const divisionList = res.data
          divisionList.forEach((e, index) => {
            // this['disabled' + index] = true
            this.optionData[index].code = e.code
            let data = {
              code: e.code,
              level: 'SUB'
            }
            this.getDivisionList(data, index + 1, 'aaa')
          })
        }
      })
    },

    getDivisionList(data, index, type) {
      if (this.optionData.length > index) {
        this.optionData[index].divisionlist = []
        if (!type) {
          for (let i = 0; i < this.optionData.length - index; i++) {
            this.optionData[index + i].code = ''
          }
        }
        getDivisionList(data).then(res => {
          if (res.meta.code === '200' && res.data != null) {
            // this.optionData[0].divisionlist = res.data
            this.optionData[index].divisionlist = res.data
          }
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .padding-5 {
  width: 140px;
  padding: 5px;
}
</style>
