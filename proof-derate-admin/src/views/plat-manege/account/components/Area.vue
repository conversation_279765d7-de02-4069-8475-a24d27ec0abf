<template>
  <div class="area-container">
    <el-select v-model="division.province" placeholder="请选择" clearable class="padding-10" @change="provinceChange">
      <el-option v-for="item in division.provinceOptions" :key="item.value" :label="item.label" :value="item.value" />
    </el-select>
    <el-select v-model="division.city" placeholder="请选择" clearable class="padding-10" @change="cityChange">
      <el-option v-for="item in division.cityOptions" :key="item.value" :label="item.label" :value="item.value" />
    </el-select>
    <el-select v-model="division.district" placeholder="请选择" clearable class="padding-10" @change="districtChange">
      <el-option v-for="item in division.districtOptions" :key="item.value" :label="item.label" :value="item.value" />
    </el-select>
  </div>
</template>

<script>
import { getAlldivision } from '@/api/commonPack/platManege'

export default {
  data() {
    return {
      division_code: '',
      divisionTree: [],
      division: {
        province: '',
        provinceName: '',
        provinceOptions: [],
        city: '',
        cityName: '',
        cityOptions: [],
        district: '',
        districtName: '',
        districtOptions: []
      }
    }
  },
  mounted() {
    this.getAlldivision()
  },
  methods: {
    // 获取省市区数据
    getAlldivision() {
      return getAlldivision().then(res => {
        if (res.meta.code === '200' && res.data != null) {
          this.divisionTree = res.data.root
          this.division.province = this.divisionTree.value
          this.division.provinceName = this.divisionTree.label
          this.division.provinceOptions.push(this.divisionTree)
          this.division.cityOptions = this.divisionTree.children
          this.getNextDivision(this.divisionTree.children, this.division_code, 'city')
        }
      })
    },
    cityChange(val) {
      this.division_code = ''
      this.division.district = ''
      const options = this.division.cityOptions.filter(i => {
        if (val === i.value) {
          this.division.cityName = i.label
        }
        return val === i.value
      })
      this.division.districtOptions = options[0].children
      this.division.city = val
      this.getDivisionCode()
    },
    // 获取下一级
    getNextDivision(data, code, val) {
      console.log(data, code)
      data.forEach(e => {
        // console.log(e, code)
        if (e.value === code) {
          if (val === 'city') {
            this.division.city = code
            this.division.cityName = e.label
            this.division.cityOptions = data
            this.division.districtOptions = e.children
          } else {
            this.division.district = code
            this.division.districtOptions = data
            this.division.city = e.parent
            this.division.cityName = this.division.cityOptions.find(item => item.value === e.parent).label
            this.division.districtName = e.label
          }
        } else {
          if (e.children != null) {
            this.getNextDivision(e.children, code)
          }
        }
      })
    },
    provinceChange() {
      this.division.city = ''
      this.division.district = ''
      this.getDivisionCode()
    },
    // 获取division_code
    getDivisionCode() {
      this.division_code = this.division.district || this.division.city || this.division.province
      this.$emit('setDivisionCode', this.division_code)
    },
    districtChange(val) {
      this.getDivisionCode()
    }
  }
}
</script>
