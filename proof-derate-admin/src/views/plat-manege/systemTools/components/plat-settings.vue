<template>
  <!-- 安全设置 -->
  <div style="width: 40%">
    <el-form ref="form" label-position="top" label-width="180px" :model="form">
      <el-form-item label="系统所属行政区划代码" prop="divisionCode">
        <el-input v-model="form.divisionCode" maxlength="50" show-word-limit />
      </el-form-item>
      <el-form-item label="系统所属行政区划名称" prop="divisionName">
        <el-input v-model="form.divisionName" maxlength="50" show-word-limit />
      </el-form-item>
      <el-form-item label="系统所属组织机构代码" prop="orgCode">
        <el-input v-model="form.orgCode" maxlength="50" show-word-limit />
      </el-form-item>
      <el-form-item label="系统所属统一社会信用代码" prop="creditCode">
        <el-input v-model="form.creditCode" maxlength="50" show-word-limit />
      </el-form-item>
      <el-form-item>
        <el-button style="width: 120px" type="primary" @click="submitForm('form')">保存</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { editSysPlatform } from '@/api/commonPack/platManege'
import { convertToCamelCase, convertToSnakeCase } from '@/utils'
export default {
  name: 'SecuritySettings',
  props: {
    platSettingsInfo: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      form: {
        divisionCode: '',
        divisionName: '',
        orgCode: '',
        creditCode: ''
      }
    }
  },
  watch: {
    platSettingsInfo: {
      handler(val) {
        if (!val) return
        this.form = val
      },
      deep: true,
      immediate: true
    }
  },

  mounted() {},

  methods: {
    submitForm(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          editSysPlatform(convertToSnakeCase(this.form)).then(res => {
            if (res.meta.code === '200') {
              /* this.$store.dispatch('settings/changeSetting').then(() => {
                }) */
              this.$message({
                message: '保存成功',
                type: 'success'
              })
              // window.location.reload()
            }
          }).catch(() => {
          })
        } else {
          return false
        }
      })
    }

  }
}
</script>

<style lang='scss' scoped>
</style>
