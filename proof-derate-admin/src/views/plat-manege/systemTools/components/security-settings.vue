<template>
  <!-- 安全设置 -->
  <div style="width: 40%">
    <el-form ref="form" label-position="top" label-width="180px" :model="form" :rules="rules">
      <el-form-item label="密码有效天数" prop="valid_days">
        <el-input v-model="form.valid_days" maxlength="4" @input="changeValue">
          <template slot="append">天</template>
        </el-input>
      </el-form-item>
      <el-form-item label="超时退出登录设置" prop="login_time_out">
        <el-input v-model.number="form.login_time_out">
          <template slot="append">分钟</template>
        </el-input>
      </el-form-item>
      <el-form-item label="退出登录路径配置" prop="logout_redirect_url">
        <el-input v-model.number="form.logout_redirect_url" />
      </el-form-item>
      <el-form-item label="客户端登录限制" prop="has_single_client">
        <el-radio-group v-model="form.has_single_client">
          <el-radio :label="true">单客户端</el-radio>
          <el-radio :label="false">多客户端</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item v-if="!form.has_single_client" label="多客户端最大连接数" prop="client_max_size">
        <el-input v-model.number="form.client_max_size" placeholder="请填写数字，最大值不超过20">
          <template slot="append">人</template>
        </el-input>
      </el-form-item>
      <el-form-item>
        <el-button style="width: 120px" type="primary" @click="submitForm('form')">保存</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { editPasswordValidate } from '@/api/commonPack/platManege'
export default {
  name: 'SecuritySettings',
  props: {
    id: {
      type: String,
      default: ''
    },
    day: {
      type: Number,
      default: 0
    },
    login_time_out: {
      type: Number,
      default: 0
    },
    logout_redirect_url: {
      type: String,
      default: ''
    },
    systemData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    const validateMaxSize = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请输入多客户端最大连接数'))
      } else if (value > 20) {
        callback(new Error('最大值不超过20'))
      } else if (!Number(value)) {
        callback(new Error('必须为数字值'))
      } else {
        callback()
      }
      /*   else if (!validPassword(value)) {
        callback(new Error('密码必须包括数字、大写字母、小写字母、特殊字符中的3种字符并且密码长度为8至16位'))
      }  */
    }
    return {
      form: {
        id: '',
        valid_days: '',
        login_time_out: '',
        logout_redirect_url: '',
        has_single_client: 'true',
        client_max_size: ''
      },
      rules: {
        valid_days: [{ required: true, message: '请输入密码有效天数', trigger: 'change' }],
        login_time_out: [
          { required: true, message: '请输入超时退出登录设置分钟', trigger: 'change' },
          { type: 'number', message: '分钟数必须为数字值' }
        ],
        client_max_size: [{ required: true, trigger: 'blur', validator: validateMaxSize }]
      }
    }
  },
  watch: {
    systemData: {
      handler(val) {
        if (!val) return
        this.form = val
      },
      deep: true,
      immediate: true
    }
    /* id: function(newVal, oldVal) {
      this.form.id = newVal
    },

    day: function(newVal, oldVal) {
      this.form.valid_days = newVal
    },
    login_time_out: function(newVal, oldVal) {
      // console.log('newVal', newVal)
      this.form.login_time_out = newVal
    },
    logout_redirect_url: function(newVal, oldVal) {
      this.form.logout_redirect_url = newVal
    } */
  },

  mounted() {
  },

  methods: {
    submitForm(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          editPasswordValidate(this.form)
            .then(res => {
              console.log('编辑有效期成功', res)
              if (res.meta.code === '200') {
                this.$store.dispatch('settings/changeSetting').then(() => {
                })
                this.$message({
                  message: '保存成功',
                  type: 'success'
                })
                window.location.reload()
              }
            })
            .catch(err => {
              console.log('编辑有效期失败', err)
            })
        } else {
          return false
        }
      })
    },

    changeValue(value) {
      this.form.valid_days = /^[0-9]*$/.test(parseInt(value)) ? String(parseInt(value)).replace('.', '') : ''
    }
  }
}
</script>

<style lang='scss' scoped>
</style>
