<template>
  <!-- 皮肤设置 -->
  <div style="width: 50%">
    <el-form label-position="top" :model="form" ref="form">
      <el-form-item label="系统皮肤" prop="color">
        <div class="block-wrap">
          <div class="blue" @click="handleClick(0)">
            <img v-if="show_1" class="image-icon" src="~@/assets/commonPack_images/selected-icon.png" alt />
          </div>
          <div class="lightblue" @click="handleClick(3)">
            <img v-if="show_4" class="image-icon" src="~@/assets/commonPack_images/selected-icon.png" alt />
          </div>
          <div class="deep-blue" @click="handleClick(1)">
            <img v-if="show_2" class="image-icon" src="~@/assets/commonPack_images/selected-icon.png" alt />
          </div>
          <div class="green" @click="handleClick(2)">
            <img v-if="show_3" class="image-icon" src="~@/assets/commonPack_images/selected-icon.png" alt />
          </div>
        </div>
      </el-form-item>
      <el-form-item style="margin-top: 40px">
        <el-button style="width: 120px" type="primary" @click="submitForm('form')">保存</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { editSystemColor } from '@/api/commonPack/platManege'
import { setSkinColor } from '@/utils/setSkin'
export default {
  props: {
    id: {
      type: String,
      default: ''
    },
    color: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      form: {
        id: '',
        color: ''
      },
      rules: {
        color: [{ required: true, message: '请选择主题色', trigger: 'blur' }]
      },
      show_1: true,
      show_2: false,
      show_3: false,
      show_4: false,
      lastStatus: 0,
      colorList: ['#1772e5', '#0747a6', '#1f9e73', '#4293f4']
    }
  },

  watch: {
    id: function (newVal, oldVal) {
      this.form.id = newVal
    },

    color: {
      handler: function (newVal, oldVal) {
        this.form.color = newVal
        let index = this.colorList.findIndex(function (item) {
          return item == newVal
        })
        if (index === 0) {
          this.show_1 = true
          this.show_2 = false
          this.show_3 = false
          this.show_4 = false
          this.lastStatus = index
        }
        if (index === 1) {
          this.show_1 = false
          this.show_2 = true
          this.show_3 = false
          this.show_4 = false
          this.lastStatus = index
        } else if (index === 2) {
          this.show_1 = false
          this.show_2 = false
          this.show_3 = true
          this.show_4 = false
          this.lastStatus = index
        } else if (index === 3) {
          this.show_1 = false
          this.show_2 = false
          this.show_3 = false
          this.show_4 = true
          this.lastStatus = index
        }
      },
      immediate: true
    }
  },

  methods: {
    handleClick(e) {
      console.log(e)
      if (e != this.lastStatus) {
        switch (e) {
          case 0:
            this.show_1 = true
            this.show_2 = false
            this.show_3 = false
            this.show_4 = false
            this.lastStatus = e
            this.form.color = this.colorList[e]
            break
          case 1:
            this.show_1 = false
            this.show_2 = true
            this.show_3 = false
            this.show_4 = false
            this.lastStatus = e
            this.form.color = this.colorList[e]
            break
          case 2:
            this.show_1 = false
            this.show_2 = false
            this.show_3 = true
            this.show_4 = false
            this.lastStatus = e
            this.form.color = this.colorList[e]
            break
          case 3:
            this.show_1 = false
            this.show_2 = false
            this.show_3 = false
            this.show_4 = true
            this.lastStatus = e
            this.form.color = this.colorList[e]
            break
        }
      }
    },
    submitForm(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          editSystemColor(this.form)
            .then(res => {
              console.log('编辑成功', res)
              if ('200' === res.meta.code) {
                this.$message({
                  message: '保存成功',
                  type: 'success'
                })
                setSkinColor(this.form.color)
                window.location.reload()
              }
            })
            .catch(err => {
              console.log('编辑失败', err)
            })
        } else {
          return false
        }
      })
    }
  }
}
</script>

<style lang='scss' scoped>
.block-wrap {
  display: flex;
  align-items: center;
}
.blue {
  width: 80px;
  height: 80px;
  background: #1772e5;
  border-radius: 4px;
  text-align: center;
}
.lightblue {
  width: 80px;
  height: 80px;
  background: #4293f4;
  border-radius: 4px;
  margin-left: 16px;
  text-align: center;
}
.deep-blue {
  width: 80px;
  height: 80px;
  background: #0747a6;
  border-radius: 4px;
  margin-left: 16px;
  text-align: center;
}
.green {
  width: 80px;
  height: 80px;
  background: #1f9e73;
  border-radius: 4px;
  margin-left: 16px;
  text-align: center;
}
.image-icon {
  margin-top: 28px;
  width: 24px;
}
</style>