<template>
  <!-- 系统设置 -->
  <div>
    <div style="width: 70%">
      <el-form ref="form" label-position="top" label-width="180px" :model="form" :rules="rules">
        <el-form-item class="custom-item" label="系统名称" prop="name">
          <el-input v-model="form.name" maxlength="30" show-word-limit />
        </el-form-item>
        <el-form-item class="custom-item" label="系统图标" prop="icon">
          <el-popover
            placement="bottom-start"
            width="550"
            trigger="click"
            popper-class="popper-class"
            @show="$refs.iconSelect.reset()"
          >
            <icon-select ref="iconSelect" :name="form.icon" @selected="handleSelected" />
            <el-input slot="reference" v-model="form.icon" placeholder="请输入内容" readonly style="cursor: pointer">
              <template slot="prepend">
                <div :style="{ padding: '3px 20px', background: `${$store.state.settings.skinColor}` }">
                  <svg-icon :icon-class="form.icon" :style="{ height: '30px', width: '16px' }" />
                </div>
              </template>
            </el-input>
          </el-popover>

          <!-- <el-select v-model="form.icon" placeholder="请选择" @change="handleSelectChange">
          <template slot="prefix"><div style="padding: 5px;line-height: 33px;font-size: 18px;"><svg-icon :icon-class="form.icon"/></div></template>
          <el-option
            v-for="item in iconsList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
            <span style="float: left">
              <svg-icon :icon-class="item.label" />
              <span style="margin-left:6px">{{item.value}}</span>
            </span>
          </el-option>
        </el-select>-->
        </el-form-item>
        <el-form-item class="custom-item" label="网页标题" prop="title">
          <el-input v-model="form.title" class="pr-input" maxlength="30" show-word-limit />
        </el-form-item>
        <el-form-item label="导航菜单设置" prop="navigate_layout">
          <el-radio-group v-model="form.navigate_layout">
            <el-radio label="TOP">顶栏分布</el-radio>
            <el-radio label="LEFT">侧栏分布</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item class="custom-item" label="网页图标" prop="web_icon">
          <el-popover
            placement="bottom-start"
            width="550"
            trigger="click"
            popper-class="popper-class"
            @show="$refs.iconSelect.reset()"
          >
            <icon-select ref="iconSelect" :name="form.web_icon" @selected="handleSelectedWebIcon" />
            <el-input slot="reference" v-model="form.web_icon" placeholder="请输入内容" readonly style="cursor: pointer">
              <template slot="prepend">
                <div :style="{ padding: '3px 20px', background: `${$store.state.settings.skinColor}` }">
                  <svg-icon :icon-class="form.web_icon" :style="{ height: '30px', width: '16px' }" />
                </div>
              </template>
            </el-input>
          </el-popover>
        </el-form-item>
        <el-form-item ref="upload" label="登录背景图" prop="attachments">
          <div style="display: flex; align-items: center">
            <el-upload
              ref="uploadItem"
              v-model="form.attachments"
              :class="{ disabled: uploadDisabled }"
              action
              :auto-upload="false"
              list-type="picture-card"
              :on-preview="handlePictureCardPreview"
              :on-remove="handleRemove"
              :on-change="handleChange"
              :on-exceed="handleExceed"
              :file-list="fileList"
              accept=".jpg, .png, .JPG, .PNG"
              :limit="1"
            >
              <i class="el-icon-plus" />
            </el-upload>
            <!-- <el-upload
            class="avatar-uploader"
            style="border: 1px dashed #d9d9d9; border-radius: 6px; overflow: hidden;"
            action="#"
            :auto-upload="false"
            :show-file-list="false"
            :on-change="handleChange"
            :on-preview="handlePictureCardPreview"
            v-model="form.attachments"
          >
            <img v-if="imageUrl" :src="imageUrl" class="avatar" />
            <i v-else class="el-icon-plus avatar-uploader-icon"></i>
          </el-upload>-->
          </div>
          <el-dialog :visible.sync="dialogVisible">
            <img width="100%" :src="dialogImageUrl" alt>
          </el-dialog>
        </el-form-item>
        <el-row>
          <el-col>
            <div class="upload_tips">
              <img src="~@/assets/images/exclamation.png" class="img_icon" alt>
              <div class="tips_text_gray">图片不大于1M，尺寸为1920x1080像素，格式为png。</div>
            </div>
          </el-col>
        </el-row>
        <div style="position: relative;">
          <div v-if="form.login_button_req_data.length < 2" class="add-button" @click="handleAddButton"><el-button
            type="text"
          >添加</el-button></div>
          <el-form-item prop="login_button_req_data" label="第三方登录按钮">
            <el-table :data="form.login_button_req_data" border style="width: 100%">
              <el-table-column prop="button_name" label="按钮名称" />
              <el-table-column prop="button_icon" label="按钮图标">
                <template slot-scope="scope">
                  <img
                    v-if="scope.row.button_icon"
                    class="icon-wrap"
                    :src="`data:image/jpeg;base64,${scope.row.button_icon}`"
                    alt=""
                  >
                  <span v-else>—</span>
                </template>
              </el-table-column>
              <el-table-column prop="jump_address" min-width="360" label="跳转地址" />
              <el-table-column label="操作" width="160">
                <template slot-scope="scope">
                  <span><el-button type="text" @click="editData(scope.$index)">编辑</el-button></span>
                  <span
                    style="color:#FF2B2B;cursor: pointer; margin-left: 10px;"
                    @click="deleteData(scope.$index)"
                  >删除</span>
                  <!-- <span v-else>——</span> -->
                </template>
              </el-table-column>
            </el-table>
          </el-form-item>
        </div>

        <el-form-item>
          <el-button style="width: 120px" type="primary" @click="submitForm('form')">保存</el-button>
        </el-form-item>
      </el-form>

    </div>
    <!-- 新增弹窗-->
    <el-dialog :visible.sync="addDialogVisible" width="30%" title="添加" top="30vh" @close="handleAddClose">
      <el-form ref="addForm" :model="addForm" :rules="addRules" label-width="80px" @submit.native.prevent>
        <el-form-item label="按钮名称" prop="button_name">
          <el-input v-model="addForm.button_name" maxlength="20" clearable placeholder="请输入" />
        </el-form-item>

        <el-form-item label="跳转地址" prop="jump_address">
          <el-input v-model="addForm.jump_address" clearable placeholder="请输入" />
        </el-form-item>

        <el-form-item label="按钮图标" prop="button_icon">
          <el-upload
            class="upload-demo"
            :auto-upload="false"
            action
            :on-remove="handleIconRemove"
            multiple
            :limit="1"
            accept=".jpg, .png, .JPG, .PNG"
            :file-list="iconFileList"
            :on-change="handleIconChange"
          >
            <el-button v-if="iconFileList.length === 0" size="small" type="primary">点击上传</el-button>
            <!-- <div slot="tip" class="el-upload__tip">图片不大于1M，尺寸为40x40像素，格式为png。</div> -->
          </el-upload>
          <el-row>
            <el-col>
              <div class="upload_tips">
                <img src="~@/assets/images/exclamation.png" class="img_icon" alt>
                <div class="tips_text_gray">图片不大于1M，尺寸为40x40像素，格式为png。</div>
              </div>
            </el-col>
          </el-row>
        </el-form-item>
      </el-form>
      <div class="dialog-footer">
        <el-button type="primary" @click="addConfirm('addForm')">确 定</el-button>
        <el-button @click="addCancel('addForm')">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 编辑弹窗 -->
    <el-dialog :visible.sync="editDialogVisible" width="30%" title="编辑" top="30vh" @close="handleEditClose">
      <el-form ref="editForm" :model="editForm" :rules="addRules" label-width="80px" @submit.native.prevent>
        <el-form-item label="按钮名称" prop="button_name">
          <el-input v-model="editForm.button_name" maxlength="20" clearable placeholder="请输入" />
        </el-form-item>

        <el-form-item label="跳转地址" prop="jump_address">
          <el-input v-model="editForm.jump_address" clearable placeholder="请输入" />
        </el-form-item>

        <el-form-item label="按钮图标" prop="button_icon">
          <el-upload
            class="upload-demo"
            :auto-upload="false"
            action
            :on-remove="handleIconRemove"
            multiple
            :limit="1"
            accept=".png, .PNG"
            :file-list="iconFileList"
            :on-change="handleIconEditChange"
          >
            <el-button v-if="iconFileList.length === 0" size="small" type="primary">点击上传</el-button>
          </el-upload>
          <el-row>
            <el-col>
              <div class="upload_tips">
                <img src="~@/assets/images/exclamation.png" class="img_icon" alt>
                <div class="tips_text_gray">图片不大于1M，尺寸为40x40像素，格式为png。</div>
              </div>
            </el-col>
          </el-row>
        </el-form-item>
      </el-form>
      <div class="dialog-footer">
        <el-button type="primary" @click="editConfirm('editForm')">确 定</el-button>
        <el-button @click="editCancel('editForm')">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 删除提示 -->
    <el-dialog :visible.sync="dialogDelVisible" width="30%" title="提示" top="30vh">
      <div style="padding-bottom:20px">是否删除该登录按钮？删除后将无法查看。</div>
      <div class="dialog-footer">
        <el-button type="primary" @click="deleteSubmit">确 定</el-button>
        <el-button @click="deleteCancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import IconSelect from '@/components/IconSelect/index.vue'
import { editSystemSettings } from '@/api/commonPack/platManege'
import icons from '@/components/IconSelect/requireIcons'
import { setIcon } from '@/utils/setfavicon'
// import icons from '@/utils/svgIcons'
export default {
  components: { IconSelect },
  props: {
    id: {
      type: String,
      default: ''
    },
    name: {
      type: String,
      default: ''
    },
    icon: {
      type: String,
      default: ''
    },
    background: {
      type: String,
      default: ''
    },
    navigate_layout: {
      type: String,
      default: ''
    },
    web_icon: {
      type: String,
      default: ''
    },
    third_button: {
      type: Array,
      default: []
    },
    title: {
      type: String,
      default: ''
    }
  },
  data() {
    var hasAttachment = (rule, value, callback) => {
      if (value) {
        callback()
      } else {
        callback(new Error('请上传附件'))
      }
    }
    return {
      form: {
        name: '',
        icon: 'logo',
        attachments: '',
        navigate_layout: '',
        web_icon: 'logo',
        login_button_req_data: [],
        title: ''
      },

      rules: {
        name: [{ required: true, message: '请输入系统名称', trigger: 'blur' }],
        icon: [{ required: true, message: '请选择系统图标', trigger: 'blur' }],
        title: [{ required: true, message: '请输入网页标题', trigger: 'blur' }],
        attachments: [
          {
            validator: hasAttachment,
            message: '请上传登录背景图',
            trigger: 'change'
          }
        ]
      },
      iconsList: [],
      dialogImageUrl: '',
      dialogVisible: false,
      fileList: [],
      uploadFile: '',
      imageUrl: '',
      uploadDisabled: false,
      addDialogVisible: false,
      addForm: {
        button_name: '',
        button_icon: '',
        jump_address: ''
      },
      addRules: {
        button_name: [{ required: true, message: '请输入按钮名称', trigger: 'change' }],
        jump_address: [{ required: true, message: '请输入跳转地址', trigger: 'change' }]
      },
      editForm: {
        button_name: '',
        button_icon: '',
        jump_address: ''
      },
      dialogDelVisible: false,
      selectIndex: 0, // 选择的条目
      iconFileList: [],
      editDialogVisible: false
    }
  },

  watch: {
    id: function(newVal, oldVal) {
      this.form.id = newVal
    },

    name: function(newVal, oldVal) {
      this.form.name = newVal
    },

    icon: function(newVal, oldVal) {
      this.form.icon = newVal
    },
    navigate_layout: function(newVal, oldVal) {
      this.form.navigate_layout = newVal
    },
    web_icon: function(newVal, oldVal) {
      this.form.web_icon = newVal
    },
    background: function(newVal, oldVal) {
      const url = this.getBase64Url('data:image/png;base64,' + newVal)
      const file = this.base64ImgtoFile('data:image/png;base64,' + newVal, 'bg')
      this.uploadFile = file
      this.fileList = [{ name: 'bg.jpg', url: url }]
      this.uploadDisabled = this.fileList.length >= 1
      this.form.attachments = newVal
    },
    third_button: function(newVal, oldVal) {
      this.form.login_button_req_data = newVal
    },
    title: function(newVal, oldVal) {
      this.form.title = newVal
    }
  },

  mounted() {
    this.iconsList = icons
  },

  methods: {
    submitForm(formName) {
      console.log('提交的数据', this.form)
      this.$refs[formName].validate(valid => {
        if (valid) {
          const params = {
            id: this.form.id,
            name: this.form.name,
            icon: this.form.icon,
            title: this.form.title,
            navigate_layout: this.form.navigate_layout,
            web_icon: this.form.web_icon,
            login_button_req_data: this.form.login_button_req_data
          }
          // const formData = new FormData()
          // formData.append('file', this.uploadFile)
          console.log('params', params)
          const uploadFile = this.uploadFile
          this.getBase64(uploadFile).then((res) => {
            // 转化后的base64
            const background = res.replace(/^data:image\/\w+;base64,/, '')
            params.background = background
            editSystemSettings(params)
              .then(res => {
                console.log('编辑成功', res)
                setIcon(this.form.web_icon)
                if (res.meta.code === '200') {
                  this.$message({
                    message: '保存成功',
                    type: 'success'
                  })
                  this.$store.dispatch('settings/changeSetting').then(() => {
                    this.$vm.$emit('setNavigateLayout')
                  })
                  this.$store.dispatch('settings/bgImgSetting').then(res => { })
                  window.location.reload()
                  // setTimeout(res => {
                  //   sessionStorage.removeItem('systemInfo')
                  //   window.location.reload()
                  // }, 200)
                }
              })
              .catch(err => {
                console.log('编辑失败', err)
              })
          })
        } else {
          return false
        }
      })
    },
    handleSelected(e) {
      this.form.icon = e
    },
    handleSelectedWebIcon(e) {
      this.form.web_icon = e
    },
    handleSelectChange(e) { },
    handleRemove(file) {
      console.log('移除', file)
      this.form.attachments = ''
      this.uploadFile = ''
      this.uploadDisabled = false
    },
    handlePictureCardPreview(file) {
      console.log('执行', file)
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },

    handleExceed(files, fileList) {
      this.$message.warning(`当前限制上传1张背景图，若要重新上传，请先删除之前上传的背景图，然后再上传`)
    },

    // 上传监听事件
    handleChange(file, fileList) {
      console.log('file', file)
      const url = URL.createObjectURL(file.raw)
      // this.imageUrl = url;
      if (fileList.length > 0) {
        this.$refs['upload'].clearValidate()
      }
      const isExcced = file.size / 1024 / 1024 < 1
      if (!isExcced) {
        this.$message.error('上传图片大小不能超过 1MB!')
        return
      }
      this.form.attachments = file
      this.uploadFile = file.raw
      this.uploadDisabled = true
      this.getBase64(file.raw).then((res) => {
        // 转化后的base64
        console.log('转换后的base64', res)
        // this.formData.attachments.push({
        //   file_name: file.uid,
        //   file_type: ".jpg",
        //   file_data: res.replace(/^data:image\/\w+;base64,/, ""),
        // });
      })
    },

    /**
     * 图片转换成base64
     */
    getBase64(file) {
      return new Promise(function(resolve, reject) {
        const reader = new FileReader()
        let imgResult = ''
        reader.readAsDataURL(file)
        reader.onload = function() {
          imgResult = reader.result
        }
        reader.onerror = function(error) {
          reject(error)
        }
        reader.onloadend = function() {
          resolve(imgResult)
        }
      })
    },

    getBase64Url(base64) {
      const blob = this.base64ImgtoFile(base64, 'bg')
      const blobUrl = window.URL.createObjectURL(blob)
      return blobUrl
    },

    base64ImgtoFile(dataurl, filename) {
      // 将base64格式分割：['data:image/png;base64','XXXX']
      const arr = dataurl.split(',')
      // .*？ 表示匹配任意字符到下一个符合条件的字符 刚好匹配到：
      // image/png
      const mime = arr[0].match(/:(.*?);/)[1] // image/png
      // [image,png] 获取图片类型后缀
      const suffix = mime.split('/')[1] // png
      const bstr = atob(arr[1]) // atob() 方法用于解码使用 base-64 编码的字符串
      let n = bstr.length
      const u8arr = new Uint8Array(n)
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n)
      }
      return new File([u8arr], `${filename}.${suffix}`, {
        type: mime
      })
    },

    /**
     * 添加第三方按钮点击事件
     */
    handleAddButton() {
      this.title = '添加'
      this.addDialogVisible = true
    },

    /**
     * 添加取消
     */
    addCancel(formName) {
      this.iconFileList = []
      this.$refs[formName].resetFields()
      this.addDialogVisible = false
    },

    handleAddClose() {
      this.iconFileList = []
      this.$refs['addForm'].resetFields()
    },

    handleEditClose() {
      console.log('编辑弹窗关闭', this.editForm)
      this.iconFileList = []
      this.$refs['editForm'].resetFields()
    },

    /**
     * 添加确定
     */
    addConfirm(formName) {
      const _this = this
      this.$refs[formName].validate(valid => {
        if (valid) {
          const copyData = JSON.parse(JSON.stringify(_this.addForm))
          console.log('copyData', copyData)
          _this.form.login_button_req_data.push(copyData)
          this.addDialogVisible = false
          this.iconFileList = []
        } else {
          return false
        }
      })
    },

    /**
     * 编辑取消
     */
    editCancel(formName) {
      console.log('编辑取消', this.editForm)
      console.log('编辑取消表单数据', this.form.login_button_req_data)
      this.$refs[formName].resetFields()
      this.editDialogVisible = false
    },

    /**
     * 编辑确定
     * @param {*} formName
     */
    editConfirm(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          const copyData = JSON.parse(JSON.stringify(this.editForm))
          console.log('copyData', copyData)
          this.$set(this.form.login_button_req_data, this.selectIndex, copyData)
          console.log('点击确定后的按钮数据', this.form.login_button_req_data)
          this.editDialogVisible = false
          this.iconFileList = []
        } else {
          return false
        }
      })
    },

    /**
     * 删除数据
     */
    deleteData(index) {
      this.dialogDelVisible = true
      this.selectIndex = index
    },

    /**
     * 确定删除
     */
    deleteSubmit() {
      this.dialogDelVisible = false
      this.form.login_button_req_data.splice(this.selectIndex, 1)
    },
    /**
     * 取消删除
     */
    deleteCancel() {
      this.dialogDelVisible = false
    },

    /**
     * 编辑数据
     * @param {*} index
     */
    editData(index) {
      console.log('当前索引', index)
      const selectData = JSON.parse(JSON.stringify(this.form.login_button_req_data[index]))
      console.log('当前选中的数据', selectData)
      this.editForm = selectData
      console.log('editForm', this.editForm)
      this.selectIndex = index
      if (this.editForm.button_icon) {
        const url = this.getBase64Url('data:image/png;base64,' + this.editForm.button_icon)
        this.iconFileList = [{ name: 'icon.png', url: url }]
      }
      this.editDialogVisible = true
    },

    /**
     *
     * @param {*} file
     * @param {*} fileList
     */
    handleIconRemove(file, fileList) {
      console.log('file', file)
      this.iconFileList = []
      this.addForm.button_icon = ''
      this.editForm.button_icon = ''
      console.log('按钮数据', this.form.login_button_req_data)
    },

    /**
     * 图标改变事件--新增时
     * @param {*} file
     * @param {*} fileList
     */
    handleIconChange(file, fileList) {
      console.log('handleIconChange', file)
      const _this = this
      const fileSuffix = file.name.substring(file.name.lastIndexOf('.') + 1)
      const whiteList = ['PNG', 'png']
      const isLt1M = file.size / 1024 / 1024 > 1
      const isSuffix = whiteList.indexOf(fileSuffix) === -1
      if (isSuffix) {
        this.$message.error('只能上传png格式的图片')
        /* 验证不通过删除此文件 */
        const currIdx = this.iconFileList.indexOf(file)
        this.iconFileList.splice(currIdx, 1)
        return
      }
      if (isLt1M) {
        this.$message.error('图片大小超过1M')
        const currIdx = this.iconFileList.indexOf(file)
        this.iconFileList.splice(currIdx, 1)
        return
      }
      _this.checkFile(file).then((res) => {
        _this.iconFileList = fileList
        _this.getBase64(file.raw).then((res) => {
          // 转化后的base64
          console.log('转换后的base64', res)
          const base64 = res.replace(/^data:image\/\w+;base64,/, '')
          _this.addForm.button_icon = base64
        })
      }).catch((err) => {
        _this.iconFileList = fileList
        this.$message.error('上传的图片尺寸必须为40px*40px')
      })
    },

    /**
     *
     * @param {*} file
     * @param {*} fileList
     */
    handleIconEditChange(file, fileList) {
      console.log('handleIconEditChange', file)
      const _this = this
      const fileSuffix = file.name.substring(file.name.lastIndexOf('.') + 1)
      const whiteList = ['PNG', 'png']
      const isLt1M = file.size / 1024 / 1024 > 1
      const isSuffix = whiteList.indexOf(fileSuffix) === -1
      if (isSuffix) {
        this.$message.error('只能上传png格式的图片')
        /* 验证不通过删除此文件 */
        const currIdx = this.iconFileList.indexOf(file)
        this.iconFileList.splice(currIdx, 1)
        return
      }
      if (isLt1M) {
        this.$message.error('图片大小超过1M')
        const currIdx = this.iconFileList.indexOf(file)
        this.iconFileList.splice(currIdx, 1)
        return
      }
      _this.checkFile(file).then((res) => {
        console.log('调用成功', res)
        _this.iconFileList = fileList
        _this.getBase64(file.raw).then((res) => {
          // 转化后的base64
          console.log('handleIconEditChange转换后的base64', res)
          const base64 = res.replace(/^data:image\/\w+;base64,/, '')
          _this.editForm.button_icon = base64
        })
      }).catch((err) => {
        console.log('调用失败', err)
        _this.iconFileList = fileList
        this.$message.error('上传的图片尺寸必须为40px*40px')
        return
      })
    },

    /**
     * 检查文件是不是40px*40px的
     * @param {*} file
     */
    checkFile(file) {
      return new Promise((resolve, reject) => {
        const reader = new FileReader()
        reader.onload = (event) => {
          const img = new Image()
          img.src = event.target.result
          img.onload = function() {
            if (img.width === 40 && img.height === 40) {
              resolve()
            } else {
              reject(new Error('上传的图片尺寸必须为40px*40px'))
            }
          }
          img.onerror = function() {
            reject(new Error('无法读取图片'))
          }
        }
        reader.onerror = () => {
          reject(new Error('无法读取文件'))
        }
        reader.readAsDataURL(file.raw)
      })
    }
  }
}
</script>

<style lang='scss' scoped>
.el-select {
  width: 100%;
}

.upload_tips {
  display: flex;
  align-items: center;
  margin-top: 10px;
  margin-bottom: 20px;
}

.img_icon {
  width: 20px;
}

.tips_text_gray {
  margin-left: 6px;
  font-size: 14px;
  font-weight: 500;
  color: #9199a5;
}

.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}

.custom-item {
  width: 40%;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}

.avatar {
  width: 178px;
  height: 178px;
  display: block;
}

::v-deep .el-form-item__content {
  line-height: 0;
}

::v-deep .disabled .el-upload--picture-card {
  display: none !important;
}

::v-deep .el-input-group__prepend {
  padding: 0;
}

::v-deep .el-upload-list--picture-card .el-upload-list__item {
  width: 240px;
}

::v-deep .el-upload-list__item:first-child {
  margin-top: 4px;
}

.add-button {
  position: absolute;
  top: 0;
  right: 0;
}

.dialog-footer {
  text-align: center;
}

.el-upload__tip {
  line-height: 16px;
}

.icon-wrap {
  width: 32px;
  height: 32px;
  vertical-align: middle;
}
.pr-input ::v-deep input {
  padding-right: 45px;
}
</style>
