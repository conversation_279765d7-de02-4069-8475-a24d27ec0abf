<template>
  <div id="systemTools">
    <el-card class="department-box">
      <div class="systemTools-edit-table">
        <div class="systemTools-edit-table-bar">
          <div class="systemTools-edit-table-bar-item">
            <img src="~@/assets/commonPack_images/systemtools.png" alt>
            <span>系统设置</span>
          </div>
          <span />
        </div>
        <el-divider />
      </div>

      <div class="custom-card">
        <el-tabs v-model="activeName" class="custom-tab" tab-position="left" style="height: 120vh;" @tab-click="tabclik()">
          <el-tab-pane label="安全设置" name="first">
            <div class="tab-content">
              <security-settings :id="id" :system-data="systemData" :day="validateDay" :login_time_out="login_time_out" :logout_redirect_url="logout_redirect_url" />
            </div>
          </el-tab-pane>
          <el-tab-pane label="皮肤设置" name="second">
            <div class="tab-content">
              <skin-settings :id="id" :color="color" />
            </div>
          </el-tab-pane>
          <el-tab-pane label="系统设置" name="third">
            <div class="tab-content">
              <system-settings
                :id="id"
                :name="name"
                :icon="logo"
                :background="background"
                :navigate_layout="navigate_layout"
                :web_icon="web_icon"
                :third_button="login_button_req_data"
                :title="title"
              />
            </div>
          </el-tab-pane>
          <el-tab-pane label="平台配置" name="plat">
            <div class="tab-content">
              <plat-settings :plat-settings-info="platSettingsInfo" />
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-card>
  </div>
</template>
<script>
import { getSystemSettings } from '@/api/commonPack/platManege'
import { getbgImg } from '@/api/user'
export default {
  components: {
    SecuritySettings: () => import('./components/security-settings.vue'),
    SkinSettings: () => import('./components/skin-settings.vue'),
    SystemSettings: () => import('./components/system-settings.vue'),
    PlatSettings: () => import('./components/plat-settings.vue')
  },

  data() {
    return {
      activeName: 'first',
      systemData: {},
      id: '',
      background: '',
      color: '',
      validateDay: 0,
      login_time_out: 0,
      logout_redirect_url: '',
      background: '',
      name: '',
      logo: '',
      navigate_layout: '',
      web_icon: '',
      login_button_req_data: [],
      title: '',
      platSettingsInfo: {}
    }
  },

  created() {
    if (this.$route.query.activeName) {
      this.activeName = this.$route.query.activeName
    } else {
      this.activeName = 'first'
    }
    this.getSettings()
    this.getBackgroundImage()
  },

  methods: {
    getSettings() {
      getSystemSettings()
        .then(res => {
          if (res.data) {
            const { data } = res
            this.systemData = data
            this.id = data.id
            this.validateDay = data.valid_days
            this.login_time_out = data.login_time_out
            this.logout_redirect_url = data.logout_redirect_url
            this.color = data.color
            this.logo = data.icon
            this.name = data.name
            this.navigate_layout = data.navigate_layout
            this.web_icon = data.web_icon
            this.title = data.title
            const loginButtonString = data.login_button_json_str
            if (loginButtonString) {
              this.login_button_req_data = JSON.parse(loginButtonString)
            } else {
              this.login_button_req_data = []
            }
            this.platSettingsInfo = {
              id: data.id,
              divisionCode: data.division_code,
              divisionName: data.division_name,
              orgCode: data.org_code,
              creditCode: data.credit_code
            }
            console.log(this.platSettingsInfo, 34)
          }
        })
        .catch(err => {
          console.log('获取设置失败', err)
        })
    },

    /**
     * 获取背景图
     */
    getBackgroundImage() {
      getbgImg().then((res) => {
        if (res.data) {
          this.background = res.data
        }
      }).catch((err) => {
      })
    },
    tabclik() {
      console.log('activeName', this.activeName)
      this.$router.push({ name: 'systemToolsList', query: { activeName: this.activeName }})
    }
  }
}
</script>
<style lang="scss" scoped>
#systemTools {
  padding: 10px;
}
.systemTools-edit-table {
  margin-bottom: 20px;
  &-bar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    //   margin-top: 51px;
    &-item {
      display: flex;
      align-items: flex-end;
      color: #333333;
      font-size: 18px;
      font-weight: 500;
    }
    img {
      width: 22px;
      height: 22px;
      margin-right: 5px;
    }
    span {
      color: #333333;
    }
  }
}

.systemTools-edit-table ::v-deep .el-divider--horizontal {
  margin: 9px 0 21px;
}
.custom-card {
  border: 1px solid #ebeef5;
  background-color: #fff;
  overflow: hidden;
  color: #303133;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

::v-deep .el-tabs--left .el-tabs__header.is-left {
  padding-top: 16px;
  width: 140px;
  background-color: #f7f8fa;
  border-right: 1px solid #e8e8e8;
}
::v-deep .el-tabs--left .el-tabs__item.is-left {
  text-align: center;
}
::v-deep .el-tabs--left .el-tabs__nav-wrap.is-left::after {
  width: 100%;
}
::v-deep .el-tabs__nav-wrap::after {
  content: none;
  clear: both;
}
::v-deep .el-tabs--left .el-tabs__active-bar.is-left {
  right: auto;
}
::v-deep .el-tabs__item {
  &.is-active {
    background: #ffffff!important;
  }
}
::v-deep .el-tabs--card {
  height: calc(120vh - 110px);
  /* overflow-y: auto; */
}
::v-deep.el-tab-pane {
  height: calc(120vh - 110px);
  overflow-y: scroll;
}
::v-deep::-webkit-scrollbar {
   width: 0 !important;
 }
 ::v-deep::-webkit-scrollbar {
   width: 0 !important;height: 0;
 }
.tab-content {
  margin-top: 26px;
  margin-left: 32px;
}
</style>
