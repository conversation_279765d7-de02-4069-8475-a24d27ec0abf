<template>
  <div class="detail-cont">
    <ul>
      <li v-for="item in detailData" :key="item.key">
        <span class="key">{{ item.key }}</span>
        <div class="value">
          <div v-if="item.type==='radio'">
            <el-radio-group v-model="item.value" disabled>
              <el-radio v-for="clounm in item.options" :key="clounm.label" :label="clounm.value">{{ clounm.label }}</el-radio>
            </el-radio-group>
          </div>
          <span v-else>{{ item.value }}</span>
        </div>
      </li>
    </ul>
  </div>
</template>

<script>
import { getOrganization } from '@/api/commonPack/platManege'
import { getOrganizationView } from '@/api/commonPack/platManege'

export default {
  props: {
    id: {
      type: String,
      default: () => {
        return ''
      }
    }
  },
  data() {
    return {
      detailData: [],
      deparmentInfo: {}
    }
  },
  watch: {
    id(value) {
      console.log('w   watch: ' + value)
      this.getOrganizationView()
      // 监听到有变化就重新获取数据
    }
  },

  mounted() {
    this.getOrganizationView()
  },

  methods: {
    getOrganizationView() {
      console.log(this.id)
      if (!this.id) return
      getOrganizationView(this.id)
        .then(res => {
          const { data } = res
          this.deparmentInfo = data
          this.detailData = [
            { key: '部门名称：', value: data.name },
            { key: '部门简称：', value: data.short_name },
            { key: '部门统一信用代码：', value: data.credit_code },
            { key: '部门所属行政区划：', value: data.division_code },
            { key: '机构地址：', value: data.address },
            { key: '机构职责：', value: data.duty },
            {
              key: '部门状态：',
              value: data.status,
              type: 'radio',
              options: [
                { label: '正常', value: 'NORMAL' },
                { label: '注销', value: 'CANCEL' }
              ]
            }
          ]
          this.$emit('getId', data.id)
          this.$emit('getPermissionCodes', data.permission_codes)
          this.getOrganization()
        })
        .catch(() => {})
    },
    getOrganization() {
      const data = { scope: true }
      getOrganization(data).then(res => {
        if (res.meta.code === '200' && res.data.length) {
          const _ = res.data.filter(item => item.credit_code === this.deparmentInfo.parent_credit_code)
          const name = _.length ? _[0].name : ''
          this.detailData.splice(4, 0, { key: '上级机构：', value: name })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.detail-cont {
  ul,
  li {
    list-style: none;
  }
  li {
    min-height: 40px;
    font-size: 14px;
    color: #888888;
    display: flex;
    padding-bottom: 5px;
  }
  .key {
    width: 150px;
    text-align: right;
    padding-right: 20px;
  }
  .value {
    max-width: 500px;
  }
}
</style>
