<template>
  <div class="edit-list-container">
    <el-form ref="form" :model="editDataForm" :rules="rules" label-width="180px">
      <el-row :gutter="24">
        <el-col :span="20">
          <el-form-item label="部门名称" prop="name">
            <el-input v-model="editDataForm.name" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="24">
        <el-col :span="20">
          <el-form-item label="部门简称" prop="short_name">
            <el-input v-model="editDataForm.short_name" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="24">
        <el-col :span="20">
          <el-form-item label="部门统一信用代码" prop="credit_code">
            <el-input v-model="editDataForm.credit_code" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="24">
        <el-col :span="20">
          <el-form-item label="部门所属行政区划" prop="division_code">
            <!-- <AdministrativeDivisionSelect
              ref="AdministrativeDivisionSelect"
              :divisionCode="divisionCode"
              :key="divisionCode"
              @setDivisionCodeAndName="setDivisionCodeAndName"
            ></AdministrativeDivisionSelect>-->
            <AdministrativeDivisionCascader
              :key="divisionCode"
              ref="AdministrativeDivisionSelect"
              :division-code="divisionCode"
              :permissionCode="'auth:organization:list'"
              @setDivisionCodeAndName="setDivisionCodeAndName"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="24">
        <el-col :span="20" class="industryOrgName">
          <el-form-item label="上级机构" prop="parent_credit_code">
            <el-select filterable  v-model="editDataForm.parent_credit_code" placeholder="请选择" class="width-100">
              <el-option v-for="item in organizationList" :key="item.credit_code" :label="item.name" :value="item.credit_code" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="24">
        <el-col :span="20">
          <el-form-item label="机构地址" prop="address">
            <el-input v-model="editDataForm.address" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="24">
        <el-col :span="20">
          <el-form-item label="机构职责">
            <el-input v-model="editDataForm.duty" type="textarea" :rows="4" placeholder="请输入内容" maxlength="200" show-word-limit />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div slot="footer" class="editDialog-footer">
      <el-button type="primary" @click="onSubmit">确 定</el-button>
      <el-button @click="onCanse">取 消</el-button>
    </div>
  </div>
</template>

<script>
import { getAlldivision, getOrganization } from '@/api/commonPack/platManege'
import { getOrganizationView, saveOrganizationCreate } from '@/api/commonPack/platManege'
let id = 0
export default {
  components: {
    AreaSelect: () => import('@/components/AreaSelect'),
    // AdministrativeDivisionSelect: () => import('@/components/AdministrativeDivisionSelect'),
    AdministrativeDivisionCascader: () => import('@/components/AdministrativeDivisionCascader')
  },
  data() {
    return {
      editDataForm: {
        name: '',
        short_name: '',
        credit_code: '',
        division_code: '',
        division_name: '',
        parent_credit_code: '',
        address: '',
        duty: ''
      },
      divisionCode: '',
      props: {
        lazy: true,
        lazyLoad(node, resolve) {
          console.log(node.data, node.level)
          const { level } = node
          setTimeout(() => {
            const nodes = Array.from({ length: level + 1 }).map(item => ({
              value: ++id,
              label: `选项${id}`,
              leaf: level >= 2
            }))
            // 通过调用resolve将子节点数据返回，通知组件数据加载完成
            resolve(nodes)
          }, 1000)
        }
      },
      rules: {
        name: [{ required: true, message: '请输入部门名称', trigger: 'blur' }],
        credit_code: [{ required: true, message: '请输入部门统一信用代码', trigger: 'blur' }],

        division_code: [{ required: true, message: '请选择部门所属行政区划', trigger: 'focus' }]
      },
      divisionTree: [],
      division: {
        province: '',
        provinceName: '',
        provinceOptions: [],
        city: '',
        cityName: '',
        cityOptions: [],
        district: '',
        districtName: '',
        districtOptions: []
      },
      organizationList: [],
      organizationDivisionCode: JSON.parse(localStorage.getItem('organization')).division_code,
      divisionCode: '',
      value: '',
      optionData: [
        {
          divisionlist: [],
          disabled: false,
          placeholderText: '请选择',
          code: '',
          name: ''
        },
        {
          divisionlist: [],
          disabled: false,
          placeholderText: '请选择',
          code: '',
          name: ''
        },
        {
          divisionlist: [],
          disabled: false,
          placeholderText: '请选择',
          code: '',
          name: ''
        }
        // {
        //   divisionlist: [],
        //   disabled: false,
        //   code: '',
        //   name: ''
        // }
      ]
    }
  },
  computed: {
    isDeparmentAdd() {
      return this.$store.state.breadcrumbBtn.platManage.isDeparmentAdd
    }
  },
  watch: {
    isDeparmentAdd(value) {
      // 监听到有变化就重新获取数据
      this.editDataForm = {
        name: '',
        short_name: '',
        credit_code: '',
        division_code: '',
        division_name: '',
        parent_credit_code: '',
        address: '',
        duty: ''
      }
      this.division = {
        province: '',
        provinceName: '',
        provinceOptions: [],
        city: '',
        cityName: '',
        cityOptions: [],
        district: '',
        districtName: '',
        districtOptions: []
      }
      // this.getAlldivision()
    }
  },
  created() {
    // this.getAlldivision()
    this.getOrganization()
  },
  mounted() {},
  methods: {
    handleChange(value) {
      console.log(value)
    },
    getOrganizationView() {
      getOrganizationView(this.dialogData.id)
        .then(res => {
          this.editDataForm = res.data
          // this.getAlldivision()
          this.getOrganization()
        })
        .catch(() => {})
    },
    // 获取省市区数据
    getAlldivision() {
      getAlldivision().then(res => {
        if (res.meta.code === '200' && res.data != null) {
          this.divisionTree = res.data.root
          if (this.organizationDivisionCode) {
            this.divisionTree.children.forEach(e => {
              if (this.divisionTree.value === this.organizationDivisionCode) {
                e.disabled = false
                if (e.children) {
                  e.children.forEach(ele => {
                    ele.disabled = false
                  })
                }
                return
              }
              e.disabled = true
              if (e.children) {
                e.children.forEach(ele => {
                  ele.disabled = true
                })
              }
              if (e.value === this.organizationDivisionCode) {
                e.disabled = false
                if (e.children) {
                  e.children.forEach(ele => {
                    ele.disabled = false
                  })
                }
              } else {
                if (e.children) {
                  e.children.forEach(ele => {
                    if (ele.value === this.organizationDivisionCode) {
                      ele.disabled = false
                      e.disabled = false
                    }
                  })
                }
              }
            })
          }
          // this.setdivisionTree()
          this.division.province = this.divisionTree.value
          this.division.provinceName = this.divisionTree.label
          this.division.provinceOptions.push(this.divisionTree)
          this.division.cityOptions = this.divisionTree.children
          this.getNextDivision(this.divisionTree.children, this.editDataForm.division_code, 'city')
        }
      })
    },
    setdivisionTree() {
      const { division_code } = JSON.parse(localStorage.getItem('organization'))
      this.divisionTree.children.forEach((e, i) => {
        if (e.value === division_code) {
          e.disabled = true
        }
      })
      console.log(this.divisionTree)
    },
    cityChange(val) {
      this.editDataForm.division_code = ''
      const options = this.division.cityOptions.filter(i => {
        if (val === i.value) {
          this.division.cityName = i.label
        }
        return val === i.value
      })
      this.division.districtOptions = options[0].children

      this.division.city = val
    },
    // 获取下一级
    getNextDivision(data, code, val) {
      console.log(data, code)
      data.forEach(e => {
        // console.log(e, code)
        if (e.value === code) {
          if (val === 'city') {
            this.division.city = code
            this.division.cityName = e.label
            this.division.cityOptions = data
            this.division.districtOptions = e.children
          } else {
            this.division.district = code
            this.division.districtOptions = data
            this.division.city = e.parent
            this.division.cityName = this.division.cityOptions.find(item => item.value === e.parent).label
            this.division.districtName = e.label
          }
        } else {
          if (e.children != null) {
            this.getNextDivision(e.children, code)
          }
        }
      })
    },
    // 获取division_code
    // getDivisionCode() {
    //   console.log(this.division)
    //   this.editDataForm.division_code = this.division.district || this.division.city || this.division.province
    //   this.editDataForm.division_name = this.division.provinceName + this.division.cityName + this.division.districtName
    // },
    getDivisionCode(val) {
      this.editDataForm.division_code = val
      console.log('this.editDataForm.division_code', this.editDataForm.division_code)
    },
    setDivisionName(val) {
      this.editDataForm.division_name = val
      console.log('this.editDataForm.division_name', this.editDataForm.division_name)
    },
    setDivisionCodeAndName(data) {
      console.log('data', data)
      this.editDataForm.division_code = data.code
      this.editDataForm.division_name = data.name
    },
    districtChange(val) {
      // this.getDivisionCode()
    },
    getOrganization() {
      const data = { scope: true }
      this.organizationList = []
      getOrganization(data).then(res => {
        if (res.meta.code === '200' && res.data.length) {
          this.organizationList = res.data
        }
      })
    },

    onSubmit() {
      // console.log(this.$refs.areaSelect.getAllCodeAndName())
      // let item = this.$refs.areaSelect.getAllCodeAndName()
      // this.editDataForm.division_code = item.division_code
      // this.editDataForm.division_name = item.division_name
      console.log(this.editDataForm)
      this.$refs['form'].validate(valid => {
        if (valid) {
          console.log(valid)
          saveOrganizationCreate(this.editDataForm).then(res => {
            if (res.meta.code !== '200') return
            this.$message({
              message: '保存成功',
              type: 'success'
            })
            this.$emit('canse', false)
          })
        }
      })
    },
    onCanse() {
      this.$refs.AdministrativeDivisionSelect.clearChose()
      this.$emit('canse', false)
    }
  }
}
</script>

<style lang="scss" scoped>
.editDialog-footer {
  text-align: center;
}
</style>
