<template>
  <div class="edit-list-container">
    <el-form ref="form" :model="editDataForm" :rules="rules" label-width="180px">
      <div v-if="!isTree" class="col1">
        <el-row :gutter="24">
          <el-col :span="20">
            <el-form-item label="部门名称" prop="name">
              <el-input v-model="editDataForm.name" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="20">
            <el-form-item label="部门简称" prop="short_name">
              <el-input v-model="editDataForm.short_name" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="20">
            <el-form-item label="部门统一信用代码" prop="credit_code">
              <el-input v-model="editDataForm.credit_code" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="20">
            <el-form-item label="部门所属行政区划" prop="division_code">
              <!-- key值为必传 -->
              <!-- <AdministrativeDivisionSelect :key="divisionCode" :division-code="divisionCode" @setDivisionCodeAndName="setDivisionCodeAndName" /> -->
              <AdministrativeDivisionCascader
                :key="divisionCode"
                :division-code="divisionCode"
                :permissionCode="'auth:organization:list'"
                @setDivisionCodeAndName="setDivisionCodeAndName"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="20" class="industryOrgName">
            <el-form-item label="上级机构" prop="parent_credit_code">
              <el-select v-model="editDataForm.parent_credit_code" filterable placeholder="请选择" class="width-100">
                <el-option
                  v-for="(item,idx) in organizationList"
                  :key="item.credit_code+idx"
                  title="客户卡身份卡首付款撒反馈阿萨德回风口阿莎客服客服号都是客户反馈的说法快递师傅肯定是付款电话费但是费d客户卡身份卡首付款撒反馈阿萨德回风口阿莎客服客服号都是客户反馈的说法快递师傅肯定是付款电话费但是费d客户卡身份卡首付款撒反馈阿萨德回风口阿莎客服客服号都是客户反馈的说法快递师傅肯定是付款电话费但是费d客户卡身份卡首付款撒反馈阿萨德回风口阿莎客服客服号都是客户反馈的说法快递师傅肯定是付款电话费但是费d客户卡身份卡首付款撒反馈阿萨德回风口阿莎客服客服号都是客户反馈的说法快递师傅肯定是付款电话费但是费d客户卡身份卡首付款撒反馈阿萨德回风口阿莎客服客服号都是客户反馈的说法快递师傅肯定是付款电话费但是费d客户卡身份卡首付款撒反馈阿萨德回风口阿莎客服客服号都是客户反馈的说法快递师傅肯定是付款电话费但是费d"
                  :label="item.name"
                  :value="item.credit_code"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="20">
            <el-form-item label="机构地址" prop="address">
              <el-input v-model="editDataForm.address" />
            </el-form-item>
          </el-col>
        </el-row>
      </div>
      <div v-else class="col2">
        <el-row :gutter="24">
          <el-col :span="10">
            <el-form-item label="部门名称" prop="name">
              <el-input v-model="editDataForm.name" />
            </el-form-item>
          </el-col>
          <el-col :span="14">
            <el-form-item label="部门简称" prop="short_name">
              <el-input v-model="editDataForm.short_name" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="10">
            <el-form-item label="部门统一信用代码" prop="credit_code">
              <el-input v-model="editDataForm.credit_code" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="部门所属行政区划" prop="division_code">
              <!-- <AdministrativeDivisionSelect
                :key="divisionCode"
                :division-code="divisionCode"
                @setDivisionCodeAndName="setDivisionCodeAndName"
              />-->
              <AdministrativeDivisionCascader
                :key="divisionCode"
                :division-code="divisionCode"
                :permissionCode="'auth:organization:list'"
                @setDivisionCodeAndName="setDivisionCodeAndName"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="10" class="industryOrgName">
            <el-form-item label="上级机构" prop="parent_credit_code">
              <el-select v-model="editDataForm.parent_credit_code" placeholder="请选择" class="width-100">
                <el-option v-for="(item,idx) in organizationList" :key="item.credit_code+idx" :label="item.name" :value="item.credit_code" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="14">
            <el-form-item label="机构地址" prop="address">
              <el-input v-model="editDataForm.address" />
            </el-form-item>
          </el-col>
        </el-row>
      </div>
      <el-row :gutter="24">
        <el-col :span="isTree?24:20">
          <el-form-item label="机构职责" prop="duty">
            <el-input v-model="editDataForm.duty" type="textarea" :rows="4" placeholder="请输入内容" maxlength="200" show-word-limit />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="24">
        <el-form-item label="部门状态" prop="status">
          <el-radio-group v-model="editDataForm.status">
            <el-radio label="NORMAL">正常</el-radio>
            <el-radio label="CANCEL">注销</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-row>
    </el-form>
    <div slot="footer" class="editDialog-footer">
      <el-button type="primary" @click="onSubmit">确 定</el-button>
      <el-button @click="onCanse">取 消</el-button>
    </div>
  </div>
</template>

<script>
import { getAlldivision, getOrganization } from '@/api/commonPack/platManege'
import { getOrganizationView, saveOrganizationEdit, getDivisionList } from '@/api/commonPack/platManege'
export default {
  components: {
    AreaSelect: () => import('@/components/AreaSelect'),
    // AdministrativeDivisionSelect: () => import('@/components/AdministrativeDivisionSelect'),
    AdministrativeDivisionCascader: () => import('@/components/AdministrativeDivisionCascader')
  },
  props: {
    id: {
      type: String,
      default: () => {
        return ''
      }
    },
    toggleEdit: {
      type: Boolean,
      default: () => {
        return false
      }
    },
    source: {
      type: String,
      default: () => {
        return ''
      }
    }
  },
  data() {
    return {
      editDataForm: {
        name: '',
        status: '',
        short_name: '',
        credit_code: '',
        division_code: '',
        division_name: '',
        parent_credit_code: '',
        address: '',
        duty: ''
      },
      rules: {
        name: [{ required: true, message: '请输入部门名称', trigger: 'blur' }],
        credit_code: [{ required: true, message: '请输入部门统一信用代码', trigger: 'blur' }],
        division_code: [{ required: true, message: '请选择部门所属行政区划', trigger: 'focus' }],
        status: [{ required: true, message: '请选择部门状态', trigger: 'focus' }]
      },
      divisionTree: [],
      division: {
        province: '',
        provinceName: '',
        provinceOptions: [],
        city: '',
        cityName: '',
        cityOptions: [],
        district: '',
        districtName: '',
        districtOptions: []
      },
      organizationList: [],
      organizationDivisionCode: JSON.parse(localStorage.getItem('organization')).division_code || '',
      isTree: false,
      divisionCode: '',
      value: '',
      optionData: [
        {
          divisionlist: [],
          disabled: false,
          placeholderText: '请选择',
          code: '',
          name: ''
        },
        {
          divisionlist: [],
          disabled: false,
          placeholderText: '请选择',
          code: '',
          name: ''
        },
        {
          divisionlist: [],
          disabled: false,
          placeholderText: '请选择',
          code: '',
          name: ''
        }
      ],
      optionProps: {
        value: 'code',
        label: 'name',
        children: 'children',
        checkStrictly: true,
        lazy: true,
        lazyLoad(node, resolve) {
          console.log('node.data', node.data)
          if (node.data) {
            const data = {
              code: node.data.code,
              level: 'SUB'
            }
            if (!node.data.children) {
              getDivisionList(data).then(res => {
                resolve(res.data)
              })
            } else {
              resolve()
            }
          }
        }
      },
      cascaderData: [],
      cascaderDataFrist: [],
      cascaderOptions1: []
    }
  },
  watch: {
    id: {
      handler(val) {
        if (!val) return
        this.getOrganizationView().then(() => {
          // this.getDivisionList()
        })
      },
      deep: true,
      immediate: true
    },
    toggleEdit: {
      handler(val) {
        this.getOrganizationView()
      },
      deep: true,
      immediate: true
    },
    source: {
      handler(val) {
        if (!val) return
        this.isTree = val === 'tree'
      },
      deep: true,
      immediate: true
    }
    /* 'division.cityOptions': {
      handler(val) {
        if (!val) return
        val.forEach(e => {
          if (e.value !== this.organizationDivisionCode) {
            console.log(1, e.label)
            e.disabled = true
            if (!e.children) return
            e.children.forEach(ele => {
              if (ele.value === this.organizationDivisionCode) {
                console.log(2, ele.label)
              }
            })
          } else {
            e.disabled = false
          }
        })
        console.log(this.division)
      },
      deep: true,
      immediate: true
    },
    'division.districtOptions': {
      handler(val) {
        if (!val) return
        val.forEach(e => {
          if (e.value === this.organizationDivisionCode) {
            console.log(e)
            e.disabled = false
            this.division.cityOptions.forEach(ele => {
              if (ele.value === e.parent)e.disabled = false
            })
          }
        })
      },
      deep: true,
      immediate: true
    } */
  },
  created() {},

  methods: {
    getDivisionList() {
      const data = {
        code: '',
        level: 'SUB'
      }
      getDivisionList(data).then(res => {
        if (res.meta.code === '200' && res.data != null) {
          this.cascaderDataFrist = res.data
          console.log('this.cascaderDataFrist', this.cascaderDataFrist)
          // console.log('this.divisionCode', this.divisionCode)
          const data1 = {
            code: this.divisionCode,
            level: 'PARENT'
          }
          this.getDivisionListByCode(data1)
        }
      })
    },
    getDivisionListByCode(data) {
      getDivisionList(data).then(res => {
        if (res.meta.code === '200' && res.data != null) {
          // console.log('getDivisionListByCode', res.data)

          this.cascaderData = res.data
          this.cascaderOptions1 = res.data
          this.cascaderData.forEach((e, index) => {
            const data = {
              code: e.code,
              level: 'SUB'
            }
            getDivisionList(data).then(res => {
              const list = []
              if (res.meta.code === '200' && res.data != null) {
                this.cascaderOptions1 = []
                e.children = res.data
                console.log('res.data', res.data)
                if (this.cascaderData.length != 0) {
                  for (var i = this.cascaderData.length - 1; i >= 0; i--) {
                    // console.log('i', this.cascaderData[i], this.cascaderData[i - 1])
                    if (this.cascaderData[i - 1]) {
                      if (this.cascaderData[i - 1].children) {
                        this.cascaderData[i - 1].children.forEach(e => {
                          if (e.code == this.cascaderData[i].code) {
                            if (this.cascaderData[i].children) {
                              e.children = this.cascaderData[i].children
                            }
                          }
                        })
                      }
                    }
                  }

                  // this.cascaderOptions.push(this.cascaderData[0])
                  list.push(this.cascaderData[0])
                  // this.cascaderOptions1 = this.cascaderDataFrist
                  this.cascaderDataFrist.forEach(e => {
                    if (e.code !== this.cascaderData[0].code) {
                      list.push(e)
                    }
                  })
                  this.cascaderOptions1 = list
                  console.log('this.cascaderOptions1', this.cascaderOptions1)
                }
              }
            })
          })
        }
      })
    },
    getOrganizationView() {
      return getOrganizationView(this.id)
        .then(res => {
          this.editDataForm = res.data
          // console.log('editDataForm', this.editDataForm)
          this.divisionCode = this.editDataForm.division_code
          // this.value = this.divisionCode
          // console.log('this.value', this.value)
          this.division = {
            province: '',
            provinceName: '',
            provinceOptions: [],
            city: '',
            cityName: '',
            cityOptions: [],
            district: '',
            districtName: '',
            districtOptions: []
          }
          // this.getAlldivision()
          this.getOrganization()
        })
        .catch(() => {})
    },
    // 获取省市区数据
    getAlldivision() {
      getAlldivision().then(res => {
        if (res.meta.code === '200' && res.data != null) {
          this.divisionTree = res.data.root
          if (this.organizationDivisionCode) {
            this.divisionTree.children.forEach(e => {
              if (this.divisionTree.value === this.organizationDivisionCode) {
                e.disabled = false
                if (e.children) {
                  e.children.forEach(ele => {
                    ele.disabled = false
                  })
                }
                return
              }
              e.disabled = true
              if (e.children) {
                e.children.forEach(ele => {
                  ele.disabled = true
                })
              }
              if (e.value === this.organizationDivisionCode) {
                e.disabled = false
                if (e.children) {
                  e.children.forEach(ele => {
                    ele.disabled = false
                  })
                }
              } else {
                if (e.children) {
                  e.children.forEach(ele => {
                    if (ele.value === this.organizationDivisionCode) {
                      ele.disabled = false
                      e.disabled = false
                    }
                  })
                }
              }
            })
          }
          // this.setdivisionTree()
          this.division.province = this.divisionTree.value
          this.division.provinceName = this.divisionTree.label
          this.division.provinceOptions[0] = this.divisionTree
          this.division.cityOptions = this.divisionTree.children
          this.getNextDivision(this.divisionTree.children, this.editDataForm.division_code, 'city')
        }
      })
    },
    setdivisionTree() {
      const { division_code } = JSON.parse(localStorage.getItem('organization'))
      this.divisionTree.children.forEach((e, i) => {
        if (e.value === division_code) {
          e.disabled = true
        }
      })
    },
    cityChange(val) {
      this.editDataForm.division_code = ''
      const options = this.division.cityOptions.filter(i => {
        if (val === i.value) {
          this.division.cityName = i.label
        }
        return val === i.value
      })
      this.division.districtOptions = options[0].children

      this.division.city = val
    },
    // 获取下一级
    getNextDivision(data, code, val) {
      data.forEach(e => {
        // console.log(e, code)
        if (Number(e.value) === Number(code)) {
          if (val === 'city') {
            this.division.city = code
            this.division.cityName = e.label
            this.division.cityOptions = data
            this.division.districtOptions = e.children
          } else {
            this.division.district = code
            this.division.districtOptions = data
            this.division.city = e.parent
            this.division.cityName = this.division.cityOptions.find(item => item.value === e.parent).label
            this.division.districtName = e.label
          }
        } else {
          if (e.children != null) {
            this.getNextDivision(e.children, code)
          }
        }
      })
    },
    // // 获取division_code
    // getDivisionCode() {
    //   this.editDataForm.division_code = this.division.district || this.division.city || this.division.province
    //   this.editDataForm.division_name = this.division.provinceName + this.division.cityName + this.division.districtName
    // },
    getDivisionCode(val) {
      this.editDataForm.division_code = val
      console.log('this.editDataForm.division_code', this.editDataForm.division_code)
    },
    setDivisionName(val) {
      this.editDataForm.division_name = val
      console.log('this.editDataForm.division_name', this.editDataForm.division_name)
    },
    setDivisionCodeAndName(data) {
      console.log('data', data)
      this.editDataForm.division_code = data.code
      this.editDataForm.division_name = data.name
    },
    districtChange(val) {
      // this.getDivisionCode()
    },
    getOrganization() {
      const filter_credit_code = [this.editDataForm.credit_code]
      const data = { scope: true, filter_credit_code }
      this.organizationList = []
      getOrganization(data).then(res => {
        if (res.meta.code === '200' && res.data.length) {
          this.organizationList = res.data
        }
      })
    },
    onSubmit() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          // const data = this.$refs.areaSelect.getAllCodeAndName()
          // this.editDataForm.division_code = data.division_code
          // this.editDataForm.division_name = data.division_name
          saveOrganizationEdit(this.editDataForm).then(res => {
            if (res.meta.code !== '200') return
            this.$message({
              message: '保存成功',
              type: 'success'
            })
            this.$emit('canse', false)
          })
        }
      })
    },
    onCanse() {
      this.$emit('canse', false)
    }
  }
}
</script>

<style lang="scss" scoped>
.editDialog-footer {
  text-align: center;
}
.cascader {
  width: 400px;
}
</style>
