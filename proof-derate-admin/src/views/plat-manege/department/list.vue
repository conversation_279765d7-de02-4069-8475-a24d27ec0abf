<template>
  <div class="department-container">
    <!-- <div slot="header" class="tabheader department-box-title">
      <h3 class="department-box-title-h3">部门列表</h3>
      <div class="department-box-title-btn">
        <template>
          <div class="btn active">
            <img src="@/assets/commonPack_images/list-active-icon.png" alt srcset />
          </div>
          <div class="btn" @click="toggleListTree('tree')">
            <img src="@/assets/commonPack_images/tree-con.png" alt srcset />
          </div>
        </template>
      </div>
    </div>-->
    <CardTitle :title-name="titleName">
      <template>
        <div class="btn active">
          <img src="@/assets/commonPack_images/list-active-icon.png" alt srcset>
        </div>
        <div class="btn" @click="toggleListTree('tree')">
          <img src="@/assets/commonPack_images/tree-con.png" alt srcset>
        </div>

        <!-- <div class="btn active">
          <img src="@/assets/commonPack_images/list-active-icon.png" alt srcset />
        </div>
         <div class="btn active">
          <img src="@/assets/commonPack_images/list-active-icon.png" alt srcset />
        </div> -->
      </template>
    </CardTitle>
    <el-card class="department-box">
      <!-- <div slot="header" class="department-box-title">
        <h3 class="department-box-title-h3">部门列表</h3>
        <div class="department-box-title-btn">
          <div class="btn active"><img src="@/assets/commonPack_images/list-active-icon.png" alt="" srcset=""></div>
          <div class="btn" @click="toggleListTree('tree')"><img src="@/assets/commonPack_images/tree-con.png" alt="" srcset=""></div>
        </div>
      </div>-->
      <el-form :model="sendFrom" label-width="120px" @submit.native.prevent>
        <el-row :gutter="24">
          <el-col :span="9">
            <el-form-item label="部门名称">
              <el-input v-model="sendFrom.name" clearable placeholder="请输入部门名称" />
            </el-form-item>
          </el-col>
          <el-col :span="9">
            <el-form-item label="行政区划">
              <AdministrativeDivisionCascader
                :key="divisionCode"
                ref="AdministrativeDivisionSelect"
                :division-code="divisionCode"
                :permission-code="'auth:organization:list'"
                @setDivisionCodeAndName="setDivisionCodeAndName"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6" class="submitbtn">
            <el-button type="primary" plain native-type="submit" @click="onSubmit">查询</el-button>
            <el-button plain native-type="submit" @click="reset">重置</el-button>
          </el-col>
        </el-row>

        <el-row :gutter="24">
          <el-col :span="9">
            <el-form-item label="部门状态">
              <el-radio-group v-model="sendFrom.status">
                <el-radio v-for="(item, idx) in statusOptions" :key="idx" :label="item.value">{{ item.label }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <custom-table
        ref="table"
        :is-card-type="false"
        :table-data="tableData"
        :table-header="tableHeader"
        @query="query"
        @refresh="query(1)"
      >
        <template #handle="{ row }">
          <el-button
            v-permission="'auth:organization:edit'"
            :disabled="!isPermission(row.permission_codes, 'auth:organization:edit')"
            type="text"
            @click="handleEdit(row)"
          >编辑</el-button>
          <el-button
            v-permission="'auth:organization:delete'"
            :disabled="!isPermission(row.permission_codes, 'auth:organization:delete')"
            type="text"
            class="table-delete"
            @click="handleDelete(row.id)"
          >删除</el-button>
        </template>
      </custom-table>
    </el-card>
    <el-dialog title="编辑部门" :visible.sync="editDialogVisible" width="50%" @close="closeEditDialog">
      <edit-department v-if="editDialogVisible" :id="editData" :toggle-edit="toggleEdit" @canse="setEditDialogVisible" />
    </el-dialog>
    <el-dialog title="新建部门" :visible.sync="addDialogVisible" width="50%" @close="closeEditDialog">
      <new-department @canse="setAddDialogVisible" />
    </el-dialog>

    <!--  <fileDialog ref="fileDialog" :whitelist="whitelist" :file-size-limit="fileSizeLimit"
      :file-dialog-title="fileDialogTitle" :multiple="multiple" :is-show-temple="true" :is-check-file-name="true"
      application-type="divisionCode" @dialogClose="dialogClose" @getFilelist="getFileList"
      @downTemple="exportTempleDownload" /> -->
    <fileDialog
      ref="fileDialog"
      :accept="accept"
      :limit="limit"
      :file-size-limit="fileSizeLimit"
      :is-show-temple="isShowTemple"
      :import-result-data="importResultData"
      @getFilelist="getFilelist2"
      @downTemple="exportTempleDownload"
    />

    <!-- <el-dialog title="提示" :visible.sync="importErrorDialog" width="50%" :show-close="false" top="300px">
      <div class="tips-title">
        <div><img class="error-icon" src="@/assets/commonPack_images/icon_import_error.png" alt=""></div>
        文件导入失败，{{importFailList.length}}条数据校验失败，失败原因如下：
      </div>
      <div class="el-dialog-div">
        <div class="error-content" v-for="(item, index) in importFailList" :key="index">
          {{ item }}
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="importErrorDialog = false">关 闭</el-button>
      </span>
    </el-dialog> -->
    <!-- <import-error-dialog ref="importErrorDialog" :import-fail-list="importFailList" /> -->
  </div>
</template>

<script>
import { getDepartmentList, deleteOrganization, departmentImport, deparmentDownTemplate } from '@/api/commonPack/platManege'
// import { getCurrentUserInfo } from '@/api/user'
import CustomTable from '@/components/Element/Table'
import CardTitle from '@/components/CardTitle'
import { getOperationPermissionList, isPermission } from '@/utils/index'
import fileDialog from '@/components/fileDialog/index2.vue'
import { exportsDown, convertToCamelCase } from '@/utils'
export default {
  components: {
    CustomTable,
    editDepartment: () => import('./components/edit-department.vue'),
    newDepartment: () => import('./components/new-department.vue'),
    CardTitle,
    fileDialog,
    AdministrativeDivisionCascader: () => import('@/components/AdministrativeDivisionCascader')
    // ImportErrorDialog: () => import('@/components/ImportErrorDialog')
  },

  data() {
    return {
      accept: '.xlsx,.xls',
      fileSizeLimit: 1,
      limit: 1,
      isShowTemple: true,
      importResultData: {},

      sendFrom: {
        name: '',
        status: null,
        page_num: 1,
        page_size: 10,
        division_code: ''
      },
      divisionCode: '',
      statusOptions: [
        { value: null, label: '全部' },
        { value: 'NORMAL', label: '正常' },
        { value: 'CANCEL', label: '注销' }
      ],
      tableData: {
        content: [], // 表格数据
        loading: false, // 控制表格加载效果
        total: 10, // 分页查询时的总数，去掉该属性，不显示分页条
        currentPage: 1, // 当前页码
        pageSize: 10,
        pageDirection: 'desc',
        isShowIndex: true,
        border: false
      },
      tableHeader: [
        {
          label: '部门名称',
          prop: 'name',
          minWidth: '180px',
          align: 'left'
        },
        {
          label: '部门统一社会信用代码',
          prop: 'credit_code',
          minWidth: '160px',
          align: 'left'
        },
        {
          label: '部门状态',
          prop: 'status',
          minWidth: '160px',
          align: 'left',
          formatter: (row, col, val) => {
            return val === null ? val : val === 'NORMAL' ? '正常' : '注销'
            // return val
          }
        },
        {
          label: '最后更新时间',
          prop: 'last_modification_time',
          minWidth: '160px',
          align: 'left'
        },
        {
          label: '操作',
          prop: '',
          slot: 'handle',
          minWidth: '80px',
          align: 'left'
        }
      ],
      editData: {},
      editDialogVisible: false,
      addDialogVisible: false,
      toggleEdit: false,
      permissionList: ['auth:organization:edit', 'auth:organization:delete'],
      titleName: '部门管理',
      /* whitelist: ['xlsx', 'xls'], // 白名单
      fileSizeLimit: 1,
      multiple: false,
      fileDialogTitle: '导入文件', */
      importErrorDialog: false, // 导入失败弹窗提示
      importFailList: []// 导入失败数据
    }
  },
  computed: {
    isDeparmentAdd() {
      return this.$store.state.breadcrumbBtn.platManage.isDeparmentAdd
    },

    isDepartmentImport() {
      return this.$store.state.breadcrumbBtn.platManage.isDepartmentImport
    }
  },
  watch: {
    isDeparmentAdd(value) {
      // 监听到有变化就重新获取数据
      this.addDialogVisible = value
    },

    isDepartmentImport(value) { // 导入
      if (value) {
        this.$refs.fileDialog.dialogVisible = true
        this.$store.commit('breadcrumbBtn/SET_PLATMANAGE', { isDepartmentImport: false })
      }
    }

  },
  mounted() {
    this.tableHeader = getOperationPermissionList(this.$route.meta.permission, this.permissionList, this.tableHeader)
    this.getDepartmentList()
    // getCurrentUserInfo().then(res => {})
  },

  methods: {
    isPermission,
    getFilelist2() {
      if (this.$refs.fileDialog.fileList.length !== 0) {
        const params = {
          config: this.$refs.fileDialog.config
        }
        const file = this.$refs.fileDialog.fileList[0].raw
        const fd = new FormData()
        fd.append('file', file)
        departmentImport(fd, params).then(res => {
          const { common } = convertToCamelCase(res.data)
          this.$refs.fileDialog.dialogVisible = false
          this.$refs.fileDialog.fileList = []
          this.importResultData = common
          this.$refs.fileDialog.dialogVisibleStatus = true

          /* const data = res.data
          // const message = data.split('/').join('<br/>')
          if (res.meta.code !== '200') { // 接口请求失败
            this.$message({
              type: 'error',
              dangerouslyUseHTMLString: true,
              message: res.meta.message
            })
            this.$refs.fileDialog.loading = false
            this.$refs.fileDialog.dialogVisible = false
          } else { // 接口请求成功
            if (!data) { // 完全请求成功
              this.$message({
                type: 'success',
                message: '全部信息导入成功'
              })
            } else {
              this.importFailList = res.data
              this.$refs.importErrorDialog.dialogVisible = true
            }
            this.$refs.fileDialog.loading = false
            this.$refs.fileDialog.dialogVisible = false
          } */
          this.getDepartmentList()
        }).catch(() => {
          this.$refs.fileDialog.loading = false
          this.$refs.fileDialog.dialogVisible = false
          this.$refs.fileDialog.fileList = []
        })
      }
    },
    getDepartmentList() {
      getDepartmentList(this.sendFrom)
        .then(res => {
          if (res.data != null && res.meta.code === '200') {
            if (res.data.content != null) {
              this.tableData.content = res.data.content
              this.tableData.total = Number(res.data.total_elements)
              // this.tableData.currentPage =
            } else {
              this.tableData.content = []
              this.tableData.total = 0
            }
          }
        })
        .catch(() => { })
    },
    onSubmit() {
      this.sendFrom.page_num = 1
      this.sendFrom.page_size = 10
      this.tableData.currentPage = this.sendFrom.page_num
      this.tableData.pageSize = this.sendFrom.page_size
      this.getDepartmentList()
      // if (this.sendFrom.page_num === 1) this.tableData.currentPage = 1
    },
    reset() {
      this.sendFrom = {
        name: '',
        status: null,
        page_num: 1,
        page_size: 10
      }
    },
    query() {
      this.sendFrom.page_num = this.tableData.currentPage
      this.sendFrom.page_size = this.tableData.pageSize
      this.getDepartmentList()
    },
    handleEdit(row) {
      this.editData = row.id
      this.editDialogVisible = true
      this.toggleEdit = !this.toggleEdit
    },
    handleDelete(id) {
      this.$confirm('确定删除吗？如果存在下级部门将一并删除，此操作将不能撤销', '警告提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          deleteOrganization(id).then(res => {
            if (res.meta.code === '200') {
              this.getDepartmentList()
              this.$message({
                type: 'success',
                message: '删除成功!'
              })
            }
          })
        })
        .catch(() => {
          /* this.$message({
          type: 'info',
          message: '已取消删除'
        }) */
        })
    },
    setEditDialogVisible(val) {
      this.editDialogVisible = val
      this.getDepartmentList()
      this.$store.commit('breadcrumbBtn/SET_PLATMANAGE', { isDeparmentAdd: false })
    },
    setAddDialogVisible() {
      // this.addDialogVisible = val
      this.getDepartmentList()
      this.$store.commit('breadcrumbBtn/SET_PLATMANAGE', { isDeparmentAdd: false })
    },
    closeEditDialog() {
      this.$store.commit('breadcrumbBtn/SET_PLATMANAGE', { isDeparmentAdd: false })
    },
    toggleListTree(val) {
      this.$router.push({ name: 'DepartmentTree' })
    },

    /**
     * 获取操作权限
     * @param {权限列表} list
     */
    getOperationPermission(list) {
      let tableHeader = this.tableHeader
      const length = this.tableHeader.length
      if (list.length) {
        const keyList = list.map(item => {
          return item.key
        })
        if (!keyList.includes('auth:organization:edit') && !keyList.includes('auth:organization:delete')) {
          tableHeader = this.tableHeader.slice(0, length - 1)
        }
      } else {
        tableHeader = this.tableHeader.slice(0, length - 1)
      }

      return tableHeader
    },

    setDivisionCodeAndName(data) {
      console.log('data', data)
      this.sendFrom.division_code = data.code
    },

    /**
   * 弹窗关闭
   */
    dialogClose() {
      this.$store.commit('breadcrumbBtn/SET_PLATMANAGE', {
        isDepartmentImport: false
      })
      this.$refs.fileDialog.fileList = []
    },
    /**
     * 获取文件
     */
    getFileList(list) {
      console.log('list', list)
      if (list.length != 0) {
        const file = list[0].raw
        const fd = new FormData()
        fd.append('file', file)
        departmentImport(fd).then(res => {
          const { common } = (res.data)
          this.$refs.fileDialog.dialogVisible = false
          this.$refs.fileDialog.fileList = []
          this.importResultData = common
          this.$refs.fileDialog.dialogVisibleStatus = true

          const data = res.data
          // const message = data.split('/').join('<br/>')
          if (res.meta.code !== '200') { // 接口请求失败
            this.$message({
              type: 'error',
              dangerouslyUseHTMLString: true,
              message: res.meta.message
            })
            this.$refs.fileDialog.loading = false
            this.$refs.fileDialog.dialogVisible = false
          } else { // 接口请求成功
            if (!data) { // 完全请求成功
              this.$message({
                type: 'success',
                message: '全部信息导入成功'
              })
            } else {
              this.importFailList = res.data
              this.$refs.importErrorDialog.dialogVisible = true
            }
            this.$refs.fileDialog.loading = false
            this.$refs.fileDialog.dialogVisible = false
            this.getDepartmentList()
          }
        }).catch(() => {
          this.$refs.fileDialog.loading = false
          this.$refs.fileDialog.dialogVisible = false
          this.$refs.fileDialog.fileList = []
        })
      }
    },
    /**
     * 下载导入模板
     */
    exportTempleDownload() {
      exportsDown(deparmentDownTemplate(), {}, '', 'licc')
    }
  }
}
</script>

<style lang="scss" scoped>
.department-container {
  padding: 10px;
}
.department-box {
  &-title {
    // height: 36px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    // margin:0px 0 30px 20px;
    &-h3 {
      margin: 0;
      padding: 0;
    }

    &-btn {
      display: flex;
      align-items: center;

      .btn {
        width: 36px;
        height: 36px;
        background: #f7f7f7;
        border: 1px solid #d9d9d9;
        border-radius: 4px;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;

        &:first-child {
          // margin-right: 12px;
        }

        &.active {
          background: #e0f0ff;
          border: 1px solid #99ceff;
        }
      }
    }
  }
}

</style>
<style lang="scss">
.department-container {
  .table-delete {
    color: #ff2b2b;
  }
}
.tabheader {
  padding: 18px 20px;
  border-bottom: 1px solid #ebeef5;
  box-sizing: border-box;
  background-color: #fff;
  box-shadow: 0 -6px 12px 0 rgb(0 0 0 / 10%);
  // border-radius: 4px;
  border-radius: 4px 4px 0px 0px;
  position: relative;
  top: 6px;
}</style>
