<template>
  <div class="department-container">
    <CardTitle :title-name="titleName">
      <template>
        <div class="btn" @click="toggleListTree()">
          <img src="@/assets/commonPack_images/list-icon.png" alt srcset />
        </div>
        <div class="btn active">
          <img src="@/assets/commonPack_images/tree-active-icon.png" alt srcset />
        </div>
      </template>
    </CardTitle>
    <el-card class="department-box">
      <div class="department-box-content">
        <div class="department-box-content-tree">
          <div class="tit">部门信息</div>

          <div class="tree">
            <el-scrollbar style="height: 500px">
              <el-tree :data="treeData" default-expand-all :props="defaultProps" node-key="value" @node-click="handleNodeClick">
                <span slot-scope="{ node, data }" class="span-ellipsis">
                  <!-- <el-tooltip class="item" effect="dark" :content="node.label||''" placement="top"> -->
                  <span>{{ node.label }}</span>
                  <!-- </el-tooltip> -->
                </span>
              </el-tree>
            </el-scrollbar>
          </div>
        </div>
        <div :class="['detail',{edit:!isEdit}]">
          <el-button v-if="!isEdit" v-permission="'auth:organization:edit'" :disabled="!isPermission(permissionCodes,'auth:organization:edit')" type="primary" plain class="edit-btn" @click="handleEdit">编辑部门</el-button>
          <el-button
            v-if="!isEdit"
            v-permission="'auth:organization:delete'"
            :disabled="!isPermission(permissionCodes,'auth:organization:delete')"
            type="primary"
            plain
            class="del-btn"
            @click="handleDelete()"
          >删除</el-button>
          <detail-tree v-if="!isEdit" :id="deparmentDetailId" @getId="getId" @getPermissionCodes="getPermissionCodes" />
          <edit-department v-else :id="deparmentDetailId" :source="source" @canse="saveEdit" />
        </div>
      </div>
    </el-card>
    <el-dialog title="新建部门" :visible.sync="addDialogVisible" width="50%" @close="closeEditDialog">
      <new-department @canse="setAddDialogVisible" />
    </el-dialog>
  </div>
</template>

<script>
import { getOrganizationTree, deleteOrganization } from '@/api/commonPack/platManege'
import CardTitle from '@/components/CardTitle'
import { isPermission } from '@/utils/index'

export default {
  components: {
    detailTree: () => import('./components/detail-tree.vue'),
    editDepartment: () => import('./components/edit-department.vue'),
    newDepartment: () => import('./components/new-department.vue'),
    CardTitle
  },
  data() {
    return {
      treeData: [],
      addDialogVisible: false,
      titleName: '部门树状图',
      data: [
        {
          label: '一级 1',
          children: [
            {
              label: '二级 1-1',
              children: [
                {
                  label: '三级 1-1-1'
                }
              ]
            }
          ]
        },
        {
          label: '一级 2',
          children: [
            {
              label: '二级 2-1',
              children: [
                {
                  label: '三级 2-1-1'
                }
              ]
            },
            {
              label: '二级 2-2',
              children: [
                {
                  label: '三级 2-2-1'
                }
              ]
            }
          ]
        },
        {
          label: '一级 3',
          children: [
            {
              label: '二级 3-1',
              children: [
                {
                  label: '三级 3-1-1'
                }
              ]
            },
            {
              label: '二级 3-2',
              children: [
                {
                  label: '三级 3-2-1'
                }
              ]
            }
          ]
        }
      ],
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      deparmentDetailId: '',
      isEdit: false,
      source: 'tree',
      delID: '',
      permissionCodes: []
    }
  },
  computed: {
    isDeparmentTreeAdd() {
      return this.$store.state.breadcrumbBtn.platManage.isDeparmentTreeAdd
    }
  },
  watch: {
    isDeparmentTreeAdd(value) {
      // 监听到有变化就重新获取数据
      console.log('isDeparmentTreeAdd', value)
      this.addDialogVisible = value
    }
  },
  mounted() {
    this.getOrganizationTree()
  },

  methods: {
    isPermission,
    getOrganizationTree() {
      getOrganizationTree().then(res => {
        this.treeData = res.data
        this.deparmentDetailId = this.treeData[0].value
      })
    },
    handleNodeClick(data) {
      this.isEdit = false
      this.deparmentDetailId = data.value
    },
    toggleListTree(val) {
      this.$router.push({ name: 'DepartmentList' })
    },
    handleEdit() {
      this.isEdit = true
    },
    handleDelete() {
      this.$confirm('确定删除吗？如果存在下级部门将一并删除，此操作将不能撤销', '警告提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          deleteOrganization(this.delID).then(res => {
            if (res.meta.code === '200') {
              this.getOrganizationTree()
              this.$message({
                type: 'success',
                message: '删除成功!'
              })
            }
          })
        })
        .catch(() => {
          /* this.$message({
          type: 'info',
          message: '已取消删除'
        }) */
        })
    },
    saveEdit() {
      this.isEdit = false
      this.getOrganizationTree()
    },
    closeEditDialog() {
      this.$store.commit('breadcrumbBtn/SET_PLATMANAGE', { isDeparmentTreeAdd: false })
    },
    setAddDialogVisible() {
      // this.addDialogVisible = val
      this.getOrganizationTree()
      this.$store.commit('breadcrumbBtn/SET_PLATMANAGE', { isDeparmentTreeAdd: false })
    },
    getId(id) {
      this.delID = id
    },
    getPermissionCodes(codes) {
      // console.log('codes', codes)
      this.permissionCodes = codes
    }
  }
}
</script>

<style lang="scss" scoped>
.department-container {
  padding: 10px;
}
.department-box {
  &-title {
    height: 36px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 0px 0 30px 20px;
    &-h3 {
      margin: 0;
      padding: 0;
    }
    &-btn {
      display: flex;
      align-items: center;
      .btn {
        width: 36px;
        height: 36px;
        background: #f7f7f7;
        border: 1px solid #d9d9d9;
        border-radius: 4px;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        &:first-child {
          margin-right: 12px;
        }
        &.active {
          background: #e0f0ff;
          border: 1px solid #99ceff;
        }
      }
    }
  }
  &-content {
    display: flex;
    &-tree {
      // width: 245px;
      width: 485px;
      border: 1px solid #e9e9e9;
      .tit {
        height: 50px;
        line-height: 50px;
        font-size: 14px;
        color: #666;
        background: #e9e9e9;
        padding-left: 15px;
      }
      .tree {
        padding: 15px 20px 30px 15px;
        // overflow-y: auto;
        // overflow-x: hidden;
        // height: 500px;
      }
    }
    .detail {
      flex: 1;
      border-left: none;
      position: relative;
      padding-top: 30px;
      &.edit {
        border: 1px solid #d9d9d9;
      }
      .edit-btn {
        position: absolute;
        top: 20px;
        right: 100px;
      }
      .del-btn {
        position: absolute;
        top: 20px;
        right: 20px;
      }
    }
  }
}
/*树悬浮框: 名字过长则显示...*/
.span-ellipsis {
  width: calc(100% - 4px);
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  min-width: auto;
  padding: 4px 2px;
  border-radius: 4px;
}
::v-deep .el-scrollbar__wrap {
  overflow-x: hidden;
}
</style>
<style lang="scss">
</style>
