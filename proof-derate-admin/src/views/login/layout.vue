<template>
  <div ref="UI" class="login-layout-container">
    <img v-if="background" class="login-bg" :src="'data:image/png;base64,'+background" alt srcset />
    <img v-else class="login-bg" src="~@/assets/images/login-bg-new.png" alt srcset />
    <div class="login-content">
      <div class="header">
        <span class="header-text">{{ title }}</span>
      </div>
      <div class="login-main-container">
        <div class="layout-container">
          <!-- <img src="@/assets/images/login-bg-left.png" class="container-bg" alt=""> -->
          <div class="form-box">
            <div class="form-box-inner">
              <router-view />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import defaultSettings from '@/settings'
import store from '../../store'
import { getbgImg } from '@/api/user'

export default {
  name: 'LoginContainer',
  data() {
    return {
      title: defaultSettings.title || '通用功能',
      bgUrl: '~@/assets/images/login-bg-new.png',
      background: '',
      color: 'red'
    }
  },
  mounted() {
    const systemInfo = JSON.parse(sessionStorage.getItem('systemInfo'))
    if (systemInfo === null) {
      store.dispatch('settings/changeSetting').then(res => {
        // this.background = res.background || ''
        this.title = res.name || defaultSettings.title
      })
    } else {
      this.title = systemInfo.name || defaultSettings.title
    }
    const bgImg = JSON.parse(sessionStorage.getItem('bgImg'))
    if (bgImg === null) {
      // this.getbgImg()
      store.dispatch('settings/bgImgSetting').then(res => {
        this.background = res.background
      })
    } else {
      this.background = bgImg.background || ''
    }
    // this.setUI()
    // this.getbgImg()
  },
  methods: {
    /* setUI() {
      this.$refs.UI.style.setProperty('--bgUrl', this.bgUrl) // 给变量赋值
    } */

    getbgImg() {
      getbgImg()
        .then(res => {
          if (res.meta.code === '200' && res.data !== null) {
            // this.background = res.data || ''
            let bgImg = {
              background: res.data
            }
            sessionStorage.setItem('bgImg', JSON.stringify(bgImg))
            this.background = res.data
          } else {
            this.background = this.bgUrl
          }
        })
        .catch(() => {
          this.background = this.bgUrl
        })
    }
  }
}
</script>
<style lang="scss" scoped>
@import '~@/styles/variables.scss';
.login-layout-container {
  position: relative;
  background: #fff;
  height: 100vh;
  position: relative;
  overflow: hidden;
  // url("data:image/png;base64,iVBORw0KGgo=...");
  /*  background-image: url(~@/assets/images/login-bg-new.png);
  background:var(--bgUrl); */
  .login-bg {
    width: 100%;
    height: 100%;
  }
  .login-content {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
  }
  .layout-container {
    display: flex;
    // justify-content: flex-end;
    // justify-content: center;
    justify-content: flex-end;
  }
  .header {
    // padding: 48px 64px;
    padding: 90px 0;
    display: flex;
    align-items: center;
    justify-content: center;
    &-icon {
      width: 64px;
      height: 64px;
      display: block;
      border-radius: 50%;
    }
    &-text {
      color: #fff;
      font-size: 36px;
      font-weight: bold;
      // margin-left: 6px;
      text-align: center;
    }
  }
  .login-main-container {
    // margin: 46px auto;
    margin: 0;
    display: flex;
    justify-content: flex-end;
    // justify-content: center;
    // .layout-container {
    //   display: flex;
    // }
    .container-bg {
      width: 60%;
      display: inline-block;
    }
    .form-box {
      // width: 50%;
      width: 35%;
      // height: 70%;
      // margin: 45px 0 0 61px;
      // margin: 0px 0 120px 0px;
      // margin: 0px 252px 120px 0px;
      margin: 0px 252px 0px 0px;
      display: inline-block;
      position: relative;
      background: #ffffff;
      box-shadow: 0px 8px 24px 0px rgba(58, 53, 52, 0.24);
      border-radius: 8px;
      &-inner {
        width: 100%;
        height: 100%;
        display: flex;
        padding: 20px 0 40px 0;
      }
    }
  }
  .tips {
    position: absolute;
    display: flex;
    width: 100%;
    bottom: 16px;
    justify-content: center;
    align-items: center;
    &-text {
      color: #c0c4cc;
      font-size: 12px;
    }
  }
}
</style>
<style lang="scss">
@media screen and (max-width: 1200px) {
  /*当屏幕尺寸小于1200px时，应用下面的CSS样式*/
  .login-layout-container {
    .layout-container {
      width: 980px;
      // height: 380px;
      overflow: hidden;
    }
  }
}
@media screen and (min-width: 1200px) and (max-width: 1500px) {
  /*当屏幕尺寸大于1200px且小于1500时，应用下面的CSS样式*/
  .login-layout-container {
    .layout-container {
      width: 1100px;
      // height: 440px;
      overflow: hidden;
    }
  }
}
@media screen and (min-width: 1500px) {
  /*当屏幕尺寸大于1500px时，应用下面的CSS样式*/
  .login-layout-container {
    .layout-container {
      width: 1336px;
      // height: 550px;
      overflow: hidden;
    }
  }
}
</style>
