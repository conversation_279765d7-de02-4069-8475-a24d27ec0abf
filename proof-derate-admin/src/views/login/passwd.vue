<template>
  <div class="login-con">
    <el-form ref="passwdForm" :model="passwdForm" :rules="passwdRules" label-width="80px" class="passwd-form" auto-complete="on">
      <div class="title-container">
        <h3 class="title">密码设置</h3>
        <p class="tips">{{ titleTips }}</p>
      </div>
      <el-form-item v-if="status!==1" label="旧密码" prop="oldPassword">
        <el-input
          :key="oldpasswdType"
          ref="oldpasswdChange"
          v-model="passwdForm.oldPassword"
          :type="oldpasswdType"
          placeholder="请输入旧密码"
          name="oldpasswdChange"
          tabindex="1"
          auto-complete="on"
        />
        <span class="show-pwd change" @click="showOldPwdChange">
          <svg-icon :icon-class="oldpasswdType === 'password' ? 'eye' : 'eye-open'" />
        </span>
      </el-form-item>
      <el-form-item label="新密码" prop="newPassword">
        <el-input
          :key="passwordChangeType"
          ref="passwordChange"
          v-model="passwdForm.newPassword"
          :type="passwordChangeType"
          placeholder="请输入密码"
          name="passwordChange"
          tabindex="2"
          auto-complete="on"
        />
        <span class="show-pwd change" @click="showPwdChange">
          <svg-icon :icon-class="passwordChangeType === 'password' ? 'eye' : 'eye-open'" />
        </span>
      </el-form-item>
      <el-form-item label="确认密码" prop="confirmPassword">
        <el-input
          :key="confirmPasswordChangeType"
          ref="confirmPasswordChange"
          v-model="passwdForm.confirmPassword"
          :type="confirmPasswordChangeType"
          placeholder="请输入密码"
          name="confirmPasswordChange"
          tabindex="3"
          auto-complete="on"
        />
        <span class="show-pwd change" @click="shoCconfirmPwdChange">
          <svg-icon :icon-class="confirmPasswordChangeType === 'password' ? 'eye' : 'eye-open'" />
        </span>
      </el-form-item>
      <el-form-item>
        <el-button :loading="loading" type="primary" @click.native.prevent="passwordChangeSubmit">确定</el-button>
        <el-button @click="toRedirect">返回</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
import { validPassword } from '@/utils/validate'

import { updateInitPassword, updatePassword } from '@/api/user'
import { sm2Encode } from '@/utils/sm-encrypt-utils'
export default {
  name: 'LoginCon',
  data() {
    const validatePasswd = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请输入密码'))
      } else {
        callback()
      }
      /*   else if (!validPassword(value)) {
        callback(new Error('密码必须包括数字、大写字母、小写字母、特殊字符中的3种字符并且密码长度为8至16位'))
      }  */
    }
    const validatePassIsidentical = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请再次输入密码'))
      } else if (value !== this.passwdForm.newPassword) {
        callback(new Error('两次输入密码不一致!'))
      } else {
        callback()
      }
    }
    return {
      status: null,
      titleTips: '请修改初始密码',
      passwdForm: {
        account: '',
        oldPassword: '',
        newPassword: '',
        confirmPassword: ''
      },
      passwdRules: {
        oldPassword: [{ required: true, trigger: 'blur', message: '请输入旧密码' }],
        newPassword: [{ required: true, trigger: 'blur', validator: validatePasswd }],
        confirmPassword: [{ required: true, trigger: 'blur', validator: validatePassIsidentical }]
      },
      loading: false,
      oldpasswdType: 'password',
      passwordChangeType: 'password',
      confirmPasswordChangeType: 'password',
      redirect: undefined,
      dialogVisible: false,
      dialogRegVisible: false,
      passwordChangeDialogVisible: false,
      applyText: '需要申请账号权限方可登录',
      applyBtn: '进行申请',
      reviewId: '',
      isCaptchaShow: false
    }
  },
  watch: {
    $route: {
      handler: function(route) {
        this.redirect = route.query && route.query.redirect
        const status = route.query && route.query.status
        this.status = Number(status)
        /*
         *  status
         * 1--修改初始密码，2---密码禁用，3--主动修改密码
         */
        let titleTips = ''
        switch (this.status) {
          case 1:
            titleTips = '请修改初始密码'
            break
          case 2:
            titleTips = '密码已过期/禁用'
            break
          case 3:
            titleTips = '请修改密码'
            break
          default:
            titleTips = '请修改密码'
            break
        }
        this.titleTips = titleTips
      },
      immediate: true
    }
  },
  created() {},
  methods: {
    passwordChangeSubmit() {
      this.$refs.passwdForm.validate(valid => {
        if (valid) {
          this.status === 1 ? this.updateInitPassword() : this.updatePassword()
        }
      })
    },
    // 修改初始密码
    updateInitPassword() {
      const data = {
        // new_password: this.Rsa.encrypt(this.passwdForm.newPassword)
        new_password: sm2Encode(this.passwdForm.newPassword,this.$appConfig.VUE_APP_ENCRYPT_KEY || process.env.VUE_APP_ENCRYPT_KEY)
      }
      updateInitPassword(data).then(res => {
        this.$message({
          message: '初始密码已修改',
          type: 'success'
        })
        this.toLogin()
      })
    },
    // 修改非初始密码
    updatePassword() {
      const data = {
        // old_password: this.Rsa.encrypt(this.passwdForm.oldPassword),
        // new_password: this.Rsa.encrypt(this.passwdForm.newPassword)
        old_password: sm2Encode(this.passwdForm.oldPassword, this.$appConfig.VUE_APP_ENCRYPT_KEY || process.env.VUE_APP_ENCRYPT_KEY),
        new_password: sm2Encode(this.passwdForm.newPassword, this.$appConfig.VUE_APP_ENCRYPT_KEY || process.env.VUE_APP_ENCRYPT_KEY)
      }

      updatePassword(data).then(res => {
        this.$message({
          message: '密码修改成功',
          type: 'success'
        })
        this.toLogin()
      })
    },
    toLogin() {
      setTimeout(() => {
        this.$router.push({ path: this.redirect || '/' })
      }, 1000)
    },
    toRedirect() {
      if (this.status === 3) {
        this.$router.push({ path: this.redirect || '/' })
      } else {
        this.$store.dispatch('user/logback')
        this.$router.push(`/login?redirect=${this.redirect}`)
      }
    },
    showOldPwdChange() {
      if (this.oldpasswdType === 'password') {
        this.oldpasswdType = ''
      } else {
        this.oldpasswdType = 'password'
      }
      this.$nextTick(() => {
        this.$refs.oldpasswdChange.focus()
      })
    },
    showPwdChange() {
      if (this.passwordChangeType === 'password') {
        this.passwordChangeType = ''
      } else {
        this.passwordChangeType = 'password'
      }
      this.$nextTick(() => {
        this.$refs.passwordChange.focus()
      })
    },
    shoCconfirmPwdChange() {
      if (this.confirmPasswordChangeType === 'password') {
        this.confirmPasswordChangeType = ''
      } else {
        this.confirmPasswordChangeType = 'password'
      }
      this.$nextTick(() => {
        this.$refs.confirmPasswordChange.focus()
      })
    }
  }
}
</script>
<style lang="scss" scoped>
@import '~@/styles/variables.scss';
h3 {
  margin: 0;
  padding: 0;
}
.login-con {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  .passwd-form {
    width: 85%;
    height: 1005;
    .title-container {
      position: relative;
      // padding-bottom: 68px;
      padding: 40px 0 30px 0;
      .title {
        color: #000000;
        font-size: 30px;
        text-align: center;
      }
      .tips {
        color: #999999;
        margin: 0;
        padding: 0;
        font-size: 14px;
        text-align: center;
        line-height: 20px;
      }
    }
  }
  .show-pwd {
    position: absolute;
    right: 10px;
    // top: 7px;
    font-size: 16px;
    cursor: pointer;
    user-select: none;
  }
  .svg-container {
    padding-left: 15px;
    vertical-align: middle;
    width: 30px;
    display: inline-block;
    font-size: 20px;
    position: absolute;
    z-index: 1;
    &::after {
      content: '';
      position: absolute;
      display: block;
      width: 1px;
      background: #e9edee;
      height: 32px;
      top: 4px;
      right: -24px;
    }
  }
  .reg-tips {
    color: blue;
    font-size: 16px;
    text-align: center;
    .active {
      color: red;
      cursor: pointer;
    }
  }
  .passwordChange-footer {
    text-align: center;
  }
}
.passwordChangeDialog ::v-deep .el-dialog__header {
  padding-bottom: 0px;
}
.passwordChangeDialog ::v-deep .el-dialog__body {
  padding-top: 0px;
}
.passwordChangeDialog ::v-deep .el-input__suffix {
  display: none;
}
.passwordChange-wrap {
  padding: 26px 16px 0px;
  .change {
    top: 0px;
  }
}
</style>
<style lang="scss">
$bg: #283443;
$light_gray: #fff;
$cursor: #fff;
.passwd-form {
  .el-form-item__content {
    display: flex;
    align-items: center;
  }
  .el-form-item {
    margin-bottom: 30px;
  }
  /* .el-input {
    display: inline-block;
    height: 48px;
    // width: 85%;
    input {
      padding-left: 70px;
      height: 48px;
      padding-right: 34px;
    }
  } */
}
</style>
