<template>
  <div class="login-con">
    <el-form ref="loginForm" :model="loginForm" :rules="loginRules" class="login-form" auto-complete="on">
      <div class="title-container">
        <h3 class="title">欢迎登录</h3>
      </div>

      <el-form-item prop="account">
        <span class="svg-container">
          <svg-icon icon-class="user" />
        </span>
        <el-input
          ref="account"
          v-model.trim="loginForm.account"
          placeholder="请输入账号"
          name="account"
          type="text"
          tabindex="1"
          auto-complete="on"
          class="form-input"
          @keyup.enter.native="handleLogin"
        />
      </el-form-item>

      <el-form-item prop="password">
        <span class="svg-container">
          <svg-icon icon-class="password" />
        </span>
        <el-input
          :key="passwordType"
          ref="password"
          v-model="loginForm.password"
          :type="passwordType"
          placeholder="请输入密码"
          name="password"
          tabindex="2"
          auto-complete="on"
          @keyup.enter.native="handleLogin"
        />
        <span class="show-pwd" @click="showPwd">
          <svg-icon :icon-class="passwordType === 'password' ? 'eye' : 'eye-open'" />
        </span>
      </el-form-item>
      <el-form-item v-if="isCaptchaShow" prop="captcha">
        <span class="svg-container">
          <svg-icon icon-class="form" />
        </span>
        <el-input
          ref="captcha"
          v-model="loginForm.captcha"
          placeholder="请输入验证码"
          name="captcha"
          type="text"
          tabindex="3"
          auto-complete="on"
          class="form-input"
          @keyup.enter.native="handleLogin"
        />
        <div class="captchaImg" @click="refreshCaptch">
          <img :src="captchaImg" alt srcset>
        </div>
      </el-form-item>

      <el-button :loading="loading" type="primary" style="width:100%;margin-bottom:20px;font-size: 18px;" @click.native.prevent="handleLogin">登录</el-button>
      <div
        v-if="thirdLoginButtonList.length"
        class="third-login-wrap"
        :style="{justifyContent: thirdLoginButtonList.length === 1 ? 'center' : 'space-between'}"
      >
        <div v-for="(item, index) in thirdLoginButtonList" :key="index" class="third-item" @click="handleThirdLoginClick(item)">
          <img v-if="item.button_icon" class="item-image" :src="`data:image/jpeg;base64,${item.button_icon}`" alt>
          <el-button class="custom-button" type="text">{{ item.button_name }}</el-button>
        </div>
      </div>
    </el-form>
    <!-- <el-dialog title="密码修改" :visible.sync="passwordChangeDialogVisible" width="30%" class="passwordChangeDialog">
      <div>请修改初始密码</div>
      <div class="passwordChange-wrap">
        <el-form
          ref="passwordChangeForm"
          :model="passwordChangeForm"
          status-icon
          :rules="passwordChangRules"
          label-width="80px"
          class="passwordChangeForm"
        >
          <el-form-item label="新密码" prop="new_password">
            <el-input
              :key="passwordChangeType"
              ref="passwordChange"
              v-model="passwordChangeForm.new_password"
              :type="passwordChangeType"
              placeholder="请输入密码"
              name="passwordChange"
              tabindex="2"
              auto-complete="on"
            />
            <span class="show-pwd change" @click="showPwdChange">
              <svg-icon :icon-class="passwordChangeType === 'password' ? 'eye' : 'eye-open'" />
            </span>
          </el-form-item>
          <el-form-item label="确认密码" prop="confirmPassword">
            <el-input
              :key="confirmPasswordChangeType"
              ref="confirmPasswordChange"
              v-model="passwordChangeForm.confirmPassword"
              :type="confirmPasswordChangeType"
              placeholder="请输入密码"
              name="confirmPasswordChange"
              tabindex="2"
              auto-complete="on"
            />
            <span class="show-pwd change" @click="shoCconfirmPwdChange">
              <svg-icon :icon-class="confirmPasswordChangeType === 'password' ? 'eye' : 'eye-open'" />
            </span>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="passwordChange-footer">
        <el-button type="primary" @click="passwordChangeSubmit">确 定</el-button>
        <el-button @click="passwordChangeDialogVisible = false">取 消</el-button>
      </div>
    </el-dialog>-->
  </div>
</template>
<script>
import { isPhone, validPassword } from '@/utils/validate'
import store from '@/store'
import { getCaptcha, updateInitPassword } from '@/api/user'
import { sm2Encode } from '@/utils/sm-encrypt-utils'
export default {
  name: 'LoginCon',
  components: {},
  data() {
    const validateUsername = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入账号'))
      } else {
        callback()
      }
    }
    const validatePassword = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入密码'))
      } else {
        callback()
      }
    }
    const validatePassIsidentical = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请再次输入密码'))
      } else if (value !== this.passwordChangeForm.new_password) {
        callback(new Error('两次输入密码不一致!'))
      } else {
        callback()
      }
    }
    return {
      captchaImg: '',
      loginForm: {
        account: '',
        password: '',
        captcha: '',
        uuid: ''
      },
      passwordChangeForm: {
        account: '',
        new_password: '',
        confirmPassword: ''
      },
      loginRules: {
        account: [{ required: true, trigger: 'blur', validator: validateUsername }],
        password: [{ required: true, trigger: 'blur', validator: validatePassword }]
      },
      passwordChangRules: {
        new_password: [{ required: true, trigger: 'blur', message: '请输入密码' }],
        confirmPassword: [{ required: true, trigger: 'blur', validator: validatePassIsidentical }]
      },
      loading: false,
      passwordType: 'password',
      passwordChangeType: 'password',
      confirmPasswordChangeType: 'password',
      redirect: undefined,
      dialogVisible: false,
      dialogRegVisible: false,
      passwordChangeDialogVisible: false,
      applyText: '需要申请账号权限方可登录',
      applyBtn: '进行申请',
      reviewId: '',
      isCaptchaShow: false,
      routerName: null,
      thirdLoginButtonList: []
    }
  },
  watch: {
    $route: {
      handler: function(route) {
        this.redirect = route.query && route.query.redirect
        // console.log('9898', this.$store.state.permission.routes, this.redirect)
      },
      immediate: true
    }
  },
  created() {
    // console.log('App Title:', this.$appConfig.version)
    this.getCaptcha()
    this.$store.dispatch('settings/changeSetting').then(res => {
      if (res && res.login_button_json_str) {
        // 第三方登录按钮
        const list = JSON.parse(res.login_button_json_str)
        console.log('list', list)
        this.thirdLoginButtonList = list
      }
    })
  },
  methods: {
    getCaptcha() {
      getCaptcha()
        .then(res => {
          if (!res.data) return
          this.isCaptchaShow = true
          this.captchaImg = res.data.img_data
          this.loginForm.uuid = res.data.uuid
        })
        .catch(() => {})
    },
    refreshCaptch() {
      this.getCaptcha()
    },

    handleLogin() {
      this.$refs.loginForm.validate(valid => {
        if (valid) {
          this.$store.commit('user/SET_permission', false)
          this.loading = true
          const params = JSON.parse(JSON.stringify(this.loginForm))
          // params.password = this.Rsa.encrypt(params.password)
          console.log('process.env.VUE_APP_ENCRYPT_KEY',this.$appConfig.VUE_APP_ENCRYPT_KEY,process.env.VUE_APP_ENCRYPT_KEY)
          params.password = sm2Encode(params.password, this.$appConfig.VUE_APP_ENCRYPT_KEY || process.env.VUE_APP_ENCRYPT_KEY)
          this.$store
            .dispatch('user/login', params)
            .then(() => {
              // this.$router.push({ path: this.redirect || '/' })
              this.$router.push({ path: '/' })
              this.loading = false
            })
            .catch(() => {
              this.getCaptcha()
              this.loading = false
            })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    passwordChangeSubmit() {
      this.$refs.passwordChangeForm.validate(valid => {
        if (valid) {
          const data = {
            account: this.loginForm.account,
            new_password: this.passwordChangeForm.new_password
          }
          // updateInitPassword(data).then(res=>{

          // })
        }
      })
    },
    showPwd() {
      if (this.passwordType === 'password') {
        this.passwordType = ''
      } else {
        this.passwordType = 'password'
      }
      this.$nextTick(() => {
        this.$refs.password.focus()
      })
    },
    showPwdChange() {
      if (this.passwordChangeType === 'password') {
        this.passwordChangeType = ''
      } else {
        this.passwordChangeType = 'password'
      }
      this.$nextTick(() => {
        this.$refs.passwordChange.focus()
      })
    },
    shoCconfirmPwdChange() {
      if (this.confirmPasswordChangeType === 'password') {
        this.confirmPasswordChangeType = ''
      } else {
        this.confirmPasswordChangeType = 'password'
      }
      this.$nextTick(() => {
        this.$refs.confirmPasswordChange.focus()
      })
    },

    /**
     * 第三方登录按钮点击事件
     * @param {*} e
     */
    handleThirdLoginClick(e) {
      if (e) {
        location.href = e.jump_address
      }
    }
  }
}
</script>
<style lang="scss" scoped>
@import '~@/styles/variables.scss';

h3 {
  margin: 0;
  padding: 0;
}

.login-con {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;

  .login-form {
    width: 85%;
    height: 1005;

    .title-container {
      position: relative;
      // padding-bottom: 68px;
      padding: 40px 0 30px 0;
      // padding: 60px 0 30px 0;

      .title {
        color: #000000;
        font-size: 30px;
        text-align: center;
      }
    }
  }

  .show-pwd {
    position: absolute;
    right: 10px;
    top: 7px;
    font-size: 16px;
    cursor: pointer;
    user-select: none;
  }

  .captchaImg {
    width: 40%;
    height: 100%;
    margin-left: 28px;
    border: 1px solid rgba(188, 188, 188, 0.6);
    cursor: pointer;

    img {
      width: 100%;
      height: 100%;
      text-align: center;
      vertical-align: middle;
      margin-top: -4px;
    }
  }

  .svg-container {
    padding-left: 15px;
    vertical-align: middle;
    width: 30px;
    display: inline-block;
    font-size: 20px;
    position: absolute;
    z-index: 1;

    &::after {
      content: '';
      position: absolute;
      display: block;
      width: 1px;
      background: #e9edee;
      height: 32px;
      top: 4px;
      right: -24px;
    }
  }

  .reg-tips {
    color: blue;
    font-size: 16px;
    text-align: center;

    .active {
      color: red;
      cursor: pointer;
    }
  }

  .passwordChange-footer {
    text-align: center;
  }
}

.passwordChangeDialog ::v-deep .el-dialog__header {
  padding-bottom: 0px;
}

.passwordChangeDialog ::v-deep .el-dialog__body {
  padding-top: 0px;
}

.passwordChangeDialog ::v-deep .el-input__suffix {
  display: none;
}
.login-form ::v-deep .el-button {
  padding: 18px 20px;
}
.passwordChange-wrap {
  padding: 26px 16px 0px;

  .change {
    top: 0px;
  }
}

.third-login-wrap {
  display: flex;
  align-items: center;
  justify-content: space-between;
  // padding-bottom: 36px;
  .third-item {
    display: flex;
    align-items: center;
  }

  .custom-button {
    font-size: 20px;
    margin-left: 4px;
  }
  .item-image {
    width: 32px;
    height: 32px;
  }
}
</style>
<style lang="scss">
$bg: #283443;
$light_gray: #fff;
$cursor: #fff;

.login-form {
  .el-form-item__content {
    display: flex;
    align-items: center;
  }

  .el-form-item {
    margin-bottom: 30px;
  }

  .el-input {
    display: inline-block;
    height: 48px;

    // width: 85%;
    input {
      padding-left: 70px;
      height: 48px;
      padding-right: 34px;
    }
  }
}
</style>
