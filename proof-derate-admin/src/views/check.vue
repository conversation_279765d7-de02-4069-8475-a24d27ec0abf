<template>
  <div />
</template>

<script>
import { getThirdAuthorization } from '@/api/user'
import { setToken } from '@/utils/auth'
import { getQueryObject } from '@/utils/index'
import Cookies from 'js-cookie'
import { asyncRoutes } from '@/router'

export default {
  data() {
    return {
      routes: this.$store.state.permission.routes
    }
  },
  async created() {
    // window.open(`${origin}/license-derate-biz/white/check?tokenName=proof-derate-web&path=licenseIssueMaking/:id&params=${params}&type=inOutherLink`, '_blank')
    const queryObject = getQueryObject()
    if (queryObject.type === 'inOutherLink') {
      await setToken(Cookies.get(queryObject.tokenName))
      this.filterRouter(asyncRoutes, queryObject.path, queryObject.params || {})
    } else {
      this.getThirdAuthorization(queryObject)
    }
  },

  methods: {
    filterRouter(routes, path, params = {}) {
      return routes.filter(route => {
        if (!route.hidden && route.children) {
          this.filterRouter(route.children, path, params)
        } else {
          if (route.path === path) {
            const _params = Object.keys(params).length > 0 ? JSON.parse(params) : {}
            this.$router.replace({
              name: route.name,
              params: _params
            })
          }
        }
      })
    },
    // 第三方登录回调换取本系统的token
    getThirdAuthorization(param) {
      const params = JSON.stringify(param)
      getThirdAuthorization(params).then((res) => {
        if (res.meta && res.meta.code === '200') {
          setToken(res.data.authorization)
          this.$router.push('/')
        }
      })
    }
  }
}
</script>
