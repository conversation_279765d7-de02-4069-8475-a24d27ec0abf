<template>
  <div class="content-wrapper investigationApply padding-10">
    <!-- <section class="content-header title-algin-midle">
      <h1>
        <i class="el-icon-arrow-left" @click="back()" />
        {{ pageTitle }}
      </h1>
      <div class>
        <el-button type="primary" @click="submitForm('form')">提交</el-button>
      </div>
    </section>-->
    <!-- <papeTitle :title-name="titleName" :is-has-back="true" @goToList="back">
      <div class>
        <el-button type="primary" @click="submitForm('form')">提交</el-button>
      </div>
    </papeTitle>-->
    <CardTitle :title-name="titleName" :ifback="true" @back="back()">
      <template>
        <el-button type="primary" @click="submitForm('form')">提交</el-button>
      </template>
    </CardTitle>
    <!-- <section class="content"> -->
    <el-card class="box-card" shadow="never" :body-style="{ padding: '30px 20px' }">
      <!-- <div slot="header" class="clearfix">
          <span>部门间协查申请信息</span>
      </div>-->
      <span class="margin-left-10 info-wrap">
        <img :src="arrow" alt />
        <span class="info-title">部门间协查申请信息</span>
      </span>
      <el-form ref="form" :model="queryForm" label-width="120px" class="el-check-form" :rules="rules">
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="业务流水号" prop="business_serial_number">
              <el-input v-model="queryForm.business_serial_number" clearable placeholder="请输入业务流水号" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="协查单号">
              <el-input v-model="queryForm.assist_serial_number" clearable disabled placeholder="请输入协查单号" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="办事人类型" prop="handle_affairs_type">
              <el-radio-group v-model="queryForm.handle_affairs_type">
                <el-radio label="NATURAL_PERSON">自然人</el-radio>
                <el-radio label="LEGAL_PERSON">法人</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item v-if="!islegalPerson" label="办事人" prop="handle_affairs_name">
              <el-input v-model="queryForm.handle_affairs_name" clearable placeholder="请输入办事人" />
            </el-form-item>
            <el-form-item v-else label="办事单位" prop="handle_affairs_name">
              <el-input v-model="queryForm.handle_affairs_name" clearable placeholder="请输入办事单位" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="!islegalPerson" :gutter="24">
          <el-col :span="12">
            <el-form-item label="证件类型" prop="handle_affairs_identity_type">
              <el-select v-model="queryForm.handle_affairs_identity_type" placeholder="请选择证件类型" class="select">
                <el-option v-for="item in cardOptions" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="证件号码" prop="handle_affairs_identity_number">
              <el-input v-model="queryForm.handle_affairs_identity_number" clearable placeholder="请输入人员证件号码" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-else :gutter="24">
          <el-col :span="12">
            <el-form-item label="证件类型" prop="legal_person_identity_type">
              <el-select v-model="queryForm.legal_person_identity_type" placeholder="请选择证件类型" class="select">
                <el-option v-for="item in legalPersonIdentityTypeList" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="证件号码" prop="handle_affairs_identity_number">
              <el-input v-model="queryForm.handle_affairs_identity_number" clearable placeholder="请输入证件号码" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="!islegalPerson" :gutter="24">
          <el-col :span="10">
            <el-form-item label="事项名称" prop="item_name">
              <el-input v-model="queryForm.item_name" clearable placeholder="请输入事项名称" :disabled="true" />
            </el-form-item>
          </el-col>
          <el-col :span="2">
            <el-button type="text" @click="dialogVisible = true">选择事项>></el-button>
          </el-col>
          <el-col :span="12">
            <el-form-item label="材料名称" prop="material_id">
              <!-- <el-input v-model="queryForm.material_id" clearable placeholder="请输入人员证件号码" /> -->
              <el-select v-model="queryForm.material_id" placeholder="请选择材料名称" class="select" @change="selectMaterialChange" @focus="materiaFocus">
                <el-option v-for="item in materialOptions" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row v-else :gutter="24">
          <el-col :span="10">
            <el-form-item label="事项名称" prop="item_name">
              <el-input v-model="queryForm.item_name" clearable placeholder="请输入事项名称" :disabled="true" />
            </el-form-item>
          </el-col>
          <el-col :span="2">
            <el-button type="text" @click="dialogVisible = true">选择事项>></el-button>
          </el-col>
          <el-col :span="12">
            <el-form-item label="材料名称" prop="material_id">
              <!-- <el-input v-model="queryForm.material_id" clearable placeholder="请输入人员证件号码" /> -->
              <el-select v-model="queryForm.material_id" placeholder="请选择材料名称" class="select" @change="selectMaterialChange" @focus="materiaFocus">
                <el-option v-for="item in materialOptions" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="islegalPerson" :gutter="24">
          <el-col :span="12">
            <el-form-item label="协查部门" prop="to_assist_org_name">
              <el-select v-model="queryForm.to_assist_org_name" placeholder="请选择协查部门" class="select" @change="selectAssistorg" @focus="assistFocus">
                <el-option v-for="item in deptOptions" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="证明目录" prop="proof_catalog_id">
              <el-select v-model="queryForm.proof_catalog_id" placeholder="请选择证明目录" class="select" @change="selectCatalog" @focus="catalogFocus">
                <el-option v-for="item in itemProofCatalogList" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="!islegalPerson" :gutter="24">
          <el-col :span="12">
            <el-form-item label="协查部门" prop="to_assist_org_name">
              <el-select v-model="queryForm.to_assist_org_name" placeholder="请选择协查部门" class="select" @change="selectAssistorg">
                <el-option v-for="item in deptOptions" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="证明目录" prop="proof_catalog_id">
              <el-select v-model="queryForm.proof_catalog_id" placeholder="请选择证明目录" class="select" @change="selectCatalog" @focus="catalogFocus">
                <el-option v-for="item in itemProofCatalogList" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <!-- <el-row :gutter="24" v-if="showProofReplaceType">
          <el-col :span="24">
            <el-form-item label="替代方式" prop="proof_replace_type" label-width="120px">
              <el-radio-group v-model="queryForm.proof_replace_type">
                <el-radio label="ARTIFICIAL" v-if="showProofReplaceType2">部门间协查</el-radio>
                <el-radio label="LICENSE_ITEM" v-if="showProofReplaceType3">电子证明</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row> -->
        <el-row :gutter="24">
          <el-col :span="24">
            <el-form-item label="协查需求描述" prop="from_demand" label-width="120px">
              <el-input v-model="queryForm.from_demand" type="textarea" :rows="4" clearable placeholder="请输入协查需求描述" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
    <!-- </section> -->
    <el-dialog title="选择事项" :visible.sync="dialogVisible" width="53%" :top="is1366 == true ? '1vh' : '15vh'">
      <div class="dialogwrap">
        <h3 class="select-title">事项名称</h3>
        <el-form ref="form1" :model="addForm" label-width="100px" class="el-check-form">
          <el-row :gutter="24" justify="center" type="flex">
            <el-col :span="18">
              <el-form-item label="事项名称">
                <el-input v-model="addForm.item_name" clearable placeholder="请输入事项名称" />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-button type="primary" @click="query">查询</el-button>
            </el-col>
          </el-row>
        </el-form>
        <custom-table ref="table" class="custom-table" :is-card-type="false" :table-data="tableData" :table-header="tableHeader" :stripe="false" :table-tools="tableTools" style="margin-top: 10px" @selection-change="selectionChange" @query="query" @refresh="query(1)">
          <template #operation="{ row }">
            <div>
              <el-button type="text" @click="detail(row)">查看</el-button>
            </div>
          </template>
        </custom-table>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="getData()">确认</el-button>
        <el-button @click="dialogVisible = false">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import CustomTable from '@/components/Element/Table'
// import { getItemPage } from '@/api/item'
// import { getitemMaterial, apply, getassistOrg } from '@/api/assistInvestigate'
import { getApplyitemMaterial, applySave, getApplyassistOrg, getApplyItemPage, getAssistSerialNumber, getItemProofCatalog, getProofCatalogById } from '@/api/assistInvestigate'
import { parseTime } from '@/utils/index.js'
import { getEnterpriseIdentityType } from '@/api/common/dict.js'
import papeTitle from '@/components/papeTitle'
import CardTitle from '@/components/CardTitle'
export default {
  name: 'ProofInvestigationHandel',
  components: { CustomTable, papeTitle, CardTitle },

  data() {
    return {
      queryForm: {
        handle_affairs_type: 'NATURAL_PERSON',
        handle_affairs_name: '',
        material_id: '',
        material_name: '',
        to_assist_org_name: '',
        to_assist_credit_code: '',
        from_assist_credit_code: '',
        from_demand: '',
        handle_affairs_identity_number: '',
        item_name: '',
        item_code: '',
        assist_time: '', // 协查发起时间
        business_serial_number: '',
        assist_serial_number: '',
        handle_affairs_identity_type: '',
        proof_catalog_id: '',
        proof_catalog_name: '',
        // proof_replace_type: 'ARTIFICIAL', //(ARTIFICIAL :部门间协查 LICENSE_ITEM :LICENSE_ITEM)
        // to_user_content: ''
      },
      addForm: {
        item_source: '',
        item_clear_status: '',
        item_name: '',
        handing_item: '',
        credit_code: '',
        division_code: '',
        item_type: '',
        page_number: 1,
        page_size: 10,
        page_direction: 'ASC',
      },
      dialogVisible: false,
      is1366: false,
      rules: {
        handle_affairs_type: [{ required: true, message: '请选择办事人类型', trigger: 'change' }],
        handle_affairs_name: [{ required: true, message: '请输入办事人/办事单位', trigger: 'blur' }],
        handle_affairs_identity_type: [{ required: true, message: '请选择协查人员证件类型', trigger: 'change' }],
        handle_affairs_identity_number: [{ required: true, message: '请输入人员证件号码/证件号码', trigger: 'blur' }],
        // from_assist_credit_code: [{ required: true, message: '请输入证件号码', trigger: 'change' }],
        item_name: [{ required: true, message: '请输入事项名称', trigger: 'blur' }],
        material_id: [{ required: true, message: '请输入材料名称', trigger: 'blur' }],
        proof_replace_type: [{ required: true, message: '请输入替代方式', trigger: 'blur' }],
        to_assist_org_name: [{ required: true, message: '请输入协查部门', trigger: 'blur' }],
        from_demand: [{ required: true, message: '请输入协查需求描述', trigger: 'blur' }],
        legal_person_identity_type: [{ required: true, message: '请选择证件类型', trigger: 'change' }],
        business_serial_number: [{ required: true, message: '请选择业务流水号', trigger: 'change' }],
        proof_catalog_id: [{ required: true, message: '请选择证明目录', trigger: 'change' }],
      },
      checkList: [],
      regionOptions: [], // 所属区域
      deptOptions: [], // 部门
      materialOptions: [], // 材料名称
      legalPersonIdentityTypeList: [], // 法人证件类型
      itemProofCatalogList: [], // 证明目录列表
      // 证件类型
      cardOptions: [
        {
          value: 'IDENTITY',
          label: '身份证',
        },
        {
          value: 'OFFICERS',
          label: '军官证',
        },
        {
          value: 'PASSPORT',
          label: '护照',
        },
        {
          value: 'EEP_HK_MACAO',
          label: '港澳通行证',
        },
        {
          value: 'OTHER_IDENTITY_LICENSE',
          label: '其他',
        },
      ],
      tableData: {
        content: [], // 表格数据

        loading: false, // 控制表格加载效果
        total: 10, // 分页查询时的总数，去掉该属性，不显示分页条
        currentPage: 1, // 当前页码
        pageSize: 10,
        isShowIndex: false,
        // height: '100px',
        maxHeight: '290px',
        multiple: false, // 是否多选 数据需要有id 属性值
        pageDirection: 'desc',
        isShowSelection: true, // 是否显示多选框，默认false
      },
      tableHeader: [
        {
          label: '事项名称',
          prop: 'item_name',
          minWidth: '100px',
        },
        {
          label: '事项编码',
          prop: 'item_code',
          minWidth: '100px',
        },
        {
          label: '实施部门',
          prop: 'impl_org_name',
          minWidth: '100px',
        },
      ],
      tableTools: [],
      pageTitle: '证明协查申请',
      islegalPerson: false, // 是否法人
      isDisable: false,
      isModify: false,
      selectData: '',
      arrow: require('@/assets/proof-exemption-admin-images/arrow.png'),
      titleName: '证明协查申请',
      proofData: {},
      showProofReplaceType: false,
      showProofReplaceType2: false,
      showProofReplaceType3: false,
    }
  },
  watch: {
    'queryForm.handle_affairs_type': {
      handler(val) {
        if (val === 'LEGAL_PERSON') {
          this.islegalPerson = true
          this.queryForm.handle_affairs_identity_type = 'OTHER_IDENTITY_LICENSE'
        } else {
          this.islegalPerson = false
          this.queryForm.handle_affairs_identity_type = ''
        }
      },
      deep: true,
      immediate: true,
    },
  },
  mounted() {
    // this.$store.state.user.orgName
    const userdata = JSON.parse(this.$store.state.user.organization)
    const accountInfo = JSON.parse(sessionStorage.getItem('accountInfo'))
    // const userdata = this.$store.state.user.userdata
    this.queryForm.from_assist_org_name = userdata.name
    // this.queryForm.business_serial_number = userdata.userInfo.orgName
    this.queryForm.from_assist_credit_code = userdata.credit_code
    this.queryForm.from_assist_user_name = accountInfo.name
    // this.queryForm.from_assist_contain = userdata.userInfo.phone
    this.addForm.credit_code = userdata.credit_code
    // this.queryForm.to_user_content = userdata.userInfo.mobile
    this.query()
    this.screenWidth()
    this.getEnterpriseIdentityType()
    this.getAssistSerialNumber()
  },
  updated() {},
  methods: {
    detail() {},
    query() {
      this.getApplyItemPage()
    },
    deleteItem() {},
    getItemProofCatalog() {
      const data = {
        material_id: this.queryForm.material_id,
      }
      getItemProofCatalog(data).then((res) => {
        if (res.data != null && res.meta.code === '200') {
          // console.log(res)
          this.itemProofCatalogList = res.data
        }
      })
    },
    getAssistSerialNumber() {
      getAssistSerialNumber().then((res) => {
        if (res.data != null && res.meta.code === '200') {
          this.queryForm.assist_serial_number = res.data
        }
      })
    },
    edit() {},
    getEnterpriseIdentityType() {
      getEnterpriseIdentityType().then((res) => {
        if (res.meta.code === '200' && res.data != null) {
          this.legalPersonIdentityTypeList = res.data
        } else {
          this.legalPersonIdentityTypeList = []
        }
      })
    },
    selectMaterialChange(value) {
      let obj = {}
      obj = this.materialOptions.find((item) => {
        return item.value === value
      })
      console.log('value', value)
      this.queryForm.material_name = obj.label
      this.getApplyassistOrg(value)
      this.getItemProofCatalog()
    },
    selectAssistorg(value) {
      console.log('value', value)
      let obj = {}
      obj = this.deptOptions.find((item) => {
        return item.value === value
      })
      // console.log(obj)
      this.queryForm.to_assist_credit_code = obj.value
      this.queryForm.to_assist_org_name = obj.label
    },

    selectCatalog(value) {
      let obj = {}
      obj = this.itemProofCatalogList.find((item) => {
        return item.value === value
      })
      this.queryForm.proof_catalog_id = obj.value
      this.queryForm.proof_catalog_name = obj.label
      // 证明目录关联替代方式
      // this.getProofCatalogById(this.queryForm.proof_catalog_id) 
    },
    getProofCatalogById(id) {
      getProofCatalogById(id).then((res) => {
        if (res.meta.code === '200' && res.data != null) {
          this.proofData = res.data
          console.log('this.proofData', this.proofData,this.proofData.proof_catalog_license_item_relation.length !== 0 , this.proofData.proof_catalog_artificial_list.length !== 0)
          if (this.proofData.proof_catalog_license_item_relation.length !== 0 && this.proofData.proof_catalog_artificial_list.length !== 0) {
            this.queryForm.proof_replace_type = ''
            this.showProofReplaceType = true
            this.showProofReplaceType2 = true
            this.showProofReplaceType3 = true
          } else if (this.proofData.proof_catalog_artificial_list.length !== 0) {
            this.queryForm.proof_replace_type = 'ARTIFICIAL'
            this.showProofReplaceType = true
            this.showProofReplaceType2 = true
          } else if (this.proofData.proof_catalog_license_item_relation.length !== 0) {
            this.queryForm.proof_replace_type = 'LICENSE_ITEM'
            this.showProofReplaceType = true
            this.showProofReplaceType3 = true
          } else {
            this.queryForm.proof_replace_type = ''
            this.showProofReplaceType = false
            this.showProofReplaceType2 = false
            this.showProofReplaceType3 = false
          }
        }
      })
    },
    getApplyassistOrg(material_id) {
      const query = {
        material_id: material_id,
      }
      getApplyassistOrg(query).then((res) => {
        if (res.meta.code === '200' && res.data != null) {
          this.deptOptions = res.data
        }
      })
    },
    getApplyItemPage() {
      this.addForm.page_number = this.tableData.currentPage
      this.addForm.page_size = this.tableData.pageSize
      this.tableData.loading = true
      const data = {
        item_name: this.addForm.item_name,
        credit_code: this.addForm.credit_code,
        page_direction: this.addForm.page_direction,
        page_number: this.addForm.page_number,
        page_size: this.addForm.page_size,
      }
      getApplyItemPage(this.addForm)
        .then((res) => {
          this.tableData.loading = false
          if (res.meta.code === '200' && res.data != null) {
            this.tableData.content = res.data.content
            this.tableData.total = Number(res.data.total_elements)
            // this.$refs.table.radioValue = ''
          } else {
            this.tableData.content = []
            // this.$refs.table.radioValue = ''
            // this.selectData = ''
          }
        })
        .catch((err) => {
          this.tableData.loading = false
        })
    },
    getApplyitemMaterial(item_code) {
      const query = {
        item_code: item_code,
      }
      this.materialOptions = []
      this.deptOptions = []
      this.queryForm.material_id = ''
      this.queryForm.material_name = ''
      this.queryForm.to_assist_org_name = ''
      this.queryForm.to_assist_credit_code = ''
      getApplyitemMaterial(query).then((res) => {
        if (res.meta.code === '200' && res.data != null) {
          this.materialOptions = res.data
          // console.log(this.materialOptions)
        }
      })
    },

    applySave() {
      this.queryForm.assist_time = parseTime(new Date(), '{y}-{m}-{d} {h}:{i}:{s}')

      applySave(this.queryForm)
        .then((res) => {
          console.log(res)
          if (res.meta.code === '200' && res.data) {
            this.$message({
              message: '添加申请成功！',
              type: 'success',
            })
            this.back()
          } else if (typeof res.data === 'string') {
            this.$message({
              message: res.meta.message,
              type: 'error',
            })
          } else {
            // console.log(res.data)
            this.$message({
              message: res.meta.message,
              type: 'error',
            })
          }
        })
        .catch((err) => {
          console.log(err)
        })
    },
    selectionChange(row) {
      this.selectData = row
    },
    getData() {
      // console.log(this.selectData)
      this.materialOptions = []
      this.deptOptions = []
      this.queryForm.material_id = ''
      this.queryForm.material_name = ''
      this.queryForm.to_assist_org_name = ''
      this.queryForm.to_assist_credit_code = ''
      if (this.selectData != '') {
        // this.tableData.content = []
        // console.log(this.selectData[0].item_name)
        this.dialogVisible = false
        this.queryForm.item_name = this.selectData[0].item_name
        this.queryForm.item_code = this.selectData[0].item_code
        this.getApplyitemMaterial(this.selectData[0].item_code)
        // // 清空列表单选选中的值
        // this.$refs.table.radioValue = ''
      } else {
        this.$message({
          message: '请选中事项',
          type: 'warning',
        })
      }
    },
    submitForm(formName) {
      console.log('this.queryForm', this.queryForm)
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.applySave()
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    back() {
      this.$router.push({
        name: 'investigationApply',
      })
    },
    materiaFocus() {
      console.log('materiaFocus')
      if (this.queryForm.item_name == '') {
        this.$message({
          message: '请先选择事项名称',
          type: 'warning',
        })
      }
    },
    assistFocus() {
      console.log('assistFocus')
      // 选择逻辑：先选事项，然后才加载材料，选择材料后才可选择协查部门和证明目录
      if (this.queryForm.item_name == '') {
        this.$message({
          message: '请先选择事项名称',
          type: 'warning',
        })
      } else if (this.queryForm.material_id == '') {
        this.$message({
          message: '请先选择材料名称',
          type: 'warning',
        })
      }
    },
    catalogFocus() {
      // 选择逻辑：先选事项，然后才加载材料，选择材料后才可选择协查部门和证明目录
      if (this.queryForm.item_name == '') {
        this.$message({
          message: '请先选择事项名称',
          type: 'warning',
        })
      } else if (this.queryForm.material_id == '') {
        this.$message({
          message: '请先选择材料名称',
          type: 'warning',
        })
      }
    },
    screenWidth() {
      if (screen.width == 1920) {
        this.is1366 = false
        console.log('1920*1080')
      } else if (screen.width == 1366) {
        console.log('1366*768')
        this.is1366 = true
      } else {
        this.is1366 = false
      }
    },
  },
}
</script>

<style scoped>
.investigationApply /deep/ .el-table .cell,
.el-table--border .el-table__cell:first-child .cell {
  text-align: center;
}

.content-header-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.content-header-title h1 {
  font-size: 24px;
}

.select {
  width: 100%;
}

.select-title {
  text-align: center;
  margin-top: 0px;
}

.investigationApply /deep/.el-dialog__body {
  padding-top: 0px;
  padding-bottom: 0px;
}

.dialogwrap {
  height: 450px;
  /* overflow: auto; */
}

/* .dialogwrap /deep/.el-dialog__body {
  padding-top: 0px;
  padding-bottom: 0px;
} */
.custom-table /deep/.el-card__body {
  /* padding-top: 0px;
  padding-bottom: 0px; */
  padding: 0px;
}

.dialogwrap .el-check-form {
  height: 50px;
}

.dialogwrap .el-check-form .el-form-item {
  margin-bottom: 0px;
}

.info-wrap {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.info-wrap img {
  width: 35px;
  height: 35px;
  margin-right: 10px;
}

.info-title {
  font-size: 20px;
  color: #333333;
}
</style>
