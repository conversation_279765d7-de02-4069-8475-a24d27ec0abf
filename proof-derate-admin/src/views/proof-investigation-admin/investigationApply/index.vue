<template>
  <div class="content-wrapper investigationApple padding-10">
    <CardTitle :title-name="titleName">
      <template>
        <el-button type="primary" v-permission="'assist:apply:proof:add'" @click="apply()">协查申请</el-button>
      </template>
    </CardTitle>
    <!-- <section class="content"> -->
    <el-card class="box-card">
      <el-form ref="form" :model="queryForm" label-width="120px" class="el-check-form" :rules="rules">
        <el-row :gutter="24" justify="center" type="flex">
          <el-col :span="10">
            <el-form-item label="证明名称">
              <el-input v-model="queryForm.material_name" clearable placeholder="请输入证明名称" />
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="办事人/办事单位">
              <el-input v-model="queryForm.handle_affairs_name" clearable placeholder="请输入办事人/办事单位" />
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-button type="primary" @click="getApplyListbySearch()">查询</el-button>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="10">
            <el-form-item label="协查状态">
              <el-checkbox-group v-model="checkList">
                <el-checkbox label="WAIT">待协查</el-checkbox>
                <el-checkbox label="SUCCESS">符合</el-checkbox>
                <el-checkbox label="FAIL">不符合</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div style="color: #888; padding:20px 10px 0" class="dashed-line">
        共
        <span class="text-red">{{ tableData.total }}</span>条符合查询条件
        <span v-if="tableData.content.length!=0">，以下是第1至第{{ tableData.content.length }}项</span>
      </div>
      <custom-table
        ref="table"
        :is-card-type="false"
        :table-data="tableData"
        :table-header="tableHeader"
        :stripe="false"
        :table-tools="tableTools"
        style="margin-top: 10px"
        @query="query"
        @refresh="query(1)"
      >
        <!-- <template #index="{$index}">{{ $index+1 }}</template> -->
        <template #operation="{ row }">
          <div>
            <el-button type="text" @click="detail(row)">查看</el-button>
          </div>
        </template>
        <template #assist_audit_result="{ row }">
          <div>
            <span v-if="row.assist_audit_result==='WAIT'">待协查</span>
            <span v-if="row.assist_audit_result==='SUCCESS'">符合</span>
            <span v-if="row.assist_audit_result==='FAIL'">不符合</span>
          </div>
        </template>
      </custom-table>
    </el-card>
    <!-- </section> -->
  </div>
</template>

<script>
import CustomTable from '@/components/Element/Table'
// import { getList } from '@/api/assistInvestigate'
import { getApplyList } from '@/api/assistInvestigate'
import papeTitle from '@/components/papeTitle'
import CardTitle from '@/components/CardTitle'
export default {
  name: 'ProofInvestigationHandel',
  components: {
    CustomTable,
    papeTitle,
    CardTitle
  },

  data() {
    return {
      queryForm: {
        material_name: '', // 证明名称
        handle_affairs_name: '',
        assist_result: '', // WAIT :待审核 SUCCESS :审核通过 FAIL :审核失败
        page_direction: 'DESC',
        page_number: 1,
        page_size: 10,
        toAssistCreditCode: '',
        fromAssistCreditCode: ''
      },
      rules: {},
      checkList: [],
      numberOfElements: '',
      tableData: {
        border: false,
        content: [], // 表格数据
        loading: false, // 控制表格加载效果
        total: 0, // 分页查询时的总数，去掉该属性，不显示分页条
        currentPage: 1, // 当前页码
        pageSize: 10,
        isShowIndex: true,
        pageDirection: 'desc',
        isShowSelection: false // 是否显示多选框，默认false
      },
      tableHeader: [
        // { label: '序号', prop: 'index', minWidth: '80px', slot: 'index' },
        { label: '证明名称', prop: 'material_name', align: 'left', minWidth: '160px' }, // 配置slot属性，可支持使用插槽
        {
          label: '事项名称',
          prop: 'item_name',
          align: 'left',
          minWidth: '100px'
        },
        { label: '办事人/办事单位', prop: 'handle_affairs_name', align: 'left', minWidth: '120px' },
        { label: '协查发起部门', prop: 'from_assist_org_name', align: 'left', width: '120px', fixed: 'right' },
        {
          label: '协查发起时间',
          prop: 'assist_time',
          align: 'left',
          minWidth: '180px'
        },
        // isHeaderslot 与 prop 不要同名
        {
          label: '协查状态',
          prop: 'assist_audit_result',
          width: '120px',
          fixed: 'right',
          align: 'left',
          slot: 'assist_audit_result'
        },
        {
          label: '协查处理部门',
          prop: 'audit_org_name',
          width: '250px',
          fixed: 'right',
          align: 'left',
        },
        {
          label: '操作',
          prop: 'operation',
          width: '120px',
          fixed: 'right',
          align: 'left',
          slot: 'operation'
        }
      ],
      tableTools: [],
      titleName: '证明协查申请'
    }
  },

  mounted() {
    this.query()

    // console.log('userdata',userdata)
  },

  methods: {
    detail(row) {
      this.$router.push({
        name: 'investigationDetail',
        query: {
          // data: JSON.stringify(row),
          type: 'apply',
          id: row.id
        }
      })
    },
    query() {
      this.getApplyList()
    },
    getApplyListbySearch() {
      this.tableData.currentPage = 1
      this.tableData.pageSize = 10
      this.getApplyList()
    },
    apply() {
      this.$router.push({
        name: 'investigationapplyAdd'
      })
    },
    getApplyList() {
      // const userdata = this.$store.state.user.userdata
      const userdata = JSON.parse(this.$store.state.user.organization)
      this.queryForm.page_number = this.tableData.currentPage
      this.queryForm.page_size = this.tableData.pageSize
      this.tableData.loading = true
      this.queryForm.fromAssistCreditCode = userdata.credit_code
      this.queryForm.assist_result = this.checkList.join(',')
      getApplyList(this.queryForm)
        .then(res => {
          // console.log(res)
          this.tableData.loading = false
          if (res.meta.code === '200' && res.data != null) {
            this.tableData.content = res.data.content
            this.tableData.total = Number(res.data.total_elements)
            this.numberOfElements = res.data.numberOfElements
          }
        })
        .catch(err => {
          this.tableData.loading = false
        })
    }
  }
}
</script>

<style scoped>
.investigationApple /deep/ .el-table .cell,
.el-table--border .el-table__cell:first-child .cell {
  /* text-align: center; */
}
.top {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.top h1 {
  font-size: 24px;
}
</style>
