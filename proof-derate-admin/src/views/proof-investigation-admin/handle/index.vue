<template>
  <div class="content-wrapper investigationHandle padding-10">
    <!-- <section class="content-header">
      <h1>证明协查处理</h1>
    </section>-->
    <!-- <papeTitle :title-name="titleName" :is-has-back="false" /> -->
    <CardTitle :title-name="titleName">
      <template />
    </CardTitle>
    <!-- <section class="content-header"> -->
    <el-card class="box-card" shadow="never">
      <el-form ref="form" :model="queryForm" label-width="120px" class="el-check-form" :rules="rules">
        <el-row :gutter="24" justify="center" type="flex">
          <el-col :span="10">
            <el-form-item label="业务流水号">
              <el-input v-model="queryForm.business_serial_number" clearable placeholder="请输入业务流水号" />
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="协查单号">
              <el-input v-model="queryForm.assist_serial_number" clearable placeholder="请输入协查单号" />
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-button type="primary" @click="getHandleListBySearch()">查询</el-button>
          </el-col>
        </el-row>
        <el-row :gutter="24" justify="center" type="flex">
          <el-col :span="10">
            <el-form-item label="证件号码">
              <el-input v-model="queryForm.handle_affairs_identity_number" clearable placeholder="请输入证件号码" />
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="发起时间">
              <el-date-picker v-model="operateTime" value-format="yyyy-MM-dd HH:mm:ss" type="datetimerange"
                style="width:100%" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
              <!-- <el-input v-model="queryForm.assist_time" clearable placeholder="请输入协查单号" /> -->
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <!-- <el-button type="primary" @click="getHandleListBySearch()">查询</el-button> -->
          </el-col>
        </el-row>
        <el-row :gutter="24" justify="center" type="flex">
          <el-col :span="10">
            <el-form-item label="证明名称">
              <el-input v-model="queryForm.material_name" clearable placeholder="请输入证明名称" />
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="办事人/办事单位">
              <el-input v-model="queryForm.handle_affairs_name" clearable placeholder="请输入办事人/办事单位" />
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <!-- <el-button type="primary" @click="getHandleListBySearch()">查询</el-button> -->
          </el-col>
        </el-row>
      </el-form>
      <div style="color: #888; padding:20px 10px 0" class="dashed-line">
        共
        <span class="text-red">{{ tableData.total }}</span>条符合查询条件
        <span v-if="tableData.content.length != 0">，以下是第1至第{{ tableData.content.length }}项</span>
      </div>
      <custom-table ref="table" :is-card-type="false" :table-data="tableData" :table-header="tableHeader"
        :stripe="false" :table-tools="tableTools" style="margin-top: 10px" @query="query" @refresh="query(1)"
        @sort-change="sortChange">
        <!-- <template #index="{$index}">{{ $index+1 }}</template> -->
        <template #operation="{ row }">
          <div>
            <el-button type="text" @click="detail(row)">处理</el-button>
          </div>
        </template>
        <template #residual_treatment_limit="{ row }">
          <div
            v-if="(row.residual_treatment_limit > 0 || row.residual_treatment_limit == 0) && (row.residual_treatment_hour_limit > 0 || row.residual_treatment_hour_limit == 0)"
            class="timeLimit">
            <span
              :class="(row.residual_treatment_limit < 30 && (row.residual_treatment_hour_limit == 0)) || (row.residual_treatment_limit === 30 && (row.residual_treatment_hour_limit == 0)) ? 'warning' : ''">{{
                row.residual_treatment_hour_limit + '小时' }}{{ row.residual_treatment_limit + '分钟' }}</span>
          </div>
          <div v-if="row.residual_treatment_limit < 0 || row.residual_treatment_hour_limit < 0" class="timeLimit">
            <span class="warning">{{ '超过' + Math.abs(row.residual_treatment_hour_limit) + '小时' }}{{
              Math.abs(row.residual_treatment_limit) + '分钟' }}</span>
          </div>
        </template>
        <template #assist_audit_result="{ row }">
          <div>
            <span v-if="row.assist_audit_result === 'WAIT'">待协查</span>
          </div>
        </template>
      </custom-table>
    </el-card>
    <!-- </section> -->
  </div>
</template>

<script>
import CustomTable from '@/components/Element/Table'
// import { getList } from '@/api/assistInvestigate'
import { getHandleList } from '@/api/assistInvestigate'
import papeTitle from '@/components/papeTitle'
import CardTitle from '@/components/CardTitle'
export default {
  name: 'ProofInvestigationHandel',
  components: {
    CustomTable,
    papeTitle,
    CardTitle
  },
  data() {
    return {
      queryForm: {
        business_serial_number: '',
        assist_serial_number: '',
        handle_affairs_identity_number: '',

        material_name: '', // 证明名称
        handle_affairs_name: '', // 办事人或者办事单位名称
        assist_result: 'WAIT', // WAIT :待审核 SUCCESS :审核通过 FAIL :审核失败
        sort_direction: 'DESC',
        toAssistCreditCode: '',
        fromAssistCreditCode: '',
        page_number: 1,
        page_size: 10
      },
      operateTime: [], // 操作时间
      rules: {},
      numberOfElements: '',
      tableData: {
        border: false,
        content: [], // 表格数据
        loading: false, // 控制表格加载效果
        total: 0, // 分页查询时的总数，去掉该属性，不显示分页条
        currentPage: 1, // 当前页码
        pageSize: 10,
        // isShowIndex: true,
        isShowIndex: true,
        pageDirection: 'desc',
        rowKey: 'rowId',//行数据的 Key，用来优化 Table 的渲染
        isShowSelection: false // 是否显示多选框，默认false
      },
      tableHeader: [
        // { label: '序号', prop: 'index', minWidth: '80px', slot: 'index' },
        { label: '证明名称', prop: 'material_name', align: 'left', minWidth: '140px' }, // 配置slot属性，可支持使用插槽
        {
          label: '事项名称',
          prop: 'item_name',
          minWidth: '100px'
        },
        { label: '办事人/办事单位', prop: 'handle_affairs_name', align: 'left', minWidth: '120px' },
        { label: '协查发起部门', prop: 'from_assist_org_name', width: '120px', align: 'left', fixed: 'right' },
        {
          label: '协查发起时间',
          prop: 'assist_time',
          align: 'left',
          minWidth: '100px',
          sortable: 'custom', // 如果需要后端排序，需将sortable设置为custom,同时在 Table 上监听sort-change事件
        },
        {
          label: '剩余处置时间',
          prop: 'residual_treatment_limit',
          minWidth: '80px',
          align: 'left',
          slot: 'residual_treatment_limit'
          // formatter: (row, col, val) => {
          //   if (val != null) {
          //     return val + '分钟'
          //   } else {
          //     return val
          //   }
          // }
        },
        // isHeaderslot 与 prop 不要同名
        {
          label: '协查状态',
          prop: 'assist_audit_result',
          width: '120px',
          fixed: 'right',
          align: 'left',
          slot: 'assist_audit_result'
        },
        {
          label: '协查处理部门',
          prop: 'audit_org_name',
          width: '250px',
          fixed: 'right',
          align: 'left',
        },
        {
          label: '操作',
          prop: 'operation',
          width: '120px',
          fixed: 'right',
          align: 'left',
          slot: 'operation'
        }
      ],
      tableTools: [],
      titleName: '证明协查处理'
    }
  },

  mounted() {
    this.query()
  },
  methods: {
    sortChange(column, prop, order) {
      // console.log(column, prop, order)
      if (column.order == null) {
        this.queryForm.sort_direction = 'DESC'
      } else if (column.order == 'descending') {
        this.queryForm.sort_direction = 'DESC'
      }
      else if (column.order == 'ascending') {
        this.queryForm.sort_direction = 'ASC'
      }
      this.getHandleList()
    },
    detail(row) {
      this.$router.push({
        name: 'investigationServicesDetail',
        query: {
          type: 'handle',
          // data: JSON.stringify(row),
          id: row.id
        }
      })
    },
    query() {
      this.getHandleList()
    },
    getHandleListBySearch() {
      this.tableData.currentPage = 1
      this.tableData.pageSize = 10
      this.getHandleList()
    },
    getHandleList() {
      const userdata = JSON.parse(this.$store.state.user.organization)
      this.queryForm.page_number = this.tableData.currentPage
      this.queryForm.page_size = this.tableData.pageSize
      this.queryForm.toAssistCreditCode = userdata.credit_code
      this.tableData.loading = true
      if (this.operateTime && this.operateTime.length !== 0) {
        this.queryForm.assist_time_start_date = this.operateTime[0]
        this.queryForm.assist_time_end_date = this.operateTime[1]
      } else {
        this.queryForm.assist_time_start_date = ''
        this.queryForm.assist_time_end_date = ''
      }
      // this.queryForm.assist_result = this.checkList.join(',')
      getHandleList(this.queryForm)
        .then(res => {
          this.tableData.loading = false
          if (res.meta.code === '200' && res.data != null) {
            
            this.tableData.content = res.data.content
            // 多个审核数据的协查单，id是同一个 设置rowID避免表单中数据重复报错
            this.tableData.content.forEach((i,index)=>{
              i.rowId = i.id +'-' +index
            })
            this.tableData.total = Number(res.data.total_elements)
            this.numberOfElements = res.data.numberOfElements
          }
        })
        .catch(err => {
          this.tableData.loading = false
        })
    }
  }
}
</script>
<style lang="scss">
@import '@/styles/element-ui.scss';
</style>
<style scoped>
.investigationHandle /deep/ .el-table .cell,
.el-table--border .el-table__cell:first-child .cell {
  /* text-align: center; */
}

.timeLimit {
  display: flex;
  align-items: flex-start;
  justify-content: center;
}

.timeLimit span {
  margin-right: 5px;
}

.warningImg {
  height: 25px;
  width: 25px;
}

.warning {
  color: crimson;
}
</style>
