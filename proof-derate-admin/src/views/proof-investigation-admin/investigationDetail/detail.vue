<template>
    <div v-loading="pageLoading" class="content-wrapper investigationDetail padding-10">
      <CardTitle :title-name="detailTitle" :ifback="true" @back="back()">
        <template>
          <el-button v-if="!onlyshowExaminedTable" type="primary" @click="submitForm('form1')">提交</el-button>
        </template>
      </CardTitle>
      <!-- <section class="content"> -->
      <el-form ref="form" :model="queryForm" label-width="180px" class="el-check-form" :rules="rules" :disabled="true">
        <el-card class="box-card" shadow="never" :body-style="{ padding: '30px 20px' }">
          <span class="margin-left-10 info-wrap">
            <img :src="arrow" alt />
            <span class="info-title">部门协查申请信息</span>
          </span>
          <!-- justify="center" type="flex" -->
          <h3 v-if="assistDataTitle !== ''" style="text-align: center">{{ assistDataTitle }}</h3>
          <el-descriptions class="descriptions" title="" :column="2" border>
            <el-descriptions-item :label-style="{ width: '140px' }">
              <template slot="label">业务流水号</template>
              {{ queryForm.business_serial_number }}
            </el-descriptions-item>
            <el-descriptions-item :label-style="{ width: '140px' }">
              <template slot="label">协查单号</template>
              {{ queryForm.assist_serial_number }}
            </el-descriptions-item>
            <el-descriptions-item :label-style="{ width: '140px' }">
              <template v-if="!islegalPerson" slot="label">办事人</template>
              <template v-else slot="label">办事单位</template>
              {{ queryForm.handle_affairs_name }}
            </el-descriptions-item>
            <el-descriptions-item :label-style="{ width: '140px' }">
              <template v-if="!islegalPerson" slot="label">证件类型</template>
              <template v-else slot="label">证件类型</template>
              <span v-if="!islegalPerson">{{ queryForm.handle_affairs_identity_type ? cardOptions.find((i) => i.value === queryForm.handle_affairs_identity_type).label : queryForm.handle_affairs_identity_type }}</span>
              <span v-else>{{ queryForm.legal_person_identity_type ? legalPersonIdentityTypeList.find((i) => i.value === queryForm.legal_person_identity_type).label : legal_person_identity_type }}</span>
            </el-descriptions-item>
            <el-descriptions-item :label-style="{ width: '140px' }">
              <template slot="label">证件号码</template>
              {{ queryForm.handle_affairs_identity_number }}
            </el-descriptions-item>
            <el-descriptions-item :label-style="{ width: '140px' }">
              <template slot="label">材料名称</template>
              {{ queryForm.material_name }}
            </el-descriptions-item>
            <el-descriptions-item :label-style="{ width: '140px' }">
              <template slot="label">事项名称</template>
              {{ queryForm.item_name }}
            </el-descriptions-item>
            <el-descriptions-item :label-style="{ width: '140px' }">
              <template slot="label">事项编码</template>
              {{ queryForm.item_code }}
            </el-descriptions-item>
            <el-descriptions-item :label-style="{ width: '140px' }">
              <template slot="label">证明目录</template>
              {{ queryForm.proof_catalog_name }}
            </el-descriptions-item>
            <el-descriptions-item :label-style="{ width: '140px' }">
              <template slot="label">协查需求描述</template>
              {{ queryForm.from_demand }}
            </el-descriptions-item>


            <el-descriptions-item v-for="(i, key) in assistItemList" :key="key+i.key" :span="i.colsNum" :label-style="{ width: '140px' }">
              <template slot="label">{{ i.key }}</template>
              {{ i.value }}
            </el-descriptions-item>
          </el-descriptions>
          <el-row v-if="assistDataBottom !== ''" :gutter="24" type="flex" class="margin-10">
            <el-col :span="20">
              <span class="readme">{{ assistDataBottom }}</span>
            </el-col>
          </el-row>
        </el-card>
        <el-card class="box-card" shadow="never" :body-style="{ padding: '0px 20px' }">
          <span class="margin-left-10 info-wrap">
            <img :src="arrow" alt />
            <span class="info-title">协查人发起信息</span>
          </span>
          <el-descriptions class="descriptions" title :column="2" border>
            <el-descriptions-item :label-style="{ width: '140px' }">
              <template slot="label">协查发起人</template>
              {{ queryForm.from_assist_user_name }}
            </el-descriptions-item>
            <el-descriptions-item :label-style="{ width: '140px' }">
              <template slot="label">联系方式</template>
              {{ queryForm.from_assist_contain }}
            </el-descriptions-item>
            <el-descriptions-item :label-style="{ width: '140px' }">
              <template slot="label">发起部门</template>
              {{ queryForm.from_assist_org_name }}
            </el-descriptions-item>
            <el-descriptions-item :label-style="{ width: '140px' }">
              <template slot="label">发起时间</template>
              {{ queryForm.assist_time }}
            </el-descriptions-item>
          </el-descriptions>
        </el-card>
      </el-form>
      <!-- v-if="isshowResult || isHandle" -->
      <el-card class="box-card wrap-examine" shadow="never" :body-style="{ padding: '30px 20px' }">
        <span class="margin-left-10 info-wrap" style="justify-content: space-between">
          <div style="display: flex; align-items: center">
            <img :src="searchImg" alt />
            <span class="info-title">协查处理结果</span>
          </div>
          <span v-if="issuePLicense_way === 'MANUALLY_GENERATED'&&this.detailType==='handle'" v-permission="'assist:service:handle:issue'" class="isuss" @click="toisuss">开具电子证明</span>
          <span v-if="issuePLicense_way === 'MANUALLY_GENERATED'&&this.detailType==='archives'" v-permission="'assist:service:archives:issue'" class="isuss" @click="toisuss">开具电子证明</span>
        </span>
        <div style="margin: 0px 24px 20px 10px" v-if="examineLevelshow || showStep">
          <el-steps :active="handleActive">
            <el-step :title="i.level_desc" v-for="(i, key) in auditRelationListCopy" :key="key" @click.native="onStepClick(key)"></el-step>
            <!-- <el-step title="镇街单位" @click.native="onStepClick(key)"></el-step>
            <el-step title="镇街政府" @click.native="onStepClick(key)"></el-step> -->
          </el-steps>
        </div>
        <el-form v-show="!onlyshowExaminedTable" v-if="isshowResult && isHandle" :key="key" ref="form1" :model="nowAuditRelation" label-width="180px" class="el-check-form2" :rules="rules">
          <el-card class="box-card" shadow="never" :body-style="{ 'padding-top': '0px' }">
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="协查处理人" label-width="160px">
                  <el-input v-model="accountInfo.name" clearable placeholder="请输入协查发起人" :disabled="true" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="协查部门" label-width="160px">
                  <el-input v-model="accountInfo.department" clearable placeholder="请输入协查发起人" :disabled="true" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24">
              <el-col :span="12" v-if="examineLevelshow">
                <el-form-item label="下级审批部门所属区划" label-width="160px">
                  <AdministrativeDivisionCascader ref="AdministrativeDivisionSelect" :divisionCodeLsit="auditDiviCode" :edit-disabled="false" :collapseTags="false" :optionProps="optionProps" @setDivisionCodeAndName="setDivisionCodeAndName" />
                </el-form-item>
              </el-col>
              <el-col :span="12" v-if="examineLevelshow">
                <el-form-item label="下级审批部门" label-width="160px" prop="auditOrgCode">
                  <el-select v-model="nowAuditRelation.auditOrgCode" multiple placeholder="请选择协查部门" class="select" @change="auditOrgChange">
                    <el-option v-for="item in subAuditObj" :key="item.audit_org_code" :label="item.audit_org_name" :value="item.audit_org_code"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item ref="assist_audit_result" label="协查结果" prop="assist_audit_result" label-width="160px">
                  <!-- <el-input v-if="!isHandle" v-model="nowAuditRelation.assist_audit_result" clearable
                    placeholder="请输入协查发起人" :disabled="true" /> -->
                  <el-radio-group v-model="nowAuditRelation.assist_audit_result" @change="setNowAuditRelationStatus">
                    <el-radio label="SUCCESS">符合</el-radio>
                    <el-radio label="FAIL">不符合</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col v-if="isHandle" :span="12">
                <el-form-item label="附件" label-width="160px">
                  <div class="filewrap">
                    <div>
                      <el-button v-if="isHandle && (nowAuditRelation.assist_attachment_name == '' || nowAuditRelation.assist_attachment_name == null)" type="primary" @click="dialogVisible = true">附件上传</el-button>
                    </div>
                    <div v-if="isHandle && nowAuditRelation.assist_attachment_name !== '' && nowAuditRelation.assist_attachment_name !== null">
                      <i class="el-icon-paperclip" />
                      <el-button type="text" @click="downFile()">{{ nowAuditRelation.assist_attachment_name }}</el-button>
                      <i class="el-icon-close" style="cursor: pointer" @click="cancle()" />
                    </div>
                    <span v-if="isHandle && (nowAuditRelation.assist_attachment_name == '' || nowAuditRelation.assist_attachment_name == null)" class="uploadtext">支持上传后缀名为docx、doc、pdf、jpg、png格式文件大小不超过1m</span>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24">
              <el-col :span="24">
                <el-form-item ref="audit_suggestion" label="协查意见" prop="audit_suggestion" label-width="160px">
                  <el-input v-model="nowAuditRelation.audit_suggestion" type="textarea" :rows="4" clearable placeholder="请输入协查意见" :disabled="isHandle === false ? true : false" />
                </el-form-item>
              </el-col>
            </el-row>
          </el-card>
        </el-form>
        <div v-for="(itemList, key) in haxAuditRelation" :key="key">
          <!-- 匹配中当前审核层级 -->
          <div v-if="key == handleActive">
            <div v-if="onlyshowExaminedTable">
              <div v-for="(item, itemkey) in itemList" :key="itemkey">
                <el-descriptions v-if="item.need_audit" style="margin: 0px 24px 20px 0px" class="descriptions" title :column="2" border>
                  <el-descriptions-item :label-style="{ width: '140px' }">
                    <template slot="label">协查处理人</template>
                    {{ item.to_user_name }}
                  </el-descriptions-item>
                  <el-descriptions-item :label-style="{ width: '140px' }">
                    <template slot="label">协查部门</template>
                    {{ item.audit_org_name }}
                  </el-descriptions-item>
                  <el-descriptions-item :label-style="{ width: '140px' }">
                    <template slot="label">审核结果</template>
                    <span v-if="item.assist_audit_result === 'SUCCESS'">通过</span>
                    <span v-if="item.assist_audit_result === 'FAIL'">不通过</span>
                    <span v-if="item.assist_audit_result === 'WAIT'" style="color: red">待审核</span>
                    <span v-if="item.assist_audit_result === 'NOT_WAIT'">不需要审核</span>
                    <!-- {{ item.assist_audit_result === 'SUCCESS' ? '通过' : item.assist_audit_result === 'WAIT' ? '待审核' : '不通过'
                  }} -->
                  </el-descriptions-item>
                  <el-descriptions-item :label-style="{ width: '140px' }">
                    <template slot="label">附件</template>
                    <span @click="downloadFile(item)" :style="{ color: $store.state.settings.skinColor, cursor: 'pointer' }">{{ item.assist_attachment_name }}</span>
                  </el-descriptions-item>
                  <el-descriptions-item :label-style="{ width: '140px' }" v-if="item.auditDiviName">
                    <template slot="label">下级审批部门所属区划</template>
                    {{ item.auditDiviName || '无' }}
                  </el-descriptions-item>
                  <el-descriptions-item :label-style="{ width: '140px' }" v-if="item.auditOrgName">
                    <template slot="label">下级审批部门</template>
                    {{ item.auditOrgName || '无' }}
                  </el-descriptions-item>
                  <el-descriptions-item :label-style="{ width: '140px' }">
                    <template slot="label">协查意见</template>
                    {{ item.audit_suggestion }}
                  </el-descriptions-item>
                  <el-descriptions-item :label-style="{ width: '140px' }">
                    <template slot="label">协查时间</template>
                    <span v-if="item.assist_audit_result !== 'NOT_WAIT'">{{ item.audit_time }}</span>
                  </el-descriptions-item>
                </el-descriptions>
              </div>
            </div>
          </div>
        </div>
        <div style="text-align: center"><el-button type="primary" v-if="liccItemAuthCode" @click="downloadliccItemAuthCode">下载电子证明</el-button></div>
      </el-card>
      <!-- </section> -->
      <el-dialog title="上传附件" :visible.sync="dialogVisible" width="40%" :before-close="cancle" center>
        <div class>
          <p>请选择您要上传的附件</p>
          <el-row :gutter="24" justify="center" align="middle" type="flex">
            <el-col :span="20">
              <el-input v-model="nowAuditRelation.assist_attachment_name" readonly placeholder />
            </el-col>
            <el-col :span="4">
              <el-upload :auto-upload="false" :show-file-list="false" class="upload-demo" action :on-change="handleChange" :file-list="fileList" accept=".doc, .pdf, .jpg, .png, .docx">
                <el-button type="primary">浏览</el-button>
              </el-upload>
            </el-col>
          </el-row>
          <p class="tip"><i class="el-icon-info" /> 温馨提示：请选择以doc/docx/pdf/jpg/png为后缀名的文件且上传的文件不能超过1m！</p>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button type="primary" @click="getFile()">上传</el-button>
          <el-button @click="cancle()">取 消</el-button>
        </span>
      </el-dialog>
    </div>
  </template>

  <script>
  import { download, downloadAttachment } from '@/api/assistAttachment'
  import { uploadFile } from '@/api/common/dict'
  // import { handle } from '@/api/assistInvestigate'
  import { handleSave, archivesSave, applyView, archivesView, handleView } from '@/api/assistInvestigate'
  import { dataURLtoDownload, getIsWhitelist, getFileType } from '@/utils/index.js'
  import { getEnterpriseIdentityType, getCurrentAccount } from '@/api/common/dict.js'
  import { validPrefix } from '@/utils/validate'
  import CardTitle from '@/components/CardTitle'
  import AdministrativeDivisionCascader from '@/components/AdministrativeDivisionCascader/index2.vue'
  import { downloadFile } from '@/api/common/download'
  import { isPermission } from '@/utils/index'
  export default {
    components: {
      CardTitle,
      AdministrativeDivisionCascader,
    },
    data() {
      return {
        queryForm: {
          business_serial_number: '',
          assist_serial_number: '',
          handle_affairs_name: '',
          clerkDep: '',
          from_assist_org_name: '',
          assist_time: '',
          material_name: '',
          proof_catalog_name: '',
          to_assist_org_name: '',
          handle_affairs_identity_number: '',
          from_assist_user_name: '',
          to_user_name: '',
          from_demand: '',
          // handle_affairs_identity_number: '',
          item_name: '',
          item_code: '',
          handle_affairs_identity_type: '',
          from_assist_contain: '',
          legal_person_identity_type: '',
        },
        addForm: {
          audit_result: 'SUCCESS',
          audit_suggestion: '',
          audit_time: '',
          fileName: '',
          raw: '',
          audit_id: '',
          to_user_id: '',
          to_user_name: '',
          to_assist_org_name: '',
          to_assist_credit_code: '',
          examineDept: '',
          audit_record: false,
        },
        // 多层审核对象
        auditRelationList: [],
        // 当前审核对象
        nowAuditRelation: {
          assist_attachment_name: '',
          raw: '',
          hasOrgName: '',
          auditOrgCode: [],
          assist_audit_result: '',
          audit_record: false,
        },
        assist_data_list: [], // 协查内容
        assistItemList:[],
        dialogVisible: false,
        islegalPerson: false,
        pageLoading: false,
        rules: {
          audit_suggestion: [{ required: true, message: '请输入协查意见', trigger: 'change' }],
          assist_audit_result: [{ required: true, message: '请选择协查结果', trigger: 'change' }],
          auditOrgCode: [{ required: true, message: '请选择下级审核部门', trigger: 'blur' }],
        },
        isshowResult: false, // 是否显示协查结果
        isHandle: false, // 是否协查申请
        onlyshowExaminedTable: false,

        fileList: [],
        // IDENTITY :身份证 OFFICERS :军官证 PASSPORT :护照 EEP_HK_MACAO :港澳通行证 OTHER_IDENTITY_LICENSE :其他
        cardOptions: [
          {
            value: 'IDENTITY',
            label: '身份证',
          },
          {
            value: 'OFFICERS',
            label: '军官证',
          },
          {
            value: 'PASSPORT',
            label: '护照',
          },
          {
            value: 'EEP_HK_MACAO',
            label: '港澳通行证',
          },
          {
            value: 'OTHER_IDENTITY_LICENSE',
            label: '其他',
          },
        ],
        legalPersonIdentityTypeList: [], // 法人证件类型
        fileBase64: {
          name: '',
          base64: '',
        },
        detailTitle: '协查处理',
        whitelist: ['doc', 'docx', 'pdf', 'jpg', 'png'],
        doubleData: [],
        singleData: [],
        account_info: [],
        handleActive: 0,
        nowExamineLeve: 0, // 当前审核的层级
        divisionCode: '',
        accountInfo: {
          name: '',
          department: '',
        },
        optionProps: {
          value: 'value',
          label: 'label',
          children: 'children',
          checkStrictly: true,
          expandTrigger: 'hover',
          emitPath: false,
          multiple: true, // 是否多选
        },
        examineLevelshow: true, // 多级审核是否展示
        showStep: true,
        subAuditObj: [], // 下级审核部门
        subAuditObjCopy: [],
        sub_audit_obj_1: {}, //一级审核对象
        sub_audit_obj_2: {}, //二级审核对象
        sub_audit_obj_3: {}, //三级审核对象
        auditOrgCode: [],
        auditDiviCode: [],
        haxAuditRelation: [], // 已审核对象
        auditResult: '', // 审核进度（SUCCESS:已完成）
        assistDataListShow: false,
        assistDataTitle: '',
        assistDataBottom: '',
        arrow: require('@/assets/proof-exemption-admin-images/arrow.png'),
        searchImg: require('@/assets/proof-exemption-admin-images/u133.png'),
        addFile: require('@/assets/proof-exemption-admin-images/addFile.png'),
        liccItemAuthCode: null, // 开具的电子证明编码
        issuePLicense_way: '', //
        auditRelationListCopy: '',
        implementCode:'',
      }
    },
    props: {
      // 判断从档案或者处理页进来的路由类型
      detailType: {
        type: String,
        default: () => {
          return ''
        }
      },
    },
    mounted() {
      // this.initData()
      this.getDatabyid()
      this.getEnterpriseIdentityType()
      this.getCurrentAccount()
    },
    beforeRouteEnter(to, from, next) {
      // 'handle'为证明协查处理 'archives' 为证明协查档案 剩余是申请协查
      if (to.query.type === 'handle') {
        to.meta.nav_key = 'proof_investigation_admin'
      } else if (to.query.type === 'archives') {
        to.meta.nav_key = 'investigation_archives'
      } else {
        to.meta.nav_key = 'investigationApply'
      }
      next((vm) => {})
    },
    methods: {
      isPermission,
      getDatabyid() {
        if (this.$route.query.type === 'apply') {
          applyView(this.$route.query.id).then((res) => {
            if (res.meta.code === '200' && res.data != null) {
              this.setAuditRelation(res)
              // console.log(res.data)
              // this.initData(res.data)
            }
          })
        } else if (this.$route.query.type === 'archives') {
          console.log('this.$route.query.type', this.$route.query.type)
          archivesView(this.$route.query.id).then((res) => {
            if (res.meta.code === '200' && res.data != null) {
              this.setAuditRelation(res)
              // console.log(res.data)
              // this.initData(res.data)
            }
          })
        } else if (this.$route.query.type === 'handle') {
          // const data = JSON.parse(this.$route.query.data)
          console.log('this.$route.query.type', this.$route.query.type)
          handleView(this.$route.query.id).then((res) => {
            console.log('handleView', res)
            if (res.meta.code === '200' && res.data != null) {
              this.setAuditRelation(res)
            }
          })
        }
      },
      setAuditRelation(res) {
        const userdata = JSON.parse(this.$store.state.user.organization)
        const accountInfo = JSON.parse(sessionStorage.getItem('accountInfo'))
        this.accountInfo = accountInfo
        this.implementCode =  res.data.implement_code
        console.log('this.implementCode',this.implementCode)
        const audit_relation_list = res.data.audit_relation_list
        console.log('res.data.audit_relation_list', res.data.audit_relation_list)
        this.auditRelationListCopy = res.data.audit_relation_list
        console.log('auditRelationList', this.auditRelationListCopy)
        this.liccItemAuthCode = res.data.licc_item_auth_code
        console.log('this.liccItemAuthCode', this.liccItemAuthCode)
        console.log('res.data.audit_result', res.data.audit_result)
        this.auditResult = res.data.audit_result
        let auditLevel1 = []
        let auditLevel2 = []
        let auditLevel3 = []
        let auditLevel4 = []
        let auditLevel5 = []
        let isHasAudit = false
        if (this.auditResult !== 'SUCCESS') {
          // this.auditRelationList =  res.data.sub_audit_obj
          this.subAuditObj = []
          // 如果是只有一级审核，不显示下级审核区划和下级审核部门
          if (audit_relation_list.length == 1) {
            if (audit_relation_list[0].audit_level == 1) {
              this.examineLevelshow = false
              this.showStep = false
            }
          }
          audit_relation_list.forEach((e0, index) => {
            // 如果是多级审核，获取下一级审核的部门
            if (this.examineLevelshow) {
              if (e0.audit_status === 'WAIT_FOR_AUDIT') {
                if (audit_relation_list[index + 1] !== undefined) {
                  this.subAuditObj = audit_relation_list[index + 1].sub_audit_obj
                } else {
                  this.examineLevelshow = false
                }
              }
            }

            e0.sub_audit_obj.forEach((e1) => {
              this.auditRelationList.push(e1)
              console.log('e1.audit_org_code', e1.audit_org_code, 'userdata.credit_code', userdata.credit_code, 'e1.assist_audit_result', e1.assist_audit_result)
              // 如果匹配到未审核
              if (e0.audit_status === 'WAIT_FOR_AUDIT') {
                if (userdata.credit_code === e1.audit_org_code) {
                  if (isHasAudit === false) {
                    // 如果多级审核进来，匹配中用户编码切状态要不为审核过才进入待审核流程
                    if (e1.assist_audit_result !== 'SUCCESS') {
                      console.log('进入待审核流程')
                      this.onlyshowExaminedTable = false
                      this.nowAuditRelation = e1
                      this.nowExamineLeve = this.nowAuditRelation.audit_level
                      isHasAudit = true
                      this.auditResult = 'WAIT_FOR_AUDIT' // 等待另外部门审核
                    } else {
                      console.log('进入待审核没有权限')
                      this.onlyshowExaminedTable = true // 只显示列表展示数据
                      this.nowExamineLeve = e0.audit_level
                      this.auditResult = 'AUDITING' // 等待另外部门审核
                      isHasAudit = true
                      // 如果处于待审核第三级，没有审核权限的人看到此数据的详情，回显待审核的人信息
                      // if (e1.audit_level === 3) {
                      //   // e1.assist_audit_result = 'NOT_WAIT'
                      //   auditLevel3.push(e1)
                      //   // this.haxAuditRelation.push(auditLevel3)
                      // }
                    }
                  }
                } else if (userdata.credit_code !== e1.audit_org_code) {
                  if (isHasAudit === false) {
                    console.log('进入待审核没有权限')
                    this.onlyshowExaminedTable = true // 只显示列表展示数据
                    this.nowExamineLeve = e0.audit_level
                    this.auditResult = 'AUDITING' // 等待另外部门审核
                    // 如果处于待审核最后一级，没有审核权限的人看到此数据的详情，回显待审核的人信息
                    if (e1.audit_level === this.auditRelationListCopy.length) {
                      // e1.assist_audit_result = 'NOT_WAIT'
                        this.nowExamineLeve = e0.audit_level - 1
                      // auditLevel3.push(e1)
                      // this.haxAuditRelation.push(auditLevel3)
                    }
                    // isHasAudit = true
                  }
                }
              }
              //|| (e0.audit_status === 'WAIT_FOR_AUDIT' && userdata.credit_code !== e1.audit_org_code)
              // 如果匹配到其中审核了一个部门还有剩余部门未审核
              else if (e0.audit_status === 'AUDITING' && userdata.credit_code === e1.audit_org_code) {
                console.log('进入已经审核，还有剩余部门未审核')
                this.nowAuditRelation = e1
                this.nowExamineLeve = this.nowAuditRelation.audit_level
                this.onlyshowExaminedTable = true // 只显示列表展示数据
                this.auditResult = 'AUDITING' // 等待另外部门审核
              } else {
              }
              console.log('e1.audit_level', e1.audit_level)
              // this.nowExamineLeve = audit_relation_list.length
              if (e1.audit_level === 1) {
                auditLevel1.push(e1)
                // this.haxAuditRelation.push(auditLevel1)
              } else if (e1.audit_level === 2) {
                auditLevel2.push(e1)
                // this.haxAuditRelation.push(auditLevel2)
              } else if (e1.audit_level === 3) {
                auditLevel3.push(e1)
                // this.haxAuditRelation.push(auditLevel3)
              } else if (e1.audit_level === 4) {
                auditLevel4.push(e1)
                // this.haxAuditRelation.push(auditLevel3)
              } else if (e1.audit_level === 5) {
                auditLevel5.push(e1)
                // this.haxAuditRelation.push(auditLevel3)
              }
            })
          })
          this.nowAuditRelation.to_user_account = accountInfo.account
          this.nowAuditRelation.to_user_name = accountInfo.name
          this.nowAuditRelation.assist_audit_result = null
          this.subAuditObj.forEach((e) => {
            this.auditDiviCode.push(e.audit_divi_code)
          })
          console.log('auditLevel1, auditLevel2, auditLevel3', auditLevel1, auditLevel2, auditLevel3)
          if (auditLevel1.length != 0) {
            this.haxAuditRelation.push(auditLevel1)
          }
          if (auditLevel2.length != 0) {
            this.haxAuditRelation.push(auditLevel2)
          }
          if (auditLevel3.length != 0) {
            this.haxAuditRelation.push(auditLevel3)
          }
          if (auditLevel4.length != 0) {
            this.haxAuditRelation.push(auditLevel4)
          }
          if (auditLevel5.length != 0) {
            this.haxAuditRelation.push(auditLevel5)
          }
          this.subAuditObjCopy = JSON.parse(JSON.stringify(this.subAuditObj))
          this.handleActive = this.nowExamineLeve - 1

          this.initData(res.data)
        } else {
          this.nowExamineLeve = audit_relation_list[audit_relation_list.length - 1].audit_level
          this.handleActive = this.nowExamineLeve - 1
          console.log('audit_relation_list', audit_relation_list)
          if (audit_relation_list.length === 1) {
            this.showStep = false
            this.examineLevelshow = false
          }
          this.onlyshowExaminedTable = true
          let auditDiviName2 = []
          let auditDiviName3 = []
          let auditDiviName4 = []
          let auditDiviName5 = []
          let auditOrgName2 = []
          let auditOrgName3 = []
          let auditOrgName4 = []
          let auditOrgName5 = []
          // 当审核完成时，获取下级审核部门和审核行政区划
          audit_relation_list.forEach((e0, index) => {
            if (audit_relation_list[index + 1]) {
              audit_relation_list[index + 1].sub_audit_obj.forEach((e2) => {
                if (e2.audit_level === 2) {
                  auditDiviName2.push(e2.audit_divi_name)
                  auditOrgName2.push(e2.audit_org_name)
                }
                if (e2.audit_level === 3) {
                  auditDiviName3.push(e2.audit_divi_name)
                  auditOrgName3.push(e2.audit_org_name)
                }
                if (e2.audit_level === 4) {
                  auditDiviName4.push(e2.audit_divi_name)
                  auditOrgName4.push(e2.audit_org_name)
                }
                if (e2.audit_level === 5) {
                  auditDiviName5.push(e2.audit_divi_name)
                  auditOrgName5.push(e2.audit_org_name)
                }
              })
            }
          })
          console.log('this.haxAuditRelation00', this.haxAuditRelation)
          console.log('auditLevel1', auditLevel1)
          console.log('auditLevel2', auditLevel2)
          console.log('auditLevel3', auditLevel3)
          audit_relation_list.forEach((e0, index) => {
            e0.sub_audit_obj.forEach((e1) => {
              if (e1.audit_level === 1) {
                if (auditDiviName2.length != 0) {
                  e1.auditDiviName = auditDiviName2.join(',')
                }
                if (auditOrgName2.length != 0) {
                  e1.auditOrgName = auditOrgName2.join(',')
                }
                console.log('第一级', e1)
                auditLevel1.push(e1)
                // this.haxAuditRelation.push(auditLevel1)
              } else if (e1.audit_level === 2) {
                if (auditDiviName3.length != 0) {
                  e1.auditDiviName = auditDiviName3.join(',')
                }
                if (auditOrgName3.length != 0) {
                  e1.auditOrgName = auditOrgName3.join(',')
                }
                console.log('第二级', e1)
                auditLevel2.push(e1)
                // this.haxAuditRelation.push(auditLevel2)
              } else if (e1.audit_level === 3) {
                if (auditDiviName4.length != 0) {
                  e1.auditDiviName = auditDiviName4.join(',')
                }
                if (auditOrgName4.length != 0) {
                  e1.auditOrgName = auditOrgName4.join(',')
                }
                console.log('第三级', e1)
                auditLevel3.push(e1)
                // this.haxAuditRelation.push(auditLevel3)
              } else if (e1.audit_level === 4) {
                if (auditDiviName5.length != 0) {
                  e1.auditDiviName = auditDiviName5.join(',')
                }
                if (auditOrgName5.length != 0) {
                  e1.auditOrgName = auditOrgName5.join(',')
                }
                console.log('第四级', e1)
                auditLevel4.push(e1)
                // this.haxAuditRelation.push(auditLevel3)
              } else if (e1.audit_level === 5) {
                console.log('第五级', e1)
                auditLevel5.push(e1)
                // this.haxAuditRelation.push(auditLevel3)
              }
            })
          })
          if (auditLevel1.length != 0) {
            this.haxAuditRelation.push(auditLevel1)
          }
          if (auditLevel2.length != 0) {
            this.haxAuditRelation.push(auditLevel2)
          }
          if (auditLevel3.length != 0) {
            this.haxAuditRelation.push(auditLevel3)
          }
          if (auditLevel4.length != 0) {
            this.haxAuditRelation.push(auditLevel4)
          }
          if (auditLevel5.length != 0) {
            this.haxAuditRelation.push(auditLevel5)
          }
          // this.haxAuditRelation.push(auditLevel1, auditLevel2, auditLevel3)

          console.log('this.haxAuditRelation', this.haxAuditRelation)
          console.log('this.showStep', this.showStep)
          console.log('this.onlyshowExaminedTable', this.onlyshowExaminedTable)
          this.initData(res.data)
        }
      },
      // 下级审核部门选中，修改下一级部门审核是否需要审核
      auditOrgChange(val) {
        this.subAuditObj.forEach((e) => {
          if (val.indexOf(e.audit_org_code) !== -1) {
            e.need_audit = true
            e.audit_record = true
            this.nowAuditRelation.hasOrgName = true
            this.nowAuditRelation.audit_record = true
          } else {
            e.need_audit = false
            this.nowAuditRelation.hasOrgName = null
          }
        })
      },
      getEnterpriseIdentityType() {
        getEnterpriseIdentityType().then((res) => {
          if (res.meta.code === '200' && res.data != null) {
            this.legalPersonIdentityTypeList = res.data
          } else {
            this.legalPersonIdentityTypeList = []
          }
        })
      },
      getCurrentAccount() {
        getCurrentAccount().then((res) => {
          if (res.meta.code === '200') {
            this.account_info = res.data.account_info
          }
        })
      },
      isEven(num) {
        return (num & 1) === 0;
      },
      setDivisionCodeAndName(data) {
        console.log('data', data)
        let code = []
        this.nowAuditRelation.audit_divi_name = data.name
        this.nowAuditRelation.audit_divi_code = data.code
        // 过滤下级审核部门
        data.forEach((e) => {
          this.subAuditObjCopy.forEach((i) => {
            if (i.audit_divi_code == e.code) {
              // console.log(i)
              code.push(i)
            }
          })
        })
        console.log('code', code)
        this.subAuditObj = code
      },
      initData(data) {
        // const data = JSON.parse(this.$route.query.data)
        // const userdata = this.$store.state.user.userdata
        const userdata = JSON.parse(this.$store.state.user.organization)
        const accountInfo = JSON.parse(sessionStorage.getItem('accountInfo'))
        console.log('accountInfo', accountInfo, 'userdata', userdata)
        // WAIT :待审核 SUCCESS :审核通过 FAIL :审核失败
        // 处理中的都为处理操作，显示提交按钮，有提交操作、选择附件、处理结果操作
        if (this.$route.query.type === 'handle') {
          this.isHandle = true
          this.isshowResult = true
          this.addForm.audit_result = 'SUCCESS'
          console.log('handle')
        } // 申请中查看操作都没有提交操作、选择附件、处理结果操作
        else if (this.$route.query.type === 'apply') {
          console.log('apply', data.audit_result)

          this.download(data.id)
          this.addForm.audit_result = data.audit_result
          if (data.audit_result === 'WAIT') {
            this.isHandle = true
            this.isshowResult = true
          } else if (data.audit_result === 'SUCCESS') {
            this.isHandle = false
            this.isshowResult = true
            // this.addForm.audit_result = '审核通过'
          } else if (data.audit_result === 'FAIL') {
            this.isHandle = false
            this.isshowResult = true
            // this.addForm.audit_result = '审核失败'
          }
        } else if (this.$route.query.type === 'archives') {
          // 档案中的待协查为处理操作
          if (data.audit_result === 'WAIT') {
            this.isHandle = true
            this.isshowResult = true
            //  this.addForm.audit_result = '审核通过'
          } // 档案中的已协查为查看操作
          else if (data.audit_result === 'SUCCESS' || data.audit_result === 'FAIL') {
            this.download(data.id)
            this.isHandle = false
            this.isshowResult = true
            this.addForm.audit_result = data.audit_result
            // this.addForm.audit_result = data.audit_result === 'SUCCESS' ? '符合' : '不符合'
          }
        }
        console.log('this.isHandle', this.isHandle, 'this.isshowResult', this.isshowResult)
        this.islegalPerson = data.handle_affairs_type != 'NATURAL_PERSON'
        this.detailTitle = data.material_name + '【' + data.handle_affairs_name + '】'
        this.queryForm = {
          business_serial_number: data.business_serial_number,
          assist_serial_number: data.assist_serial_number,
          handle_affairs_name: data.handle_affairs_name,
          from_assist_org_name: data.from_assist_org_name,
          assist_time: data.creation_time,
          material_name: data.material_name,
          proof_catalog_name: data.proof_catalog_name,
          to_assist_org_name: data.to_assist_org_name,
          handle_affairs_identity_number: data.handle_affairs_identity_number,
          from_assist_user_name: data.from_assist_user_name,
          from_assist_contain: data.from_assist_contain,
          to_user_name: data.from_assist_user_name,
          from_demand: data.from_demand,
          item_name: data.item_name,
          item_code: data.item_code,
          handle_affairs_identity_type: data.handle_affairs_identity_type,
          legal_person_identity_type: data.legal_person_identity_type,
        }
        this.assist_data_list = data.assist_data_list
        this.issuePLicense_way = data.issue_p_license_way
        console.log('issue_p_license_way', this.issuePLicense_way)
        if (this.assist_data_list != null) {
          if (this.assist_data_list.length !== 0) {
            this.assistDataTitle = this.assist_data_list[0].title
            this.assistDataBottom = this.assist_data_list[0].bottom
            if (this.assist_data_list[0].item_list !== null) {
              this.assistItemList = this.assist_data_list[0].item_list
              let totalCols = 0;
              this.assistItemList.forEach( ( item, i ) => {
                if (item.cols === '1') {
                  item.colsNum = 2;
                } else if (item.cols === '2') {
                  item.colsNum = 1;
                }
              });
              console.log( '===========', this.assistItemList )
              for (let i = 1; i < this.assistItemList.length; i++) {
                  const current = this.assistItemList[i];
                  const previous = this.assistItemList[i - 1];
                  console.log(this.assistItemList[i].key,current.cols,previous.cols,current.cols === '1' && previous.cols === '2')
                  if (current.cols === '1' && previous.cols === '2') {
                    // Check if the item before previous also has cols=2
                    console.log(i >= 2,this.assistItemList[i - 2].cols === '2')
                    if (i >= 2 && this.assistItemList[i - 2].cols === '2') {
                      previous.colsNum = 2; // Keep colsNum as 1 if two consecutive cols=2
                    } else {
                      previous.colsNum = 1; // Otherwise set to 2
                    }
                  }
                }
              /* this.assistItemList.forEach( (item,i) => {
                console.log('totalCols',item.key,item.cols,this.isEven(totalCols),totalCols)
                  if ( item.cols === '1'  ) {
                    totalCols += 1
                    item.colsNum=2
                  }else if ( item.cols === '2'&& this.isEven(totalCols) ) {
                    totalCols += 2
                    item.colsNum=1
                  } else {
                    totalCols += 1
                    item.colsNum=2
                  }
                } ) */
              // console.log('this.assistItemList',this.assistItemList)
            }
            if (this.doubleData.length != 0 && this.doubleData % 2 != 0) {
              this.doubleData.push({ key: null, value: null, cols: '2' })
            }
          }
        }
        console.log('this.assistDataBottom', this.assistDataBottom)
        // console.log('this.doubleData', this.doubleData)
        this.addForm.audit_suggestion = data.audit_suggestion
        this.addForm.audit_time = data.audit_time
        this.addForm.audit_id = data.id
        this.addForm.to_user_name = accountInfo.name
        this.addForm.to_assist_org_name = data.to_assist_org_name
        this.addForm.to_assist_credit_code = userdata.credit_code
        this.addForm.to_user_id = userdata.id
        console.log('this.addForm', this.addForm)
      },
      getDoubleData(data) {
        const doubleData = []
        const singleData = []
        data.forEach((e) => {
          if (e.cols === '2') {
            doubleData.push(e)
          } else {
            singleData.push(e)
          }
        })
        return {
          doubleData,
          singleData,
        }
      },
      submitForm(formName) {
        console.log('this.$refs[formName]', this.$refs[formName])
        this.$refs[formName].validate((valid, object) => {
          console.log('valid', valid)
          if (valid) {
            if (this.addForm.raw !== '') {
              Promise.all([this.handle()]).then((res) => {
                if (res[0].meta.code === '200' && res[0].data != null) {
                  this.$message({
                    message: '处理成功',
                    type: 'success',
                  })
                  this.back()
                } else {
                  this.$message({
                    message: res[0].meta.message + ',' + res[1].meta.message,
                    type: 'error',
                  })
                }
              })
            } else {
              Promise.all([this.handle()]).then((res) => {
                if (res[0].meta.code === '200' && res[0].data != null) {
                  this.$message({
                    message: '处理成功',
                    type: 'success',
                  })
                  this.back()
                } else {
                  this.$message({
                    message: res[0].meta.message,
                    type: 'error',
                  })
                }
              })
            }
          } else {
            const str = []
            for (const key in object) {
              object[key].map((item) => {
                str.push(item.message)
              })
              let dom = this.$refs[Object.keys(object)[0]]
              if (Object.prototype.toString.call(dom) !== '[object Object]') {
                dom = dom[0]
                break // 结束语句并跳出语句，进行下个语句执行
              }
              // console.log('dom',dom)
              // 定位代码
              dom.$el.scrollIntoView({
                block: 'center',
                behavior: 'smooth',
              })
            }
            // console.log('error submit!!')
            // return false
          }
        })
      },
      handleChange(file, list) {
        console.log(file, list)
        const fileName = getFileType(file.name).fileName
        if (fileName.indexOf('http') != -1 || validPrefix(fileName)) {
          this.$message.error('文件名开头包含特殊符号！')
        } else {
          if (!getIsWhitelist(file.name, this.whitelist)) {
            this.$message.error(`请重新选择以${this.whitelist.join(',')}为后缀名的文件！`)
          } else {
            this.nowAuditRelation.assist_attachment_name = file.raw.name
            this.nowAuditRelation.raw = file.raw
            this.fileList = list
          }
        }
        // }
      },
      onStepClick(index) {
        if (index < this.nowExamineLeve) {
          this.handleActive = index
        }
        console.log('this.auditResult', this.auditResult)
        if (this.auditResult !== 'SUCCESS' && this.auditResult !== 'AUDITING') {
          // 如果当前选中审核等级小于需要展示对象等级
          console.log('index', index, 'this.nowAuditRelation.audit_level', this.nowAuditRelation.audit_level)
          if (index < this.nowAuditRelation.audit_level - 1) {
            this.onlyshowExaminedTable = true
          } else {
            this.onlyshowExaminedTable = false
          }
        } else {
          this.onlyshowExaminedTable = true
        }

        console.log('this.onlyshowExaminedTable', this.onlyshowExaminedTable)
      },
      back() {
        if (this.$route.query.type === 'handle') {
          this.$router.push({
            name: 'proof_investigation_admin',
          })
        } else if (this.$route.query.type === 'archives') {
          this.$router.push({
            name: 'investigation_archives',
          })
        } else {
          this.$router.push({
            name: 'investigationApply',
          })
        }
      },
      cancle() {
        this.dialogVisible = false
        this.nowAuditRelation.assist_attachment_name = ''
        this.fileList = []
        this.addForm.raw = ''
      },
      getFile() {
        if (this.nowAuditRelation.assist_attachment_name === '') {
          this.$message({
            message: '请选择文件',
            type: 'warning',
          })
        } else {
          this.upload()
            .then((res) => {
              if (res.data != null && res.meta.code === '200') {
                this.$message({
                  message: '文件上传成功',
                  type: 'success',
                })
                // this.addForm.fileName = this.addForm.fileName
                this.nowAuditRelation.assist_attachment_id = res.data
              } else {
                this.nowAuditRelation.assist_attachment_name = ''
                this.nowAuditRelation.raw = ''
                this.$message({
                  message: res.meta.message,
                  type: 'warning',
                })
              }
            })
            .catch(() => {
              this.nowAuditRelation.assist_attachment_name = ''
              this.nowAuditRelation.raw = ''
            })
          this.dialogVisible = false
        }
      },
      // 下载当前上传的文件
      downFile() {
        const blob = new Blob([this.nowAuditRelation.raw])
        const downLink = document.createElement('a')
        downLink.download = this.nowAuditRelation.raw.name
        downLink.href = URL.createObjectURL(blob)
        // 触发点击
        document.body.appendChild(downLink)
        downLink.click()
        // 然后移除
        document.body.removeChild(downLink)
      },
      // 通用下载接口
      downloadFile(item) {
        downloadFile({ id: item.assist_attachment_id }).then((res) => {
          dataURLtoDownload(res.data.file_data_base64, res.data.file_name)
        })
      },
      // 上传附件
      upload() {
        const query = {
          relation: this.addForm.audit_id,
        }
        // this.nowAuditRelation.assist_attachment_name.raw =''
        const fd = new FormData()
        fd.append('file', this.nowAuditRelation.raw)
        return new Promise((resolve, reject) => {
          uploadFile(query, fd).then((res) => {
            resolve(res)
          })
        })
      },
      // 处理申请
      handle() {
        return new Promise((resolve, reject) => {
          const data = {
            audit_id: this.addForm.audit_id,
            audit_relation_list: this.auditRelationList,
          }
          console.log('data', data)
          if (this.$route.query.type === 'handle') {
            handleSave(data).then((res) => {
              resolve(res)
            })
          } else if (this.$route.query.type === 'archives') {
            archivesSave(data).then((res) => {
              resolve(res)
            })
          }
        })
      },
      // 获取附件base64编码附件
      download(id) {
        const query = {
          relation: id,
        }
        this.pageLoading = true
        download(query)
          .then((res) => {
            this.pageLoading = false
            if (res.meta.code === '200' && res.data !== null) {
              this.fileBase64 = {
                name: res.data.file_name,
                base64: res.data.file_data,
              }
            }
          })
          .catch((err) => {
            this.pageLoading = false
          })
      },
      // 下载base64编码附件
      downFileBase64() {
        dataURLtoDownload(this.fileBase64.base64, this.fileBase64.name)
      },
      // 下载电子证明文件
      downloadliccItemAuthCode() {
        downloadAttachment({ assist_serial_number: this.queryForm.assist_serial_number }).then((res) => {
          // console.log(res)
          if (res) {
            dataURLtoDownload(res.data.file_data_base64, res.data.file_name)
          }
        })
      },
      // 跳转制证签发
      toisuss() {
        // this.$router.push({ path: '/license-derate-biz/proofInvestigationAdmin/investigationServices/licenseIssueList' })
        // https://192.168.10.34:6041/license-derate-biz/proofInvestigationAdmin/investigationServices/licenseIssueList
        const origin = window.location.origin
        console.log('origin', origin)
        const params = JSON.stringify({ id: this.implementCode })
        window.open(`${origin}/license-derate-biz/white/check?tokenName=proof-derate-web&path=licenseIssueMaking/:id&params=${params}&type=inOutherLink`, '_blank')
        console.log(`${origin}/license-derate-biz/white/check?tokenName=proof-derate-web&path=licenseIssueMaking/:id&params=${params}&type=inOutherLink`)
      },
      // 对当前审匹配到的审核对象记录修改审核状态设值
      setNowAuditRelationStatus() {
        this.nowAuditRelation.audit_record = true
      },
    },
  }
  </script>

  <style scoped>
  .investigationDetail /deep/ .el-table .cell,
  .el-table--border .el-table__cell:first-child .cell {
    text-align: center;
  }

  .content-header-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .content-header-title i {
    cursor: pointer;
  }

  .content-header-title h1 {
    font-size: 24px;
  }

  .select {
    width: 100%;
  }

  .investigationDetail /deep/.el-upload__input {
    display: none;
  }

  .investigationDetail /deep/.el-card {
    border-bottom: 0px;
    border-top: 0px;
  }

  .uploadtext {
    /* margin-left: 10px; */
    margin-left: 10px;
    line-height: 20px;
    margin-top: 10px;
    color: #999999;
  }

  .tip {
    margin-top: 5px;
    display: flex;
    align-items: center;
  }

  .tip i {
    color: #e6a23c;
    font-size: 20px;
    margin-right: 10px;
  }

  .filewrap {
    display: flex;
  }

  .filewrap .add-button {
    cursor: pointer;
  }

  .readme {
    /* margin-top: 80px;
    margin-left: 80px; */
  }

  .margin-10 {
    margin-top: 10px;
  }

  .info-wrap {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
  }

  .isuss {
    cursor: pointer;
    color: #1f9e73;
  }

  .info-wrap img {
    width: 35px;
    height: 35px;
    margin-right: 10px;
  }

  .info-title {
    font-size: 20px;
    color: #333333;
  }

  .wrap-examine {
    /* background: #f0faf6; */
  }

  .wrap-examine img {
    width: 35px;
  }

  .wrap-examine /deep/ .el-card__body {
    /* background: #f0faf6; */
  }
  </style>
