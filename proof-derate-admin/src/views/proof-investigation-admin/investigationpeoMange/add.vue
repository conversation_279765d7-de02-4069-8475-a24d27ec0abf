<template>
  <div v-loading="dataLoading" class="content-wrapper investigationArchive padding-10">
    <!-- <papeTitle :title-name="pageTitle" :is-has-back="true" @goToList="back">
      <div class>
        <div v-if="this.$route.query.type !== 'detail'&& this.$route.query.type !=='delete'" class>
          <el-button type="primary" :loading="submitLoading" @click="submitForm('form')">提交</el-button>
        </div>
        <div v-else-if="this.$route.query.type == 'detail'" class>
          <el-button v-permission="'assist:user:manage:update'" type="primary" @click="toEdit()">修改</el-button>
          <el-button v-permission="'assist:user:manage:delete'" type="danger" @click="toDelete()">删除</el-button>
        </div>
        <div v-else-if="this.$route.query.type == 'delete'" class>
          <el-button type="danger" :loading="submitLoading" @click="delectItem()">删除</el-button>
        </div>
      </div>
    </papeTitle>-->
    <CardTitle :title-name="pageTitle" :ifback="true" @back="back()">
      <template>
        <div v-if="this.$route.query.type !== 'detail'&& this.$route.query.type !=='delete'" class>
          <el-button type="primary" :loading="submitLoading" @click="submitForm('form')">提交</el-button>
        </div>
        <div v-else-if="this.$route.query.type == 'detail'" class>
          <el-button v-permission="'assist:user:manage:update'" type="primary" @click="toEdit()">修改</el-button>
          <el-button v-permission="'assist:user:manage:delete'" type="danger" @click="toDelete()">删除</el-button>
        </div>
        <div v-else-if="this.$route.query.type == 'delete'" class>
          <el-button type="danger" :loading="submitLoading" @click="delectItem()">删除</el-button>
        </div>
      </template>
    </CardTitle>
    <!-- <section class="content"> -->
    <el-card class="box-card" shadow="never" :class="this.$route.query.type == 'detail'?'nopadding':''">
      <el-tabs v-if="this.$route.query.type == 'detail'" v-model="activeName" tab-position="top">
        <el-tab-pane label="详情" name="first">
          <span class="margin-left-10 info-wrap">
            <img :src="arrow" alt>
            <span class="info-title">基本信息</span>
          </span>
          <el-descriptions class="descriptions" title :column="2" border>
            <el-descriptions-item :label-style="{width:'140px'}">
              <template slot="label">所属区域</template>
              {{ queryForm.division_code }}
            </el-descriptions-item>
            <el-descriptions-item :label-style="{width:'140px'}">
              <template slot="label">部门名称</template>
              {{ queryForm.ORG_NAME }}
            </el-descriptions-item>
            <el-descriptions-item :label-style="{width:'140px'}">
              <template slot="label">协查人员姓名</template>
              {{ queryForm.ASSISTOR_NAME }}
            </el-descriptions-item>
            <el-descriptions-item :label-style="{width:'140px'}">
              <template slot="label">协查人员联系方式</template>
              {{ queryForm.ASSISTOR_PHONE }}
            </el-descriptions-item>
            <el-descriptions-item :label-style="{width:'140px'}">
              <template slot="label">协查人员证件类型</template>
              {{ cardOptions.find(i=>i.value==queryForm.ASSISTOR_IDENTITY_TYPE).label }}
            </el-descriptions-item>
            <el-descriptions-item :label-style="{width:'140px'}">
              <template slot="label">协查人员证件号码</template>
              {{ queryForm.ASSISTOR_IDENTITY_NUMBER }}
            </el-descriptions-item>
            <el-descriptions-item :label-style="{width:'150px'}">
              <template slot="label">粤证易账号唯一标识</template>
              {{ queryForm.USER_ID }}
            </el-descriptions-item>
            <el-descriptions-item :label-style="{width:'140px'}">
              <template slot="label">证明目录</template>
              {{ queryForm.PROOF_CATALOG_NAME }}
            </el-descriptions-item>
          </el-descriptions>
        </el-tab-pane>
        <el-tab-pane label="变更日志" name="second">
          <el-row v-for="(item,index) in proofRecordList" :key="index">
            <el-row>&nbsp;</el-row>
            <el-col :span="24">
              <el-card class="haspadding">
                <el-row>
                  <el-col :span="8">操作人:{{ item.account_name }}</el-col>
                  <el-col :span="9" :offset="2">修改账号：{{ item.account }}</el-col>
                </el-row>
                <br>
                <el-row>
                  <el-col :span="8">操作时间:{{ item.last_modification_time }}</el-col>
                  <el-col :span="9" :offset="2">操作名称：{{ item.operation_name }}</el-col>
                </el-row>
                <br>
                <el-row>
                  <el-col :span="8">修改对比:</el-col>
                </el-row>
                <br>
                <el-row class="amend">
                  <el-col :span="12">修改前</el-col>
                  <el-col :span="12">修改后</el-col>
                </el-row>
                <el-row v-for="(itemChange,indexChange) in item.change_prefix_and_post_list" :key="indexChange" class="amendInfo">
                  <el-col :span="12">{{ itemChange.change_prefix }}</el-col>
                  <el-col :span="12">{{ itemChange.change_post }}</el-col>
                </el-row>
              </el-card>
            </el-col>
            <br>
          </el-row>
        </el-tab-pane>
      </el-tabs>
      <el-form v-else ref="form" :model="queryForm" label-width="180px" class="el-check-form" :rules="rules">
        <el-row :gutter="24" justify="center" type="flex">
          <el-col :span="18">
            <el-form-item label="所属区域">
              <!-- isDisable -->
              <!-- <division-selector v-model="queryForm.division_code" :disabled="isDisable" @change="divisionChange" @childValue="codeSon" /> -->
              <AdministrativeDivisionCascader
                :key="divisionCode"
                :division-code="divisionCode"
                :edit-disabled="false"
                :permission-code="'assist:user:manage:list'"
                @setDivisionCodeAndName ="setDivisionCodeAndName"
              />
              <!-- <span>{{ queryForm.division_code }}</span> -->
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24" justify="center" type="flex">
          <el-col :span="18">
            <el-form-item label="部门名称" prop="ORG_NAME">
              <!-- :disabled="isDisable" -->
              <el-select v-model="queryForm.ORG_NAME" placeholder="请选择部门" class="select"  @change="depChange">
                <el-option v-for="item in deptOptions" :key="item.credit_code" :label="item.name" :value="item.credit_code" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24" justify="center" type="flex">
          <el-col :span="18">
            <el-form-item label="协查人员姓名" prop="ASSISTOR_NAME">
              <el-input v-model="queryForm.ASSISTOR_NAME" clearable placeholder="请输入协查人员姓名" :disabled="isModify" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24" justify="center" type="flex">
          <el-col :span="18">
            <el-form-item label="协查人员联系方式" prop="ASSISTOR_PHONE">
              <el-input v-model="queryForm.ASSISTOR_PHONE" clearable placeholder="请输入协查人员联系方式" :disabled="isModify" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24" justify="center" type="flex">
          <el-col :span="18">
            <el-form-item label="协查人员证件类型" prop="ASSISTOR_IDENTITY_TYPE">
              <el-select v-model="queryForm.ASSISTOR_IDENTITY_TYPE" placeholder="请选择协查人员证件类型" class="select" :disabled="isModify">
                <el-option v-for="item in cardOptions" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24" justify="center" type="flex">
          <el-col :span="18">
            <el-form-item label="协查人员证件号码" prop="ASSISTOR_IDENTITY_NUMBER">
              <el-input v-model="queryForm.ASSISTOR_IDENTITY_NUMBER" clearable placeholder="请输入人员证件号码" :disabled="isModify" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="24" justify="center" type="flex">
          <el-col :span="18">
            <el-form-item label="粤证易账号唯一标识">
              <el-input v-model="queryForm.USER_ID" clearable placeholder="请输入粤证易账号唯一标识" :disabled="isModify" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24" justify="center" type="flex">
          <el-col v-if="!isModify" :span="is1366==true?14:16">
            <el-form-item label="证明目录">
              <el-input v-model="queryForm.PROOF_CATALOG_NAME" clearable placeholder="请输入证明目录" :disabled="true" />
            </el-form-item>
          </el-col>
          <el-col v-if="isModify" :span="18">
            <el-form-item label="证明目录">
              <el-input v-model="queryForm.PROOF_CATALOG_NAME" clearable placeholder="请输入证明目录" :disabled="true" />
            </el-form-item>
          </el-col>
          <el-col v-if="!isModify" :span="is1366==true?4:2">
            <el-button type="text" @click="dialogVisible=true">选择</el-button>
            <el-button type="text" @click="setEmpty()">置空</el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
    <!-- </section> -->
    <el-dialog title="关联目录" :visible.sync="dialogVisible" :width="is1366==true?'55%':'40%'" :top="is1366==true?'1vh':'15vh'">
      <div class="dialogwrap">
        <el-transfer
          ref="transfer"
          v-model="value"
          :filterable="true"
          :titles="['待选', '已选']"
          filter-placeholder="请输入证明目录名称"
          :data="generateData"
          class="transfer"
          @change="selectionChange"
        >
          <div slot="left-footer" class="transfer-input">
            <el-input v-model="addForm.proof_catalog_name" placeholder="请输入内容" />
            <el-button class="transfer-footer" size="small" @click="searchTransfer">查询</el-button>
          </div>
        </el-transfer>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="getData()">确认</el-button>
        <el-button @click="dialogVisible = false">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import CustomTable from '@/components/Element/Table'
import { proofCatalogPage, create, edit, deleteUser, getUserByid, getViewWithoutMaskField } from '@/api/assistInvestigate'
import divisionSelector from '@/components/DivisionSelector1'
import { getOrgListNoAuth } from '@/api/admin/org.js'
import { getAssistUserChangeLog } from '@/api/procedureLog'
import papeTitle from '@/components/papeTitle'
import { getOrganizationList } from '@/api/commonPack/platManege'
import CardTitle from '@/components/CardTitle'
const valiPhone = (rule, value, callback) => {
  // if (value === '') {
  //   callback(new Error('请输入手机号'))
  // } else if (!validatePhone(value)) {
  //   callback(new Error('请输入正确的手机号'))
  // } else {
  //   callback()
  // }
  if (value === '') {
    callback(new Error('请输入手机号'))
  } else {
    callback()
  }
}
export default {
  name: 'ProofInvestigationHandel',
  components: {
    divisionSelector,
    CustomTable,
    papeTitle,
    CardTitle,
    AdministrativeDivisionCascader: () => import('@/components/AdministrativeDivisionCascader')
  },
  data() {
    return {
      queryForm: {
        division_code: '',
        ORG_NAME: '',
        ASSISTOR_NAME: '',
        ASSISTOR_PHONE: '',
        ASSISTOR_IDENTITY_NUMBER: '',
        USER_ID: '',
        PROOF_CATALOG_NAME: '',
        ASSISTOR_IDENTITY_TYPE: '',

        certificateName: '',
        clerkName: '',
        directoryName: '',
        assistantType: '',
        assistantNum: '',
        proof_code: '',
        certificationCatalogue: ''
      },
      divisionCode: '430100',
      PROOF_CATALOG_NAME: '',
      addForm: {
        proof_catalog_name: '',
        page_direction: 'DESC',
        page_number: 1,
        page_size: 10
      },
      dialogVisible: false,
      is1366: false,
      activeName: 'first',
      proofRecordList: [],

      rules: {
        division_code: [{ required: true, message: '请选择所属区域', trigger: 'blur' }],
        ORG_NAME: [{ required: true, message: '请选择部门', trigger: 'blur' }],
        ASSISTOR_NAME: [{ required: true, message: '请输入协查人员姓名', trigger: 'change' }],
        ASSISTOR_PHONE: [{ required: true, trigger: 'change', validator: valiPhone }],
        //  message: '请输入协查人员联系方式'
        ASSISTOR_IDENTITY_TYPE: [{ required: true, message: '请选择协查人员证件类型', trigger: 'change' }],
        ASSISTOR_IDENTITY_NUMBER: [{ required: true, message: '请输入协查人员证件号码', trigger: 'change' }],
        USER_ID: [{ required: true, message: '请输入粤证易账号唯一标识', trigger: 'change' }]
      },
      checkList: [],
      regionOptions: [], // 所属区域
      deptOptions: [], // 部门
      selectData: [], // 选中的目录
      // 证件类型
      cardOptions: [
        {
          value: 'IDENTITY',
          label: '身份证'
        },
        {
          value: 'OFFICERS',
          label: '军官证'
        },
        {
          value: 'PASSPORT',
          label: '护照'
        },
        {
          value: 'EEP_HK_MACAO',
          label: '港澳通行证'
        },
        {
          value: 'OTHER_IDENTITY_LICENSE',
          label: '其他'
        }
      ],
      tableData: {
        content: [], // 表格数据

        loading: false, // 控制表格加载效果
        total: 10, // 分页查询时的总数，去掉该属性，不显示分页条
        currentPage: 1, // 当前页码
        pageSize: 10,
        isShowIndex: false,
        maxHeight: '290px',
        multiple: true,
        pageDirection: 'desc',
        isShowSelection: true, // 是否显示多选框，默认false multiple 需为falase
        reserveSelection: true
      },
      tableHeader: [
        {
          label: '证明目录名称',
          prop: 'name',
          minWidth: '100px'
        },

        {
          label: '操作',
          prop: 'operation',
          width: '180px',
          fixed: 'right',
          slot: 'operation'
        }
        //  { label: '归档', prop: 'investigationStatus',slot: 'investigationStatus', width: '120px', fixed: 'right'}
      ],
      tableTools: [],
      pageTitle: '新建协查人员',
      isDisable: false,
      isModify: false,
      organizationList: [],
      nowData: {}, // 当前根据id获取数据对象
      dataLoading: false,
      submitLoading: false,
      value: [],
      generateData: [],
      arrow: require('@/assets/proof-exemption-admin-images/arrow.png'),
      searchImg: require('@/assets/proof-exemption-admin-images/u133.png'),
      filterMethod(query, item) {
        return item.pinyin.indexOf(query) > -1
      },
      titleName: ''
    }
  },
  watch: {
    dialogVisible: {
      handler(val) {
        if (val) {
          this.proofCatalogPage()
          // console.log(val)
          setTimeout(() => {
            this.watchLeftAllChecked()
            this.watchRightAllChecked()
            // const dom = $('div .is-filterable')[0]
            const dom = this.$refs.transfer.$el.firstChild.childNodes[1].childNodes[1]
            dom.addEventListener('scroll', () => {
              if (dom.scrollTop + dom.clientHeight === dom.scrollHeight) {
                // console.log('到底了')
                this.tableData.pageSize = 10
                this.tableData.currentPage++
                this.query()
              }
            })
          }, 0)
        }
      }
    }
  },
  mounted() {
    // this.getQueryType()
    if (this.$route.query.type == 'detail') {
      this.getDataByid()
    } else {
      this.getViewWithoutMaskField()
    }

    this.screenWidth()
    this.getAssistUserChangeLog()
  },
  // updated() {
  //   this.getQueryType()
  // },
  methods: {
    // 监听左侧全选是否选中
    watchLeftAllChecked() {
      this.leftscrollAllCheckedWatch && this.leftscrollAllCheckedWatch()
      const transferVm = this.$refs.transfer
      transferVm.$refs.leftPanel.handleAllCheckedChange = () => {
        return false
      }
    },
    // 监听右侧全选是否选中
    watchRightAllChecked() {
      this.rightscrollAllCheckedWatch && this.rightscrollAllCheckedWatch()
      const transferVm = this.$refs.transfer
      transferVm.$refs.rightPanel.handleAllCheckedChange = () => {
        return false
      }
    },
    searchTransfer() {
      this.generateData = []
      this.tableData.currentPage = 1
      this.query()
    },
    getDataByid() {
      if (this.$route.query.id) {
        this.dataLoading = true
        getUserByid(this.$route.query.id)
          .then(res => {
            this.dataLoading = false
            if (res.meta.code === '200' && res.data != null) {
              // console.log(res)

              this.getQueryType(res.data)
              console.log('this.queryForm.proof_code', this.queryForm.proof_code, this.queryForm.PROOF_CATALOG_NAME)
              this.nowData = res.data
              this.value = this.queryForm.proof_code.split(',')

              if (this.value.length > 4) {
                this.tableData.pageSize = this.value.length + 10
              }
              if (this.queryForm.PROOF_CATALOG_NAME != '') {
                // console.log(this.queryForm.PROOF_CATALOG_NAME.split(','))
                this.queryForm.PROOF_CATALOG_NAME.split(',').forEach((i, key) => {
                  this.selectData.push({ label: i, key: this.value[key], pinyin: i, name: i, code: this.value[key] })
                })
              }
              console.log('this.selectData', this.selectData)
            }
          })
          .catch(err => {
            this.dataLoading = false
          })
      } else {
        this.getQueryType()
      }
    },
    getViewWithoutMaskField() {
      if (this.$route.query.id) {
        this.dataLoading = true
        getViewWithoutMaskField(this.$route.query.id)
          .then(res => {
            this.dataLoading = false
            if (res.meta.code === '200' && res.data != null) {
              // console.log(res)

              this.getQueryType(res.data)
              console.log('this.queryForm.proof_code', this.queryForm.proof_code, this.queryForm.PROOF_CATALOG_NAME)
              this.nowData = res.data
              this.value = this.queryForm.proof_code.split(',')

              if (this.value.length > 4) {
                this.tableData.pageSize = this.value.length + 10
              }
              if (this.queryForm.PROOF_CATALOG_NAME != '') {
                // console.log(this.queryForm.PROOF_CATALOG_NAME.split(','))
                this.queryForm.PROOF_CATALOG_NAME.split(',').forEach((i, key) => {
                  this.selectData.push({ label: i, key: this.value[key], pinyin: i, name: i, code: this.value[key] })
                })
              }
              console.log('this.selectData', this.selectData)
            }
          })
          .catch(err => {
            this.dataLoading = false
          })
      } else {
        this.getQueryType()
      }
    },
    getAssistUserChangeLog() {
      getAssistUserChangeLog(this.$route.query.id).then(res => {
        // console.log(res)
        if (res.data != null && res.meta.code === '200') {
          const proofRecordList = res.data
          proofRecordList.forEach(e => {
            const item = {
              account_name: e.account_name,
              account: e.account,
              last_modification_time: e.last_modification_time,
              operation_name: e.operation_name,
              change_prefix_and_post_list: [
                {
                  change_prefix: e.change_prefix,
                  change_post: e.change_post
                }
              ]
            }
            this.proofRecordList.push(item)
          })
        }
      })
    },
    detail(row) {
      // console.log(row)
      this.$router.push({
        path: `proof_derate_admin/certificationManagement/certificationList/info?id=${row.id}`
      })
    },
    divisionChange(code) {
      // this.queryForm.division_code = code || ''
      this.deptOptions = []
      // this.queryForm.ORG_NAME = ''
      if (code) {
        // console.log('divisionChange', d, this.queryForm.division_code)
        // this.queryForm.division_code = code
        getOrgListNoAuth({
          pageSize: 1000,
          pageNumber: 0,
          divisionCode: code
        }).then(res => {
          this.deptOptions = res.content.map(i => {
            return { label: i.name, value: i.tyshxydm }
          })
        })
      }
    },
    // 获取实施机构
    getOrganizationList(id) {
      const data = {
        division_code: id,
        permission_code: 'assist:user:manage:list',
        scope: true
      }
      getOrganizationList(data).then(res => {
        if (res.meta.code === '200') {
          this.deptOptions = res.data
          console.log(this.deptOptions)
        }
      })
    },
    codeSon(v) {
      console.log(v)
      // this.ArtificialForm.division_name = v
    },
    proofCatalogPage() {
      this.addForm.page_number = this.tableData.currentPage
      this.addForm.page_size = this.tableData.pageSize
      this.tableData.loading = true
      proofCatalogPage(this.addForm)
        .then(res => {
          // console.log(res)
          this.tableData.loading = false
          if (res.meta.code === '200' && res.data != null) {
            this.tableData.content = res.data.content
            this.tableData.total = res.data.totalElements
            this.tableData.content.forEach(e => {
              this.generateData.push({ label: e.name, key: e.code, pinyin: e.name, name: e.name, code: e.code })
            })

            this.selectData.forEach(e0 => {
              this.generateData.push(e0)
            })
            this.generateData = _.uniqBy(this.generateData, 'key')
            console.log('this.generateData', this.generateData)
            // this.numberOfElements = res.data.numberOfElements
          }
        })
        .catch(err => {
          this.tableData.loading = false
        })
    },
    create(data) {
      this.submitLoading = true
      create(data)
        .then(res => {
          // console.log(res)
          this.submitLoading = false
          if (res.meta.code === '200' && res.data != null) {
            this.$message({
              message: '添加人员成功！',
              type: 'success'
            })
            this.back()
          } else {
            this.$message({
              message: res.meta.message,
              type: 'error'
            })
          }
        })
        .catch(() => {
          this.submitLoading = false
        })
    },
    query() {
      this.proofCatalogPage()
    },
    deleteUser(id) {
      this.submitLoading = true
      deleteUser(id)
        .then(res => {
          // console.log(res)
          this.submitLoading = false
          if (res.meta.code === '200' && res.data != null) {
            this.$message({
              message: '删除人员成功！',
              type: 'success'
            })
            this.back()
          }
        })
        .catch(() => {
          this.submitLoading = false
        })
    },
    edit(data, id) {
      this.submitLoading = true
      edit(data, id)
        .then(res => {
          // console.log(res)
          this.submitLoading = false
          if (res.meta.code === '200' && res.data != null) {
            this.$message({
              message: '编辑人员成功！',
              type: 'success'
            })
            this.back()
          } else {
            this.$message({
              message: res.meta.message,
              type: 'error'
            })
          }
        })
        .catch(() => {
          this.submitLoading = false
        })
    },

    selectionChange(data) {
      // console.log(row)
      const list = []
      data.forEach(e => {
        const item = this.generateData.filter(i => {
          return i.key === e
        })
        list.push(item[0])
      })
      console.log('list', list)
      this.selectData = list
    },
    getcatalogueLsit() {
      this.query()
    },
    getData() {
      // console.log(this.selectData)
      this.dialogVisible = false
      // 单选逻辑
      // if (this.selectData != '') {
      //   this.queryForm.proof_code = this.selectData[0].code
      //   this.queryForm.PROOF_CATALOG_NAME = this.selectData[0].name
      //   // 清空单选选中的值
      //   this.$refs.table.radioValue = ''
      //   console.log(this.$refs.table.radioValue)
      // }

      // 多选逻辑
      if (this.selectData != '') {
        const code = []
        const name = []
        this.selectData.forEach(e => {
          code.push(e.code)
          name.push(e.name)
        })
        this.queryForm.proof_code = code.join(',')
        this.queryForm.PROOF_CATALOG_NAME = name.join(',')
        // 清空单选选中的值
        // this.$refs.table.clearSelection()
        console.log(this.$refs.table)
      }
    },
    setEmpty() {
      this.queryForm.proof_code = ''
      this.queryForm.PROOF_CATALOG_NAME = ''
      this.value = []
    },
    delectItem() {
      this.deleteUser(this.queryForm.id)
    },
    submitForm(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          const data = {
            org_credit_code: this.queryForm.org_credit_code,
            org_name: this.queryForm.ORG_NAME,
            assistor_name: this.queryForm.ASSISTOR_NAME,
            assistor_phone: this.queryForm.ASSISTOR_PHONE,
            assistor_identity_type: this.queryForm.ASSISTOR_IDENTITY_TYPE,
            assistor_identity_number: this.queryForm.ASSISTOR_IDENTITY_NUMBER,
            proof_code: this.queryForm.proof_code,
            user_id: this.queryForm.USER_ID,
            // id: this.queryForm.id,
            // division_code: this.queryForm.division_code
            division_code: this.divisionCode
          }
          console.log(data)
          if (this.$route.query.type === 'add') {
            this.create(data)
          } else if (this.$route.query.type === 'edit') {
            // console.log(data, data.id)
            this.edit(data, this.queryForm.id)
          }
          // else if(this.$route.query.type === 'delete'){
          //   this.deleteUser(data.id)
          // }
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    depChange(val) {
      let obj = {}
      console.log(this.deptOptions, val)
      obj = this.deptOptions.find(item => {
        return item.credit_code === val
      })
      this.queryForm.org_credit_code = obj.credit_code
      this.queryForm.ORG_NAME = obj.name
    },
    getQueryType(data) {
      // const userdata = this.$store.state.user.userdata
      const accountInfo = JSON.parse(sessionStorage.getItem('accountInfo'))
      const userdata = JSON.parse(this.$store.state.user.organization)
      console.log('当前用户信息', userdata)

      if (data) {
        // const data = JSON.parse(this.$route.query.data)
        console.log(data)
        this.queryForm = {
          ASSISTOR_NAME: data.ASSISTOR_NAME,
          ORG_NAME: data.ORG_NAME,
          ASSISTOR_PHONE: data.ASSISTOR_PHONE,
          ASSISTOR_IDENTITY_TYPE: data.ASSISTOR_IDENTITY_TYPE,
          ASSISTOR_IDENTITY_NUMBER: data.ASSISTOR_IDENTITY_NUMBER,
          USER_ID: data.USER_ID,
          PROOF_CATALOG_NAME: data.PROOF_CATALOG_NAME,
          org_credit_code: data.ORG_CREDIT_CODE,
          id: data.id,
          division_code: data.DIVISION_CODE,
          proof_code: data.PROOF_CODE
        }
      }
      this.divisionCode = userdata.division_code
      this.getOrganizationList(this.divisionCode)
      console.log('this.divisionCode', this.divisionCode)
      // 编辑页面
      if (this.$route.query.type === 'edit') {
        this.pageTitle = '协查人员【' + this.queryForm.ASSISTOR_NAME + '】'
        this.isDisable = true
        this.isModify = false
      } // 删除页面
      else if (this.$route.query.type === 'delete') {
        this.pageTitle = '协查人员【' + this.queryForm.ASSISTOR_NAME + '】'
        this.isDisable = true
        this.isModify = true
      } // 查看页面
      else if (this.$route.query.type === 'detail') {
        this.pageTitle = '协查人员【' + this.queryForm.ASSISTOR_NAME + '】'
        this.isDisable = true
        this.isModify = true
      }
      // this.queryForm.division_code = userdata.division_code
      // 判断政务工作人员
      // const codeList = userdata.roles.map(v => {
      //   return v.code
      // })
      // if (codeList.indexOf('ZHENG_WU_GONG_ZUO_REN_YUAN') !== -1 && this.$route.query.type === 'add') {
      //   this.isDisable = true
      //   this.queryForm.division_code = userdata.division_code
      //   getDivisionByCode(userdata.division_code).then(res => {
      //     this.queryForm.ORG_NAME = userdata.name
      //     this.queryForm.org_credit_code = userdata.credit_code
      //   })
      // }
    },
    back() {
      this.$router.push({
        name: 'investigationpeoMange'
      })
    },
    toDelete() {
      this.$router.push({
        name: 'investigationpeoAdd',
        query: {
          type: 'delete',
          id: this.$route.query.id
        }
      })
      this.getQueryType(this.nowData)
    },
    toEdit() {
      this.$router.push({
        name: 'investigationpeoAdd',
        query: {
          type: 'edit',
          id: this.$route.query.id
        }
      })
      this.getQueryType(this.nowData)
    },
    setDivisionCodeAndName(data){
      console.log('setDivisionCodeAndName',data)
      this.queryForm.ORG_NAME = ''
      this.deptOptions = []
      this.getOrganizationList(data.code)
       
    },
    screenWidth() {
      if (screen.width == 1920) {
        this.is1366 = false
        console.log('1920*1080')
      } else if (screen.width == 1366) {
        console.log('1366*768')
        this.is1366 = true
      } else {
        this.is1366 = false
      }
    }
  }
}
</script>

<style scoped>
.investigationArchive /deep/ .el-table .cell,
.el-table--border .el-table__cell:first-child .cell {
  text-align: center;
}
.content-header-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.content-header-title i {
  cursor: pointer;
}
.content-header-title h1 {
  font-size: 24px;
  width: 60%;
  word-wrap: break-word;
  word-break: normal;
  flex: 1;
}
.content-header-title-btn {
  display: flex;
  justify-content: flex-end;
  flex: 0 0 240px;
}
.select {
  width: 100%;
}
.select-title {
  text-align: center;
  margin-top: 0px;
}
.investigationArchive /deep/.el-dialog__body {
  padding-top: 0px;
  padding-bottom: 0px;
}
.dialogwrap {
  height: 350px;
}
.custom-table /deep/.el-card__body {
  /* padding-top: 0px;
  padding-bottom: 0px; */
  padding: 0px;
}
.dialogwrap .el-check-form {
  height: 50px;
}
.dialogwrap .el-check-form .el-form-item {
  margin-bottom: 0px;
}
.amend {
  background: #f4f4f4;
  text-align: center;
  padding: 10px 0;
}
.amendInfo {
  text-align: center;
  padding: 10px 0;
}
.dialog-footer {
  text-align: center;
}
.transfer ::v-deep .el-button--primary.is-disabled {
  display: block;
}
.transfer ::v-deep .el-button + .el-button {
  margin-left: 0;
}
.transfer ::v-deep .el-transfer-panel__header .el-checkbox__input {
  display: none;
}
.transfer ::v-deep .el-transfer-panel {
  width: 278px;
}
.transfer ::v-deep .el-transfer-panel__body {
  width: 264px;
  /* height: 214px; */
}
.transfer ::v-deep .el-transfer-panel .el-transfer-panel__footer {
  top: 50px;
  bottom: 0;
  width: 0%;
  left: initial;
  right: 251px;
  display: inline-block;
}
.transfer-input {
  display: flex;
  width: 230px;
}
.transfer-input .transfer-footer {
  margin-left: 5px;
}
/*  */
.transfer ::v-deep .el-transfer-panel:first-child .el-transfer-panel__filter {
  width: 75%;
  height: 30px;
}
/* :first-child */
.transfer ::v-deep .el-transfer-panel:first-child .el-transfer-panel__filter input {
  display: none;
}

.transfer ::v-deep .el-transfer-panel__filter .el-input__icon {
  display: none;
}
.transfer ::v-deep .el-transfer-panel .el-transfer-panel__header .el-checkbox .el-checkbox__label span {
  display: none;
}
.transfer ::v-deep .el-transfer-panel__list {
  overflow-y: scroll;
}
.nopadding ::v-deep .el-card__body {
  padding: 0;
}
.haspadding ::v-deep .el-card__body {
  padding: 20px;
}
.margin-left-10 {
  margin-left: 10px;
}
.info-title {
  font-size: 20px;
  color: #333333;
}
.info-wrap {
  display: flex;
}
.info-wrap img {
  width: 35px;
  height: 35px;
  margin-right: 10px;
}
.descriptions {
  margin-top: 10px;
  padding: 0 10px;
}
</style>
