<template>
  <div class="content-wrapper">
    <section class="content-header">
      <el-card class="box-card">
        <el-tabs v-model="activeName" @tab-click="handleClick">
          <el-tab-pane label="协查信息" name="first">
            <assistInfo></assistInfo>
          </el-tab-pane>
          <el-tab-pane label="历史协查结果" name="second">历史协查结果</el-tab-pane>
        </el-tabs>
      </el-card>
      <el-card class="box-card card1">
        <div class="sendfrom">
          <div class="sendfrom-title">协查处理</div>
          <div class="from-wrap">
            <el-form ref="form" :model="form" label-width="100px">
              <el-form-item label="协查结果">
                <el-select v-model="form.value" placeholder="请选择">
                  <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="协查意见">
                <el-input v-model="form.name"></el-input>
              </el-form-item>
              <el-form-item label="协查人">
                <span>王五</span>
              </el-form-item>
              <el-form-item label="协查部门">
                <span>国土局</span>
              </el-form-item>
            </el-form>
          </div>
        </div>
      </el-card>
    </section>
  </div>
</template>

<script>
import assistInfo from './components/assistInfo'
export default {
  name: 'ProofExemptionAdminInfo',
  components: {
    assistInfo
  },
  data() {
    return {
      activeName: 'first',
      form: {
        name: '',
        value: ''
      },
      options: [
        { value: '1', label: '通过' },
        { value: '2', label: '不通过' }
      ]
    }
  },

  mounted() {},

  methods: {
    handleClick() {}
  }
}
</script>

<style lang="scss" scoped>
.card1 {
  margin-top: 10px;
  .sendfrom {
    width: 300px;
    font-size: 18px;
    font-weight: 550;
  }
  .sendfrom ::v-deep .el-form-item__label {
    font-size: 18px;
    font-weight: 550;
    color: #000;
  }
  .from-wrap {
    margin-left: 109px;
    width: 300px;

    span {
      font-size: 18px;
      font-weight: 550;
      color: gray;
    }
  }
}
</style>