<template>
  <div class="content-wrapper investigationArchive padding-10">
    <CardTitle :title-name="titleName">
      <template>
        <el-button v-permission="'assist:user:manage:add'" type="primary" @click="add()">新建</el-button>
        <el-button v-permission="'assist:user:manage:import'" type="primary" @click="importData()">导入</el-button>
      </template>
    </CardTitle>
    <!-- <section class="content-header"> -->
    <el-card class="box-card">
      <el-form ref="form" :model="queryForm" label-width="180px" class="el-check-form" :rules="rules">
        <el-row :gutter="24" justify="center" type="flex">
          <!-- <el-col :span="10">
              <el-form-item label="协查部门统一信用代码">
                <el-input v-model="queryForm.orgCreditCode" clearable placeholder="请输入协查部门统一信用代码" />
              </el-form-item>
          </el-col>-->
          <el-col :span="10">
            <el-form-item label="部门名称">
              <el-input v-model="queryForm.org_name" clearable placeholder="请输入部门名称" />
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="协查人员姓名">
              <el-input v-model="queryForm.assistor_name" clearable placeholder="请输入协查人员姓名" />
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label-width="0px">
              <el-button type="primary" @click="userPagebySearch">查询</el-button>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24" justify="center" type="flex">
          <el-col :span="10">
            <el-form-item label="证明目录名称">
              <el-input v-model="queryForm.proof_catalog_name" clearable placeholder="请输入证明目录名称" />
            </el-form-item>
          </el-col>
          <el-col :span="10" />
          <el-col :span="4" />
        </el-row>
      </el-form>
      <div style="color: #888; padding:20px 10px 0" class="dashed-line">
        共
        <span class="text-red">{{ tableData.total }}</span>条符合查询条件
        <span v-if="tableData.content.length!=0">，以下是第1至第{{ tableData.content.length }}项</span>
      </div>
      <custom-table
        ref="table"
        :is-card-type="false"
        :table-data="tableData"
        :table-header="tableHeader"
        :stripe="false"
        :table-tools="tableTools"
        style="margin-top: 10px"
        @query="query"
        @refresh="query(1)"
      >
        <!-- <template #index="{$index}">{{ $index+1 }}</template> -->
        <template #operation="{ row }">
          <div>
            <el-button v-permission="'assist:user:manage:view'" type="text" @click="detail(row)">查看</el-button>
            <el-button v-permission="'assist:user:manage:update'" type="text" @click="edit(row)">修改</el-button>
            <el-button class="table-delete" v-permission="'assist:user:manage:delete'" type="text" @click="deleteItem(row)">删除</el-button>
          </div>
        </template>
      </custom-table>
    </el-card>
    <!-- </section> -->
    <el-dialog title="协查人员信息导入" :visible.sync="dialogVisible" width="40%" :before-close="cancle">
      <div class>
        <p>请选择您要导入的数据</p>
        <el-row :gutter="24" justify="center" align="middle" type="flex">
          <el-col :span="20">
            <el-input v-model="fileForm.fileName" placeholder />
          </el-col>
          <el-col :span="4">
            <el-upload
              :auto-upload="false"
              :show-file-list="false"
              class="upload-demo"
              action
              :on-change="handleChange"
              :file-list="fileList"
              accept=".xls, .xlsx"
            >
              <el-button type="primary">浏览</el-button>
            </el-upload>
          </el-col>
        </el-row>
        <p class="tip">
          <i class="el-icon-info" /> 温馨提示：请选择以xls/xlsx为后缀名的文件且上传的文件不能超过1m！
          <el-button type="text" @click="getUserTemplate">下载导入模板</el-button>
        </p>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="cancle()">取 消</el-button>
        <el-button type="primary" :loading="fileLoading" @click="importFile()">导入</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import CustomTable from '@/components/Element/Table'
import { userPage, getUserTemplate, importFile } from '@/api/assistInvestigate'
import { dataURLtoDownload, getIsWhitelist, getFileType } from '@/utils/index.js'
import { validPrefix } from '@/utils/validate'
import papeTitle from '@/components/papeTitle'
import CardTitle from '@/components/CardTitle'
export default {
  name: 'ProofInvestigationHandel',
  components: {
    CustomTable,
    papeTitle,
    CardTitle
  },

  data() {
    return {
      queryForm: {
        org_name: '',
        assistor_name: '',
        proof_catalog_name: '',
        // orgCreditCode: '',
        page_direction: 'DESC',
        page_number: 1,
        page_size: 10
      },
      fileForm: {
        fileName: '',
        file: ''
      },
      rules: {},
      dialogVisible: false,
      fileList: [],
      whitelist: ['xlsx', 'xls'],
      numberOfElements: '',
      tableData: {
        border: false,
        content: [
          // {
          //   deptName: '汕尾市公安局',
          //   proveName: '无犯罪证明',
          //   assistantName: '张三'
          // }
        ], // 表格数据

        loading: false, // 控制表格加载效果
        total: 0, // 分页查询时的总数，去掉该属性，不显示分页条
        currentPage: 1, // 当前页码
        pageSize: 10,
        // maxHeight: '200px',
        isShowIndex: true,
        pageDirection: 'desc',
        isShowSelection: false // 是否显示多选框，默认false
      },
      tableHeader: [
        // { label: '序号', prop: 'index', minWidth: '80px', slot: 'index' },
        { label: '部门名称', prop: 'ORG_NAME', align: 'left', minWidth: '160px' }, // 配置slot属性，可支持使用插槽
        {
          label: '证明目录名称',
          prop: 'PROOF_CATALOG_NAME',
          align: 'left',
          minWidth: '100px'
        },
        { label: '协查人员姓名', prop: 'ASSISTOR_NAME', align: 'left', minWidth: '120px' },

        {
          label: '操作',
          prop: 'operation',
          width: '180px',
          fixed: 'right',
          align: 'left',
          slot: 'operation'
        }
        //  { label: '归档', prop: 'investigationStatus',slot: 'investigationStatus', width: '120px', fixed: 'right'}
      ],
      tableTools: [],
      fileBase64: {
        name: '',
        base64: ''
      },
      fileLoading: false,
      titleName: '协查人员管理'
    }
  },

  mounted() {
    this.query()
  },

  methods: {
    detail(row) {
      this.$router.push({
        name: 'investigationpeoAdd',
        query: {
          type: 'detail',
          // data: JSON.stringify(row),
          id: row.id
        }
      })
    },
    query() {
      this.userPage()
    },
    deleteItem(row) {
      this.$router.push({
        name: 'investigationpeoAdd',
        query: {
          type: 'delete',
          data: JSON.stringify(row),
          id: row.id
        }
      })
    },
    edit(row) {
      this.$router.push({
        name: 'investigationpeoAdd',
        query: {
          type: 'edit',
          id: row.id,
          data: JSON.stringify(row)
        }
      })
    },
    add() {
      this.$router.push({
        name: 'investigationpeoAdd',
        query: {
          type: 'add'
        }
      })
    },
    getUserTemplate() {
      getUserTemplate().then(res => {
        if (res.meta.code === '200' && res.data !== null) {
          this.fileBase64 = {
            name: '协查人员信息导入模板.xlsx',
            base64: res.data
          }
          this.downFileBase64()
        }
      })
    },
    importFile() {
      if (this.fileForm.file != '') {
        this.fileLoading = true
        const f = new FormData()
        f.append('file', this.fileForm.file)
        importFile(f)
          .then(res => {
            this.fileLoading = false
            // console.log(res)
            if (res.meta.code === '200' && res.data !== null) {
              const tipArray = res.data.split('，')
              if (tipArray.length > 1) {
                let newStr = tipArray[2].replace(/\//g, '<br/>')

                // this.insertStr(newStr,5,'<span>')
                newStr = '<span class="tiptitle">' + this.insertStr(newStr, 6, '：</span>')
                console.log(newStr)
                this.$message({
                  // duration: 0,
                  iconClass: 'el-icon-warning-outline',
                  customClass: 'tipmessage',
                  dangerouslyUseHTMLString: true,
                  message: '<span class="tiptitle">' + tipArray[0] + ',' + tipArray[1] + '<span class="tipitem">' + newStr + '</span>' + '</span>',
                  // message: '<strong>这是 <i>HTML</i> 片段</strong>',
                  type: 'warning'
                })
              } else {
                this.$message({
                  iconClass: 'el-icon-circle-check',
                  // duration: 0,
                  message: tipArray[0],
                  type: 'success',
                  customClass: 'tipmessage1'
                })
              }
              this.fileForm.fileName = ''
              this.fileForm.file = ''
              this.dialogVisible = false
              this.query()
            } else {
              this.$message({
                message: res.meta.message,
                type: 'error'
              })
              this.fileForm.fileName = ''
              this.fileForm.file = ''
              this.dialogVisible = false
            }
          })
          .catch(err => {
            this.fileLoading = false
            this.$message({
              message: '导入失败',
              type: 'error'
            })
            this.fileForm.fileName = ''
            this.fileForm.file = ''
            this.dialogVisible = false
          })
      } else {
        this.$message({
          message: '请选择文件',
          type: 'warning'
        })
      }
    },
    downFileBase64() {
      dataURLtoDownload(this.fileBase64.base64, this.fileBase64.name)
    },
    userPagebySearch() {
      this.tableData.currentPage = 1
      this.tableData.pageSize = 10

      this.userPage()
    },
    userPage() {
      this.queryForm.page_number = this.tableData.currentPage
      this.queryForm.page_size = this.tableData.pageSize
      this.tableData.loading = true
      userPage(this.queryForm)
        .then(res => {
          this.tableData.loading = false
          if (res.meta.code === '200' && res.data != null) {
            this.tableData.content = res.data.content
            this.tableData.total = Number(res.data.total_elements)
            this.numberOfElements = res.data.numberOfElements
          } else {
            this.tableData.content = []
            this.tableData.total = 0
            this.numberOfElements = 0
          }
        })
        .catch(err => {
          this.tableData.loading = false
        })
    },
    importData() {
      this.dialogVisible = true
    },
    handleChange(file, fileList) {
      const fileName = getFileType(file.name).fileName
      const isLt10m = file.size / (1024 * 1024) < 1
      if (!isLt10m) {
        this.$message.error('文件大小不能超过1m！')
      } else {
        if (fileName.indexOf('http') != -1 || validPrefix(fileName)) {
          this.$message.error('文件名开头包含特殊符号！')
        } else {
          if (!getIsWhitelist(file.name, this.whitelist)) {
            this.$message.error(`请重新选择以${this.whitelist.join(',')}为后缀名的文件！`)
          } else {
            this.fileForm.fileName = file.name
            this.fileForm.file = file.raw
          }
        }
      }
    },
    getFileType(filePath) {
      const startIndex = filePath.lastIndexOf('.')
      if (startIndex != -1) {
        return filePath.substring(startIndex + 1, filePath.length).toLowerCase()
      } else {
        return ''
      }
    },
    cancle() {
      this.dialogVisible = false
      this.fileForm.fileName = ''
      this.fileForm.file = ''
      this.fileList = []
    },
    /**
     * 字符串指定位置添加元素
     * @param str1:原字符串
     * @param n:插入位置
     * @param str2:插入元素
     * @return  拼接后的字符串
     */

    insertStr(str1, n, str2) {
      let s1 = ''
      let s2 = ''
      if (str1.length < n) {
        return str1 + str2
      } else {
        s1 = str1.substring(0, n)
        s2 = str1.substring(n, str1.length)
        return s1 + str2 + s2
      }
    }
  }
}
</script>

<style scoped>
.investigationArchive /deep/ .el-table .cell,
.el-table--border .el-table__cell:first-child .cell {
  /* text-align: center; */
}
.top {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.top h1 {
  font-size: 24px;
}
.investigationArchive /deep/.el-upload__input {
  display: none;
}
.tip {
  display: flex;
  align-items: center;
}
.tip i {
  color: #e6a23c;
  font-size: 20px;
  margin-right: 10px;
}
</style>
<style>
.tiptitle {
  color: #444;
}
.tipitem {
  color: rgba(0, 0, 0, 0.424);
}
.tipmessage {
  background-color: #fdf6ec;
  border-color: #faecd8;
  align-items: start;
  line-height: 30px;
}
.tipmessage i {
  margin-top: 5px;
  margin-right: 15px;
  color: #e6a23c;
  font-size: 20px;
}
.tipmessage .el-message__content {
  line-height: 25px;
}

.tipmessage1 {
  background-color: #f0f9eb;
  border-color: #57ec0d;
  align-items: start;
  color: #444;
  /* line-height: 30px; */
}
.tipmessage1 i {
  color: #67c23a;
  /* margin-top: 5px; */
  margin-right: 15px;
  font-size: 20px;
}
.tipmessage1 .el-message__content {
  line-height: 18px;
}
.table-delete {
  color: #ff2b2b;
}
</style>
