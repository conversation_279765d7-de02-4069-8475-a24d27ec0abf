import Layout from '@/layout'
const proofInvestigationAdmin = [
  {
    path: '/proofInvestigationAdmin',
    component: Layout,
    redirect: 'noRedirect',
    name: 'proofInvestigationAdmin',
    meta: {
      title: '协查管理',
      icon: ''
    },
    children: [
      {
        path: 'investigationServices',
        name: 'investigationServices',
        component: () => import('@/views/proof-investigation-admin/index'),
        alwaysShow: true,
        // redirect: 'DepartmentList',
        meta: { title: '协查服务' },
        children: [
          {
            path: 'proof_investigation_admin',
            name: 'proof_investigation_admin',
            component: () => import('@/views/proof-investigation-admin/handle/index'),
            meta: {
              title: '证明协查处理',
              keepAlive: true
            },
            children: [
              {
                path: 'investigationServicesDetail',
                name: 'investigationServicesDetail',
                component: () => import('@/views/proof-investigation-admin/investigationDetail/handleIndex'),
                hidden: true,
                meta: {
                  keepAlive: true,
                  title: '查看证明协查处理详情',
                  activeMenu: '/proofInvestigationAdmin/investigationServices/proof_investigation_admin',
                  fatherPath: 'proof_investigation_admin'
                }
              }
            ]
          },
          {
            path: 'investigation_archives',
            name: 'investigation_archives',
            component: () => import('@/views/proof-investigation-admin/archives/index'),
            meta: {
              title: '证明协查档案',
              keepAlive: true
            },
            children: [
              {
                path: 'investigationDetail',
                name: 'investigationDetail',
                component: () => import('@/views/proof-investigation-admin/investigationDetail/archivesIndex'),
                hidden: true,
                meta: {
                  keepAlive: true,
                  title: '查看证明协查处理详情',
                  activeMenu: '/proofInvestigationAdmin/investigationServices/proof_investigation_admin',
                  fatherPath: 'investigation_archives'
                }
              }
            ]
          },
          
          // {
          //   path: 'investigationDetail',
          //   name: 'investigationDetail',
          //   component: () => import('@/views/proof-investigation-admin/investigationDetail/index'),
          //   hidden: true,
          //   meta: {
          //     keepAlive: true,
          //     title: '查看证明协查处理详情',
          //     activeMenu: '/proofInvestigationAdmin/investigationServices/proof_investigation_admin'
          //   }
          // }
        ]
      },
      {
        path: 'collaboratingPersonnel',
        component: () => import('@/views/proof-investigation-admin/index'),
        name: 'collaboratingPersonnel',
        alwaysShow: true,
        meta: { title: '协查人员' },
        children: [
          {
            path: 'investigationpeoMange',
            name: 'investigationpeoMange',
            component: () => import('@/views/proof-investigation-admin/investigationpeoMange/index'),
            meta: {
              title: '协查人员管理',
              keepAlive: true
            }
          },
          {
            path: 'investigationpeoAdd',
            name: 'investigationpeoAdd',
            component: () => import('@/views/proof-investigation-admin/investigationpeoMange/add'),
            hidden: true,
            meta: {
              keepAlive: true,
              activeMenu: '/proofInvestigationAdmin/collaboratingPersonnel/investigationpeoMange',
              fatherPath: 'investigationpeoMange'
            }
          }
        ]
      },
      {
        path: 'investigationApplication',
        component: () => import('@/views/proof-investigation-admin/index'),
        name: 'investigationApplication',
        alwaysShow: true,
        // redirect: 'DepartmentList',
        meta: { title: '协查申请' },
        children: [
          {
            path: 'investigationApply',
            name: 'investigationApply',
            component: () => import('@/views/proof-investigation-admin/investigationApply/index'),
            meta: {
              title: '证明协查申请',
              keepAlive: true
            }
          },
          {
            path: 'investigationapplyAdd',
            name: 'investigationapplyAdd',
            component: () => import('@/views/proof-investigation-admin/investigationApply/add'),
            hidden: true,
            meta: {
              keepAlive: true,
              activeMenu: '/proofInvestigationAdmin/investigationApplication/investigationApply',
              fatherPath: 'investigationApply'
            }
          }
        ]
      }
    ]
  }
]

export default proofInvestigationAdmin
