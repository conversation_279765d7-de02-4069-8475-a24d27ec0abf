/** When your routing table is too long, you can split it into small modules**/

import Layout from '@/views/login/layout'

const loginRouter = [
  {
    path: '/white',
    component: Layout,
    redirect: '/white/login',
    hidden: true,
    name: 'White',
    menuType: 'licc', // 骨架菜单:licc
    children: [
      {
        path: 'login',
        component: () => import('@/views/login/index'),
        name: 'WhiteLogin',
        menuType: 'licc' // 骨架菜单:licc
      },
      {
        path: 'passwd',
        component: () => import('@/views/login/passwd'),
        name: 'WhitePasswd',
        menuType: 'licc' // 骨架菜单:licc
      }
    ]
  }
]

export default loginRouter
