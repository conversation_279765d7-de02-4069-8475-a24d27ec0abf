/** When your routing table is too long, you can split it into small modules**/

import Layout from '@/layout'

const platformRouter = [
  {
    path: '/Interface',
    component: Layout,
    redirect: 'noRedirect',
    name: 'Interface',
    menuType: 'licc', // 骨架菜单:licc
    meta: {
      title: '接口管理',
      icon: ''
    },
    children: [
      {
        path: 'accessSystem',
        component: () => import('@/views/access-system/accessSystemApplication/index'),
        name: 'accessSystem',
        menuType: 'licc', // 骨架菜单:licc
        alwaysShow: true,
        // redirect: 'DepartmentList',
        meta: { title: '应用接入系统' },
        children: [
          {
            path: 'accessSystemApplication',
            component: () => import('@/views/access-system/accessSystemApplication/list'),
            name: 'accessSystemApplication',
            menuType: 'licc', // 骨架菜单:licc
            meta: {
              title: '接入应用系统',
              breadcrumbBtnComponentId: 'AccessSystem'
            }
          },
          {
            path: 'accessSystemApplicationDetail',
            component: () => import('@/views/access-system/accessSystemApplication/detail.vue'),
            name: 'accessSystemApplicationDetail',
            menuType: 'licc', // 骨架菜单:licc
            hidden: true,
            meta: {
              title: '接入应用系统',
              breadcrumbBtnComponentId: 'accessSystemrDetail',
              activeMenu: '/Interface/accessSystem/accessSystemApplication',
              fatherPath: 'accessSystemApplication',
              originalMeta: {
                title: '接入应用系统详情',
                activeMenu: '/Interface/accessSystem/accessSystemApplication',
                path: 'accessSystemrDetail'
              }
            }
          },
          {
            path: 'accessSystemApplicationEdit',
            component: () => import('@/views/access-system/accessSystemApplication/edit.vue'),
            name: 'accessSystemApplicationEdit',
            menuType: 'licc', // 骨架菜单:licc
            hidden: true,
            meta: {
              title: '接入应用系统',
              breadcrumbBtnComponentId: 'AssestSystemtAdd',
              activeMenu: '/Interface/accessSystem/accessSystemApplication',
              fatherPath: 'accessSystemApplication'
            }
          },
          {
            path: 'InterfaceManager',
            component: () => import('@/views/access-system/InterfaceManager/list'),
            name: 'InterfaceManager',
            menuType: 'licc', // 骨架菜单:licc
            meta: {
              title: '接口管理',
              breadcrumbBtnComponentId: 'InterfaceManagerAdd'
            }
          },
          {
            path: 'InterfaceManagerDetail',
            component: () => import('@/views/access-system/InterfaceManager/detail'),
            name: 'InterfaceManagerDetail',
            menuType: 'licc', // 骨架菜单:licc
            hidden: true,
            meta: {
              title: '接口管理',
              breadcrumbBtnComponentId: 'InterfaceManagerDetail',
              activeMenu: '/Interface/accessSystem/InterfaceManager',
              fatherPath: 'InterfaceManager',
              originalMeta: {
                title: '接口管理详情',
                activeMenu: '/Interface/accessSystem/InterfaceManager',
                path: 'InterfaceManager'
              }
            }
          },
          {
            path: 'InterfaceManagerAdd',
            component: () => import('@/views/access-system/InterfaceManager/add'),
            name: 'InterfaceManagerAdd',
            menuType: 'licc', // 骨架菜单:licc
            hidden: true,
            meta: {
              title: '接口管理',
              activeMenu: '/Interface/accessSystem/InterfaceManager',
              fatherPath: 'InterfaceManager'
            }
          }
        ]
      }
    ]
  }
]

export default platformRouter
