/** When your routing table is too long, you can split it into small modules**/

import Layout from '@/views/login/layout'

const userinfo = [
  {
    path: 'userinfo',
    component: Layout,
    redirect: 'userinfo',
    hidden: false,
    name: 'userinfo',
    menuType: 'licc', // 骨架菜单:licc
    meta: {
      title: '个人信息',
      icon: ''
    },
    children: [
      {
        path: 'userpassword',
        component: Layout,
        name: 'userpassword',
        menuType: 'licc', // 骨架菜单:licc
        meta: {
          title: '修改密码',
          icon: ''
        },
        hidden: false,
        children: null
      },
      {
        path: 'userexit',
        component: Layout,
        name: 'userexit',
        menuType: 'licc', // 骨架菜单:licc
        meta: {
          title: '退出登录',
          icon: ''
        },
        hidden: false,
        children: null
      }
    ]
  }
]

export default userinfo
