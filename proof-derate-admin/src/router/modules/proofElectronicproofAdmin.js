const moduleRoutes = [
  {
    path: '/proof_electronicproof_admin/home',
    component: () => import('@/views/proof-electronicproof-admin/home'),
    redirect: 'noRedirect',
    name: 'proof_electronicproof_admin',
    meta: {
      title: '电子证明',
      icon: ''
    },
    children: [
      {
        path: 'electronicServices',
        name: 'ElectronicServices',
        component: () => import('@/views/common/index'),
        alwaysShow: true,
        // redirect: 'DepartmentList',
        meta: { title: '证明业务' },
        children: [
          {
            path: 'electronicArchives',
            name: 'ElectronicArchives',
            component: () => import('@/views/proof-electronicproof-admin/archives/index'),
            meta: {
              title: '证明档案',
              keepAlive: true
            }
          },
          {
            path: 'electronicArchivesDetail',
            name: 'ElectronicArchivesDetail',
            hidden: true,
            component: () => import('@/views/proof-electronicproof-admin/archives/detail.vue'),
            meta: {
              title: '证明档案',
              // activeMenu: '/proof_electronicproof_admin/home/<USER>',
              fatherPath: 'electronicArchives'
            }
          }
        ]
      }
    ]
  }
]

export default moduleRoutes
