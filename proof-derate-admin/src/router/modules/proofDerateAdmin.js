
import Layout from '@/layout'
const moduleRoutes = [
  /** ***************
   *
   * 证明管理--首页
   *
   ****************/

  {
    path: '/proofDerateAdmin',
    component: Layout,
    redirect: 'noRedirect',
    name: 'proofDerateAdmin',
    meta: {
      title: '证明管理',
      icon: ''
    },
    children: [
      {
        path: 'cleaningService',
        name: 'cleaningService',
        component: () => import('@/views/proof-derate-admin/index'),
        alwaysShow: true,
        meta: {
          title: '证明材料清理服务',
          nav: false,
          nav_key: false
        },
        children: [
          {
            path: 'itemBiz/itemClear',
            name: 'item_clear',
            component: () =>
              import('@/views/proof-derate-admin/itemBiz/itemClear/index.vue'),
            meta: {
              title: '证明材料清理',
              rolePermissions: ['ZMGL:YWBL:SXZMQL:LIST']
              // keepAlive:true,
            }
          },
          {
            path: 'itemBiz/itemClear/toClear',
            name: 'item_to_clear',
            component: () =>
              import(
                '@/views/proof-derate-admin/itemBiz/itemClear/toClear/index.vue'
              ),
            hidden: true,
            meta: {
              nav_key: 'item_clear',
              rolePermissions: ['ZMGL:YWBL:SXZMQL:LIST'],
              activeMenu: '/proofDerateAdmin/cleaningService/itemBiz/itemClear',
              fatherPath: 'itemBiz/itemClea'
            }
          },
          {
            path: 'itemBiz/itemClear/toClear/edit',
            name: 'item_to_clear_edit',
            component: () =>
              import(
                '@/views/proof-derate-admin/itemBiz/itemClear/toClear/edit/index.vue'
              ),
            hidden: true,
            meta: {
              nav_key: 'item_clear',
              activeMenu: '/proofDerateAdmin/cleaningService/itemBiz/itemClear',
              fatherPath: 'itemBiz/itemClear'
            }
          },
          {
            path: 'itemBiz/itemClear/cleared',
            name: 'item_cleared',
            component: () =>
              import(
                '@/views/proof-derate-admin/itemBiz/itemClear/cleared/index.vue'
              ),
            hidden: true,
            meta: {
              rolePermissions: ['ZMGL:YWBL:SXZMQL:LIST']
            }
          },
          {
            path: 'itemBiz/itemClear/cleared/add',
            name: 'item_cleared_add',
            component: () =>
              import(
                '@/views/proof-derate-admin/itemBiz/itemClear/cleared/add/index.vue'
              ),
            hidden: true,
            meta: {
              nav_key: 'item_clear',
              rolePermissions: ['ZMGL:YWBL:SXZMQL:XZZM']
            }
          },
          {
            path: 'itemBiz/itemClear/cleared/info/proof',
            name: 'item_cleared_info_proof',
            component: () =>
              import(
                '@/views/proof-derate-admin/itemBiz/itemClear/cleared/info/proof.vue'
              ),
            hidden: true,
            meta: {
              nav_key: 'item_clear',
              rolePermissions: ['ZMGL:YWBL:SXZMQL:INFO']
            }
          },
          {
            path: 'itemBiz/itemClear/cleared/info/way',
            name: 'item_cleared_info_way',
            component: () =>
              import(
                '@/views/proof-derate-admin/itemBiz/itemClear/cleared/info/way.vue'
              ),
            hidden: true,
            meta: {
              nav_key: 'item_clear',
              activeMenu: '/proofDerateAdmin/cleaningService/itemBiz/itemClear',
              fatherPath: 'itemBiz/itemClear'
            }
          },
          {
            path: 'itemBiz/itemClear/info/firstDraft',
            name: 'item_clear_info_first_draft',
            component: () =>
              import(
                '@/views/proof-derate-admin/itemBiz/itemClear/firstDraft.vue'
              ),
            hidden: true,
            meta: {
              nav_key: 'item_clear',
              rolePermissions: ['ZMGL:YWBL:SXZMQL:LIST']
            }
          },
          {
            path: 'itemBiz/itemClear/noClear',
            name: 'item_not_clear',
            component: () =>
              import(
                '@/views/proof-derate-admin/itemBiz/itemClear/noClear/index.vue'
              ),
            hidden: true,
            meta: {
              rolePermissions: ['ZMGL:YWBL:SXZMQL:LIST']
            }
          },
          {
            path: 'itemBiz/itemCarding',
            name: 'item_carding',
            component: () =>
              import(
                '@/views/proof-derate-admin/itemBiz/itemCarding/index.vue'
              ),
            meta: {
              title: '证明材料梳理',
              rolePermissions: ['ZMGL:YWBL:SXZMSL:LIST']
            }
          },
          {
            path: 'itemBiz/itemCarding/info/proof',
            name: 'item_carding_info_proof',
            component: () =>
              import(
                '@/views/proof-derate-admin/itemBiz/itemCarding/info/proof.vue'
              ),
            hidden: true,
            meta: {
              nav_key: 'item_carding',
              rolePermissions: ['ZMGL:YWBL:SXZMSL:LIST']
            }
          },
          {
            path: 'itemBiz/itemCarding/info/way',
            name: 'item_carding_info_way',
            component: () =>
              import(
                '@/views/proof-derate-admin/itemBiz/itemCarding/info/way.vue'
              ),
            hidden: true,
            meta: {
              nav_key: 'item_carding',
              activeMenu:
                '/proofDerateAdmin/cleaningService/itemBiz/itemCarding',
              fatherPath: 'itemBiz/itemCarding'
            }
          },
          {
            path: 'itemBiz/itemCarding/info/firstDraft',
            name: 'item_carding_info_first_draft',
            component: () =>
              import(
                '@/views/proof-derate-admin/itemBiz/itemCarding/info/firstDraft.vue'
              ),
            hidden: true,
            meta: {
              nav_key: 'item_carding',
              rolePermissions: ['ZMGL:YWBL:SXZMSL:LIST']
            }
          },
          {
            path: 'itemBiz/itemCarding/edit',
            name: 'item_carding_edit',
            component: () =>
              import(
                '@/views/proof-derate-admin/itemBiz/itemCarding/edit/index.vue'
              ),
            hidden: true,
            meta: {
              nav_key: 'item_carding',
              rolePermissions: ['ZMGL:YWBL:SXZMSL:LIST']
            }
          },
          {
            path: 'itemBiz/itemAudit',
            name: 'item_audit',
            component: () =>
              import('@/views/proof-derate-admin/itemBiz/itemAudit/index.vue'),
            meta: {
              title: '证明材料审核',
              rolePermissions: ['ZMGL:YWBL:SXZMSH:LIST']
            }
          },
          {
            path: 'itemBiz/itemAudit/info/way',
            name: 'item_audit_info_way',
            component: () =>
              import(
                '@/views/proof-derate-admin/itemBiz/itemAudit/info/way.vue'
              ),
            hidden: true,
            meta: {
              nav_key: 'item_audit',
              activeMenu: '/proofDerateAdmin/cleaningService/itemBiz/itemAudit',
              fatherPath: 'itemBiz/itemAudit'
            }
          },
          {
            path: 'itemBiz/itemAudit/info/proof',
            name: 'item_audit_info_proof',
            component: () =>
              import(
                '@/views/proof-derate-admin/itemBiz/itemAudit/info/proof.vue'
              ),
            hidden: true,
            meta: {
              nav_key: 'item_audit',
              rolePermissions: ['ZMGL:YWBL:SXZMSH:LIST']
            }
          },
          {
            path: 'itemBiz/itemAudit/info/firstDraft',
            name: 'item_audit_info_first_draft',
            component: () =>
              import(
                '@/views/proof-derate-admin/itemBiz/itemAudit/info/firstDraft.vue'
              ),
            hidden: true,
            meta: {
              nav_key: 'item_audit',
              rolePermissions: ['ZMGL:YWBL:SXZMSH:LIST']
            }
          },

          {
            path: 'certificationManagement/data_sharing',
            name: 'certification_data_sharing',
            component: () =>
              import(
                '@/views/proof-derate-admin/certificationManagement/dataSharing/index.vue'
              ),
            hidden: true,
            meta: {
              nav_key: 'certification_List'
              // rolePermissions: ['ZMGL:ZMMLGL:ZMML:UPDATE'],
            }
          }
        ]
      },
      {
        path: 'directoryManagement',
        name: 'directoryManagement',
        component: () => import('@/views/proof-derate-admin/index'),
        alwaysShow: true,
        meta: {
          title: '证明目录管理',
          nav: false,
          nav_key: false
        },
        children: [
          {
            path: 'certificationManagement/certificationList',
            name: 'certification_List',
            component: () =>
              import(
                '@/views/proof-derate-admin/certificationManagement/certificationList/index.vue'
              ),
            meta: {
              title: '证明目录',
              rolePermissions: ['ZMGL:ZMMLGL:ZMML:LIST']
            }
          },
          {
            path: 'certificationManagement/certificationList/info',
            name: 'certification_List_info',
            component: () =>
              import(
                '@/views/proof-derate-admin/certificationManagement/certificationList/info/index.vue'
              ),
            hidden: true,
            meta: {
              title: '证明目录',
              activeMenu:
                '/proofDerateAdmin/directoryManagement/certificationManagement/certificationList',
              fatherPath: 'certificationManagement/certificationList'
            }
          },
          {
            path: 'certificationManagement/certificationList/add',
            name: 'certification_List_add',
            component: () =>
              import(
                '@/views/proof-derate-admin/certificationManagement/certificationList/add/index.vue'
              ),
            hidden: true,
            meta: {
              title: '证明目录',
              activeMenu:
                '/proofDerateAdmin/directoryManagement/certificationManagement/certificationList',
              fatherPath: 'certificationManagement/certificationList'
            }
          },
          {
            path: 'certificationManagement/certificationList/edit',
            name: 'certification_List_edit',
            component: () =>
              import(
                '@/views/proof-derate-admin/certificationManagement/certificationList/edit/index.vue'
              ),
            hidden: true,
            meta: {
              title: '证明目录',
              activeMenu:
                '/proofDerateAdmin/directoryManagement/certificationManagement/certificationList',
              fatherPath: 'certificationManagement/certificationList'
            }
          }
        ]
      },
      {
        path: 'fileManagement',
        name: 'fileManagement',
        component: () => import('@/views/proof-derate-admin/index'),
        alwaysShow: true,
        meta: {
          title: '档案管理',
          nav: false,
          nav_key: false
        },
        children: [
          {
            path: 'archiveManage/itemList',
            name: 'item_list',
            component: () =>
              import(
                '@/views/proof-derate-admin/archiveManage/itemList/index.vue'
              ),
            meta: {
              title: '事项证明清单',
              rolePermissions: ['ZMGL:DAGL:SXZMQD:LIST']
            }
          },
          {
            path: 'itemBiz/itemList/info/proof',
            name: 'item_list_info_proof',
            component: () =>
              import(
                '@/views/proof-derate-admin/archiveManage/itemList/info/proof.vue'
              ),
            hidden: true,
            meta: {
              title: '事项证明清单',
              nav_key: 'item_list',
              rolePermissions: ['ZMGL:DAGL:SXZMQD:LIST']
            }
          },
          {
            path: 'itemBiz/itemList/info/way',
            name: 'item_list_info_way',
            component: () =>
              import(
                '@/views/proof-derate-admin/archiveManage/itemList/info/way.vue'
              ),
            hidden: true,
            meta: {
              title: '事项证明清单',
              activeMenu:
                '/proofDerateAdmin/fileManagement/archiveManage/itemList',
              fatherPath: 'archiveManage/itemList'
            }
          },
          {
            path: 'itemBiz/itemList/info/firstDraft',
            name: 'item_list_info_first_draft',
            component: () =>
              import(
                '@/views/proof-derate-admin/archiveManage/itemList/info/firstDraft.vue'
              ),
            hidden: true,
            meta: {
              title: '事项证明清单',
              nav_key: 'item_list',

              rolePermissions: ['ZMGL:DAGL:SXZMQD:LIST']
            }
          },
          {
            path: 'itemBiz/itemList/edit',
            name: 'item_list_edit',
            component: () =>
              import(
                '@/views/proof-derate-admin/archiveManage/itemList/edit/index.vue'
              ),
            hidden: true,
            meta: {
              title: '事项证明清单',
              nav_key: 'item_list',
              rolePermissions: ['ZMGL:DAGL:SXZMQD:LIST']
            }
          },
          {
            path: 'archiveManage/mattersComparisonManageList',
            name: 'mattersComparisonManageList',
            component: () =>
              import(
                '@/views/proof-derate-admin/archiveManage/mattersComparisonManage/list.vue'
              ),
            meta: {
              title: '事项对比管理',
            },
          },
          {
            path: 'archiveManage/mattersComparisonManageDetail',
            name: 'mattersComparisonManageDetail',
            component: () =>
              import(
                '@/views/proof-derate-admin/archiveManage/mattersComparisonManage/detail.vue'
              ),
            hidden: true,
            meta: {
              title: '事项对比清单',
              activeMenu:
                '/proofDerateAdmin/fileManagement/archiveManage/mattersComparisonManageList',
              fatherPath: 'archiveManage/mattersComparisonManageList'
            }
          },
        ]
      },
      {
        path: 'issueManagement',
        name: 'issueManagement',
        component: () => import('@/views/proof-derate-admin/index'),
        alwaysShow: true,
        meta: {
          title: '事项管理',
          nav: false,
          nav_key: false
        },
        children: [
          {
            path: 'ItemManagement/itemProve',
            name: 'ItemManagement_itemProve',
            component: () =>
              import(
                '@/views/proof-derate-admin/ItemManagement/itemProve/index.vue'
              ),
            meta: {
              title: '事项与证明管理',
              rolePermissions: ['ZMGL:SXGL:ZMCLGL:LIST']
            }
          },
          {
            path: 'ItemManagement/itemProve/add',
            name: 'itemManagement_itemProve_add',
            component: () =>
              import(
                '@/views/proof-derate-admin/ItemManagement/itemProve/add/index.vue'
              ),
            hidden: true,
            meta: {
              activeMenu:
                '/proofDerateAdmin/issueManagement/ItemManagement/itemProve',
              fatherPath: 'ItemManagement/itemProve'
            }
          },
          {
            path: 'ItemManagement/itemProve/info',
            name: 'itemManagement_itemProve_info',
            hidden: true,
            component: () =>
              import(
                '@/views/proof-derate-admin/ItemManagement/itemProve/info/index.vue'
              ),
            meta: {
              activeMenu:
                '/proofDerateAdmin/issueManagement/ItemManagement/itemProve',
              fatherPath: 'ItemManagement/itemProve'
            }
          },
          {
            path: 'ItemManagement/itemProve/info/way',
            name: 'itemManagement_itemProve_info_way',
            component: () =>
              import(
                '@/views/proof-derate-admin/ItemManagement/itemProve/info/way.vue'
              ),
            hidden: true,
            meta: {
              nav_key: 'ItemManagement_itemProve'
            }
          },
          {
            path: 'synchronizationMangement',
            name: 'synchronizationMangement',
            component: () =>
              import(
                '@/views/proof-derate-admin/synchronizationMangement/index.vue'
              ),
            meta: {
              title: '事项同步管理'
            }
          },
          {
            path: 'synchronizationInfo',
            name: 'synchronizationInfo',
            component: () =>
              import(
                '@/views/proof-derate-admin/synchronizationMangement/info.vue'
              ),
            hidden: true,
            meta: {
              title: '事项与证明管理',
              activeMenu:
                '/proofDerateAdmin/issueManagement/synchronizationMangement',
              fatherPath: 'synchronizationMangement'
            }
          }
        ]
      },
      {
        path: 'dataThemeManagement',
        name: 'dataThemeManagement',
        component: () => import('@/views/proof-derate-admin/index'),
        alwaysShow: true,
        meta: {
          title: '数据主题管理',
          nav: false,
          nav_key: false
        },
        children: [
          {
            path: 'dataSharingManagement',
            name: 'dataSharingManagement',
            component: () =>
              import(
                '@/views/proof-derate-admin/dataSharingManagement/index.vue'
              ),
            meta: {
              title: '数据主题管理'
            }
          },
          {
            path: 'dataSharingManagementEdit',
            name: 'DataSharingManagementEdit',
            component: () =>
              import(
                '@/views/proof-derate-admin/dataSharingManagement/edit.vue'
              ),
            hidden: true,
            meta: {
              activeMenu:
                '/proofDerateAdmin/dataThemeManagement/dataSharingManagement',
              fatherPath: 'dataSharingManagement'
            }
          },
          {
            path: 'dataSharingManagementDetail',
            name: 'DataSharingManagementDetail',
            component: () =>
              import(
                '@/views/proof-derate-admin/dataSharingManagement/detail.vue'
              ),
            hidden: true,
            meta: {
              activeMenu:
                '/proofDerateAdmin/dataThemeManagement/dataSharingManagement',
              fatherPath: 'dataSharingManagement'
            }
          }
        ]
      }
    ]
  }

  /** ***************
   *
   * 证明管理--main
   *
   ****************/

  /** ***************
   *
   * 业务办理--事项证明清理
   *
   ****************/

  /** ***************
   *
   * 业务办理--事项证明梳理
   *
   ****************/

  /** ***************
   *
   * 业务办理--事项证明审核
   *
   ****************/

  /** ***************
   *
   * 证明目录管理--证明目录
   *
   ****************/

  /** ***************
   *
   * 档案管理--事项证明清单
   *

  /** ***************
   *
   * 非标准化管理--非标准事项材料
   *
   ****************/

  /** ***************
   *
   * 事项管理--证明材料管理
   *
   ****************/

  /** ***************
   *
   * 数据主题管理
   *
   ****************/

  // dataSharingManagement
]

export default moduleRoutes
