import Layout from '@/layout'
const moduleRoutes = [
  {
    path: '/proofExemptcertificatesAdmin',
    component: Layout,
    redirect: 'noRedirect',
    name: 'proofExemptcertificatesAdmin',
    meta: {
      title: '免证管理',
      icon: ''
    },

    children: [
      {
        path: 'freeService',
        component: () => import('@/views/proof-exemptcertificates-admin/proof_exemption_admin/index'),
        name: 'freeService',
        alwaysShow: true,
        // redirect: 'DepartmentList',
        meta: { title: '免证办服务' },
        children: [
          // {
          //   path: 'proof_exemptcertificates_admin/home',
          //   name: 'proof_exemptcertificates_admin',
          //   component: () => import('@/views/proof-exemptcertificates-admin/proof_exemption_admin/home.vue'),
          //   hidden: true,
          //   meta: {
          //     title: '免证办服务'
          //   }
          // },
          {
            path: 'outLink',
            name: 'outLink',
            component: () => import('@/views/proof-exemptcertificates-admin/proof_exemption_admin/license-info/index.vue'),
            hidden: true,
            meta: {
              nav_key: 'freeServiceitem1',
              keepAlive: true,
              activeMenu: '/proofExemptcertificatesAdmin/freeService/freeServiceitem1',
              fatherPath: 'freeServiceitem1'
            }
          },
          {
            path: 'freeServiceContent',
            name: 'freeServiceContent',
            component: () => import('@/views/proof-exemptcertificates-admin/proof_exemption_admin/freeService/checkpage/index.vue'),
            hidden: true,
            meta: {
              nav_key: 'freeServiceitem1',
              keepAlive: true,
              activeMenu: '/proofExemptcertificatesAdmin/freeService/freeServiceitem1',
              fatherPath: 'freeServiceitem1'
            }
          },
          {
            path: 'freeServiceitem1',
            name: 'freeServiceitem1',
            component: () => import('@/views/proof-exemptcertificates-admin/proof_exemption_admin/freeService/index.vue'),
            meta: {
              title: '免证办服务',
              keepAlive: true
            }
          },
          {
            path: 'depAssist',
            name: 'depAssist',
            component: () => import('@/views/proof-exemptcertificates-admin/proof_exemption_admin/freeService/depAssist/index.vue'),
            hidden: true,
            meta: {
              nav_key: 'freeServiceitem1',
              keepAlive: true
            }
          },
          {
            path: 'freeArchives',
            name: 'freeArchives',
            component: () => import('@/views/proof-exemptcertificates-admin/proof_exemption_admin/freeArchives/index.vue'),
            meta: {
              title: '免证办管理',
              nav_key: 'freeArchives',
              keepAlive: true
            }
          },
          {
            path: 'handlingOpinions',
            name: 'handlingOpinions',
            component: () => import('@/views/proof-exemptcertificates-admin/proof_exemption_admin/freeService/handlingOpinions/index.vue'),
            hidden: true,
            meta: {
              activeMenu: '/proofExemptcertificatesAdmin/freeService/freeArchives',
              fatherPath: 'freeArchives'
            }
          },
          {
            path: 'freeArchivesContent',
            name: 'freeArchivesContent',
            component: () => import('@/views/proof-exemptcertificates-admin/proof_exemption_admin/freeArchives/detail/index.vue'),
            hidden: true,
            meta: {
              title: '免证办服务',
              nav_key: 'freeArchives',
              keepAlive: true,
              activeMenu: '/proofExemptcertificatesAdmin/freeService/freeArchives',
              fatherPath: 'freeArchives'
            }
          }
        ]
      },
      {
        path: 'recordProcessing',
        component: () => import('@/views/proof-exemptcertificates-admin/proof_exemption_admin/index'),
        name: 'recordProcessing',
        alwaysShow: true,
        // redirect: 'DepartmentList',
        meta: { title: '办理记录' },
        children: [
          {
            path: 'handlingRecords',
            name: 'handlingRecords',
            component: () => import('@/views/proof-exemptcertificates-admin/proof_exemption_admin/handlingRecords/index.vue'),
            meta: {
              title: '办理记录',
              nav_key: 'handlingRecords',
              keepAlive: true
            }
          },
          {
            path: 'handlingRecordsDetail',
            name: 'handlingRecordsDetail',
            component: () => import('@/views/proof-exemptcertificates-admin/proof_exemption_admin/handlingRecords/detail.vue'),
            hidden: true,
            meta: {
              title: '办理记录',
              keepAlive: true,
              activeMenu: '/proofExemptcertificatesAdmin/recordProcessing/handlingRecords',
              fatherPath: 'handlingRecords'
            }
          }
        ]
      },
      {
        path: 'watermarkManagement',
        component: () => import('@/views/proof-exemptcertificates-admin/proof_exemption_admin/index'),
        name: 'watermarkManagement',
        alwaysShow: true,
        // redirect: 'DepartmentList',
        meta: { title: '水印管理' },
        children: [
          {
            path: 'dokumenWatermarkList',
            name: 'dokumenWatermarkList',
            component: () => import('@/views/proof-exemptcertificates-admin/proof_exemption_admin/dokumen/watermark-list.vue'),
            meta: {
              title: '水印管理',
              nav_key: 'dokumenWater_markList'
            }
          },
          {
            path: 'dokumenWatermarkEdit',
            name: 'dokumenWatermarkEdit',
            component: () => import('@/views/proof-exemptcertificates-admin/proof_exemption_admin/dokumen/watermark-edit.vue'),
            hidden: true,
            meta: {
              title: '水印管理',
              nav_key: 'dokumenWatermarkList',
              activeMenu: '/proofExemptcertificatesAdmin/watermarkManagement/dokumenWatermarkList',
              fatherPath: 'dokumenWatermarkList'
            }
          },
          {
            path: 'watermarkContentConfig',
            name: 'watermarkContentConfig',
            component: () => import('@/views/proof-exemptcertificates-admin/proof_exemption_admin/dokumen/watermark-content.vue'),
            hidden: true,
            meta: {
              title: '水印管理',
              nav_key: 'dokumenWatermarkList',
              activeMenu: '/proofExemptcertificatesAdmin/watermarkManagement/dokumenWatermarkList',
              fatherPath: 'dokumenWatermarkList'
            }
          }
        ]
      }
    ]
  }
]

export default moduleRoutes
