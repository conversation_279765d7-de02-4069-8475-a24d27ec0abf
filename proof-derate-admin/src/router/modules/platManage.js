/** When your routing table is too long, you can split it into small modules**/

import Layout from '@/layout'

const platformRouter = [
  {
    path: '/platform',
    component: Layout,
    redirect: 'noRedirect',
    name: 'Platform',
    menuType: 'licc', // 骨架菜单:licc
    meta: {
      title: '平台管理',
      icon: ''
    },
    children: [
      {
        path: 'department',
        component: () => import('@/views/plat-manege/department/index'),
        name: 'Department',
        menuType: 'licc', // 骨架菜单:licc
        alwaysShow: true,
        // redirect: 'DepartmentList',
        meta: { title: '部门管理' },
        children: [
          {
            path: 'departmentList',
            component: () => import('@/views/plat-manege/department/list'),
            name: 'DepartmentList',
            menuType: 'licc', // 骨架菜单:licc
            meta: {
              title: '部门信息管理11',
              breadcrumbBtnComponentId: 'DeparmentAdd'
            }
          },
          {
            path: 'departmentTree',
            component: () => import('@/views/plat-manege/department/tree'),
            name: 'DepartmentTree',
            menuType: 'licc', // 骨架菜单:licc
            hidden: true,
            meta: {
              title: '部门信息管理',
              activeMenu: '/platform/department/departmentList',
              fatherPath: 'departmentList',
              breadcrumbBtnComponentId: 'deparmentTreeAdd'
            }
          }
        ]
      },
      {
        path: 'account',
        component: () => import('@/views/plat-manege/account/index'),
        name: 'Account',
        menuType: 'licc', // 骨架菜单:licc
        alwaysShow: true,
        // redirect: 'DepartmentList',
        meta: { title: '用户与权限' },
        children: [
          {
            path: 'accountList',
            component: () => import('@/views/plat-manege/account/account-list'),
            name: 'accountList',
            menuType: 'licc', // 骨架菜单:licc
            meta: { title: '用户管理', breadcrumbBtnComponentId: 'AccountAdd' }
          },
          {
            path: 'accountDetail',
            component: () => import('@/views/plat-manege/account/components/detail-account'),
            name: 'AccountDetail',
            menuType: 'licc', // 骨架菜单:licc
            hidden: true,
            meta: {
              title: '用户管理',
              breadcrumbBtnComponentId: 'AccountEdit',
              activeMenu: '/platform/account/accountList',
              fatherPath: 'accountList'
            }
          },
          {
            path: 'accountSetting',
            component: () => import('@/views/plat-manege/account/components/setting-account'),
            name: 'AccountSetting',
            menuType: 'licc', // 骨架菜单:licc
            hidden: true,
            meta: {
              title: '用户管理',
              activeMenu: '/platform/account/accountList',
              fatherPath: 'accountList'
            }
          },
          {
            path: 'userList',
            component: () => import('@/views/plat-manege/user/userList-list'),
            name: 'userList',
            menuType: 'licc', // 骨架菜单:licc
            meta: { title: '用户管理', breadcrumbBtnComponentId: 'userAdd' }
          },
          {
            path: 'userAdd',
            component: () => import('@/views/plat-manege/user/userAdd'),
            name: 'userAdd',
            menuType: 'licc', // 骨架菜单:licc
            hidden: true,
            meta: { title: '用户管理', activeMenu: '/platform/account/userList', fatherPath: 'userList' }
          },
          {
            path: 'userDetail',
            component: () => import('@/views/plat-manege/user/userDetail'),
            name: 'userDetail',
            menuType: 'licc', // 骨架菜单:licc
            hidden: true,
            meta: { title: '用户管理', activeMenu: '/platform/account/userList', fatherPath: 'userList' }
          },
          {
            path: 'roleList',
            component: () => import('@/views/plat-manege/account/role-list'),
            name: 'RoleList',
            menuType: 'licc', // 骨架菜单:licc
            meta: { title: '角色管理', breadcrumbBtnComponentId: 'RoleList' }
          },
          {
            path: 'roleDetail',
            component: () => import('@/views/plat-manege/account/components/detail-role'),
            name: 'RoleDetail',
            menuType: 'licc', // 骨架菜单:licc
            meta: {
              title: '角色管理',
              activeMenu: '/platform/account/roleList',
              fatherPath: 'roleList'
            },
            hidden: true
          },
          {
            path: 'roleDetailWatch',
            component: () => import('@/views/plat-manege/account/components/detail-role'),
            name: 'RoleDetailWatch',
            menuType: 'licc', // 骨架菜单:licc
            meta: {
              title: '角色管理',
              activeMenu: '/platform/account/roleList',
              fatherPath: 'roleList',
              breadcrumbBtnComponentId: 'RoleListDetal'
            },
            hidden: true
          },
          {
            path: 'menuList',
            component: () => import('@/views/plat-manege/account/menu-list'),
            name: 'MenuList',
            menuType: 'licc', // 骨架菜单:licc
            meta: { title: '菜单管理', breadcrumbBtnComponentId: 'Menu' }
          },
          {
            path: 'menuTree',
            component: () => import('@/views/plat-manege/account/menu-tree.vue'),
            name: 'MenuTree',
            menuType: 'licc', // 骨架菜单:licc
            hidden: true,
            meta: {
              title: '菜单管理',
              activeMenu: '/platform/account/menuList',
              fatherPath: 'menuList'
            }
          }
        ]
      },
      {
        path: 'log',
        component: () => import('@/views/plat-manege/log/index'),
        name: 'Log',
        menuType: 'licc', // 骨架菜单:licc
        alwaysShow: true,
        // redirect: 'DepartmentList',
        meta: { title: '日志管理' },
        children: [
          {
            path: 'operationList',
            component: () => import('@/views/plat-manege/log/operation-list'),
            name: 'OperationList',
            menuType: 'licc', // 骨架菜单:licc
            meta: { title: '操作日志' }
          },
          {
            path: 'operationDetail',
            component: () => import('@/views/plat-manege/log/components/detail-operation'),
            name: 'OperationDetail',
            menuType: 'licc', // 骨架菜单:licc
            hidden: true,

            meta: {
              breadcrumbBtnComponentId: 'operationDetail',
              title: '操作日志',
              activeMenu: '/platform/log/operationList',
              fatherPath: 'operationList',
              originalMeta: {
                title: '操作日志详情',
                activeMenu: '/platform/log/operationList',
                path: 'operationDetail'
              }
            }
          }
        ]
      },
      {
        path: 'configManage',
        component: () => import('@/views/plat-manege/configManage/index'),
        name: 'configManage',
        menuType: 'licc', // 骨架菜单:licc
        alwaysShow: true,
        // redirect: 'DepartmentList',
        meta: { title: '配置管理' },
        children: [
          {
            path: 'configManageList',
            component: () => import('@/views/plat-manege/configManage/list'),
            name: 'configManageList',
            menuType: 'licc', // 骨架菜单:licc
            meta: {
              title: '字典管理',
              breadcrumbBtnComponentId: 'configManageAdd'
            }
          },
          {
            path: 'configManageDetail',
            component: () => import('@/views/plat-manege/configManage/detail-list'),
            name: 'configManageDetail',
            menuType: 'licc', // 骨架菜单:licc
            hidden: true,
            meta: {
              title: '字典管理',
              breadcrumbBtnComponentId: 'configManageDetail',
              activeMenu: '/platform/configManage/configManageList',
              fatherPath: 'configManageList',
              originalMeta: {
                title: '字典管理详情',
                activeMenu: '/platform/configManage/configManageList',
                path: 'configManageDetail'
              }
            }
          },
          {
            path: 'administrativeDivision',
            component: () => import('@/views/plat-manege/configManage/administrative-division'),
            name: 'administrativeDivision',
            menuType: 'licc', // 骨架菜单:licc
            // AccountAdd  administrativeDivisionAdd
            meta: {
              title: '行政区划管理',
              breadcrumbBtnComponentId: 'administrativeDivisionAdd'
            }
          }
        ]
      },
      {
        path: 'systemTools',
        component: () => import('@/views/plat-manege/systemTools/index'),
        name: 'systemTools',
        menuType: 'licc', // 骨架菜单:licc
        alwaysShow: true,
        // redirect: 'DepartmentList',
        meta: { title: '系统工具' },
        children: [
          {
            path: 'systemToolsList',
            component: () => import('@/views/plat-manege/systemTools/list.vue'),
            name: 'systemToolsList',
            menuType: 'licc', // 骨架菜单:licc
            meta: { title: '系统工具' }
          }
        ]
      },
      {
        path: 'systemMonitor',
        component: () => import('@/views/plat-manege/systemMonitor/index'),
        name: 'systemMonitor',
        menuType: 'licc', // 骨架菜单:licc
        alwaysShow: true,
        // redirect: 'DepartmentList',
        meta: { title: '系统监控' },
        children: [
          {
            path: 'onlineUserList',
            component: () => import('@/views/plat-manege/systemMonitor/list.vue'),
            name: 'onlineUserList',
            menuType: 'licc', // 骨架菜单:licc
            meta: { title: '在线用户' }
          }
        ]
      }
    ]
  }
]

export default platformRouter
