import LayoutMain from '@/layout/main.vue'
const moduleRoutes = [
  {
    path: '/proofDerateMain',
    component: LayoutMain,
    redirect: 'noRedirect',
    name: 'proofDerateMain',
    meta: {
      title: '首页',
      icon: ''
    },
    children: [
      {
        path: 'main',
        component: () => import('@/views/proof-derate-main/index.vue'),
        redirect: 'noRedirect',
        name: 'main',
        meta: {
          title: '首页',
          icon: ''
        },
        children: [
          {
            path: 'home',
            component: () => import('@/views/proof-derate-main/main.vue'),
            redirect: 'noRedirect',
            name: 'home',
            meta: {
              title: '首页',
              icon: ''
            }
          }
        ]
      }
    ]
  }
]

export default moduleRoutes
