import Vue from 'vue'
import Router from 'vue-router'
import loginRouter from './modules/login'
import platformManageRouter from './modules/platManage'
import InterfaceManagement from './modules/InterfaceManagement'
import proofExemptcertificatesAdmin from './modules/proofExemptcertificatesAdmin'
import proofInvestigationAdmin from './modules/proofInvestigationAdmin'
import proofElectronicproofAdmin from './modules/proofElectronicproofAdmin'
import proofMonitorproofAdmin from './modules/proofMonitorproofAdmin'
import proofDerateAdmin from './modules/proofDerateAdmin'
import proofDerateMain from './modules/proofDerateMain'
import userinfo from './modules/userinfo'

Vue.use(Router)

/* Layout */
import Layout from '@/layout'

export const constantRoutes = [
  /*  {
    path: '/login',
    component: () => import('@/views/login/index'),
    hidden: true
  },
 */
  {
    path: '/login',
    component: () => import('@/views/login/index'),
    menuType: 'licc', // 骨架菜单:licc
    hidden: true,
    redirect: '/white/login'
  },
  {
    path: '/404',
    component: () => import('@/views/404'),
    menuType: 'licc', // 骨架菜单:licc
    hidden: true
  },
  {
    path: '/',
    component: () => import('@/views/home'),
    menuType: 'licc', // 骨架菜单:licc
    hidden: true,
    redirect: ''
  },
  {
    path: '/white/check',
    component: () => import('@/views/check'),
    menuType: 'licc', // 骨架菜单:licc
    hidden: true
  },
  // ...platformManageRouter,
  // ...reportingManagementRouter,

  ...loginRouter
  // 404 page must be placed at the end !!!
]
/**
 * asyncRoutes
 * 异步路由（需要根据用户权限加载）
 */
export const asyncRoutes = [
  ...platformManageRouter,
  ...proofExemptcertificatesAdmin,
  ...InterfaceManagement,
  ...proofInvestigationAdmin,
  ...proofDerateAdmin,
  ...userinfo,
  ...proofElectronicproofAdmin,
  ...proofMonitorproofAdmin,
  ...proofDerateMain,
  // 404 page must be placed at the end !!!
  { path: '*', redirect: '/', hidden: true }
]
console.log('VUE_APP_BASE_PROJECT_NAME', process.env.VUE_APP_BASE_PROJECT_NAME)
export const createRouter = (routes) =>
  new Router({
    mode: 'history',
    // base: 'licenes-common-auth-web',
    base: process.env.VUE_APP_BASE_PROJECT_NAME,
    scrollBehavior: () => ({ y: 0 }),
    routes: constantRoutes
  })
const router = createRouter()

export function resetRouter() {
  const newRouter = createRouter()
  router.matcher = newRouter.matcher // reset router
}

export default router
