<template>
  <div v-if="isTopOrLeft==='top'" :class="classObj" class="app-wrapper">
    <!-- <topbar :skinColor="$store.state.settings.skinColor" :skinClass="$store.state.settings.skinClass" /> -->
    <NewTopbar type="top" :skin-color="$store.state.settings.skinColor" />
    <div v-if="device==='mobile'&&sidebar.opened" class="drawer-bg" @click="handleClickOutside" />
    <!-- <sidebar v-if="!sidebar.hide" class="sidebar-container" /> -->
    <div :class="{sidebarHide: sidebar.hide}" class="main-container">
      <div :class="{'fixed-header':fixedHeader}">
        <navbar />
      </div>
      <app-main />
    </div>
  </div>
  <div v-else :class="classObj" class="left-app-wrapper">
    <!-- <topbarLeft :skinClass="$store.state.settings.skinClass" :skinColor="$store.state.settings.skinColor" /> -->
    <NewTopbar type="left" :skin-color="$store.state.settings.skinColor" />
    <div v-if="device==='mobile'&&sidebar.opened" class="drawer-bg" @click="handleClickOutside" />
    <!-- <sidebar v-if="!sidebar.hide" id="sidebar-container-left" class="sidebar-container" :is-top-or-left="isTopOrLeft" /> -->
    <div id="main-container-left" :class="{sidebarHide: sidebar.hide}" class="main-container">
      <div :class="{'fixed-header':fixedHeader}">
        <navbar />
      </div>
      <app-main />
    </div>
  </div>
</template>
<script>
import { Navbar, Sidebar, AppMain } from './components'
import Topbar from './components/Topbar.vue'
import TopbarLeft from './components/TopbarLeft.vue'
import NewTopbar from './components/NewTopbar.vue'
import ResizeMixin from './mixin/ResizeHandler'
export default {
  name: 'Layout',
  components: {
    Navbar,
    Sidebar,
    AppMain,
    Topbar,
    TopbarLeft,
    NewTopbar
  },
  mixins: [ResizeMixin],
  data() {
    return {
      isTopOrLeft: 'top' // top 顶栏分布  left 侧栏分布
    }
  },
  computed: {
    sidebar() {
      return this.$store.state.app.sidebar
    },
    device() {
      return this.$store.state.app.device
    },
    fixedHeader() {
      return this.$store.state.settings.fixedHeader
    },
    classObj() {
      return {
        hideSidebar: !this.sidebar.opened,
        openSidebar: this.sidebar.opened,
        withoutAnimation: this.sidebar.withoutAnimation,
        mobile: this.device === 'mobile'
      }
    }
  },
  mounted() {
    this.initNavigateLayout()
    this.$vm.$on('setNavigateLayout', () => {
      this.initNavigateLayout()
    })
  },
  methods: {
    handleClickOutside() {
      this.$store.dispatch('app/closeSideBar', { withoutAnimation: false })
    },
    initNavigateLayout() {
      const systemInfoString = sessionStorage.getItem('systemInfo')
      if (systemInfoString) {
        const systemInfo = JSON.parse(systemInfoString)
        this.isTopOrLeft = systemInfo.navigate_layout ? systemInfo.navigate_layout.toLowerCase() : 'top'
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@/styles/mixin.scss';
@import '~@/styles/variables.scss';

.left-app-wrapper .app-wrapper {
  @include clearfix;
  position: relative;
  // height: 100%;
  height: $contentHeight;
  width: 100%;
  &.mobile.openSidebar {
    position: fixed;
    top: 0;
  }
}
.drawer-bg {
  background: #000;
  opacity: 0.3;
  width: 100%;
  top: 0;
  height: 100%;
  position: absolute;
  z-index: 999;
}

.fixed-header {
  position: fixed;
  top: 0;
  right: 0;
  z-index: 9;
  width: calc(100% - #{$sideBarWidth});
  transition: width 0.28s;
}

.hideSidebar .fixed-header {
  width: calc(100% - 54px);
}

.mobile .fixed-header {
  width: 100%;
}
#app .main-container {
  margin-left: 0px;
  margin-top: 0px;
}
#app #main-container-left {
  margin-left: 100px;
}
#app .app-main{
  width: 100vw;
}
</style>
