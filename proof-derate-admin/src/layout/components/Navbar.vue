<template>
  <div class="navbar">
    <div class="left-container">
      <hamburger v-if="!sidebar.hide" :is-active="sidebar.opened" class="hamburger-container" @toggleClick="toggleSideBar" />
      <breadcrumb class="breadcrumb-container" />
    </div>
    <div class="right-btn">
      <component :is="btnComponentId" />
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import Breadcrumb from '@/components/Breadcrumb'
import Hamburger from '@/components/Hamburger'

export default {
  components: {
    Breadcrumb,
    Hamburger,
    AccountAdd: () => import('@/components/BreadcrumbBtn/commonPark/accountAdd.vue'),
    AccountEdit: () => import('@/components/BreadcrumbBtn/commonPark/accountEdit.vue'),
    DeparmentAdd: () => import('@/components/BreadcrumbBtn/commonPark/deparmentAdd.vue'),
    deparmentTreeAdd: () => import('@/components/BreadcrumbBtn/commonPark/deparmentTreeAdd.vue'),
    directoryList: () => import('@/components/BreadcrumbBtn/commonPark/directoryList.vue'),
    directoryDetail: () => import('@/components/BreadcrumbBtn/commonPark/directoryDetail.vue'),
    Menu: () => import('@/components/BreadcrumbBtn/commonPark/menu.vue'),
    RoleList: () => import('@/components/BreadcrumbBtn/commonPark/roleList.vue'),
    RoleListDetal: () => import('@/components/BreadcrumbBtn/commonPark/roleListDetal.vue'),
    AccessSystem: () => import('@/components/BreadcrumbBtn/commonPark/accessSystem.vue'),
    AssestSystemtAdd: () => import('@/components/BreadcrumbBtn/commonPark/assestSystemtAdd.vue'),
    InterfaceManagerAdd: () => import('@/components/BreadcrumbBtn/commonPark/InterfaceManagerAdd.vue'),
    InterfaceManagerDetail: () => import('@/components/BreadcrumbBtn/commonPark/InterfaceManagerDetail.vue'),
    accessSystemrDetail: () => import('@/components/BreadcrumbBtn/commonPark/accessSystemrDetail.vue'),
    configManageAdd: () => import('@/components/BreadcrumbBtn/commonPark/configManageAdd.vue'),
    configManageDetail: () => import('@/components/BreadcrumbBtn/commonPark/configManageDetail.vue'),
    administrativeDivisionAdd: () => import('@/components/BreadcrumbBtn/commonPark/administrativeDivisionAdd.vue'),
    operationDetail: () => import('@/components/BreadcrumbBtn/commonPark/operationDetail.vue'),
    userAdd: () => import('@/components/BreadcrumbBtn/commonPark/userAdd.vue') // 用户管理新增用户
  },
  computed: {
    ...mapGetters(['sidebar']),
    btnComponentId() {
      // let breadcrumbBtnComponentId = ''
      // const { permission } = this.$route.meta
      // if (permission.length > 0) {
      //   const btnArr = permission[0].key.split(':')
      //   breadcrumbBtnComponentId = btnArr[0] + btnArr[1]
      // }
      // console.log(permission, breadcrumbBtnComponentId, 'permission')
      // return breadcrumbBtnComponentId
      return this.$route.meta.breadcrumbBtnComponentId
    }
  },
  mounted() {
    // console.log(this.$route)
  },
  methods: {
    toggleSideBar() {
      this.$store.dispatch('app/toggleSideBar')
    }
  }
}
</script>

<style lang="scss" scoped>
.navbar {
  height: 50px;
  overflow: hidden;
  position: relative;
  background: #fff;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  display: flex;
  justify-content: space-between;
  align-items: center;
  .hamburger-container {
    line-height: 46px;
    height: 100%;
    float: left;
    cursor: pointer;
    transition: background 0.3s;
    -webkit-tap-highlight-color: transparent;

    &:hover {
      background: rgba(0, 0, 0, 0.025);
    }
  }

  .breadcrumb-container {
    float: left;
  }

  .right-btn {
    margin-right: 10px;
  }
}
</style>
