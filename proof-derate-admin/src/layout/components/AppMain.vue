<template>
  <section :class="isTopOrLeft==='top'?'app-main':'app-main-lit'">
    <transition name="fade-transform" mode="out-in">
      <router-view :key="key" />
    </transition>
  </section>
</template>

<script>
export default {
  name: 'AppMain',
  data() {
    return {
      isTopOrLeft: 'top' // top 顶栏分布  left 侧栏分布
    }
  },
  computed: {
    key() {
      return this.$route.path
    }
  },
  mounted() {
    this.initNavigateLayout()
  },
  methods: {
    initNavigateLayout() {
      const systemInfoString = sessionStorage.getItem('systemInfo')
      if (systemInfoString) {
        const systemInfo = JSON.parse(systemInfoString)
        this.isTopOrLeft = systemInfo.navigate_layout ? systemInfo.navigate_layout.toLowerCase() : 'top'
      }
    }
  }
}
</script>

<style scoped>
.app-main {
  /*50 = navbar  */
  min-height: calc(100vh - 56px - 50px);
  width: 100%;
  position: relative;
  overflow: hidden;
}
.app-main-lit {
  min-height: calc(100vh - 50px);
}
.fixed-header + .app-main {
  padding-top: 50px;
}
</style>

<style lang="scss">
// fix css style bug in open el-dialog
.el-popup-parent--hidden {
  .fixed-header {
    padding-right: 15px;
  }
}
</style>
