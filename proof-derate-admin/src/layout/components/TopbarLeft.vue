<template>
  <div class="top-nav" :style="{backgroundColor:skinColor}">
    <svg-icon class="logo" :icon-class="logo" />
    <!-- <div class="log">{{ title }}</div> -->
    <!-- :active-text-color="variables.BmenuActiveText" -->
    <el-menu
      :default-active="activeMenu"
      mode="horizontal"
      :class="['menu-left',$store.state.settings.skinClass]"
      active-text-color="#fff"
      :style="{backgroundColor:skinColor}"
      @select="handleSelect"
    >
      <div v-for="(item,index) in routes" :key="item.path" class="nav-item" @click="activeClick(index)">
        <app-link v-if="item.path!=='userinfo'" :to="resolvePath(item,index)" :item="item" :class="$store.state.settings.skinClass">
          <el-menu-item v-if="!item.hidden" :index="currentIndex(item)">
            <svg-icon
              v-if="item.meta.icon!==''&&item.meta.icon!==null"
              :class="selectIndex===index?'selected':''"
              :icon-class="item.meta.icon"
              style="height: 34px;width: 34px;"
            />
            <svg-icon v-else :icon-class="topbarSvgList[0].normal" style="height: 34px;width: 34px;" />
            <span>{{ item.meta ? item.meta.title : item.children[0].meta.title }}</span>
          </el-menu-item>
        </app-link>
      </div>
    </el-menu>

    <div v-if="!messageInfo.meta.hidden" class="right-menu">
      <el-dropdown class="avatar-container right" trigger="click">
        <div class="account-wrap">
          <div class="avatar-wrapper" :class="$store.state.settings.skinClass">
            <!-- <img src="~@/assets/images/header_icon_bule.png" class="user-avatar-custom" /> -->
            <img src="~@/assets/images/header_icon.png" class="user-avatar-custom">
          </div>
          <span>{{ accountInfo.account }}</span>
          <!-- <div class="right-wrap">
            <div>欢迎您</div>
            <div style="margin-top:4px"> <span class="icon">
              <img src="~@/assets/images/down-icon.svg">
            </span></div>
          </div>-->
        </div>
        <el-dropdown-menu slot="dropdown" class="user-dropdown">
          <el-dropdown-item disabled>
            姓名：
            <span class="text_blue">{{ accountInfo.name }}</span>
          </el-dropdown-item>
          <el-dropdown-item disabled>
            账号：
            <span class="text_blue">{{ accountInfo.account }}</span>
          </el-dropdown-item>
          <el-dropdown-item disabled>
            部门：
            <span class="text_blue">{{ accountInfo.department }}</span>
          </el-dropdown-item>
          <el-dropdown-item divided>
            <span v-if="userpassword">
              <el-button v-if="!userpassword.meta.hidden" type="text" class="text_blue" @click="changePasswd">修改密码</el-button>
            </span>
            <!-- v-if="!messageInfo.children[1].meta.hidden" -->
            <span v-if="userexit">
              <el-button v-if="!userexit.meta.hidden" type="text" class="text_blue" style="margin-left:16px" @click="logout">退出登录</el-button>
            </span>
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import AppLink from './Sidebar/Link'
import variables from '@/styles/variables.scss'
import { isExternal } from '@/utils/validate'
import defaultSettings from '@/settings'
import { getSystemSettings } from '@/api/commonPack/platManege'
export default {
  name: 'Topbar',
  components: {
    AppLink
  },
  props: {
    skinColor: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      title: '',
      logo: '',
      logout_redirect_url: '',
      routes: this.$store.state.permission.routes,
      accountInfo: {},
      selectIndex: 0,
      topbarSvg: 'topbar00_active',
      topbarSvgList: [
        {
          normal: 'topbar00',
          active: 'topbar00_active'
        },
        {
          normal: 'topbar00',
          active: 'topbar00_active'
        },
        {
          normal: 'topbar00',
          active: 'topbar00_active'
        },
        {
          normal: 'topbar00',
          active: 'topbar00_active'
        },
        {
          normal: 'topbar00',
          active: 'topbar00_active'
        },
        {
          normal: 'topbar00',
          active: 'topbar00_active'
        },
        {
          normal: 'topbar00',
          active: 'topbar00_active'
        },
        {
          normal: 'topbar00',
          active: 'topbar00_active'
        },
        {
          normal: 'topbar00',
          active: 'topbar00_active'
        },
        {
          normal: 'topbar00',
          active: 'topbar00_active'
        }
      ],
      skinClass: this.$store.state.settings.skinClass,
      messageInfo: {
        meta: { hidden: true },
        children: [
          { meta: { hidden: true }, path: 'userpassword' },
          { meta: { hidden: true }, path: 'userexit' }
        ]
      },
      messageSysInfo: {
        meta: { hidden: false },
        children: [
          { meta: { hidden: false }, path: 'userpassword' },
          { meta: { hidden: false }, path: 'userexit' }
        ]
      },
      userpassword: { meta: { hidden: false }, path: 'userpassword' },
      userexit: { meta: { hidden: false }, path: 'userexit' }
    }
  },
  computed: {
    activeMenu() {
      const route = this.$route
      console.log(route, 111)
      const { meta, path } = route
      // if set path, the sidebar will highlight the path you set
      if (meta.activeMenu) {
        // console.log(meta.activeMenu)
        const activeMenu = '/' + path.split('/')[1]
        // console.log('activeMenu',activeMenu)
        return activeMenu
        // return meta.activeMenu
      }
      // 如果是首页，首页高亮
      if (path === '/dashboard') {
        return '/'
      }
      // 如果不是首页，高亮一级菜单
      console.log(path.split('/'), 234)
      const activeMenu = '/' + path.split('/')[1]
      console.log(activeMenu)
      return activeMenu
    },
    variables() {
      return variables
    },
    sidebar() {
      return this.$store.state.app.sidebar
    },
    ...mapGetters(['avatar', 'account'])
  },
  mounted() {
    this.initCurrentRoutes()
    // console.log(this.$store.state.user.account);
    this.accountInfo = JSON.parse(sessionStorage.getItem('accountInfo'))
    const systemInfo = JSON.parse(sessionStorage.getItem('systemInfo'))
    if (this.accountInfo.account !== 'system_admin' && this.accountInfo.account !== 'audit_admin' && this.accountInfo.account !== 'security_admin') {
      this.routes.find(i => i.path === 'userinfo') ? (this.messageInfo = this.routes.find(i => i.path === 'userinfo')) : ''
      if (this.messageInfo.children) {
        this.userpassword = this.messageInfo.children.find(i => i.path === 'userpassword')
        this.userexit = this.messageInfo.children.find(i => i.path === 'userexit')
      } else {
        this.userpassword.meta.hidden = true
        this.userexit.meta.hidden = true
      }

      // console.log('this.userpassword', this.userpassword, 'this.userexit', this.userexit)
    } else {
      this.messageInfo = this.messageSysInfo
      // console.log('this.messageInfo',this.messageInfo)
    }
    if (systemInfo === null) {
      this.store.dispatch('settings/changeSetting').then(res => {
        this.title = res.name
        this.logo = res.icon
        this.background = res.background || ''
        this.logout_redirect_url = res.logout_redirect_url || ''
      })
    } else {
      this.title = systemInfo.name
      this.logo = systemInfo.icon
      this.background = systemInfo.background || ''
      this.logout_redirect_url = systemInfo.logout_redirect_url || ''
    }
  },
  methods: {
    currentIndex(item) {
      if (item.link_external) {
        return 
      }
      return item.path    
    },    
    // 通过当前路径找到二级菜单对应项，存到store，用来渲染左侧菜单
    initCurrentRoutes() {
      const { path } = this.$route
      let route = this.routes.find(item => item.path === '/' + path.split('/')[1])
      // 如果找不到这个路由，说明是首页
      if (!route) {
        route = this.routes.find(item => item.path === '/')
      }
      this.$store.commit('permission/SET_CURRENT_ROUTES', route)
      this.setSidebarHide(route)
    },
    // 判断该路由是否只有一个子项或者没有子项，如果是，则在一级菜单添加跳转路由
    isOnlyOneChild(item) {
      if (item.children && item.children.length === 1) {
        return true
      }
      return false
    },
    resolvePath(item, index) {
      // 如果是个完成的url直接返回
      if (isExternal(item.path)) {
        return item.path
      }
      // 如果是首页，就返回重定向路由
      if (item.path === '/') {
        const path = item.redirect
        return path
      }

      // 如果有子项，默认跳转第一个子项路由
      let path = ''
      /**
       * item 路由子项
       * parent 路由父项
       */
      const getDefaultPath = (item, parent) => {
        // 如果path是个外部链接（不建议），直接返回链接，存在个问题：如果是外部链接点击跳转后当前页内容还是上一个路由内容
        if (isExternal(item.path)) {
          path = item.path
          return
        }
        // 第一次需要父项路由拼接，所以只是第一个传parent
        if (parent) {
          path += parent.path + '/' + item.path
        } else {
          path += '/' + item.path
        }
        // 如果还有子项，继续递归
        if (item.children) {
          getDefaultPath(item.children[0])
        }
      }

      let currentPath = ''
      const tree = (data,parentPath) => {
        for (let i = 0; i < data.length; i++) {
          let item = data[i];
          if (!item.hidden && !item.link_external && !item.children) {
            currentPath = currentPath + '/' + item.path
            return item;
          } else {
            // item.children 不等于 undefined && item.children.length 大于 0 时
            if (item.children && item.children.length > 0 && !item.hidden ) {
              currentPath = ''
              item.path ? currentPath = parentPath + currentPath + '/' + item.path : ''
              let res = tree(item.children, item.path);
              // 当 res = undefined 时不会进入判断中
              if (res) return 
            }
          }
        }
      }
      if (item.children) {
        tree(item.children,item.path);
        // getDefaultPath(item.children[0], item)
        return currentPath
      }
      return item.path
    },
    handleSelect(key, keyPath) {
      const route = this.routes.find(item => item.path === key)
      // 判断跳转地址是否外链
      if (!route || route.link_external) {
        return 
      }
      // 把选中路由的子路由保存store
      console.log(1111233, route)

      this.$store.commit('permission/SET_CURRENT_ROUTES', route)
      this.setSidebarHide(route)
    },
    // 设置侧边栏的显示和隐藏
    setSidebarHide(route) {
      /* if (!route.children || route.children.length === 1) {
        this.$store.dispatch('app/toggleSideBarHide', true)
      } else {
        this.$store.dispatch('app/toggleSideBarHide', false)
      } */
      this.$store.dispatch('app/toggleSideBarHide', false)
    },
    async logout() {
      this.$confirm('是否确认退出系统？', '警告提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          // getSystemSettings()
          //   .then(res => {
          //     if (res.meta.code === '200' && res.data != null) {
          //       // this.$store.dispatch('user/logout')
          //       if (res.data.logout_redirect_url) {
          //         this.$store.dispatch('user/logout').then(() => {
          //           window.open(res.data.logout_redirect_url, '_self')
          //         })
          //       } else {
          //         this.setLoginOut()
          //       }
          //     }
          //   })
          //   .catch(err => {
          //     this.setLoginOut()
          //   })
          this.$store.dispatch('user/logout').then(() => {
            if(this.logout_redirect_url) {
              window.open(this.logout_redirect_url, '_self')
            } else {
              this.$router.push({
                path:'/login'
              })
            }
          })
        })
        .catch(() => {})
    },
    async setLoginOut() {
      await this.$store.dispatch('user/logout')
      this.$router.push(`/login?redirect=${this.$route.fullPath}`)
    },
    changePasswd() {
      this.$router.push(`/white/passwd?redirect=${this.$route.fullPath}&status=3`)
    },
    activeClick(index) {
      // console.log('activeClick', index)
      this.selectIndex = index
    }
  }
}
</script>

<style lang='scss' scoped>
.account-wrap {
  position: relative;
  display: flex;
  align-items: center;
  flex-direction: column;
  color: #ffffff;
  font-size: 14px;
  cursor: pointer;
  span {
    margin-top: 5px;
  }
  .right-wrap {
    margin-left: 6px;
    color: #ffffff;
    font-size: 14px;
  }
}
.avatar-wrapper {
  height: 60px;
  width: 60px;
  background: #2177df;
  border-radius: 50%;
  // img {
  //   width: 60px;
  //   height: 60px;
  // }
  .user-avatar-custom {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    margin: auto;
    width: 20px;
    height: 20px;
  }
}

.text_blue {
  color: #409eff;
}
.el-dropdown-menu__item:focus,
.el-dropdown-menu__item:not(.is-disabled):hover {
  background-color: white;
  color: #409eff;
}
.el-dropdown-menu__item.is-disabled {
  color: #333333;
}
.top-nav {
  width: 100px;
  height: 100vh;
}
.greenTheme {
  background: #02754e;
}

.darkBlueTheme {
  background: #0962e5;
}
//
/* .defaultTheme .svg-icon {
  fill: #1772e5 !important;
  stroke: #fff;
  stroke-width: 6px;
}
.defaultTheme .is-active .svg-icon {
  fill: #fff !important;
  stroke: #1772e5;
}
.defaultTheme :hover {
  .svg-icon {
    fill: #fff !important;
    stroke: #1772e5;
  }
}
.greenTheme .svg-icon {
  fill: #1f9e73 !important;
  stroke: #fff;
  stroke-width: 6px;
}
.greenTheme .is-active .svg-icon {
  fill: #fff !important;
  stroke: #1f9e73;
}
.greenTheme :hover {
  .svg-icon {
    fill: #fff !important;
    stroke: #1f9e73;
  }
}
.lightBlueTheme .svg-icon {
  fill: #4293f4 !important;
  stroke: #fff;
  stroke-width: 6px;
}
.lightBlueTheme .is-active .svg-icon {
  fill: #fff !important;
  stroke: #4293f4;
}
.lightBlueTheme :hover {
  .svg-icon {
    fill: #fff !important;
    stroke: #4293f4;
  }
}
.darkBlueTheme .svg-icon {
  fill: #1772e5 !important;
  stroke: #fff;
  stroke-width: 6px;
}
.darkBlueTheme .is-active .svg-icon {
  fill: #fff !important;
  stroke: #1772e5;
}
.darkBlueTheme :hover {
  .svg-icon {
    fill: #fff !important;
    stroke: #1772e5;
  }
} */

.logo{
  width:54px !important;
  height:54px !important;
  margin-left:0px;
  margin-bottom:29px;
  margin-top:30px;
  position: absolute;
}
.menu-left{
  height: calc(100vh - 113px - 130px);
  overflow-y: auto;
  margin: 113px 0 130px 0;
}
.right-menu{
  position: absolute;
  bottom: 0;
}
</style>
