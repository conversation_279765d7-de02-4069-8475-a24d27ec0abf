<script>
export default {
  name: 'MenuItem',
  functional: true,
  props: {
    icon: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    },
    skinColor: {
      type: String,
      default: ''
    },
    skinClass: {
      type: String,
      default: ''
    }
  },
  render(h, context, store) {
    const { icon, title, skinColor, skinClass } = context.props
    const vnodes = []
    // console.log('skinClass',skinClass,'skinColor',skinColor)
    if (icon) {
      if (icon.includes('el-icon')) {
        vnodes.push(<i class={[icon, 'sub-el-icon']} />)
      } else {
        const systemInfoString = sessionStorage.getItem('systemInfo')
        const systemInfo = JSON.parse(systemInfoString)
        const isTopOrLeft = systemInfo.navigate_layout ? systemInfo.navigate_layout.toLowerCase() : 'top'
        // console.log('this.isTopOrLeft', isTopOrLeft)
        if (isTopOrLeft === 'left') {
          vnodes.push(<svg-icon icon-class={icon} id={skinClass} style={`fill:#333`} />)
        } else {
          vnodes.push(<svg-icon icon-class={icon} id={skinClass} style={`fill:${skinColor}`} />)
        }
      }
    }

    if (title) {
      vnodes.push(
        <span slot='title' class={skinClass}>
          {title}
        </span>
      )
    }
    return vnodes
  }
}
</script>

<style scoped>
.sub-el-icon {
  color: currentColor;
  width: 1em;
  height: 1em;
}
#darkBlueTheme {
  fill: #0747a6;
}
#greenTheme {
  fill: #1f9e73;
}
#lightBlueTheme {
  fill: #3c8ef0;
}

#defaultTheme {
  fill: #409eff;
}
.greenTheme:hover {
  color: #1f9e73;
}
#lightBlueTheme:hover {
  fill: #3c8ef0;
}
.darkBlueTheme:hover {
  color: #0747a6;
}
.defaultTheme:hover {
  color: #409eff;
}
</style>
<style lang="scss" scoped>
.is-active > .el-submenu__title > #lightBlueTheme {
  // color: #d3dce6;
  fill: #4293f4 !important;
}
.is-active > .el-submenu__title > #darkBlueTheme {
  // color: #d3dce6;
  fill: #0747a6 !important;
}
.is-active > .el-submenu__title > #greenTheme {
  // color: #d3dce6;
  fill: #1f9e73 !important;
}
.is-active > .el-submenu__title > #defaultTheme {
  // color: #d3dce6;
  fill: #3c8ef0 !important;
}
</style>
