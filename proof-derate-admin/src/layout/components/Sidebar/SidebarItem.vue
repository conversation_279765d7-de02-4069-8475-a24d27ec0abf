<template>
  <div v-if="!item.hidden">
    <template v-if="hasOneShowingChild(item.children,item) && (!onlyOneChild.children||onlyOneChild.noShowingChildren)&&!item.alwaysShow">
      <!-- onlyOneChild.meta&&onlyOneChild.meta.externalUrl===null" -->
      <app-link v-if="onlyOneChild.meta" :to="resolvePath(onlyOneChild.path)" :item="item">
        <el-menu-item :index="resolvePath(onlyOneChild.path, item)" :class="{'submenu-title-noDropdown':!isNest}">
          <item
            :icon="onlyOneChild.meta.icon||(item.meta&&item.meta.icon)"
            :title="onlyOneChild.meta.title"
            :skin-class="$store.state.settings.skinClass"
          />
        </el-menu-item>
      </app-link>
      <!-- <div v-else  @click="sidebarClick(onlyOneChild)">
        <el-menu-item :index="resolvePath(onlyOneChild.path)" :class="{'submenu-title-noDropdown':!isNest}" >
          <item
            :icon="onlyOneChild.meta.icon||(item.meta&&item.meta.icon)"
            :title="onlyOneChild.meta.title"
            :skin-class="$store.state.settings.skinClass"
          />
        </el-menu-item>
      </div> -->
    </template>

    <el-submenu v-else ref="subMenu" :index="resolvePath(item.path)" popper-append-to-body>
      <template slot="title">
        <item
          v-if="item.meta"
          :icon="item.meta && item.meta.icon"
          :skin-color="$store.state.settings.skinColor"
          :skin-class="$store.state.settings.skinClass"
          :title="item.meta.title"
        />
      </template>
      <sidebar-item
        v-for="child in item.children"
        :key="child.path"
        :is-nest="true"
        :item="child"
        :base-path="resolvePath(child.path)"
        class="nest-menu"
      />
    </el-submenu>
  </div>
</template>

<script>
import path from 'path'
import { isExternal } from '@/utils/validate'
import Item from './Item'
import AppLink from './Link'
import FixiOSBug from './FixiOSBug'

export default {
  name: 'SidebarItem',
  components: { Item, AppLink },
  mixins: [FixiOSBug],
  props: {
    // route object
    item: {
      type: Object,
      required: true
    },
    isNest: {
      type: Boolean,
      default: false
    },
    basePath: {
      type: String,
      default: ''
    }
  },
  data() {
    // TODO: refactor with render function
    this.onlyOneChild = null
    return {
      skinColor: this.$store.state.skinColor,
      skinClass: this.$store.state.settings.skinClass
    }
  },
  watch: {
    '$store.state.settings.skinColor': {
      deep: true,
      handler(newVal, oldVal) {}
    },
    '$store.state.settings.skinClass': {
      deep: true,
      handler(newVal, oldVal) {}
    }
  },
  mounted() {
    // console.log('basePath', this.basePath)
    // console.log('this.$store.state.settings.skinClass', this.$store.state.settings.skinClass)
  },
  methods: {
    sidebarClick(onlyOneChild) {
      // console.log(1111,onlyOneChild)
      // window.open(onlyOneChild.meta.externalUrl, '_blank');
    },
    hasOneShowingChild(children = [], parent) {
      // if (!children) return false
      const showingChildren = children.filter(item => {
        if (item.hidden) {
          return false
        } else {
          // Temp set(will be used if only has one showing child)
          this.onlyOneChild = item
          return true
        }
      })

      // When there is only one child router, the child router is displayed by default
      if (showingChildren.length === 1) {
        return true
      }

      // Show parent if there are no child router to display
      if (showingChildren.length === 0) {
        this.onlyOneChild = { ...parent, path: '', noShowingChildren: true }
        return true
      }

      return false
    },
    resolvePath(routePath, item) {
      if (item && item.link_external) {
        return
      }
      if (isExternal(routePath)) {
        return routePath
      }
      if (isExternal(this.basePath)) {
        return this.basePath
      }
      const currentRoutes = this.$store.state.permission.currentRoutes
      if (currentRoutes && currentRoutes.path && this.basePath !== 1 && routePath !== 2) {
        return path.resolve(currentRoutes.path, this.basePath, routePath)
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.el-menu-item:focus {
  background: #fff!important;
}
</style>
