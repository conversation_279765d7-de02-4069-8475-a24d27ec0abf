<template>
  <div :class="{'has-logo':showLogo}">
    <p v-if="isTopOrLeft==='left'" class="title" :style="{color:$store.state.settings.skinColor}">{{ title }}</p>
    <logo v-if="showLogo" :collapse="isCollapse" />
    <el-scrollbar wrap-class="scrollbar-wrapper">
      <!-- :active-text-color="variables.BsubMenuActiveThrText"  :background-color="variables.BsubMenuBg"-->
      <!--                   -->
      <el-menu
        :key="isCollapse||randomNum"
        :unique-opened="false"
        :collapse-transition="false"
        :default-active="activeMenu"
        :collapse="isCollapse"
        :background-color="variables.BsubMenuBg"
        :text-color="variables.BsubMenuText"
        :class="$store.state.settings.skinClass"
        :active-text-color="$store.state.settings.skinColor"
        :default-openeds="openedsRoutes"
        mode="vertical"
        @open="handleOpen"
      >
        <sidebar-item v-for="route in routes" :key="route.path" :item="route" :base-path="route.path" />
      </el-menu>
    </el-scrollbar>
  </div>
</template>

<script>
import path from 'path'
import { mapGetters } from 'vuex'
import Logo from './Logo'
import SidebarItem from './SidebarItem'
import variables from '@/styles/variables.scss'

export default {
  components: { SidebarItem, Logo },
  props: {
    isTopOrLeft: {
      type: String,
      default: () => {
        return ''
      }
    }
  },
  data() {
    return {
      skinColor: this.$store.state.settings.skinColor,
      title: '',
      randomNum: 0,
      // openedsRoutes: [
      //   '/platform/log/log',
      //   '/platform/department/department',
      //   '/platform/configManage/configManage',
      //   '/platform/systemTools/systemTools',
      //   '/platform/systemMonitor/systemMonitor',
      //   '/platform/account/account',
      //   '/Interface/accessSystem/accessSystem'
      // ],
      openedsRoutes: []
      // wwwwww: ['/platform/account/account']
    }
  },
  watch: {
    '$store.state.settings.skinColor': {
      deep: true,
      handler(newVal, oldVal) {}
    },
    '$store.state.settings.skinClass': {
      deep: true,
      handler(newVal, oldVal) {}
    },
    $route(to, from) {
      // 路由变化时的处理逻辑
      console.log('Route changed from', from.path, 'to', to.path)
    },
    routes: {
      deep: true,
      handler(val) {
        this.openedsRoutes = []
        const parentPath = this.$store.state.permission.currentRoutes.path
        console.log('val', val)
        val.forEach(e => {
          if (!e.hidden && !e.link_external && !e.isOutherPath) {
            this.openedsRoutes.push(parentPath + '/' + e.path + '/' + e.path)
          }
        })
        console.log('this.openedsRoutes1', this.openedsRoutes)
        this.randomNum = Math.random()
      }
    }
  },
  computed: {
    ...mapGetters(['sidebar']),
    routes() {
      return this.$store.state.permission.currentRoutes.children
    },
    activeMenu() {
      const route = this.$route
      const { meta, path } = route
      // if set path, the sidebar will highlight the path you set
      // return 'proof_investigation_admin'
      if (meta.activeMenu) {
        return meta.activeMenu
      }
      // console.log('获取route信息', meta, path)
      return path
    },
    showLogo() {
      return this.$store.state.settings.sidebarLogo
    },
    variables() {
      return variables
    },
    isCollapse() {
      return !this.sidebar.opened
    }
  },

  mounted() {
    this.extendList()
    this.accountInfo = JSON.parse(sessionStorage.getItem('accountInfo'))
    const systemInfoString = sessionStorage.getItem('systemInfo')
    if (systemInfoString) {
      const systemInfo = JSON.parse(systemInfoString)
      this.title = systemInfo.name
      this.logo = systemInfo.icon
    }
    this.$nextTick(() => {})
  },
  methods: {
    handleOpen(index) {
      // console.log(index, ********)
    },
    sidebarClick() {},
    extendList() {
      this.openedsRoutes = []
      const parentPath = this.$store.state.permission.currentRoutes.path
      // console.log('val', val)
      this.$store.state.permission.currentRoutes.children.forEach(e => {
        if (!e.hidden && !e.link_external && !e.isOutherPath) {
          this.openedsRoutes.push(parentPath + '/' + e.path + '/' + e.path)
        }
      })
      this.randomNum = Math.random()
    }
  }
}
</script>
<style lang="scss" scoped>
.title {
  // font-size: 14px;
  font-size: 20px;
  color: #3c8ef0;
  text-align: center;
  text-overflow: ellipsis;
  display: -webkit-box; /* 将对象作为弹性伸缩盒子模型显示 */
  -webkit-box-orient: vertical; /* 设置或检索伸缩盒对象的子元素的排列方式 */
  -webkit-line-clamp: 2; /* 2行，只有 webkit内核支持 */
  word-break: break-all; /* 纯英文换行 */
  overflow: hidden;
  margin: 27px 0 24px;
}
</style>
