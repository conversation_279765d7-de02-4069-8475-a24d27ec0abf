<template>
  <span v-if="!isLinkExternal && !isOutherPath">
    <!-- <span v-if="!isLinkExternal"> -->
    <component :is="type" v-bind="linkProps(to)">
      <slot />
    </component>
  </span>
  <span v-else>
    <a :href="isHttps(item)" :target="item.open_way === 'NEW_WINDOW' ? '_blank' : ''" @click="handleExternalUrl(item)">
      <slot />
    </a>
  </span>
</template>

<script>
import { setExternallinksLog } from '@/api/commonPack/platManege'
import { isExternal } from '@/utils/validate'
import { Base64 } from 'js-base64'
import Cookies from 'js-cookie'
import { TokenKey, getToken } from '@/utils/auth' // get token from cookie
import { collectLastChildrenRoutes } from '@/utils' // get token from cookie

import store from '@/store'

export default {
  props: {
    to: {
      type: String,
      required: true
    },
    item: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  computed: {
    isExternal() {
      return isExternal(this.to)
    },
    type() {
      if (this.isExternal) {
        return 'a'
      }
      return 'router-link'
    },
    // 是否外链
    isLinkExternal() {
      return this.item.link_external
    },
    // 是否外部项目地址
    isOutherPath() {
      /* const currentRoutes = store.state.permission.currentRoutes
      const allRoutes = collectLastChildrenRoutes(currentRoutes.children)
      const newRoutes = allRoutes.filter((route) => !route.isCatalogue)
      console.log(newRoutes, 222222333)
      if (newRoutes.length > 0) {
        return false
      }
      return this.item.isOutherPath */
      // if()
      if (this.item.children && this.item.children.length > 0) {
        return false
      } else {
        return this.item.isOutherPath
      }
    }
    /* externalUrl() {
      return this.isHttps(this.item.external_url)
    } */
  },
  methods: {
    linkProps(to) {
      if (this.isExternal) {
        return {
          href: to,
          target: '_blank',
          rel: 'noopener'
        }
      }
      return {
        to: to
      }
    },
    isHttps(values) {
      // console.log(values.path, values.isOutherPath, 222222, TokenKey)
      if (values.isOutherPath) {
        // console.log(values.meta.otherPath)
        // return window.location.origin + '/' + values.meta.otherProjectNmae + (values.meta.otherPath.slice(0, 1) === '/' ? values.meta.otherPath : '/' + values.meta.otherPath)
        const url = window.location.origin + '/' + values.meta.projectName + '/white/check'
        const par = '?tokenName=' + TokenKey + '&path=' + values.path + '&type=inOutherLink'
        console.log(url + par)
        return url + par
      } else if (values.external_url) {
        return values.external_url.slice(0, 4) === 'http' ? values.external_url : 'https://' + values.external_url
      }
      /* if (values) {
        return values.slice(0, 4) === 'http' ? values : 'https://' + values
      } */
    },
    // 记录外链日志
    handleExternalUrl(item) {
      if (this.isOutherPath) return
      const base64 = Base64.encode(item.meta.title)
      Cookies.set('Menu-Name', base64)
      setExternallinksLog({ link: item.external_url }).then(res => {

      }).catch(() => {})
    }
  }
}
</script>
