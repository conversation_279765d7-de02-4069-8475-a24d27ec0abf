NODE_ENV = production

# just a flag
ENV = 'staging'

# 项目名称
VUE_APP_BASE_PROJECT_NAME = 'proof-derate-web'
# base api
# VUE_APP_BASE_LICC_API = '/licc-func-server'
# 骨架api前缀（单击版本需与业务系统api前缀一致）
VUE_APP_BASE_LICC_API = 'licc-func-server'
# 业务系统api前缀
VUE_APP_BASE_API = 'proof-derate-api'
VUE_APP_DOWN_URL=''
# 部署应用包时的基本URL
VUE_APP_PUBLIC_PATH = 'proof-derate-web'
#骨架34:6021公钥
VUE_APP_ENCRYPT_KEY = '308193020100301306072a8648ce3d020106082a811ccf5501822d047930770201010420820f5359d6d2c5a08170b9da36d7603d0cbb69d4e64a59dd35af1155517bca5ba00a06082a811ccf5501822da14403420004b82c84e07aac3062970e33257e3d9e9551913abaaa4f944244e8f97d30b976c3464dfce37f80d3d78299b951d2d4c3f299b28b5a25582192d8c90998bb11a47a'
VUE_APP_DECRYPT_KEY = ''
