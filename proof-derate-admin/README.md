# 无证明城市

# 版本号
V1.3029.0_20250214

## 代码目录

- build: 打包相关

- docs: 使用文档相关

- mock: 生成随机数据相关

- public: 目标html文件

|- src: 源码
  |- api: 请求相关函数
  |- assets: 静态资源
  |- components: 组件
    |- base: 公共组件
  |- directive: 全局指令
  |- enum: 枚举
  |- icons: 存放svg图标文件
  |- layout: 全局布局
  |- mixins: 存放mixins文件
  |- router: vuerouter
  |- store: vuex
  |- styles: 全局样式
  |- utils: 工具函数
    |-sm-encrypt-utils sm加解密工具
  |- views: 页面代码
    |- commonPack: 公共模块
    |- palt-manage: 平台管理
      |- department: 部门管理
    |- access-system: 接口管理
  |- main.js: 入口文件

|- tests: 测试用例


|- 国密算法sm2
  获取密钥对

  let keypair = sm2.generateKeyPairHex()

  publicKey = keypair.publicKey // 公钥
  privateKey = keypair.privateKey // 私钥

  // 默认生成公钥 130 位太长，可以压缩公钥到 66 位
  const compressedPublicKey = sm2.compressPublicKeyHex(publicKey) // compressedPublicKey 和 publicKey 等价
  sm2.comparePublicKeyHex(publicKey, compressedPublicKey) // 判断公钥是否等价

  // 自定义随机数，参数会直接透传给 jsbn 库的 BigInteger 构造器
  // 注意：开发者使用自定义随机数，需要自行确保传入的随机数符合密码学安全
  let keypair2 = sm2.generateKeyPairHex('123123123123123')
  let keypair3 = sm2.generateKeyPairHex(256, SecureRandom)

  let verifyResult = sm2.verifyPublicKey(publicKey) // 验证公钥
  verifyResult = sm2.verifyPublicKey(compressedPublicKey) // 验证公钥

  加密解密

  const cipherMode = 1 // 1 - C1C3C2，0 - C1C2C3，默认为1

  let encryptData = sm2.doEncrypt(msgString, publicKey, cipherMode) // 加密结果
  let decryptData = sm2.doDecrypt(encryptData, privateKey, cipherMode) // 解密结果

  encryptData = sm2.doEncrypt(msgArray, publicKey, cipherMode) // 加密结果，输入数组
  decryptData = sm2.doDecrypt(encryptData, privateKey, cipherMode, {output: 'array'}) // 解密结果，输出数组


  sm2Encode() // 加密结果考虑到后端解密程序，加密出来的密文开头会加上04
  sm2Decode() // 解密会提前判断是否为后端程序的密文，如果是后端返回的私钥则会进行解密为sm-crypto可以解密的格式进行解密

## Build Setup

```bash
# clone the project
git clone http://*************:7901/dzzz/materials/materials-admin.git

# enter the project directory
cd materials-admin

# install dependency
npm install

# develop
npm run dev
```

This will automatically open http://localhost:9528

## Build

```bash
# build for test environment
npm run build:stage

# build for production environment
npm run build:prod
```

## Advanced

```bash
# preview the release environment effect
npm run preview

# preview the release environment effect + static resource analysis
npm run preview -- --report

# code format check
npm run lint

# code format check and auto fix
npm run lint -- --fix
```
sm3加密修改源码部分 node_modules->sm-crypto->src->sm3->index.js

//入参增加了摘要次数
function(input, times, options) {
    input = typeof input === 'string' ? utf8ToArray(input) : Array.prototype.slice.call(input)

    if (options) {
        const mode = options.mode || 'hmac'
        if (mode !== 'hmac') throw new Error('invalid mode')

        let key = options.key
        if (!key) throw new Error('invalid key')

        key = typeof key === 'string' ? hexToArray(key) : Array.prototype.slice.call(key)
        return ArrayToHex(hmac(input, key))
    }

    //源码改动(改动前)
    // return ArrayToHex(sm3(input))

    //摘要次数-源码改动(改动后)
    let tepTimes = times ? times : 1
    let temp = input
    for (let i = 0; i < tepTimes; i++) {
        if (i === 0) {
            temp = sm3(temp)
        } else {
            temp = sm3(temp)
        }
    }
    //源码改动(改动后)
    return ArrayToHex(temp)
}

sm3加密使用方法
import { sm3Encrypt } from "@/utils/sm-encrypt-utils"
 let text = '我是中智'  //明文
 let salt = 'zsoft'    //盐值
 let saltPosition = 3   //盐值位置 默认值:0
 let digestTime = 3     //摘要次数
 let encryptText = sm3Encrypt(text, salt, saltPosition, digestTime)


sm4使用方法
import {  generateSM4RandomKey } from "@/utils/sm-encrypt-utils"
 const sm4 = require('sm-crypto').sm4
 //加密
 //生成随机key，如有需要请自动输入
 let randomKey = generateSM4RandomKey()
 let encryptText = sm4.encrypt(text,randomKey)

 //解密
 //生成随机key，如有需要请自动输入
 let randomKey = generateSM4RandomKey()
 let encryptText = sm4.decrypt(text,randomKey)


# 骨架2.0.1集群部署和单机升级注意事项
1.菜单改造
  菜单管理页面上配置目录、菜单时需加上项目名称。例：project/abc
  本地路由配置，hidden=true的路由需加多一个参数fatherPath，值取当前高亮菜单的path值。
2.api前缀
  如有多个业务系统api前缀或者是集群版本，需配置多个request文件，参考api/request下的案例。
  集群版的骨架api前缀需独立，单机设置成跟业务一致。
3.公钥需注意单机与集群有可能不同需区分